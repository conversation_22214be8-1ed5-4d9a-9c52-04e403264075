"""
商品排名和对比系统

提供商品综合排名、分类排名、对比分析等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.comprehensive_analyzer import ComprehensiveAnalyzer, ComprehensiveScore

logger = get_logger(__name__)


class RankingCriteria(Enum):
    """排名标准"""
    OVERALL_SCORE = "overall_score"           # 综合评分
    PRICE_COMPETITIVENESS = "price_competitiveness"  # 价格竞争力
    SALES_PERFORMANCE = "sales_performance"   # 销量表现
    CUSTOMER_SATISFACTION = "customer_satisfaction"  # 客户满意度
    MARKET_POTENTIAL = "market_potential"     # 市场潜力
    RISK_ADJUSTED_SCORE = "risk_adjusted_score"  # 风险调整评分


class RankingCategory(Enum):
    """排名类别"""
    ALL_PRODUCTS = "all_products"         # 全部商品
    BY_PRODUCT_TYPE = "by_product_type"   # 按商品类型
    BY_PRICE_RANGE = "by_price_range"     # 按价格区间
    BY_SALES_VOLUME = "by_sales_volume"   # 按销量区间
    TOP_PERFORMERS = "top_performers"     # 表现优秀
    UNDERPERFORMERS = "underperformers"   # 表现不佳


@dataclass
class RankingItem:
    """排名项目"""
    product_id: str
    product_title: str
    rank: int
    score: float
    grade: str
    key_metrics: Dict[str, float]
    strengths: List[str]
    weaknesses: List[str]
    trend: str  # improving, declining, stable


@dataclass
class RankingResult:
    """排名结果"""
    ranking_id: str
    criteria: RankingCriteria
    category: RankingCategory
    total_products: int
    rankings: List[RankingItem]
    category_stats: Dict[str, float]
    insights: List[str]
    generated_at: datetime = field(default_factory=datetime.now)


@dataclass
class ComparisonMetric:
    """对比指标"""
    metric_name: str
    product_values: Dict[str, float]
    best_product: str
    worst_product: str
    average_value: float
    variance: float


@dataclass
class ProductComparison:
    """商品对比"""
    comparison_id: str
    product_ids: List[str]
    comparison_metrics: List[ComparisonMetric]
    overall_winner: str
    detailed_analysis: Dict[str, Dict[str, Any]]
    recommendations: List[str]
    generated_at: datetime = field(default_factory=datetime.now)


class ProductRankingSystem:
    """商品排名和对比系统"""
    
    def __init__(self):
        self.comprehensive_analyzer = ComprehensiveAnalyzer()
        self.ranking_cache: Dict[str, RankingResult] = {}
        self.comparison_cache: Dict[str, ProductComparison] = {}
        
        # 价格区间定义
        self.price_ranges = {
            "budget": (0, 100),      # 经济型
            "mid_range": (100, 500), # 中端
            "premium": (500, 1000),  # 高端
            "luxury": (1000, float('inf'))  # 奢侈品
        }
        
        # 销量区间定义
        self.sales_ranges = {
            "low": (0, 1000),        # 低销量
            "medium": (1000, 5000),  # 中等销量
            "high": (5000, 10000),   # 高销量
            "very_high": (10000, float('inf'))  # 超高销量
        }
    
    async def generate_product_ranking(self, products: List[Product],
                                     criteria: RankingCriteria = RankingCriteria.OVERALL_SCORE,
                                     category: RankingCategory = RankingCategory.ALL_PRODUCTS,
                                     limit: int = 50) -> RankingResult:
        """
        生成商品排名
        
        Args:
            products: 商品列表
            criteria: 排名标准
            category: 排名类别
            limit: 返回数量限制
        
        Returns:
            RankingResult: 排名结果
        """
        try:
            logger.info(f"开始生成商品排名: {len(products)} 个商品")
            
            # 检查缓存
            cache_key = f"{criteria.value}_{category.value}_{len(products)}_{limit}"
            if cache_key in self.ranking_cache:
                cached_result = self.ranking_cache[cache_key]
                if (datetime.now() - cached_result.generated_at).seconds < 3600:  # 1小时缓存
                    return cached_result
            
            # 过滤商品
            filtered_products = self._filter_products_by_category(products, category)
            
            if not filtered_products:
                return self._create_empty_ranking(criteria, category)
            
            # 生成综合分析报告
            reports = await self.comprehensive_analyzer.batch_generate_reports(filtered_products)
            
            # 创建排名项目
            ranking_items = []
            for product in filtered_products:
                if product.id in reports:
                    report = reports[product.id]
                    ranking_item = self._create_ranking_item(product, report, criteria)
                    ranking_items.append(ranking_item)
            
            # 排序
            ranking_items.sort(key=lambda x: x.score, reverse=True)
            
            # 分配排名
            for i, item in enumerate(ranking_items):
                item.rank = i + 1
            
            # 限制数量
            ranking_items = ranking_items[:limit]
            
            # 计算统计信息
            category_stats = self._calculate_category_stats(ranking_items)
            
            # 生成洞察
            insights = self._generate_ranking_insights(ranking_items, criteria, category)
            
            # 创建排名结果
            result = RankingResult(
                ranking_id=f"ranking_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                criteria=criteria,
                category=category,
                total_products=len(ranking_items),
                rankings=ranking_items,
                category_stats=category_stats,
                insights=insights
            )
            
            # 缓存结果
            self.ranking_cache[cache_key] = result
            
            logger.info(f"商品排名生成完成: {len(ranking_items)} 个商品")
            return result
            
        except Exception as e:
            logger.error(f"商品排名生成失败: {e}")
            return self._create_empty_ranking(criteria, category)
    
    def _filter_products_by_category(self, products: List[Product], 
                                   category: RankingCategory) -> List[Product]:
        """按类别过滤商品"""
        if category == RankingCategory.ALL_PRODUCTS:
            return products
        
        elif category == RankingCategory.BY_PRODUCT_TYPE:
            # 按产品类型分组，这里返回竞品类型
            return [p for p in products if p.product_type == ProductType.COMPETITOR]
        
        elif category == RankingCategory.BY_PRICE_RANGE:
            # 返回中端价格区间的商品
            return [p for p in products 
                   if p.price and self.price_ranges["mid_range"][0] <= p.price.current_price <= self.price_ranges["mid_range"][1]]
        
        elif category == RankingCategory.BY_SALES_VOLUME:
            # 返回高销量商品
            return [p for p in products 
                   if p.metrics and p.metrics.sales_count >= self.sales_ranges["high"][0]]
        
        elif category == RankingCategory.TOP_PERFORMERS:
            # 这里需要先计算评分，简化实现返回有评分的商品
            return [p for p in products if p.metrics and p.metrics.sales_count > 1000]
        
        elif category == RankingCategory.UNDERPERFORMERS:
            # 返回表现不佳的商品
            return [p for p in products if p.metrics and p.metrics.sales_count < 500]
        
        else:
            return products
    
    def _create_ranking_item(self, product: Product, report, criteria: RankingCriteria) -> RankingItem:
        """创建排名项目"""
        # 根据排名标准获取评分
        if criteria == RankingCriteria.OVERALL_SCORE:
            score = report.comprehensive_score.overall_score
        elif criteria == RankingCriteria.PRICE_COMPETITIVENESS:
            price_component = next((c for c in report.comprehensive_score.components 
                                  if c.category.value == "price_competitiveness"), None)
            score = price_component.score if price_component else 0
        elif criteria == RankingCriteria.SALES_PERFORMANCE:
            sales_component = next((c for c in report.comprehensive_score.components 
                                  if c.category.value == "sales_performance"), None)
            score = sales_component.score if sales_component else 0
        elif criteria == RankingCriteria.CUSTOMER_SATISFACTION:
            satisfaction_component = next((c for c in report.comprehensive_score.components 
                                         if c.category.value == "customer_satisfaction"), None)
            score = satisfaction_component.score if satisfaction_component else 0
        elif criteria == RankingCriteria.MARKET_POTENTIAL:
            potential_component = next((c for c in report.comprehensive_score.components 
                                      if c.category.value == "market_potential"), None)
            score = potential_component.score if potential_component else 0
        elif criteria == RankingCriteria.RISK_ADJUSTED_SCORE:
            # 风险调整评分 = 综合评分 * (1 - 风险分数/100)
            risk_factor = 1 - (report.risk_assessment.risk_score / 100)
            score = report.comprehensive_score.overall_score * risk_factor
        else:
            score = report.comprehensive_score.overall_score
        
        # 收集关键指标
        key_metrics = {}
        if product.price:
            key_metrics["price"] = product.price.current_price
        if product.metrics:
            key_metrics["sales"] = product.metrics.sales_count or 0
            if hasattr(product.metrics, 'rating'):
                key_metrics["rating"] = getattr(product.metrics, 'rating', 0)
        
        return RankingItem(
            product_id=product.id,
            product_title=product.title,
            rank=0,  # 将在排序后设置
            score=score,
            grade=report.comprehensive_score.grade,
            key_metrics=key_metrics,
            strengths=report.comprehensive_score.strengths,
            weaknesses=report.comprehensive_score.weaknesses,
            trend=report.comprehensive_score.score_trend
        )
    
    def _calculate_category_stats(self, ranking_items: List[RankingItem]) -> Dict[str, float]:
        """计算类别统计信息"""
        if not ranking_items:
            return {}
        
        scores = [item.score for item in ranking_items]
        
        return {
            "average_score": statistics.mean(scores),
            "median_score": statistics.median(scores),
            "max_score": max(scores),
            "min_score": min(scores),
            "score_std": statistics.stdev(scores) if len(scores) > 1 else 0,
            "top_10_percent_threshold": sorted(scores, reverse=True)[max(0, len(scores)//10 - 1)] if len(scores) >= 10 else max(scores)
        }
    
    def _generate_ranking_insights(self, ranking_items: List[RankingItem],
                                 criteria: RankingCriteria, category: RankingCategory) -> List[str]:
        """生成排名洞察"""
        insights = []
        
        if not ranking_items:
            return ["无可分析的商品数据"]
        
        # 基本统计洞察
        total_count = len(ranking_items)
        avg_score = statistics.mean([item.score for item in ranking_items])
        
        insights.append(f"共分析 {total_count} 个商品，平均{criteria.value}评分 {avg_score:.1f}")
        
        # 顶部商品洞察
        if ranking_items:
            top_item = ranking_items[0]
            insights.append(f"排名第一：{top_item.product_title[:30]}...，评分 {top_item.score:.1f}")
        
        # 评分分布洞察
        high_score_count = len([item for item in ranking_items if item.score >= 80])
        if high_score_count > 0:
            insights.append(f"有 {high_score_count} 个商品评分达到80分以上")
        
        low_score_count = len([item for item in ranking_items if item.score < 60])
        if low_score_count > 0:
            insights.append(f"有 {low_score_count} 个商品评分低于60分，需要重点关注")
        
        # 等级分布洞察
        grade_distribution = {}
        for item in ranking_items:
            grade_distribution[item.grade] = grade_distribution.get(item.grade, 0) + 1
        
        if grade_distribution:
            top_grade = max(grade_distribution, key=grade_distribution.get)
            insights.append(f"最常见等级：{top_grade}级，共 {grade_distribution[top_grade]} 个商品")
        
        return insights
    
    async def compare_products(self, products: List[Product]) -> ProductComparison:
        """
        对比商品
        
        Args:
            products: 要对比的商品列表
        
        Returns:
            ProductComparison: 商品对比结果
        """
        try:
            logger.info(f"开始商品对比: {len(products)} 个商品")
            
            if len(products) < 2:
                raise ValueError("至少需要2个商品进行对比")
            
            # 检查缓存
            product_ids = sorted([p.id for p in products])
            cache_key = "_".join(product_ids)
            if cache_key in self.comparison_cache:
                cached_result = self.comparison_cache[cache_key]
                if (datetime.now() - cached_result.generated_at).seconds < 1800:  # 30分钟缓存
                    return cached_result
            
            # 生成综合分析报告
            reports = await self.comprehensive_analyzer.batch_generate_reports(products)
            
            # 创建对比指标
            comparison_metrics = self._create_comparison_metrics(products, reports)
            
            # 确定总体优胜者
            overall_winner = self._determine_overall_winner(products, reports)
            
            # 生成详细分析
            detailed_analysis = self._generate_detailed_analysis(products, reports)
            
            # 生成建议
            recommendations = self._generate_comparison_recommendations(products, reports, comparison_metrics)
            
            # 创建对比结果
            result = ProductComparison(
                comparison_id=f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                product_ids=product_ids,
                comparison_metrics=comparison_metrics,
                overall_winner=overall_winner,
                detailed_analysis=detailed_analysis,
                recommendations=recommendations
            )
            
            # 缓存结果
            self.comparison_cache[cache_key] = result
            
            logger.info(f"商品对比完成: {len(products)} 个商品")
            return result
            
        except Exception as e:
            logger.error(f"商品对比失败: {e}")
            return ProductComparison(
                comparison_id="error_comparison",
                product_ids=[p.id for p in products],
                comparison_metrics=[],
                overall_winner="",
                detailed_analysis={},
                recommendations=["对比分析失败，请检查商品数据"]
            )
    
    def _create_comparison_metrics(self, products: List[Product], reports: Dict[str, Any]) -> List[ComparisonMetric]:
        """创建对比指标"""
        metrics = []
        
        # 综合评分对比
        overall_scores = {}
        for product in products:
            if product.id in reports:
                overall_scores[product.id] = reports[product.id].comprehensive_score.overall_score
            else:
                overall_scores[product.id] = 0
        
        if overall_scores:
            metrics.append(ComparisonMetric(
                metric_name="综合评分",
                product_values=overall_scores,
                best_product=max(overall_scores, key=overall_scores.get),
                worst_product=min(overall_scores, key=overall_scores.get),
                average_value=float(statistics.mean(overall_scores.values())),
                variance=float(statistics.variance(overall_scores.values())) if len(overall_scores) > 1 else 0.0
            ))
        
        # 价格对比
        prices = {}
        for product in products:
            if product.price and product.price.current_price > 0:
                prices[product.id] = product.price.current_price
        
        if prices:
            metrics.append(ComparisonMetric(
                metric_name="价格",
                product_values=prices,
                best_product=min(prices, key=prices.get),  # 价格越低越好
                worst_product=max(prices, key=prices.get),
                average_value=float(statistics.mean(prices.values())),
                variance=float(statistics.variance(prices.values())) if len(prices) > 1 else 0.0
            ))
        
        # 销量对比
        sales = {}
        for product in products:
            if product.metrics and product.metrics.sales_count:
                sales[product.id] = product.metrics.sales_count
        
        if sales:
            metrics.append(ComparisonMetric(
                metric_name="销量",
                product_values=sales,
                best_product=max(sales, key=sales.get),
                worst_product=min(sales, key=sales.get),
                average_value=float(statistics.mean(sales.values())),
                variance=float(statistics.variance(sales.values())) if len(sales) > 1 else 0.0
            ))
        
        # 客户评分对比
        ratings = {}
        for product in products:
            if product.metrics and hasattr(product.metrics, 'rating'):
                rating = getattr(product.metrics, 'rating', 0)
                if rating > 0:
                    ratings[product.id] = rating
        
        if ratings:
            metrics.append(ComparisonMetric(
                metric_name="客户评分",
                product_values=ratings,
                best_product=max(ratings, key=ratings.get),
                worst_product=min(ratings, key=ratings.get),
                average_value=float(statistics.mean(ratings.values())),
                variance=float(statistics.variance(ratings.values())) if len(ratings) > 1 else 0.0
            ))
        
        return metrics
    
    def _determine_overall_winner(self, products: List[Product], reports: Dict[str, Any]) -> str:
        """确定总体优胜者"""
        best_product_id = ""
        best_score = -1
        
        for product in products:
            if product.id in reports:
                score = reports[product.id].comprehensive_score.overall_score
                if score > best_score:
                    best_score = score
                    best_product_id = product.id
        
        return best_product_id
    
    def _generate_detailed_analysis(self, products: List[Product], reports: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """生成详细分析"""
        analysis = {}
        
        for product in products:
            if product.id in reports:
                report = reports[product.id]
                analysis[product.id] = {
                    "overall_score": report.comprehensive_score.overall_score,
                    "grade": report.comprehensive_score.grade,
                    "strengths": report.comprehensive_score.strengths,
                    "weaknesses": report.comprehensive_score.weaknesses,
                    "risk_level": report.risk_assessment.overall_risk_level,
                    "opportunities_count": len(report.market_opportunities),
                    "key_insights": report.key_insights[:3]  # 前3个洞察
                }
            else:
                analysis[product.id] = {
                    "overall_score": 0,
                    "grade": "N/A",
                    "strengths": [],
                    "weaknesses": ["数据不足"],
                    "risk_level": "unknown",
                    "opportunities_count": 0,
                    "key_insights": ["无法分析"]
                }
        
        return analysis
    
    def _generate_comparison_recommendations(self, products: List[Product], reports: Dict[str, Any],
                                           metrics: List[ComparisonMetric]) -> List[str]:
        """生成对比建议"""
        recommendations = []
        
        # 基于综合评分的建议
        overall_metric = next((m for m in metrics if m.metric_name == "综合评分"), None)
        if overall_metric:
            best_product = next((p for p in products if p.id == overall_metric.best_product), None)
            if best_product:
                recommendations.append(f"推荐选择 {best_product.title[:30]}...，综合表现最佳")
        
        # 基于价格的建议
        price_metric = next((m for m in metrics if m.metric_name == "价格"), None)
        if price_metric:
            cheapest_product = next((p for p in products if p.id == price_metric.best_product), None)
            if cheapest_product:
                recommendations.append(f"价格最优：{cheapest_product.title[:30]}...")
        
        # 基于销量的建议
        sales_metric = next((m for m in metrics if m.metric_name == "销量"), None)
        if sales_metric:
            top_sales_product = next((p for p in products if p.id == sales_metric.best_product), None)
            if top_sales_product:
                recommendations.append(f"市场热度最高：{top_sales_product.title[:30]}...")
        
        # 基于风险的建议
        low_risk_products = []
        for product in products:
            if product.id in reports:
                risk_level = reports[product.id].risk_assessment.overall_risk_level
                if risk_level in ["very_low", "low"]:
                    low_risk_products.append(product)
        
        if low_risk_products:
            recommendations.append(f"低风险选择：{low_risk_products[0].title[:30]}...")
        
        return recommendations if recommendations else ["建议根据具体需求选择合适商品"]
    
    def _create_empty_ranking(self, criteria: RankingCriteria, category: RankingCategory) -> RankingResult:
        """创建空排名结果"""
        return RankingResult(
            ranking_id="empty_ranking",
            criteria=criteria,
            category=category,
            total_products=0,
            rankings=[],
            category_stats={},
            insights=["无可排名的商品数据"]
        )
    
    def get_ranking_statistics(self) -> Dict[str, Any]:
        """获取排名统计信息"""
        return {
            "cached_rankings": len(self.ranking_cache),
            "cached_comparisons": len(self.comparison_cache),
            "ranking_criteria": [criteria.value for criteria in RankingCriteria],
            "ranking_categories": [category.value for category in RankingCategory],
            "price_ranges": self.price_ranges,
            "sales_ranges": self.sales_ranges
        }
