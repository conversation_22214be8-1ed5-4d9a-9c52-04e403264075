# Moniit 系统环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境配置
MONIIT_ENV=development
NODE_ENV=development
FRONTEND_TARGET=development

# 安全配置
SECRET_KEY=dev-secret-key-for-development-only
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 数据库配置 - 使用TimescaleDB
DATABASE_URL=*************************************/moniit

# TimescaleDB/PostgreSQL配置
POSTGRES_DB=moniit
POSTGRES_USER=moniit
POSTGRES_PASSWORD=moniit123

# Redis配置
REDIS_PASSWORD=
# 生产环境建议设置密码
# REDIS_PASSWORD=your-redis-password

# 日志配置
LOG_LEVEL=INFO
DEBUG=false

# 邮件配置
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=false
SMTP_SSL=false

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads

# 监控配置
MONITOR_INTERVAL=300  # 5分钟
MAX_CONCURRENT_MONITORS=10
REQUEST_TIMEOUT=30

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# API配置
API_V1_PREFIX=/api/v1
API_RATE_LIMIT=100  # 每分钟请求数

# 前端配置
REACT_APP_API_URL=http://localhost:8000
REACT_APP_TITLE=Moniit商品监控系统
REACT_APP_VERSION=1.0.0

# 开发工具配置（仅开发环境）
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true
FAST_REFRESH=true

# 生产环境配置示例
# MONIIT_ENV=production
# NODE_ENV=production
# FRONTEND_TARGET=production
# DEBUG=false
# LOG_LEVEL=WARNING
# SECRET_KEY=your-production-secret-key-min-32-chars
# DATABASE_URL=**********************************/moniit_prod
# REDIS_PASSWORD=your-production-redis-password
# CORS_ORIGINS=https://yourdomain.com
