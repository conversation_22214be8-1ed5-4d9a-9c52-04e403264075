"""
Task-Middleware API客户端

统一的爬虫任务提交和管理接口
支持批量任务提交、状态查询和结果获取
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from app.core.logging import get_logger
from app.core.config import get_settings

logger = get_logger(__name__)
settings = get_settings()


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    PERMANENTLY_FAILED = "permanently_failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class CrawlConfig:
    """爬取配置"""
    urls: List[str]
    query: str  # LLM查询指令
    schema_str: Optional[str] = None  # LLM输出模式
    cache: bool = False
    priority: TaskPriority = TaskPriority.MEDIUM
    batch_name: Optional[str] = None
    batch_description: Optional[str] = None
    callback_url: Optional[str] = None
    max_concurrent_tasks: Optional[int] = None
    batch_timeout: Optional[int] = None


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    batch_id: str
    url: str
    status: TaskStatus
    priority: TaskPriority
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    processing_duration: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None


@dataclass
class BatchTaskResult:
    """批量任务结果"""
    success: bool
    batch_id: str
    task_ids: List[str]
    total_tasks: int
    valid_tasks: int
    invalid_tasks: int
    invalid_urls: List[str]
    created_at: datetime
    message: str


class TaskMiddlewareClient:
    """Task-Middleware API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:11238"):
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            
    async def _ensure_session(self):
        """确保会话存在"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
    async def submit_crawl_task(self, config: CrawlConfig) -> BatchTaskResult:
        """
        提交爬取任务
        
        Args:
            config: 爬取配置
            
        Returns:
            BatchTaskResult: 批量任务提交结果
        """
        await self._ensure_session()
        
        # 构建请求数据
        request_data = {
            "request": {
                "urls": config.urls,
                "q": config.query,
                "schema_str": config.schema_str,
                "cache": config.cache,
                "priority": config.priority.value,
                "batch_name": config.batch_name,
                "batch_description": config.batch_description,
                "callback_url": config.callback_url,
                "max_concurrent_tasks": config.max_concurrent_tasks,
                "batch_timeout": config.batch_timeout
            }
        }
        
        try:
            url = f"{self.base_url}/api/v1/tasks/batch"
            logger.info(f"提交批量任务到 {url}, URLs数量: {len(config.urls)}")
            
            async with self.session.post(url, json=request_data) as response:
                if response.status == 200:
                    data = await response.json()
                    result = BatchTaskResult(
                        success=data["success"],
                        batch_id=data["batch_id"],
                        task_ids=data["task_ids"],
                        total_tasks=data["total_tasks"],
                        valid_tasks=data["valid_tasks"],
                        invalid_tasks=data["invalid_tasks"],
                        invalid_urls=data.get("invalid_urls", []),
                        created_at=datetime.fromisoformat(data["created_at"]),
                        message=data["message"]
                    )
                    logger.info(f"任务提交成功: batch_id={result.batch_id}, 有效任务={result.valid_tasks}")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"任务提交失败: {response.status} - {error_text}")
                    raise Exception(f"任务提交失败: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"提交爬取任务失败: {e}")
            raise
            
    async def get_task_status(self, task_id: str) -> TaskResult:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            TaskResult: 任务状态信息
        """
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/api/v1/tasks/{task_id}"
            logger.debug(f"查询任务状态: {task_id}")
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_task_result(data)
                else:
                    error_text = await response.text()
                    logger.error(f"获取任务状态失败: {response.status} - {error_text}")
                    raise Exception(f"获取任务状态失败: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            raise

    async def get_crawl_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取爬取结果

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict[str, Any]]: 爬取结果数据
        """
        await self._ensure_session()

        try:
            url = f"{self.base_url}/api/v1/tasks/{task_id}/result"
            logger.debug(f"获取任务结果: {task_id}")

            async with self.session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.debug(f"成功获取任务结果: {task_id}")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"获取任务结果失败: {response.status} - {error_text}")
                    return None

        except Exception as e:
            logger.error(f"获取任务结果失败: {e}")
            return None

    async def get_batch_tasks(self, batch_id: str, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """
        获取批次任务列表

        Args:
            batch_id: 批次ID
            page: 页码
            size: 每页大小

        Returns:
            Dict[str, Any]: 批次任务信息
        """
        await self._ensure_session()

        try:
            url = f"{self.base_url}/api/v1/tasks/batch/{batch_id}"
            params = {"page": page, "size": size}
            logger.debug(f"获取批次任务: {batch_id}, 页码: {page}")

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"成功获取批次任务: {batch_id}, 任务数: {len(data.get('tasks', []))}")
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"获取批次任务失败: {response.status} - {error_text}")
                    raise Exception(f"获取批次任务失败: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"获取批次任务失败: {e}")
            raise

    async def batch_submit_tasks(self, configs: List[CrawlConfig]) -> List[BatchTaskResult]:
        """
        批量提交多个爬取任务

        Args:
            configs: 爬取配置列表

        Returns:
            List[BatchTaskResult]: 批量任务提交结果列表
        """
        results = []

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(5)  # 最多同时提交5个批次

        async def submit_single_config(config: CrawlConfig) -> BatchTaskResult:
            async with semaphore:
                return await self.submit_crawl_task(config)

        try:
            logger.info(f"开始批量提交任务，配置数量: {len(configs)}")

            # 并发提交所有配置
            tasks = [submit_single_config(config) for config in configs]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理异常结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"配置 {i} 提交失败: {result}")
                else:
                    success_count += 1

            logger.info(f"批量提交完成，成功: {success_count}/{len(configs)}")

            # 过滤掉异常结果
            return [r for r in results if not isinstance(r, Exception)]

        except Exception as e:
            logger.error(f"批量提交任务失败: {e}")
            raise

    def _parse_task_result(self, data: Dict[str, Any]) -> TaskResult:
        """
        解析任务结果数据

        Args:
            data: API返回的任务数据

        Returns:
            TaskResult: 解析后的任务结果
        """
        return TaskResult(
            task_id=data["id"],
            batch_id=data["batch_id"],
            url=data["url"],
            status=TaskStatus(data["status"]),
            priority=TaskPriority(data["priority"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            started_at=datetime.fromisoformat(data["started_at"]) if data.get("started_at") else None,
            completed_at=datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
            retry_count=data["retry_count"],
            max_retries=data["max_retries"],
            processing_duration=data.get("processing_duration"),
            result=data.get("result"),
            error=data.get("error"),
            config=data.get("config")
        )

    async def close(self):
        """关闭客户端会话"""
        if self.session:
            await self.session.close()
            self.session = None
