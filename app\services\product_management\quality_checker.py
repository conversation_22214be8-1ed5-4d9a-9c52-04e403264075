"""
数据质量检查器

负责检查商品数据的完整性、准确性和一致性
"""

import re
import asyncio
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from app.core.logging import get_logger
from app.models.product import Product, DataQuality

logger = get_logger(__name__)


@dataclass
class QualityIssue:
    """质量问题"""
    type: str
    severity: str  # critical, warning, info
    field: str
    message: str
    suggestion: Optional[str] = None


@dataclass
class QualityReport:
    """质量报告"""
    product_id: str
    overall_score: float
    quality_level: DataQuality
    issues: List[QualityIssue]
    passed_checks: int
    total_checks: int
    generated_at: datetime


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.quality_rules = [
            self._check_basic_info,
            self._check_price_validity,
            self._check_image_quality,
            self._check_seller_info,
            self._check_metrics_consistency,
            self._check_url_validity,
            self._check_title_quality,
            self._check_data_freshness,
        ]
    
    async def check_product_quality(self, product: Product) -> QualityReport:
        """
        检查商品数据质量
        
        Args:
            product: 商品对象
        
        Returns:
            QualityReport: 质量报告
        """
        try:
            logger.info(f"开始质量检查: {product.id}")
            
            issues = []
            passed_checks = 0
            total_checks = len(self.quality_rules)
            
            # 执行所有质量检查规则
            for rule in self.quality_rules:
                try:
                    rule_issues = await rule(product)
                    if rule_issues:
                        issues.extend(rule_issues)
                    else:
                        passed_checks += 1
                except Exception as e:
                    logger.error(f"质量检查规则执行失败: {e}")
                    issues.append(QualityIssue(
                        type="system_error",
                        severity="warning",
                        field="system",
                        message=f"检查规则执行失败: {str(e)}"
                    ))
            
            # 计算总体质量分数
            overall_score = await self._calculate_overall_score(product, issues, passed_checks, total_checks)
            quality_level = self._get_quality_level(overall_score)
            
            report = QualityReport(
                product_id=product.id,
                overall_score=overall_score,
                quality_level=quality_level,
                issues=issues,
                passed_checks=passed_checks,
                total_checks=total_checks,
                generated_at=datetime.now()
            )
            
            logger.info(f"质量检查完成: {product.id}, 分数: {overall_score:.2f}, 问题数: {len(issues)}")
            return report
            
        except Exception as e:
            logger.error(f"质量检查失败: {e}")
            return QualityReport(
                product_id=product.id,
                overall_score=0.0,
                quality_level=DataQuality.BAD,
                issues=[QualityIssue(
                    type="system_error",
                    severity="critical",
                    field="system",
                    message=f"质量检查系统错误: {str(e)}"
                )],
                passed_checks=0,
                total_checks=len(self.quality_rules),
                generated_at=datetime.now()
            )
    
    async def _check_basic_info(self, product: Product) -> List[QualityIssue]:
        """检查基础信息"""
        issues = []
        
        # 检查标题
        if not product.title or len(product.title.strip()) < 5:
            issues.append(QualityIssue(
                type="missing_data",
                severity="critical",
                field="title",
                message="商品标题缺失或过短",
                suggestion="确保商品标题至少包含5个字符"
            ))
        
        # 检查URL
        if not product.url:
            issues.append(QualityIssue(
                type="missing_data",
                severity="critical",
                field="url",
                message="商品URL缺失"
            ))
        
        # 检查平台信息
        if not product.platform:
            issues.append(QualityIssue(
                type="missing_data",
                severity="warning",
                field="platform",
                message="平台信息缺失"
            ))
        
        return issues
    
    async def _check_price_validity(self, product: Product) -> List[QualityIssue]:
        """检查价格有效性"""
        issues = []
        
        if not product.price:
            issues.append(QualityIssue(
                type="missing_data",
                severity="critical",
                field="price",
                message="价格信息缺失"
            ))
            return issues
        
        # 检查当前价格
        if not product.price.current_price or product.price.current_price <= 0:
            issues.append(QualityIssue(
                type="invalid_data",
                severity="critical",
                field="price.current_price",
                message="当前价格无效或为零"
            ))
        
        # 检查价格合理性
        if product.price.current_price and product.price.current_price > 1000000:
            issues.append(QualityIssue(
                type="suspicious_data",
                severity="warning",
                field="price.current_price",
                message="价格异常高，请确认",
                suggestion="检查价格是否包含了错误的单位或格式"
            ))
        
        # 检查原价和现价的关系
        if (product.price.original_price and product.price.current_price and
            product.price.original_price < product.price.current_price):
            issues.append(QualityIssue(
                type="inconsistent_data",
                severity="warning",
                field="price",
                message="原价低于现价，数据可能不一致"
            ))
        
        # 检查折扣率合理性
        if product.price.discount_rate and product.price.discount_rate > 90:
            issues.append(QualityIssue(
                type="suspicious_data",
                severity="warning",
                field="price.discount_rate",
                message="折扣率过高，可能存在问题",
                suggestion="确认折扣率计算是否正确"
            ))
        
        return issues
    
    async def _check_image_quality(self, product: Product) -> List[QualityIssue]:
        """检查图片质量"""
        issues = []
        
        if not product.images:
            issues.append(QualityIssue(
                type="missing_data",
                severity="warning",
                field="images",
                message="商品图片缺失"
            ))
            return issues
        
        # 检查主图
        main_images = [img for img in product.images if img.is_main]
        if not main_images:
            issues.append(QualityIssue(
                type="missing_data",
                severity="warning",
                field="images.main",
                message="缺少主图标识"
            ))
        elif len(main_images) > 1:
            issues.append(QualityIssue(
                type="inconsistent_data",
                severity="warning",
                field="images.main",
                message="存在多个主图"
            ))
        
        # 检查图片URL有效性
        for i, image in enumerate(product.images):
            if not image.url or not self._is_valid_url(image.url):
                issues.append(QualityIssue(
                    type="invalid_data",
                    severity="warning",
                    field=f"images[{i}].url",
                    message="图片URL无效"
                ))
        
        return issues
    
    async def _check_seller_info(self, product: Product) -> List[QualityIssue]:
        """检查卖家信息"""
        issues = []
        
        if not product.seller:
            issues.append(QualityIssue(
                type="missing_data",
                severity="info",
                field="seller",
                message="卖家信息缺失"
            ))
            return issues
        
        # 检查卖家名称
        if not product.seller.name or len(product.seller.name.strip()) < 2:
            issues.append(QualityIssue(
                type="invalid_data",
                severity="warning",
                field="seller.name",
                message="卖家名称缺失或过短"
            ))
        
        # 检查评分合理性
        if product.seller.rating and (product.seller.rating < 0 or product.seller.rating > 5):
            issues.append(QualityIssue(
                type="invalid_data",
                severity="warning",
                field="seller.rating",
                message="卖家评分超出合理范围(0-5)"
            ))
        
        return issues
    
    async def _check_metrics_consistency(self, product: Product) -> List[QualityIssue]:
        """检查指标数据一致性"""
        issues = []
        
        if not product.metrics:
            return issues
        
        # 检查销量合理性
        if product.metrics.sales_count and product.metrics.sales_count < 0:
            issues.append(QualityIssue(
                type="invalid_data",
                severity="warning",
                field="metrics.sales_count",
                message="销量不能为负数"
            ))
        
        # 检查库存合理性
        if product.metrics.stock_quantity and product.metrics.stock_quantity < 0:
            issues.append(QualityIssue(
                type="invalid_data",
                severity="warning",
                field="metrics.stock_quantity",
                message="库存不能为负数"
            ))
        
        # 检查评分合理性
        if product.metrics.rating and (product.metrics.rating < 0 or product.metrics.rating > 5):
            issues.append(QualityIssue(
                type="invalid_data",
                severity="warning",
                field="metrics.rating",
                message="商品评分超出合理范围(0-5)"
            ))
        
        # 检查评论数和评分的一致性
        if (product.metrics.review_count and product.metrics.review_count > 0 and
            not product.metrics.rating):
            issues.append(QualityIssue(
                type="inconsistent_data",
                severity="info",
                field="metrics",
                message="有评论数但缺少评分信息"
            ))
        
        return issues
    
    async def _check_url_validity(self, product: Product) -> List[QualityIssue]:
        """检查URL有效性"""
        issues = []
        
        if not self._is_valid_url(product.url):
            issues.append(QualityIssue(
                type="invalid_data",
                severity="critical",
                field="url",
                message="商品URL格式无效"
            ))
        
        return issues
    
    async def _check_title_quality(self, product: Product) -> List[QualityIssue]:
        """检查标题质量"""
        issues = []
        
        if not product.title:
            return issues
        
        title = product.title.strip()
        
        # 检查标题长度
        if len(title) > 200:
            issues.append(QualityIssue(
                type="quality_issue",
                severity="info",
                field="title",
                message="标题过长，可能影响显示效果",
                suggestion="建议标题长度控制在200字符以内"
            ))
        
        # 检查是否包含特殊字符过多
        special_char_count = len(re.findall(r'[^\w\s\u4e00-\u9fff]', title))
        if special_char_count > len(title) * 0.3:
            issues.append(QualityIssue(
                type="quality_issue",
                severity="info",
                field="title",
                message="标题包含过多特殊字符"
            ))
        
        return issues
    
    async def _check_data_freshness(self, product: Product) -> List[QualityIssue]:
        """检查数据新鲜度"""
        issues = []
        
        # 检查最后爬取时间
        if product.last_crawled_at:
            time_diff = datetime.now() - product.last_crawled_at
            if time_diff > timedelta(days=7):
                issues.append(QualityIssue(
                    type="stale_data",
                    severity="info",
                    field="last_crawled_at",
                    message="数据可能已过期，建议重新爬取",
                    suggestion="数据超过7天未更新"
                ))
        
        return issues
    
    def _is_valid_url(self, url: str) -> bool:
        """检查URL是否有效"""
        if not url:
            return False
        
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return url_pattern.match(url) is not None
    
    async def _calculate_overall_score(self, product: Product, issues: List[QualityIssue],
                                     passed_checks: int, total_checks: int) -> float:
        """计算总体质量分数"""
        # 基础分数：通过的检查比例
        base_score = passed_checks / total_checks if total_checks > 0 else 0
        
        # 根据问题严重程度扣分
        penalty = 0.0
        for issue in issues:
            if issue.severity == "critical":
                penalty += 0.2
            elif issue.severity == "warning":
                penalty += 0.1
            elif issue.severity == "info":
                penalty += 0.02
        
        # 计算最终分数
        final_score = max(0.0, base_score - penalty)
        
        # 考虑数据完整性加分
        completeness_bonus = await self._calculate_completeness_bonus(product)
        final_score = min(1.0, final_score + completeness_bonus)
        
        return final_score
    
    async def _calculate_completeness_bonus(self, product: Product) -> float:
        """计算数据完整性加分"""
        bonus = 0.0
        
        # 有描述信息
        if product.description and len(product.description.strip()) > 10:
            bonus += 0.05
        
        # 有多张图片
        if len(product.images) > 3:
            bonus += 0.03
        
        # 有规格信息
        if product.specs and (product.specs.brand or product.specs.model):
            bonus += 0.03
        
        # 有完整的价格信息
        if (product.price and product.price.current_price and 
            product.price.original_price):
            bonus += 0.02
        
        # 有销量和评分信息
        if (product.metrics and product.metrics.sales_count and 
            product.metrics.rating):
            bonus += 0.02
        
        return min(0.15, bonus)  # 最多15%的加分
    
    def _get_quality_level(self, score: float) -> DataQuality:
        """根据分数获取质量等级"""
        if score >= 0.9:
            return DataQuality.EXCELLENT
        elif score >= 0.7:
            return DataQuality.GOOD
        elif score >= 0.5:
            return DataQuality.FAIR
        elif score >= 0.3:
            return DataQuality.POOR
        else:
            return DataQuality.BAD
    
    async def batch_check_quality(self, products: List[Product]) -> List[QualityReport]:
        """批量检查商品质量"""
        logger.info(f"开始批量质量检查: {len(products)} 个商品")
        
        tasks = [self.check_product_quality(product) for product in products]
        reports = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_reports = []
        for i, report in enumerate(reports):
            if isinstance(report, Exception):
                logger.error(f"商品 {products[i].id} 质量检查失败: {report}")
                # 创建错误报告
                error_report = QualityReport(
                    product_id=products[i].id,
                    overall_score=0.0,
                    quality_level=DataQuality.BAD,
                    issues=[QualityIssue(
                        type="system_error",
                        severity="critical",
                        field="system",
                        message=f"质量检查异常: {str(report)}"
                    )],
                    passed_checks=0,
                    total_checks=len(self.quality_rules),
                    generated_at=datetime.now()
                )
                valid_reports.append(error_report)
            else:
                valid_reports.append(report)
        
        logger.info(f"批量质量检查完成: {len(valid_reports)} 个报告")
        return valid_reports
