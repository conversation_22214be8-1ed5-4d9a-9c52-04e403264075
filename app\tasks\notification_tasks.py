"""
通知任务

系统通知和警报任务
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from app.core.celery_app import celery_app
from app.core.logging import get_logger

logger = get_logger(__name__)


class NotificationType(Enum):
    """通知类型"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class NotificationChannel(Enum):
    """通知渠道"""
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    SYSTEM = "system"


@celery_app.task(bind=True)
def send_notification(self, message: str, notification_type: str = "info", 
                     channels: List[str] = None, recipients: List[str] = None):
    """
    发送通知
    
    Args:
        message: 通知消息
        notification_type: 通知类型
        channels: 通知渠道列表
        recipients: 接收者列表
    
    Returns:
        Dict: 发送结果
    """
    try:
        logger.info(f"发送通知: {message}")
        
        if channels is None:
            channels = ["system"]
        
        if recipients is None:
            recipients = ["admin"]
        
        notification_data = {
            "id": f"notif_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "message": message,
            "type": notification_type,
            "channels": channels,
            "recipients": recipients,
            "timestamp": datetime.now().isoformat(),
            "status": "sent"
        }
        
        # 模拟发送到不同渠道
        send_results = {}
        for channel in channels:
            if channel == "email":
                send_results[channel] = {"status": "sent", "count": len(recipients)}
            elif channel == "sms":
                send_results[channel] = {"status": "sent", "count": len(recipients)}
            elif channel == "webhook":
                send_results[channel] = {"status": "sent", "url": "https://webhook.example.com"}
            elif channel == "system":
                send_results[channel] = {"status": "logged", "location": "system_notifications"}
        
        logger.info(f"通知发送完成: {notification_data['id']}")
        
        return {
            "success": True,
            "notification": notification_data,
            "send_results": send_results,
            "message": "通知发送成功"
        }
        
    except Exception as exc:
        logger.error(f"发送通知失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def price_alert(self, product_url: str, current_price: float, 
               threshold_price: float, change_percentage: float):
    """
    价格警报
    
    Args:
        product_url: 商品URL
        current_price: 当前价格
        threshold_price: 阈值价格
        change_percentage: 变化百分比
    
    Returns:
        Dict: 警报结果
    """
    try:
        logger.info(f"价格警报: {product_url}, 当前价格: {current_price}")
        
        # 判断警报类型
        if abs(change_percentage) >= 20:
            alert_type = "error"
            urgency = "高"
        elif abs(change_percentage) >= 10:
            alert_type = "warning"
            urgency = "中"
        else:
            alert_type = "info"
            urgency = "低"
        
        message = f"""
价格警报通知

商品URL: {product_url}
当前价格: ¥{current_price}
阈值价格: ¥{threshold_price}
价格变化: {change_percentage:+.1f}%
紧急程度: {urgency}

请及时关注价格变动情况。
        """.strip()
        
        # 发送通知
        notification_result = send_notification.delay(
            message=message,
            notification_type=alert_type,
            channels=["system", "email"],
            recipients=["price_monitor", "admin"]
        )
        
        return {
            "success": True,
            "alert_type": alert_type,
            "urgency": urgency,
            "notification_task_id": notification_result.id,
            "product_url": product_url,
            "price_info": {
                "current": current_price,
                "threshold": threshold_price,
                "change": change_percentage
            },
            "message": "价格警报已发送"
        }
        
    except Exception as exc:
        logger.error(f"价格警报失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def task_failure_alert(self, task_id: str, task_name: str, error_message: str, 
                      retry_count: int = 0):
    """
    任务失败警报
    
    Args:
        task_id: 任务ID
        task_name: 任务名称
        error_message: 错误消息
        retry_count: 重试次数
    
    Returns:
        Dict: 警报结果
    """
    try:
        logger.info(f"任务失败警报: {task_name} ({task_id})")
        
        message = f"""
任务失败警报

任务ID: {task_id}
任务名称: {task_name}
失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
重试次数: {retry_count}
错误信息: {error_message}

请检查任务执行情况并采取相应措施。
        """.strip()
        
        # 根据重试次数确定警报级别
        if retry_count >= 3:
            alert_type = "error"
            channels = ["system", "email", "sms"]
        elif retry_count >= 1:
            alert_type = "warning"
            channels = ["system", "email"]
        else:
            alert_type = "info"
            channels = ["system"]
        
        notification_result = send_notification.delay(
            message=message,
            notification_type=alert_type,
            channels=channels,
            recipients=["task_monitor", "admin"]
        )
        
        return {
            "success": True,
            "alert_type": alert_type,
            "notification_task_id": notification_result.id,
            "task_info": {
                "id": task_id,
                "name": task_name,
                "retry_count": retry_count
            },
            "message": "任务失败警报已发送"
        }
        
    except Exception as exc:
        logger.error(f"任务失败警报失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def system_health_alert(self, component: str, status: str, details: Dict[str, Any]):
    """
    系统健康警报
    
    Args:
        component: 组件名称
        status: 状态
        details: 详细信息
    
    Returns:
        Dict: 警报结果
    """
    try:
        logger.info(f"系统健康警报: {component} - {status}")
        
        # 格式化详细信息
        details_text = "\n".join([f"{k}: {v}" for k, v in details.items()])
        
        message = f"""
系统健康警报

组件: {component}
状态: {status}
检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

详细信息:
{details_text}

请及时检查系统状态。
        """.strip()
        
        # 根据状态确定警报级别
        if status.lower() in ["error", "critical", "down"]:
            alert_type = "error"
            channels = ["system", "email", "sms"]
        elif status.lower() in ["warning", "degraded"]:
            alert_type = "warning"
            channels = ["system", "email"]
        else:
            alert_type = "info"
            channels = ["system"]
        
        notification_result = send_notification.delay(
            message=message,
            notification_type=alert_type,
            channels=channels,
            recipients=["system_admin", "ops_team"]
        )
        
        return {
            "success": True,
            "alert_type": alert_type,
            "notification_task_id": notification_result.id,
            "component": component,
            "status": status,
            "message": "系统健康警报已发送"
        }
        
    except Exception as exc:
        logger.error(f"系统健康警报失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def daily_report(self, report_date: str = None):
    """
    每日报告
    
    Args:
        report_date: 报告日期
    
    Returns:
        Dict: 报告结果
    """
    try:
        if report_date is None:
            report_date = datetime.now().date().isoformat()
        
        logger.info(f"生成每日报告: {report_date}")
        
        # 模拟报告数据
        report_data = {
            "date": report_date,
            "crawl_statistics": {
                "total_tasks": 1250,
                "successful_tasks": 1180,
                "failed_tasks": 70,
                "success_rate": "94.4%"
            },
            "platform_breakdown": {
                "1688": {"tasks": 650, "success_rate": "95.2%"},
                "taobao": {"tasks": 400, "success_rate": "93.5%"},
                "jd": {"tasks": 200, "success_rate": "94.0%"}
            },
            "product_type_breakdown": {
                "competitor": {"tasks": 450, "success_rate": "96.0%"},
                "supplier": {"tasks": 500, "success_rate": "94.8%"},
                "other": {"tasks": 300, "success_rate": "92.3%"}
            },
            "performance_metrics": {
                "avg_response_time": "2.3s",
                "data_quality_score": "0.92",
                "system_uptime": "99.8%"
            }
        }
        
        # 格式化报告
        report_text = f"""
每日运营报告 - {report_date}

=== 爬取统计 ===
总任务数: {report_data['crawl_statistics']['total_tasks']}
成功任务: {report_data['crawl_statistics']['successful_tasks']}
失败任务: {report_data['crawl_statistics']['failed_tasks']}
成功率: {report_data['crawl_statistics']['success_rate']}

=== 平台分布 ===
1688: {report_data['platform_breakdown']['1688']['tasks']} 任务 ({report_data['platform_breakdown']['1688']['success_rate']})
淘宝: {report_data['platform_breakdown']['taobao']['tasks']} 任务 ({report_data['platform_breakdown']['taobao']['success_rate']})
京东: {report_data['platform_breakdown']['jd']['tasks']} 任务 ({report_data['platform_breakdown']['jd']['success_rate']})

=== 商品类型分布 ===
竞品: {report_data['product_type_breakdown']['competitor']['tasks']} 任务 ({report_data['product_type_breakdown']['competitor']['success_rate']})
供货商: {report_data['product_type_breakdown']['supplier']['tasks']} 任务 ({report_data['product_type_breakdown']['supplier']['success_rate']})
其他: {report_data['product_type_breakdown']['other']['tasks']} 任务 ({report_data['product_type_breakdown']['other']['success_rate']})

=== 性能指标 ===
平均响应时间: {report_data['performance_metrics']['avg_response_time']}
数据质量分数: {report_data['performance_metrics']['data_quality_score']}
系统正常运行时间: {report_data['performance_metrics']['system_uptime']}
        """.strip()
        
        # 发送报告
        notification_result = send_notification.delay(
            message=report_text,
            notification_type="info",
            channels=["email", "system"],
            recipients=["management", "ops_team", "data_team"]
        )
        
        return {
            "success": True,
            "report_date": report_date,
            "notification_task_id": notification_result.id,
            "report_data": report_data,
            "message": "每日报告已生成并发送"
        }
        
    except Exception as exc:
        logger.error(f"每日报告生成失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }
