"""
翻译服务系统测试
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from app.services.translation.translation_engine import (
    TranslationEngine, TranslationRequest, LanguageCode, TextType, TranslationStatus
)
from app.services.translation.provider_manager import (
    ProviderManager, ProviderConfig, ProviderType, OpenAIProvider, ClaudeProvider, BaiduProvider
)
from app.services.translation.quality_assessor import (
    QualityAssessor, QualityRule, QualityMetric, QualityLevel
)
from app.services.translation.cache_manager import (
    CacheManager, CacheType
)


class TestTranslationEngine:
    """翻译引擎测试"""
    
    @pytest.fixture
    def translation_components(self):
        """翻译组件"""
        provider_manager = ProviderManager()
        quality_assessor = QualityAssessor()
        cache_manager = CacheManager(CacheType.MEMORY)
        
        return provider_manager, quality_assessor, cache_manager
    
    @pytest.fixture
    def translation_engine(self, translation_components):
        """翻译引擎实例"""
        provider_manager, quality_assessor, cache_manager = translation_components
        return TranslationEngine(provider_manager, quality_assessor, cache_manager)
    
    def test_initialization(self, translation_engine):
        """测试初始化"""
        assert translation_engine.provider_manager is not None
        assert translation_engine.quality_assessor is not None
        assert translation_engine.cache_manager is not None
        assert translation_engine.translation_config["max_concurrent"] == 10
        assert translation_engine.stats["total_requests"] == 0
    
    @pytest.mark.asyncio
    async def test_translate_english_to_chinese(self, translation_engine):
        """测试英文到中文翻译"""
        request = TranslationRequest(
            request_id="test_001",
            text="iPhone 15 Pro smartphone",
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.PRODUCT_TITLE
        )
        
        result = await translation_engine.translate(request)
        
        assert result.request_id == "test_001"
        assert result.status == TranslationStatus.COMPLETED
        assert result.original_text == "iPhone 15 Pro smartphone"
        assert len(result.translated_text) > 0
        assert result.source_lang == LanguageCode.ENGLISH
        assert result.target_lang == LanguageCode.CHINESE
        assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_translate_chinese_to_english(self, translation_engine):
        """测试中文到英文翻译"""
        request = TranslationRequest(
            request_id="test_002",
            text="苹果手机 iPhone 15 Pro",
            source_lang=LanguageCode.CHINESE,
            target_lang=LanguageCode.ENGLISH,
            text_type=TextType.PRODUCT_TITLE
        )
        
        result = await translation_engine.translate(request)
        
        assert result.status == TranslationStatus.COMPLETED
        assert len(result.translated_text) > 0
        assert result.quality_score is not None
        assert result.quality_score.overall_score > 0
    
    @pytest.mark.asyncio
    async def test_batch_translate(self, translation_engine):
        """测试批量翻译"""
        requests = [
            TranslationRequest(
                request_id=f"batch_{i}",
                text=f"Test product {i}",
                source_lang=LanguageCode.ENGLISH,
                target_lang=LanguageCode.CHINESE,
                text_type=TextType.PRODUCT_TITLE,
                priority=i % 3 + 1
            )
            for i in range(5)
        ]
        
        results = await translation_engine.batch_translate(requests)
        
        assert len(results) == 5
        assert all(result.status == TranslationStatus.COMPLETED for result in results)
        assert all(len(result.translated_text) > 0 for result in results)
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, translation_engine):
        """测试缓存功能"""
        request = TranslationRequest(
            request_id="cache_test",
            text="Cache test text",
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.GENERAL_TEXT
        )
        
        # 第一次翻译
        result1 = await translation_engine.translate(request)
        assert result1.status == TranslationStatus.COMPLETED
        assert not result1.cached
        
        # 第二次翻译（应该命中缓存）
        request.request_id = "cache_test_2"
        result2 = await translation_engine.translate(request)
        assert result2.status == TranslationStatus.CACHED
        assert result2.cached
        assert result2.translated_text == result1.translated_text
    
    def test_get_translation_statistics(self, translation_engine):
        """测试获取翻译统计"""
        stats = translation_engine.get_translation_statistics()
        
        assert "total_requests" in stats
        assert "successful_translations" in stats
        assert "failed_translations" in stats
        assert "cache_hits" in stats
        assert "success_rate" in stats
        assert "cache_hit_rate" in stats
        assert "available_languages" in stats
        assert "available_text_types" in stats


class TestProviderManager:
    """提供商管理器测试"""
    
    @pytest.fixture
    def provider_manager(self):
        """提供商管理器实例"""
        return ProviderManager()
    
    def test_initialization(self, provider_manager):
        """测试初始化"""
        assert len(provider_manager.providers) == 3  # OpenAI, Claude, Baidu
        assert "openai" in provider_manager.providers
        assert "claude" in provider_manager.providers
        assert "baidu" in provider_manager.providers
        assert provider_manager.default_provider_name == "openai"
    
    @pytest.mark.asyncio
    async def test_openai_provider_translate(self, provider_manager):
        """测试OpenAI提供商翻译"""
        provider = provider_manager.providers["openai"]
        
        result = await provider.translate(
            "iPhone smartphone", "en", "zh"
        )
        
        assert len(result) > 0
        assert "苹果手机" in result or "iPhone" in result
    
    @pytest.mark.asyncio
    async def test_claude_provider_translate(self, provider_manager):
        """测试Claude提供商翻译"""
        provider = provider_manager.providers["claude"]
        
        result = await provider.translate(
            "smartphone device", "en", "zh"
        )
        
        assert len(result) > 0
        assert "智能手机" in result or "smartphone" in result
    
    @pytest.mark.asyncio
    async def test_baidu_provider_translate(self, provider_manager):
        """测试百度提供商翻译"""
        provider = provider_manager.providers["baidu"]
        
        result = await provider.translate(
            "Hello world", "en", "zh"
        )
        
        assert len(result) > 0
        assert "百度翻译" in result
    
    @pytest.mark.asyncio
    async def test_select_best_provider(self, provider_manager):
        """测试选择最佳提供商"""
        provider = await provider_manager.select_best_provider(
            LanguageCode.ENGLISH, LanguageCode.CHINESE, TextType.PRODUCT_TITLE
        )
        
        assert provider is not None
        assert provider.name in ["openai", "claude", "baidu"]
        assert provider.can_handle_request()
    
    @pytest.mark.asyncio
    async def test_get_default_provider(self, provider_manager):
        """测试获取默认提供商"""
        provider = await provider_manager.get_default_provider()
        
        assert provider is not None
        assert provider.name == "openai"
    
    def test_set_default_provider(self, provider_manager):
        """测试设置默认提供商"""
        result = provider_manager.set_default_provider("claude")
        assert result is True
        assert provider_manager.default_provider_name == "claude"
        
        # 测试设置不存在的提供商
        result = provider_manager.set_default_provider("nonexistent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_check_providers_health(self, provider_manager):
        """测试检查提供商健康状态"""
        health_status = await provider_manager.check_all_providers_health()
        
        assert len(health_status) == 3
        assert "openai" in health_status
        assert "claude" in health_status
        assert "baidu" in health_status
        # 模拟提供商应该都是健康的
        assert all(status for status in health_status.values())
    
    def test_get_provider_statistics(self, provider_manager):
        """测试获取提供商统计"""
        stats = provider_manager.get_provider_statistics()
        
        assert "total_providers" in stats
        assert "active_providers" in stats
        assert "providers" in stats
        assert stats["total_providers"] == 3
        assert len(stats["providers"]) == 3


class TestQualityAssessor:
    """质量评估器测试"""
    
    @pytest.fixture
    def quality_assessor(self):
        """质量评估器实例"""
        return QualityAssessor()
    
    def test_initialization(self, quality_assessor):
        """测试初始化"""
        assert len(quality_assessor.quality_rules) == 6  # 6个默认规则
        assert "length_check" in quality_assessor.quality_rules
        assert "empty_translation" in quality_assessor.quality_rules
        assert "number_consistency" in quality_assessor.quality_rules
    
    @pytest.mark.asyncio
    async def test_assess_good_translation(self, quality_assessor):
        """测试评估良好翻译"""
        score = await quality_assessor.assess_translation(
            "iPhone 15 Pro smartphone",
            "iPhone 15 Pro 智能手机",
            LanguageCode.ENGLISH,
            LanguageCode.CHINESE,
            TextType.PRODUCT_TITLE
        )
        
        assert score.overall_score > 6.0
        assert score.quality_level in [QualityLevel.GOOD, QualityLevel.EXCELLENT]
        assert score.confidence > 0.5
        assert len(score.metric_scores) > 0
    
    @pytest.mark.asyncio
    async def test_assess_empty_translation(self, quality_assessor):
        """测试评估空翻译"""
        score = await quality_assessor.assess_translation(
            "Test text",
            "",
            LanguageCode.ENGLISH,
            LanguageCode.CHINESE,
            TextType.GENERAL_TEXT
        )
        
        assert score.overall_score < 7.0  # 空翻译应该得分较低，但不一定很低
        assert score.quality_level in [QualityLevel.POOR, QualityLevel.VERY_POOR, QualityLevel.FAIR]
        assert "翻译结果为空" in score.issues
    
    @pytest.mark.asyncio
    async def test_assess_number_consistency(self, quality_assessor):
        """测试评估数字一致性"""
        # 数字一致的情况
        score1 = await quality_assessor.assess_translation(
            "iPhone 15 Pro 256GB",
            "iPhone 15 Pro 256GB",
            LanguageCode.ENGLISH,
            LanguageCode.CHINESE,
            TextType.PRODUCT_TITLE
        )
        
        # 数字不一致的情况
        score2 = await quality_assessor.assess_translation(
            "iPhone 15 Pro 256GB",
            "iPhone 15 Pro 128GB",
            LanguageCode.ENGLISH,
            LanguageCode.CHINESE,
            TextType.PRODUCT_TITLE
        )
        
        # 数字一致的翻译应该比数字不一致的翻译得分更高（如果有问题的话）
        if score2.issues:
            assert any("数字" in issue for issue in score2.issues)
        # 如果没有检测到数字问题，两个评分可能相近
    
    def test_add_quality_rule(self, quality_assessor):
        """测试添加质量规则"""
        rule = QualityRule(
            rule_id="test_rule",
            name="测试规则",
            description="测试规则描述",
            metric=QualityMetric.ACCURACY,
            weight=1.0
        )
        
        result = quality_assessor.add_quality_rule(rule)
        assert result is True
        assert "test_rule" in quality_assessor.quality_rules
    
    def test_remove_quality_rule(self, quality_assessor):
        """测试移除质量规则"""
        result = quality_assessor.remove_quality_rule("length_check")
        assert result is True
        assert "length_check" not in quality_assessor.quality_rules
        
        # 测试移除不存在的规则
        result = quality_assessor.remove_quality_rule("nonexistent")
        assert result is False
    
    def test_get_quality_statistics(self, quality_assessor):
        """测试获取质量统计"""
        stats = quality_assessor.get_quality_statistics()
        
        assert "total_assessments" in stats
        assert "average_score" in stats
        assert "quality_distribution" in stats
        assert "common_issues" in stats
        assert "total_rules" in stats


class TestCacheManager:
    """缓存管理器测试"""
    
    @pytest.fixture
    def cache_manager(self):
        """缓存管理器实例"""
        return CacheManager(CacheType.MEMORY)
    
    def test_initialization(self, cache_manager):
        """测试初始化"""
        assert cache_manager.cache_type == CacheType.MEMORY
        assert cache_manager.max_size_bytes > 0
        assert len(cache_manager.memory_cache) == 0
        assert cache_manager.stats.total_entries == 0
    
    @pytest.mark.asyncio
    async def test_set_and_get(self, cache_manager):
        """测试设置和获取缓存"""
        key = "test_key"
        value = {"text": "test value", "score": 8.5}
        
        # 设置缓存
        result = await cache_manager.set(key, value, expire_seconds=3600)
        assert result is True
        
        # 获取缓存
        cached_value = await cache_manager.get(key)
        assert cached_value == value
        assert cache_manager.stats.hit_count == 1
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache_manager):
        """测试缓存过期"""
        key = "expire_test"
        value = "expire value"
        
        # 设置1秒过期的缓存
        await cache_manager.set(key, value, expire_seconds=1)
        
        # 立即获取应该成功
        cached_value = await cache_manager.get(key)
        assert cached_value == value
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 再次获取应该失败
        cached_value = await cache_manager.get(key)
        assert cached_value is None
        assert cache_manager.stats.miss_count > 0
    
    @pytest.mark.asyncio
    async def test_delete_cache(self, cache_manager):
        """测试删除缓存"""
        key = "delete_test"
        value = "delete value"
        
        # 设置缓存
        await cache_manager.set(key, value)
        
        # 确认存在
        cached_value = await cache_manager.get(key)
        assert cached_value == value
        
        # 删除缓存
        result = await cache_manager.delete(key)
        assert result is True
        
        # 确认已删除
        cached_value = await cache_manager.get(key)
        assert cached_value is None
    
    @pytest.mark.asyncio
    async def test_clear_cache(self, cache_manager):
        """测试清空缓存"""
        # 设置多个缓存
        for i in range(5):
            await cache_manager.set(f"key_{i}", f"value_{i}")
        
        assert len(cache_manager.memory_cache) == 5
        
        # 清空缓存
        result = await cache_manager.clear()
        assert result is True
        assert len(cache_manager.memory_cache) == 0
        assert cache_manager.stats.total_entries == 0
    
    @pytest.mark.asyncio
    async def test_cleanup_expired(self, cache_manager):
        """测试清理过期缓存"""
        # 设置一些缓存，其中一些很快过期
        await cache_manager.set("key1", "value1", expire_seconds=3600)  # 不会过期
        await cache_manager.set("key2", "value2", expire_seconds=1)     # 很快过期
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 执行清理
        await cache_manager.cleanup_expired()
        
        # 检查结果
        value1 = await cache_manager.get("key1")
        value2 = await cache_manager.get("key2")
        
        assert value1 == "value1"  # 应该还在
        assert value2 is None      # 应该被清理
    
    def test_get_cache_statistics(self, cache_manager):
        """测试获取缓存统计"""
        stats = cache_manager.get_cache_statistics()
        
        assert "cache_type" in stats
        assert "total_entries" in stats
        assert "active_entries" in stats
        assert "hit_count" in stats
        assert "miss_count" in stats
        assert "hit_rate" in stats
        assert "config" in stats
        assert stats["cache_type"] == "memory"
