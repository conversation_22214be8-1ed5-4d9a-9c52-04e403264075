#!/usr/bin/env python3
"""
测试供货商对比功能
"""

import asyncio
import httpx
import json


async def test_supplier_compare():
    """测试供货商对比功能"""
    print("🔍 测试供货商对比功能...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=30.0) as client:
        try:
            # 1. 先获取供货商列表
            print("  📋 获取供货商列表...")
            response = await client.get("/api/v1/suppliers/")
            if response.status_code == 200:
                data = response.json()
                suppliers = data.get('items', [])
                if len(suppliers) >= 2:
                    supplier1_id = suppliers[0]['id']
                    supplier2_id = suppliers[1]['id']
                    print(f"    ✅ 找到供货商 - ID1: {supplier1_id}")
                    print(f"    ✅ 找到供货商 - ID2: {supplier2_id}")
                    
                    # 2. 尝试对比分析
                    print("  🔍 尝试对比分析...")
                    response = await client.get(f"/api/v1/suppliers/compare?supplier_ids={supplier1_id},{supplier2_id}")
                    print(f"    状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"    ✅ 对比成功")
                        print(f"    📊 对比供货商数: {result.get('summary', {}).get('total_suppliers', 0)}")
                        print(f"    ⭐ 最高评分: {result.get('summary', {}).get('best_rating', 'N/A')}")
                        print(f"    🚚 最快交货: {result.get('summary', {}).get('fastest_delivery', 'N/A')}天")
                    else:
                        print(f"    ❌ 对比失败")
                        try:
                            error_data = response.json()
                            print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                        except:
                            print(f"    错误内容: {response.text}")
                else:
                    print("    ❌ 供货商数量不足，无法进行对比测试")
            else:
                print(f"    ❌ 获取供货商列表失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 测试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_supplier_compare())
