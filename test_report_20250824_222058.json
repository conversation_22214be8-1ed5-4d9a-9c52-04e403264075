{"timestamp": "2025-08-24T22:20:58.632195", "summary": {"total_tests": 43, "total_passed": 43, "total_failed": 0, "success_rate": 100.0}, "results": [{"name": "商品监控业务逻辑测试", "status": "PASSED", "passed": 11, "failed": 0, "total": 11, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 11 items\n\ntests/test_product_monitoring_business.py::TestProductDataProcessorLogic::test_data_processing_workflow PASSED [  9%]\ntests/test_product_monitoring_business.py::TestProductDataProcessorLogic::test_price_extraction_logic PASSED [ 18%]\ntests/test_product_monitoring_business.py::TestProductDataProcessorLogic::test_data_quality_assessment PASSED [ 27%]\ntests/test_product_monitoring_business.py::TestTaskSchedulerBusinessLogic::test_task_creation_and_scheduling PASSED [ 36%]\ntests/test_product_monitoring_business.py::TestTaskSchedulerBusinessLogic::test_task_execution_workflow PASSED [ 45%]\ntests/test_product_monitoring_business.py::TestTaskSchedulerBusinessLogic::test_error_handling_and_retry_logic PASSED [ 54%]\ntests/test_product_monitoring_business.py::TestDataValidationLogic::test_price_data_validation PASSED [ 63%]\ntests/test_product_monitoring_business.py::TestDataValidationLogic::test_data_normalization_logic PASSED [ 72%]\ntests/test_product_monitoring_business.py::TestBusinessLogicIntegration::test_complete_monitoring_workflow PASSED [ 81%]\ntests/test_product_monitoring_business.py::TestBusinessLogicIntegration::test_error_recovery_workflow PASSED [ 90%]\ntests/test_product_monitoring_business.py::TestBusinessLogicIntegration::test_data_quality_monitoring PASSED [100%]\n\n============================== warnings summary ===============================\n..\\..\\..\\..\\left2\\miniconda3\\Lib\\site-packages\\pydantic\\fields.py:1076: 19 warnings\n  C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pydantic\\fields.py:1076: PydanticDeprecatedSince20: Using extra keyword arguments on `Field` is deprecated and will be removed. Use `json_schema_extra` instead. (Extra keys: 'env'). Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    warn(\n\napp\\core\\config.py:137\n  C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\\app\\core\\config.py:137: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n    self.last_reload = datetime.utcnow()\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n======================= 11 passed, 20 warnings in 0.34s =======================\n", "stderr": ""}, {"name": "价格趋势算法测试", "status": "PASSED", "passed": 8, "failed": 0, "total": 8, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 8 items\n\ntests/test_price_trend_algorithms.py::TestTrendCalculatorAlgorithms::test_linear_trend_calculation PASSED [ 12%]\ntests/test_price_trend_algorithms.py::TestTrendCalculatorAlgorithms::test_moving_average_calculation PASSED [ 25%]\ntests/test_price_trend_algorithms.py::TestTrendCalculatorAlgorithms::test_volatility_calculation PASSED [ 37%]\ntests/test_price_trend_algorithms.py::TestTrendCalculatorAlgorithms::test_support_resistance_levels PASSED [ 50%]\ntests/test_price_trend_algorithms.py::TestPriceAnalysisAlgorithms::test_price_change_analysis PASSED [ 62%]\ntests/test_price_trend_algorithms.py::TestPriceAnalysisAlgorithms::test_price_pattern_recognition PASSED [ 75%]\ntests/test_price_trend_algorithms.py::TestPriceAnalysisAlgorithms::test_price_anomaly_detection PASSED [ 87%]\ntests/test_price_trend_algorithms.py::TestPriceAnalysisAlgorithms::test_basic_prediction_algorithm PASSED [100%]\n\n============================== 8 passed in 0.15s ==============================\n", "stderr": ""}, {"name": "简化利润计算测试", "status": "PASSED", "passed": 6, "failed": 0, "total": 6, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 6 items\n\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_basic_profit_calculation PASSED [ 16%]\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_total_cost_calculation PASSED [ 33%]\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_roi_calculation PASSED [ 50%]\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_supplier_comparison PASSED [ 66%]\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_profit_optimization_scenario PASSED [ 83%]\ntests/test_simple_profit_calculation.py::TestSimpleProfitCalculation::test_cost_trend_analysis PASSED [100%]\n\n============================== 6 passed in 0.03s ==============================\n", "stderr": ""}, {"name": "简化供货商管理测试", "status": "PASSED", "passed": 5, "failed": 0, "total": 5, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 5 items\n\ntests/test_simple_supplier_management.py::TestSimpleSupplierManagement::test_supplier_evaluation PASSED [ 20%]\ntests/test_simple_supplier_management.py::TestSimpleSupplierManagement::test_performance_tracking PASSED [ 40%]\ntests/test_simple_supplier_management.py::TestSimpleSupplierManagement::test_contract_management PASSED [ 60%]\ntests/test_simple_supplier_management.py::TestSimpleSupplierManagement::test_supplier_risk_assessment PASSED [ 80%]\ntests/test_simple_supplier_management.py::TestSimpleSupplierManagement::test_supplier_relationship_management PASSED [100%]\n\n============================== 5 passed in 0.03s ==============================\n", "stderr": ""}, {"name": "简化爬虫集成测试", "status": "PASSED", "passed": 5, "failed": 0, "total": 5, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 5 items\n\ntests/test_simple_crawler_integration.py::TestSimpleCrawlerIntegration::test_task_middleware_integration PASSED [ 20%]\ntests/test_simple_crawler_integration.py::TestSimpleCrawlerIntegration::test_data_standardization_integration PASSED [ 40%]\ntests/test_simple_crawler_integration.py::TestSimpleCrawlerIntegration::test_error_handling_integration PASSED [ 60%]\ntests/test_simple_crawler_integration.py::TestSimpleCrawlerIntegration::test_crawler_performance_integration PASSED [ 80%]\ntests/test_simple_crawler_integration.py::TestSimpleCrawlerIntegration::test_data_pipeline_integration PASSED [100%]\n\n============================== 5 passed in 0.03s ==============================\n", "stderr": ""}, {"name": "简化数据库集成测试", "status": "PASSED", "passed": 3, "failed": 0, "total": 3, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 6 items\n\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_database_connection PASSED [ 16%]\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_test_data_exists PASSED [ 33%]\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_price_trend_analysis_with_real_data PASSED [ 50%]\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_price_statistics_analysis SKIPPED [ 66%]\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_time_series_query_performance SKIPPED [ 83%]\ntests/test_simple_db_integration.py::TestSimpleDatabaseIntegration::test_price_anomaly_detection_with_real_data SKIPPED [100%]\n\n======================== 3 passed, 3 skipped in 0.40s =========================\n", "stderr": ""}, {"name": "修复后数据库集成测试", "status": "PASSED", "passed": 5, "failed": 0, "total": 5, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-7.4.3, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Desktop\\GitResearch\\Moniit\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-0.21.1, cov-6.2.1\nasyncio: mode=Mode.STRICT\ncollecting ... collected 5 items\n\ntests/test_fixed_database_integration.py::TestFixedDatabaseIntegration::test_database_connection_and_data_exists PASSED [ 20%]\ntests/test_fixed_database_integration.py::TestFixedDatabaseIntegration::test_price_trend_analysis_with_real_data PASSED [ 40%]\ntests/test_fixed_database_integration.py::TestFixedDatabaseIntegration::test_price_volatility_analysis PASSED [ 60%]\ntests/test_fixed_database_integration.py::TestFixedDatabaseIntegration::test_price_anomaly_detection PASSED [ 80%]\ntests/test_fixed_database_integration.py::TestFixedDatabaseIntegration::test_multi_product_comparison PASSED [100%]\n\n============================== 5 passed in 0.40s ==============================\n", "stderr": ""}]}