"""
平台配置管理API端点

提供平台配置的CRUD操作、模板管理和验证功能
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from app.core.logging import get_logger
from app.services.task_middleware.config_service import PlatformConfigService, ConfigTemplate
from app.services.task_middleware.config_manager import Platform, ProductType

logger = get_logger(__name__)
router = APIRouter()

# 全局配置服务实例
config_service = PlatformConfigService()


class PlatformConfigRequest(BaseModel):
    """平台配置请求模型"""
    platform: str = Field(..., description="平台类型")
    config_data: Dict[str, Any] = Field(..., description="配置数据")
    template_name: Optional[str] = Field(None, description="模板名称")


class PlatformConfigUpdate(BaseModel):
    """平台配置更新模型"""
    updates: Dict[str, Any] = Field(..., description="更新数据")


class ConfigTemplateRequest(BaseModel):
    """配置模板请求模型"""
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    platform: str = Field(..., description="平台类型")
    template_data: Dict[str, Any] = Field(..., description="模板数据")
    version: str = Field("1.0", description="模板版本")


@router.post("/configs", summary="创建平台配置")
async def create_platform_config(request: PlatformConfigRequest):
    """创建新的平台配置"""
    try:
        platform = Platform(request.platform)
        
        success = await config_service.create_platform_config(
            platform, 
            request.config_data, 
            request.template_name
        )
        
        if success:
            return {
                "success": True,
                "message": f"平台配置创建成功: {request.platform}",
                "platform": request.platform
            }
        else:
            raise HTTPException(status_code=400, detail="配置创建失败")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {request.platform}")
    except Exception as e:
        logger.error(f"创建平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/configs/{platform}", summary="获取平台配置")
async def get_platform_config(platform: str):
    """获取指定平台的配置"""
    try:
        platform_enum = Platform(platform)
        config_data = await config_service.get_platform_config(platform_enum)
        
        if config_data:
            return {
                "success": True,
                "platform": platform,
                "config": config_data
            }
        else:
            raise HTTPException(status_code=404, detail=f"平台配置不存在: {platform}")
            
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"获取平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/configs/{platform}", summary="更新平台配置")
async def update_platform_config(platform: str, request: PlatformConfigUpdate):
    """更新指定平台的配置"""
    try:
        platform_enum = Platform(platform)
        
        success = await config_service.update_platform_config(
            platform_enum, 
            request.updates
        )
        
        if success:
            return {
                "success": True,
                "message": f"平台配置更新成功: {platform}",
                "platform": platform
            }
        else:
            raise HTTPException(status_code=400, detail="配置更新失败")
            
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"更新平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/configs/{platform}", summary="删除平台配置")
async def delete_platform_config(platform: str):
    """删除指定平台的配置"""
    try:
        platform_enum = Platform(platform)
        
        success = await config_service.delete_platform_config(platform_enum)
        
        if success:
            return {
                "success": True,
                "message": f"平台配置删除成功: {platform}",
                "platform": platform
            }
        else:
            raise HTTPException(status_code=400, detail="配置删除失败")
            
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"删除平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/configs", summary="列出所有平台配置")
async def list_platform_configs():
    """列出所有平台配置"""
    try:
        configs = await config_service.list_platform_configs()
        
        return {
            "success": True,
            "total": len(configs),
            "configs": configs
        }
        
    except Exception as e:
        logger.error(f"列出平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/configs/{platform}/validate", summary="验证平台配置")
async def validate_platform_config(platform: str, config_data: Dict[str, Any] = Body(...)):
    """验证平台配置"""
    try:
        validation_result = await config_service.validate_config(config_data)
        
        return {
            "success": True,
            "platform": platform,
            "validation": {
                "is_valid": validation_result.is_valid,
                "score": validation_result.score,
                "errors": validation_result.errors,
                "warnings": validation_result.warnings
            }
        }
        
    except Exception as e:
        logger.error(f"验证平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/configs/{platform}/test", summary="测试平台配置")
async def test_platform_config(
    platform: str, 
    test_urls: List[str] = Query(None, description="测试URL列表")
):
    """测试平台配置"""
    try:
        platform_enum = Platform(platform)
        
        test_result = await config_service.test_config(platform_enum, test_urls)
        
        return {
            "success": True,
            "platform": platform,
            "test_result": test_result
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"测试平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 模板管理API
@router.post("/templates", summary="创建配置模板")
async def create_config_template(request: ConfigTemplateRequest):
    """创建新的配置模板"""
    try:
        from datetime import datetime
        
        template = ConfigTemplate(
            name=request.name,
            description=request.description,
            platform=Platform(request.platform),
            template_data=request.template_data,
            version=request.version,
            created_at=datetime.now()
        )
        
        success = await config_service.create_template(template)
        
        if success:
            return {
                "success": True,
                "message": f"配置模板创建成功: {request.name}",
                "template_name": request.name
            }
        else:
            raise HTTPException(status_code=400, detail="模板创建失败")
            
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {request.platform}")
    except Exception as e:
        logger.error(f"创建配置模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates", summary="列出所有配置模板")
async def list_config_templates():
    """列出所有配置模板"""
    try:
        templates = config_service.list_templates()
        
        return {
            "success": True,
            "total": len(templates),
            "templates": templates
        }
        
    except Exception as e:
        logger.error(f"列出配置模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/{template_name}", summary="获取配置模板")
async def get_config_template(template_name: str):
    """获取指定的配置模板"""
    try:
        template = config_service.get_template(template_name)
        
        if template:
            return {
                "success": True,
                "template": {
                    "name": template.name,
                    "description": template.description,
                    "platform": template.platform.value,
                    "version": template.version,
                    "created_at": template.created_at.isoformat(),
                    "template_data": template.template_data
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"配置模板不存在: {template_name}")
            
    except Exception as e:
        logger.error(f"获取配置模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 备份和恢复API
@router.post("/configs/{platform}/backup", summary="备份平台配置")
async def backup_platform_config(platform: str):
    """备份平台配置"""
    try:
        platform_enum = Platform(platform)
        await config_service._backup_config(platform_enum)
        
        return {
            "success": True,
            "message": f"平台配置备份成功: {platform}",
            "platform": platform
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"备份平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/configs/{platform}/backups", summary="列出配置备份")
async def list_config_backups(platform: str):
    """列出平台配置备份"""
    try:
        platform_enum = Platform(platform)
        backups = config_service.list_backups(platform_enum)
        
        return {
            "success": True,
            "platform": platform,
            "total": len(backups),
            "backups": backups
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"列出配置备份失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/configs/{platform}/restore/{backup_timestamp}", summary="恢复平台配置")
async def restore_platform_config(platform: str, backup_timestamp: str):
    """恢复平台配置"""
    try:
        platform_enum = Platform(platform)
        
        success = await config_service.restore_config(platform_enum, backup_timestamp)
        
        if success:
            return {
                "success": True,
                "message": f"平台配置恢复成功: {platform}",
                "platform": platform,
                "backup_timestamp": backup_timestamp
            }
        else:
            raise HTTPException(status_code=400, detail="配置恢复失败")
            
    except ValueError:
        raise HTTPException(status_code=400, detail=f"无效的平台类型: {platform}")
    except Exception as e:
        logger.error(f"恢复平台配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
