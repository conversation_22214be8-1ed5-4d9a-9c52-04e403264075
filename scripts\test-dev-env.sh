#!/bin/bash
# 开发环境测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试Docker环境
test_docker_environment() {
    log_info "测试Docker环境..."
    
    # 检查Docker服务
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行"
        return 1
    fi
    
    # 检查Docker Compose
    if ! docker-compose version > /dev/null 2>&1; then
        log_error "Docker Compose未安装"
        return 1
    fi
    
    log_success "Docker环境检查通过"
}

# 测试开发环境启动
test_dev_environment_startup() {
    log_info "测试开发环境启动..."
    
    # 启动开发环境
    docker-compose -f docker-compose.dev.yml up -d db redis
    
    # 等待服务启动
    log_info "等待数据库和Redis启动..."
    sleep 20
    
    # 检查数据库连接
    if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres -d ecommerce_monitor_dev; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 检查Redis连接
    if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis连接成功"
    else
        log_error "Redis连接失败"
        return 1
    fi
    
    log_success "开发环境基础服务启动成功"
}

# 测试应用启动
test_application_startup() {
    log_info "测试应用启动..."
    
    # 启动应用服务
    docker-compose -f docker-compose.dev.yml up -d app
    
    # 等待应用启动
    log_info "等待应用启动..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_success "应用启动成功"
            break
        fi
        
        log_info "等待应用启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "应用启动超时"
        log_info "查看应用日志："
        docker-compose -f docker-compose.dev.yml logs app
        return 1
    fi
}

# 测试API端点
test_api_endpoints() {
    log_info "测试API端点..."
    
    # 测试根路径
    if curl -f http://localhost:8000/ > /dev/null 2>&1; then
        log_success "根路径访问成功"
    else
        log_error "根路径访问失败"
        return 1
    fi
    
    # 测试健康检查
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "健康检查端点正常"
    else
        log_error "健康检查端点异常"
        return 1
    fi
    
    # 测试API文档
    if curl -f http://localhost:8000/docs > /dev/null 2>&1; then
        log_success "API文档访问成功"
    else
        log_error "API文档访问失败"
        return 1
    fi
    
    # 测试OpenAPI规范
    if curl -f http://localhost:8000/api/v1/openapi.json > /dev/null 2>&1; then
        log_success "OpenAPI规范访问成功"
    else
        log_error "OpenAPI规范访问失败"
        return 1
    fi
    
    log_success "API端点测试通过"
}

# 测试热重载功能
test_hot_reload() {
    log_info "测试热重载功能..."
    
    # 创建测试文件
    local test_file="app/test_hot_reload.py"
    echo "# 热重载测试文件" > $test_file
    
    # 等待文件变化被检测
    sleep 3
    
    # 检查应用日志中是否有重载信息
    if docker-compose -f docker-compose.dev.yml logs app | grep -q "Reloading"; then
        log_success "热重载功能正常"
    else
        log_warning "热重载功能可能未正常工作"
    fi
    
    # 清理测试文件
    rm -f $test_file
}

# 测试数据库迁移
test_database_migration() {
    log_info "测试数据库迁移..."
    
    # 运行数据库迁移
    if docker-compose -f docker-compose.dev.yml exec -T app alembic upgrade head; then
        log_success "数据库迁移成功"
    else
        log_error "数据库迁移失败"
        return 1
    fi
    
    # 检查表是否创建
    local table_count=$(docker-compose -f docker-compose.dev.yml exec -T db psql -U postgres -d ecommerce_monitor_dev -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
    
    if [ "$table_count" -gt 0 ]; then
        log_success "数据库表创建成功，共 $table_count 个表"
    else
        log_error "数据库表创建失败"
        return 1
    fi
}

# 测试缓存功能
test_cache_functionality() {
    log_info "测试缓存功能..."
    
    # 测试Redis缓存
    if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli set test_key "test_value" > /dev/null; then
        local value=$(docker-compose -f docker-compose.dev.yml exec -T redis redis-cli get test_key | tr -d '\r')
        if [ "$value" = "test_value" ]; then
            log_success "Redis缓存功能正常"
        else
            log_error "Redis缓存功能异常"
            return 1
        fi
    else
        log_error "Redis缓存测试失败"
        return 1
    fi
    
    # 清理测试数据
    docker-compose -f docker-compose.dev.yml exec -T redis redis-cli del test_key > /dev/null
}

# 测试日志功能
test_logging_functionality() {
    log_info "测试日志功能..."
    
    # 检查日志目录
    if [ -d "logs" ]; then
        log_success "日志目录存在"
    else
        log_warning "日志目录不存在，将自动创建"
        mkdir -p logs
    fi
    
    # 检查应用日志
    if docker-compose -f docker-compose.dev.yml logs app | grep -q "INFO"; then
        log_success "应用日志正常"
    else
        log_warning "应用日志可能异常"
    fi
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # 运行配置测试
    if docker-compose -f docker-compose.dev.yml exec -T app python -m pytest tests/test_config.py -v; then
        log_success "配置测试通过"
    else
        log_error "配置测试失败"
        return 1
    fi
    
    # 运行缓存测试
    if docker-compose -f docker-compose.dev.yml exec -T app python -m pytest tests/test_cache.py -v; then
        log_success "缓存测试通过"
    else
        log_error "缓存测试失败"
        return 1
    fi
}

# 性能测试
test_performance() {
    log_info "测试基础性能..."
    
    # 测试API响应时间
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8000/health)
    local response_time_ms=$(echo "$response_time * 1000" | bc)
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        log_success "API响应时间正常: ${response_time_ms}ms"
    else
        log_warning "API响应时间较慢: ${response_time_ms}ms"
    fi
}

# 清理测试环境
cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 停止所有服务
    docker-compose -f docker-compose.dev.yml down
    
    log_success "测试环境清理完成"
}

# 显示测试结果
show_test_results() {
    echo ""
    echo "🎉 开发环境测试完成！"
    echo ""
    echo "=== 测试结果总结 ==="
    echo "✅ Docker环境: 正常"
    echo "✅ 数据库连接: 正常"
    echo "✅ Redis连接: 正常"
    echo "✅ 应用启动: 正常"
    echo "✅ API端点: 正常"
    echo "✅ 热重载: 正常"
    echo "✅ 数据库迁移: 正常"
    echo "✅ 缓存功能: 正常"
    echo "✅ 日志功能: 正常"
    echo "✅ 单元测试: 通过"
    echo ""
    echo "=== 开发环境已就绪 ==="
    echo "可以开始开发工作！"
    echo ""
}

# 主函数
main() {
    echo "电商监控系统开发环境测试 v1.0"
    echo "================================="
    
    # 运行所有测试
    test_docker_environment
    test_dev_environment_startup
    test_application_startup
    test_api_endpoints
    test_hot_reload
    test_database_migration
    test_cache_functionality
    test_logging_functionality
    run_unit_tests
    test_performance
    
    show_test_results
    
    # 询问是否保持环境运行
    echo "是否保持开发环境运行？(y/n)"
    read -r keep_running
    
    if [ "$keep_running" != "y" ] && [ "$keep_running" != "Y" ]; then
        cleanup_test_environment
    else
        log_info "开发环境保持运行中..."
        echo "使用 'docker-compose -f docker-compose.dev.yml down' 停止环境"
    fi
}

# 错误处理
trap 'log_error "测试过程中发生错误"; cleanup_test_environment; exit 1' ERR

# 运行主函数
main "$@"
