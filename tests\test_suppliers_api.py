"""
供货商管理API测试

测试供货商管理API的所有功能
"""

import pytest
import pytest_asyncio
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import uuid4
from decimal import Decimal

from app.main import app
from app.core.database import get_db_session
from app.models.database import Supplier as DBSupplier, ProductCost as DBProductCost, Product as DBProduct

# 配置pytest异步支持
pytestmark = pytest.mark.asyncio


@pytest_asyncio.fixture
async def client():
    """创建测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture
async def db_session():
    """创建测试数据库会话"""
    async with get_db_session() as session:
        yield session


@pytest_asyncio.fixture
async def sample_supplier(db_session: AsyncSession):
    """创建测试供货商"""
    supplier = DBSupplier(
        name="测试供货商",
        contact_person="张三",
        phone="13800138000",
        email="<EMAIL>",
        address="测试地址123号",
        payment_terms="30天付款",
        delivery_time=7,
        min_order_quantity=100,
        is_active=True,
        rating=Decimal("4.5"),
        notes="测试供货商备注"
    )
    db_session.add(supplier)
    await db_session.commit()
    await db_session.refresh(supplier)
    return supplier


@pytest_asyncio.fixture
async def sample_product(db_session: AsyncSession):
    """创建测试商品"""
    product = DBProduct(
        url="https://example.com/test-product",
        platform="test_platform",
        title="测试商品",
        category="electronics",
        status="active",
        monitoring_frequency=24,
        is_active=True
    )
    db_session.add(product)
    await db_session.commit()
    await db_session.refresh(product)
    return product


@pytest_asyncio.fixture
async def sample_product_cost(db_session: AsyncSession, sample_supplier, sample_product):
    """创建测试商品成本记录"""
    cost = DBProductCost(
        product_id=sample_product.id,
        supplier_id=sample_supplier.id,
        unit_cost=Decimal("99.99"),
        currency="USD",
        shipping_cost=Decimal("10.00"),
        other_costs=Decimal("5.00"),
        total_cost=Decimal("114.99"),
        min_quantity=50,
        is_preferred=True
    )
    db_session.add(cost)
    await db_session.commit()
    await db_session.refresh(cost)
    return cost


class TestSuppliersAPI:
    """供货商管理API测试类"""
    
    async def test_get_suppliers_empty(self, client: AsyncClient):
        """测试获取空供货商列表"""
        response = await client.get("/api/v1/suppliers/")
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert data["total"] >= 0
    
    async def test_get_suppliers_with_filters(self, client: AsyncClient, sample_supplier):
        """测试带筛选条件的供货商查询"""
        # 测试搜索功能
        response = await client.get("/api/v1/suppliers/?search=测试")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 测试激活状态筛选
        response = await client.get("/api/v1/suppliers/?is_active=true")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 测试评分筛选
        response = await client.get("/api/v1/suppliers/?min_rating=4.0")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
    
    async def test_create_supplier_success(self, client: AsyncClient):
        """测试成功创建供货商"""
        supplier_data = {
            "name": "新供货商",
            "contact_person": "李四",
            "phone": "13900139000",
            "email": "<EMAIL>",
            "address": "新地址456号",
            "payment_terms": "15天付款",
            "delivery_time": 5,
            "min_order_quantity": 50,
            "is_active": True,
            "rating": 4.0,
            "notes": "新供货商备注"
        }
        
        response = await client.post("/api/v1/suppliers/", json=supplier_data)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == supplier_data["name"]
        assert data["contact_person"] == supplier_data["contact_person"]
        assert data["phone"] == supplier_data["phone"]
        assert "id" in data
    
    async def test_create_supplier_duplicate_name(self, client: AsyncClient, sample_supplier):
        """测试创建重复名称的供货商"""
        supplier_data = {
            "name": sample_supplier.name,
            "contact_person": "重复名称",
            "phone": "13700137000",
            "email": "<EMAIL>",
            "is_active": True
        }
        
        response = await client.post("/api/v1/suppliers/", json=supplier_data)
        assert response.status_code == 400
        assert "该供货商名称已存在" in response.json()["detail"]
    
    async def test_get_supplier_detail_success(self, client: AsyncClient, sample_supplier):
        """测试成功获取供货商详情"""
        response = await client.get(f"/api/v1/suppliers/{sample_supplier.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(sample_supplier.id)
        assert data["name"] == sample_supplier.name
        assert data["contact_person"] == sample_supplier.contact_person
        assert "product_count" in data
    
    async def test_get_supplier_detail_not_found(self, client: AsyncClient):
        """测试获取不存在供货商的详情"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/suppliers/{fake_id}")
        assert response.status_code == 404
        assert "供货商不存在" in response.json()["detail"]
    
    async def test_get_supplier_detail_invalid_id(self, client: AsyncClient):
        """测试无效ID格式"""
        response = await client.get("/api/v1/suppliers/invalid-id")
        assert response.status_code == 400
        assert "无效的供货商ID格式" in response.json()["detail"]
    
    async def test_update_supplier_success(self, client: AsyncClient, sample_supplier):
        """测试成功更新供货商"""
        update_data = {
            "contact_person": "更新后的联系人",
            "phone": "13600136000",
            "rating": 5.0
        }
        
        response = await client.put(f"/api/v1/suppliers/{sample_supplier.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["contact_person"] == update_data["contact_person"]
        assert data["phone"] == update_data["phone"]
        assert data["rating"] == update_data["rating"]
    
    async def test_update_supplier_not_found(self, client: AsyncClient):
        """测试更新不存在的供货商"""
        fake_id = str(uuid4())
        update_data = {"contact_person": "不存在的供货商"}
        
        response = await client.put(f"/api/v1/suppliers/{fake_id}", json=update_data)
        assert response.status_code == 404
        assert "供货商不存在" in response.json()["detail"]
    
    async def test_delete_supplier_soft_delete(self, client: AsyncClient, sample_supplier):
        """测试软删除供货商"""
        response = await client.delete(f"/api/v1/suppliers/{sample_supplier.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["deleted_permanently"] is False
        assert "可恢复" in data["message"]
    
    async def test_delete_supplier_force_delete(self, client: AsyncClient, sample_supplier):
        """测试强制删除供货商"""
        response = await client.delete(f"/api/v1/suppliers/{sample_supplier.id}?force=true")
        assert response.status_code == 200
        data = response.json()
        assert data["deleted_permanently"] is True
        assert "永久删除" in data["message"]
    
    async def test_delete_supplier_not_found(self, client: AsyncClient):
        """测试删除不存在的供货商"""
        fake_id = str(uuid4())
        response = await client.delete(f"/api/v1/suppliers/{fake_id}")
        assert response.status_code == 404
        assert "供货商不存在" in response.json()["detail"]


class TestSupplierProducts:
    """供货商商品关联测试类"""
    
    async def test_get_supplier_products_success(self, client: AsyncClient, sample_supplier, sample_product_cost):
        """测试成功获取供货商商品列表"""
        response = await client.get(f"/api/v1/suppliers/{sample_supplier.id}/products")
        assert response.status_code == 200
        data = response.json()
        assert data["supplier_id"] == str(sample_supplier.id)
        assert data["supplier_name"] == sample_supplier.name
        assert len(data["items"]) >= 1
        
        # 验证商品成本信息
        first_item = data["items"][0]
        assert "cost_id" in first_item
        assert "product_id" in first_item
        assert "unit_cost" in first_item
        assert "total_cost" in first_item
    
    async def test_get_supplier_products_not_found(self, client: AsyncClient):
        """测试获取不存在供货商的商品列表"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/suppliers/{fake_id}/products")
        assert response.status_code == 404
        assert "供货商不存在" in response.json()["detail"]
    
    async def test_get_supplier_stats_success(self, client: AsyncClient, sample_supplier, sample_product_cost):
        """测试成功获取供货商统计信息"""
        response = await client.get(f"/api/v1/suppliers/{sample_supplier.id}/stats")
        assert response.status_code == 200
        data = response.json()
        assert data["supplier_id"] == str(sample_supplier.id)
        assert data["supplier_name"] == sample_supplier.name
        assert "total_products" in data
        assert "preferred_products" in data
        assert "average_cost" in data
        assert "performance_score" in data
    
    async def test_get_supplier_stats_not_found(self, client: AsyncClient):
        """测试获取不存在供货商的统计信息"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/suppliers/{fake_id}/stats")
        assert response.status_code == 404
        assert "供货商不存在" in response.json()["detail"]


class TestSupplierComparison:
    """供货商对比分析测试类"""
    
    async def test_compare_suppliers_success(self, client: AsyncClient, sample_supplier):
        """测试成功的供货商对比"""
        # 创建第二个供货商用于对比
        supplier_data = {
            "name": "对比供货商",
            "contact_person": "王五",
            "phone": "13500135000",
            "email": "<EMAIL>",
            "is_active": True,
            "rating": 3.5
        }
        
        create_response = await client.post("/api/v1/suppliers/", json=supplier_data)
        second_supplier_id = create_response.json()["id"]
        
        # 进行对比
        supplier_ids = f"{sample_supplier.id},{second_supplier_id}"
        response = await client.get(f"/api/v1/suppliers/compare?supplier_ids={supplier_ids}")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["suppliers"]) == 2
        assert "summary" in data
        
        # 验证对比数据结构
        first_supplier = data["suppliers"][0]
        assert "id" in first_supplier
        assert "name" in first_supplier
        assert "rating" in first_supplier
        assert "total_products" in first_supplier
    
    async def test_compare_suppliers_insufficient_suppliers(self, client: AsyncClient, sample_supplier):
        """测试供货商数量不足的对比"""
        response = await client.get(f"/api/v1/suppliers/compare?supplier_ids={sample_supplier.id}")
        assert response.status_code == 400
        assert "至少需要2个供货商" in response.json()["detail"]
    
    async def test_compare_suppliers_too_many_suppliers(self, client: AsyncClient):
        """测试供货商数量过多的对比"""
        fake_ids = ",".join([str(uuid4()) for _ in range(6)])
        response = await client.get(f"/api/v1/suppliers/compare?supplier_ids={fake_ids}")
        assert response.status_code == 400
        assert "最多支持5个供货商" in response.json()["detail"]
    
    async def test_get_supplier_ranking_success(self, client: AsyncClient, sample_supplier):
        """测试成功获取供货商排名"""
        response = await client.get("/api/v1/suppliers/evaluation/ranking")
        assert response.status_code == 200
        
        data = response.json()
        assert "rankings" in data
        assert "total_suppliers" in data
        assert "sort_by" in data
        
        # 验证排名数据结构
        if data["rankings"]:
            first_ranking = data["rankings"][0]
            assert "rank" in first_ranking
            assert "id" in first_ranking
            assert "name" in first_ranking
            assert "performance_score" in first_ranking
    
    async def test_get_supplier_ranking_with_sort(self, client: AsyncClient, sample_supplier):
        """测试带排序的供货商排名"""
        response = await client.get("/api/v1/suppliers/evaluation/ranking?sort_by=rating&limit=5")
        assert response.status_code == 200
        
        data = response.json()
        assert data["sort_by"] == "rating"
        assert len(data["rankings"]) <= 5
    
    async def test_get_supplier_ranking_invalid_sort(self, client: AsyncClient):
        """测试无效排序字段"""
        response = await client.get("/api/v1/suppliers/evaluation/ranking?sort_by=invalid")
        assert response.status_code == 400
        assert "不支持的排序字段" in response.json()["detail"]


if __name__ == "__main__":
    pytest.main([__file__])
