<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>所有修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            width: 100%;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-success {
            background-color: #52c41a;
            color: white;
        }
        .status-error {
            background-color: #ff4d4f;
            color: white;
        }
        .status-pending {
            background-color: #faad14;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Moniit Docker环境修复验证</h1>
        <p>验证所有已修复的问题是否正常工作</p>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>1. 登录功能测试 <span id="loginStatus" class="status-badge status-pending">待测试</span></h3>
                <button onclick="testLogin()">测试登录</button>
                <div id="loginResult" class="result" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3>2. 用户信息API <span id="userInfoStatus" class="status-badge status-pending">待测试</span></h3>
                <button onclick="testUserInfo()">测试 /api/v1/auth/me</button>
                <div id="userInfoResult" class="result" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3>3. 仪表板统计 <span id="dashboardStatus" class="status-badge status-pending">待测试</span></h3>
                <button onclick="testDashboard()">测试仪表板数据</button>
                <div id="dashboardResult" class="result" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3>4. 系统健康检查 <span id="healthStatus" class="status-badge status-pending">待测试</span></h3>
                <button onclick="testHealth()">测试健康检查</button>
                <div id="healthResult" class="result" style="display: none;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. 综合测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="allTestsResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="summary">
            <h3>📊 测试结果总结</h3>
            <div id="testSummary">
                <p>点击"运行所有测试"开始验证所有修复...</p>
            </div>
        </div>
    </div>

    <script>
        let accessToken = null;
        const credentials = {
            username: 'admin',
            password: ',d7@b]FDs]9s'
        };

        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = `status-badge status-${status}`;
            element.textContent = status === 'success' ? '✅ 通过' : status === 'error' ? '❌ 失败' : '⏳ 测试中';
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试登录功能...';
            updateStatus('loginStatus', 'pending');
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    accessToken = data.access_token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录成功!
用户: ${data.user.username}
邮箱: ${data.user.email}
角色: ${data.user.role}
令牌: ${data.access_token.substring(0, 30)}...
过期时间: ${data.expires_in}秒

🎉 登录功能修复成功！`;
                    updateStatus('loginStatus', 'success');
                    return true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败!
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                    updateStatus('loginStatus', 'error');
                    return false;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!
错误: ${error.message}`;
                updateStatus('loginStatus', 'error');
                return false;
            }
        }

        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试用户信息API...';
            updateStatus('userInfoStatus', 'pending');
            
            if (!accessToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 需要先登录获取访问令牌';
                updateStatus('userInfoStatus', 'error');
                return false;
            }
            
            try {
                const response = await fetch('/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 用户信息获取成功!
用户ID: ${data.user_id}
用户名: ${data.username}
邮箱: ${data.email}
全名: ${data.full_name || '未设置'}
角色: ${data.role}
状态: ${data.is_active ? '活跃' : '非活跃'}
创建时间: ${data.created_at}
最后登录: ${data.last_login || '从未登录'}

🎉 /api/v1/auth/me 端点修复成功！`;
                    updateStatus('userInfoStatus', 'success');
                    return true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取用户信息失败!
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                    updateStatus('userInfoStatus', 'error');
                    return false;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!
错误: ${error.message}`;
                updateStatus('userInfoStatus', 'error');
                return false;
            }
        }

        async function testDashboard() {
            const resultDiv = document.getElementById('dashboardResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试仪表板统计...';
            updateStatus('dashboardStatus', 'pending');
            
            try {
                const response = await fetch('/api/v1/system/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 仪表板统计获取成功!
商品总数: ${data.total_products}
活跃监控: ${data.active_monitors}
价格记录: ${data.total_price_records}
系统健康: ${data.system_health}
最近告警: ${data.recent_alerts}

今日统计:
- 新增商品: ${data.today_stats?.new_products || 0}
- 价格更新: ${data.today_stats?.price_updates || 0}
- 生成告警: ${data.today_stats?.alerts_generated || 0}

🎉 仪表板API端点修复成功！`;
                    updateStatus('dashboardStatus', 'success');
                    return true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取仪表板统计失败!
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                    updateStatus('dashboardStatus', 'error');
                    return false;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!
错误: ${error.message}`;
                updateStatus('dashboardStatus', 'error');
                return false;
            }
        }

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试健康检查...';
            updateStatus('healthStatus', 'pending');
            
            try {
                const response = await fetch('/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 健康检查通过!
状态: ${data.status}
服务: ${JSON.stringify(data.services, null, 2)}

🎉 健康检查端点正常工作！`;
                    updateStatus('healthStatus', 'success');
                    return true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 健康检查失败!
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                    updateStatus('healthStatus', 'error');
                    return false;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!
错误: ${error.message}`;
                updateStatus('healthStatus', 'error');
                return false;
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('allTestsResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在运行所有测试...\n';
            
            const results = {
                login: false,
                userInfo: false,
                dashboard: false,
                health: false
            };
            
            // 按顺序运行测试
            results.login = await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            results.userInfo = await testUserInfo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            results.dashboard = await testDashboard();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            results.health = await testHealth();
            
            // 更新总结
            const passedTests = Object.values(results).filter(r => r).length;
            const totalTests = Object.keys(results).length;
            
            const summaryDiv = document.getElementById('testSummary');
            if (passedTests === totalTests) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `🎉 所有测试通过! (${passedTests}/${totalTests})

✅ 登录功能: ${results.login ? '通过' : '失败'}
✅ 用户信息API: ${results.userInfo ? '通过' : '失败'}
✅ 仪表板统计: ${results.dashboard ? '通过' : '失败'}
✅ 健康检查: ${results.health ? '通过' : '失败'}

🚀 Moniit Docker环境所有问题已修复完成！`;
                
                summaryDiv.innerHTML = `
                    <h4>🎊 修复验证完成！</h4>
                    <p><strong>测试结果: ${passedTests}/${totalTests} 通过</strong></p>
                    <ul>
                        <li>✅ 登录数据结构不匹配问题 - 已修复</li>
                        <li>✅ /api/v1/auth/me 401错误 - 已修复</li>
                        <li>✅ 仪表板API 404错误 - 已修复</li>
                        <li>✅ Antd Drawer警告 - 已修复</li>
                        <li>✅ API路径不完整问题 - 已修复</li>
                    </ul>
                    <p><strong>🎉 系统现在完全正常工作！</strong></p>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `⚠️ 部分测试失败 (${passedTests}/${totalTests})

${results.login ? '✅' : '❌'} 登录功能: ${results.login ? '通过' : '失败'}
${results.userInfo ? '✅' : '❌'} 用户信息API: ${results.userInfo ? '通过' : '失败'}
${results.dashboard ? '✅' : '❌'} 仪表板统计: ${results.dashboard ? '通过' : '失败'}
${results.health ? '✅' : '❌'} 健康检查: ${results.health ? '通过' : '失败'}

请检查失败的测试项目。`;
                
                summaryDiv.innerHTML = `
                    <h4>⚠️ 部分测试失败</h4>
                    <p><strong>测试结果: ${passedTests}/${totalTests} 通过</strong></p>
                    <p>请检查失败的测试项目并进行修复。</p>
                `;
            }
        }
    </script>
</body>
</html>
