-- TimescaleDB连续聚合视图刷新策略 - 针对每日数据刷新场景优化
-- 适用于数据每天刷新一次的业务场景

-- =============================================================================
-- 场景分析：数据每天刷新一次的优化策略
-- =============================================================================

/*
业务特点分析：
1. 数据频率：每天刷新一次，通常在固定时间（如凌晨）
2. 数据特性：批量更新，单次更新量大，更新间隔长
3. 查询模式：主要查询历史趋势，对实时性要求不高
4. 资源优化：避免频繁无效刷新，节省系统资源

优化原则：
1. 降低刷新频率：避免频繁检查无新数据的情况
2. 增大刷新窗口：确保捕获完整的日更新数据
3. 错峰执行：避开数据更新时间段
4. 分层策略：不同聚合级别采用不同策略
*/

-- 删除现有策略（如果需要重新配置）
SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
SELECT remove_continuous_aggregate_policy('system_logs_hourly', if_exists => TRUE);

-- =============================================================================
-- 每日数据刷新场景的优化策略
-- =============================================================================

-- 每小时聚合 - 日更新优化策略
SELECT add_continuous_aggregate_policy('price_records_hourly',
    start_offset => INTERVAL '2 days',       -- 扩大窗口确保捕获完整日更新
    end_offset => INTERVAL '6 hours',        -- 给数据更新留出充足时间
    schedule_interval => INTERVAL '6 hours', -- 每6小时检查一次（一天4次）
    if_not_exists => TRUE
);

-- 每日聚合 - 日更新优化策略  
SELECT add_continuous_aggregate_policy('price_records_daily',
    start_offset => INTERVAL '3 days',       -- 确保捕获完整的日更新数据
    end_offset => INTERVAL '12 hours',       -- 给数据处理留出半天时间
    schedule_interval => INTERVAL '12 hours', -- 每12小时检查一次（一天2次）
    if_not_exists => TRUE
);

-- 每周聚合 - 低频策略
SELECT add_continuous_aggregate_policy('price_records_weekly',
    start_offset => INTERVAL '2 weeks',      -- 确保数据完整性
    end_offset => INTERVAL '1 day',          -- 1天缓冲时间足够
    schedule_interval => INTERVAL '1 day',   -- 每天检查一次
    if_not_exists => TRUE
);

-- 系统日志聚合 - 适中策略
SELECT add_continuous_aggregate_policy('system_logs_hourly',
    start_offset => INTERVAL '1 day',        -- 系统日志相对及时
    end_offset => INTERVAL '2 hours',        -- 2小时缓冲
    schedule_interval => INTERVAL '4 hours', -- 每4小时检查一次
    if_not_exists => TRUE
);

-- =============================================================================
-- 针对特定数据更新时间的策略（假设数据在凌晨2点更新）
-- =============================================================================

-- 创建基于时间的智能刷新策略
CREATE OR REPLACE FUNCTION smart_daily_refresh_policy()
RETURNS void AS $$
DECLARE
    current_hour INTEGER;
    last_data_update TIMESTAMP;
    last_refresh TIMESTAMP;
BEGIN
    -- 获取当前小时
    current_hour := EXTRACT(hour FROM NOW());
    
    -- 获取最新数据时间
    SELECT MAX(recorded_at) INTO last_data_update FROM price_records;
    
    -- 获取最后一次刷新时间
    SELECT MAX(last_run_success_time) INTO last_refresh
    FROM timescaledb_information.job_stats js
    JOIN timescaledb_information.jobs j ON js.job_id = j.job_id
    WHERE j.application_name = 'price_records_hourly';
    
    -- 智能刷新逻辑
    IF current_hour BETWEEN 3 AND 5 THEN
        -- 数据更新后的时间段：提高刷新频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '1 day',
            end_offset => INTERVAL '2 hours',
            schedule_interval => INTERVAL '1 hour'
        );
        
        RAISE NOTICE '数据更新时段：应用高频刷新策略';
        
    ELSIF current_hour BETWEEN 6 AND 23 THEN
        -- 正常工作时间：标准频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '6 hours'
        );
        
        RAISE NOTICE '工作时段：应用标准刷新策略';
        
    ELSE
        -- 夜间时段：低频刷新节省资源
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '12 hours'
        );
        
        RAISE NOTICE '夜间时段：应用低频刷新策略';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 数据更新检测和触发机制
-- =============================================================================

-- 创建函数：检测是否有新数据更新
CREATE OR REPLACE FUNCTION detect_daily_data_update()
RETURNS BOOLEAN AS $$
DECLARE
    today_count INTEGER;
    yesterday_count INTEGER;
    data_updated BOOLEAN := FALSE;
BEGIN
    -- 检查今天是否有新数据
    SELECT COUNT(*) INTO today_count
    FROM price_records
    WHERE recorded_at >= CURRENT_DATE;
    
    -- 检查昨天的数据量
    SELECT COUNT(*) INTO yesterday_count
    FROM price_records
    WHERE recorded_at >= CURRENT_DATE - INTERVAL '1 day'
    AND recorded_at < CURRENT_DATE;
    
    -- 如果今天有数据且数量合理，认为数据已更新
    IF today_count > 0 AND today_count >= yesterday_count * 0.5 THEN
        data_updated := TRUE;
        
        -- 记录数据更新事件
        INSERT INTO system_logs (level, message, module)
        VALUES ('INFO', 
                FORMAT('检测到日数据更新：今日%s条，昨日%s条', today_count, yesterday_count),
                'daily-data-detection');
    END IF;
    
    RETURN data_updated;
END;
$$ LANGUAGE plpgsql;

-- 创建触发式刷新函数
CREATE OR REPLACE FUNCTION trigger_refresh_after_data_update()
RETURNS void AS $$
BEGIN
    -- 检测到数据更新后，立即触发一次刷新
    IF detect_daily_data_update() THEN
        -- 手动触发连续聚合视图刷新
        CALL refresh_continuous_aggregate('price_records_hourly', NULL, NULL);
        CALL refresh_continuous_aggregate('price_records_daily', NULL, NULL);
        
        -- 记录触发刷新事件
        INSERT INTO system_logs (level, message, module)
        VALUES ('INFO', '数据更新后触发连续聚合视图刷新', 'triggered-refresh');
        
        RAISE NOTICE '检测到数据更新，已触发聚合视图刷新';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 资源优化策略
-- =============================================================================

-- 创建资源感知的刷新策略
CREATE OR REPLACE FUNCTION resource_aware_refresh_policy()
RETURNS void AS $$
DECLARE
    active_connections INTEGER;
    system_load NUMERIC;
BEGIN
    -- 获取当前活跃连接数（简化的系统负载指标）
    SELECT COUNT(*) INTO active_connections
    FROM pg_stat_activity
    WHERE state = 'active';
    
    -- 根据系统负载调整刷新策略
    IF active_connections > 20 THEN
        -- 高负载：降低刷新频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '12 hours'
        );
        
        RAISE NOTICE '系统高负载：应用低频刷新策略';
        
    ELSIF active_connections < 5 THEN
        -- 低负载：可以提高刷新频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '4 hours'
        );
        
        RAISE NOTICE '系统低负载：应用中频刷新策略';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 监控和统计视图（针对日更新场景）
-- =============================================================================

-- 创建日更新监控视图
CREATE OR REPLACE VIEW daily_update_monitoring AS
SELECT 
    DATE(recorded_at) as update_date,
    COUNT(*) as record_count,
    MIN(recorded_at) as first_record_time,
    MAX(recorded_at) as last_record_time,
    COUNT(DISTINCT product_id) as unique_products,
    AVG(price) as avg_price,
    CASE 
        WHEN DATE(recorded_at) = CURRENT_DATE THEN '今日'
        WHEN DATE(recorded_at) = CURRENT_DATE - 1 THEN '昨日'
        ELSE '历史'
    END as date_category
FROM price_records
WHERE recorded_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(recorded_at)
ORDER BY update_date DESC;

-- 创建刷新效率统计视图
CREATE OR REPLACE VIEW refresh_efficiency_stats AS
SELECT 
    j.application_name as view_name,
    js.total_runs,
    js.total_successes,
    js.total_failures,
    ROUND(js.total_successes::numeric / NULLIF(js.total_runs, 0) * 100, 2) as success_rate,
    js.last_run_duration,
    js.average_run_duration,
    CASE 
        WHEN js.average_run_duration < INTERVAL '5 minutes' THEN '高效'
        WHEN js.average_run_duration < INTERVAL '15 minutes' THEN '正常'
        ELSE '需优化'
    END as efficiency_status,
    j.config->>'schedule_interval' as current_interval
FROM timescaledb_information.jobs j
JOIN timescaledb_information.job_stats js ON j.job_id = js.job_id
WHERE j.proc_name = 'policy_refresh_continuous_aggregate';

-- =============================================================================
-- 定时任务建议（需要在系统层面配置）
-- =============================================================================

/*
建议的cron任务配置：

# 每天凌晨3点（数据更新后1小时）触发智能刷新
0 3 * * * psql -d moniit -c "SELECT trigger_refresh_after_data_update();"

# 每天上午9点应用工作时段策略
0 9 * * * psql -d moniit -c "SELECT smart_daily_refresh_policy();"

# 每天晚上11点应用夜间策略
0 23 * * * psql -d moniit -c "SELECT smart_daily_refresh_policy();"

# 每周日凌晨进行资源感知优化
0 1 * * 0 psql -d moniit -c "SELECT resource_aware_refresh_policy();"
*/

-- 记录优化策略应用完成
INSERT INTO system_logs (level, message, module) 
VALUES ('INFO', 'TimescaleDB日更新场景优化策略应用完成', 'daily-refresh-optimization');

COMMIT;
