#!/usr/bin/env python3
"""
调试供货商ID的脚本
"""

import asyncio
import httpx
import json


async def debug_supplier_ids():
    """调试供货商ID"""
    print("🔍 调试供货商ID...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=30.0) as client:
        try:
            # 1. 获取供货商列表
            print("  📋 获取供货商列表...")
            response = await client.get("/api/v1/suppliers/")
            if response.status_code == 200:
                data = response.json()
                suppliers = data.get('items', [])
                print(f"    ✅ 找到{len(suppliers)}个供货商")
                
                for i, supplier in enumerate(suppliers[:3]):
                    supplier_id = supplier['id']
                    supplier_name = supplier['name']
                    print(f"    {i+1}. ID: {supplier_id}, 名称: {supplier_name}")
                    
                    # 2. 尝试获取该供货商的详情
                    print(f"      🔍 测试获取详情...")
                    detail_response = await client.get(f"/api/v1/suppliers/{supplier_id}")
                    if detail_response.status_code == 200:
                        print(f"      ✅ 详情获取成功")
                    else:
                        print(f"      ❌ 详情获取失败 - 状态码: {detail_response.status_code}")
                
                # 3. 如果有至少2个供货商，测试对比功能
                if len(suppliers) >= 2:
                    supplier1_id = suppliers[0]['id']
                    supplier2_id = suppliers[1]['id']
                    print(f"\n  🔍 测试对比功能...")
                    print(f"    供货商1 ID: {supplier1_id}")
                    print(f"    供货商2 ID: {supplier2_id}")
                    
                    # 直接调用对比API
                    compare_url = f"/api/v1/suppliers/compare?supplier_ids={supplier1_id},{supplier2_id}"
                    print(f"    请求URL: {compare_url}")
                    
                    compare_response = await client.get(compare_url)
                    print(f"    状态码: {compare_response.status_code}")
                    
                    if compare_response.status_code == 200:
                        result = compare_response.json()
                        print(f"    ✅ 对比成功")
                        print(f"    📊 对比供货商数: {result.get('summary', {}).get('total_suppliers', 0)}")
                    else:
                        print(f"    ❌ 对比失败")
                        try:
                            error_data = compare_response.json()
                            print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                        except:
                            print(f"    错误内容: {compare_response.text}")
                
            else:
                print(f"    ❌ 获取供货商列表失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 调试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(debug_supplier_ids())
