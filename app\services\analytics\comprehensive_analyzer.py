"""
综合分析报告引擎

提供多维度数据综合分析、商品评分排名、对比分析等功能
"""

import asyncio
import statistics
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductPrice, ProductMetrics
from app.services.analytics.price_analyzer import PriceAnalyzer
from app.services.analytics.advanced_sales_analyzer import AdvancedSalesAnalyzer
from app.services.analytics.prediction_engine import PredictionEngine

logger = get_logger(__name__)


class ScoreCategory(Enum):
    """评分类别"""
    PRICE_COMPETITIVENESS = "price_competitiveness"     # 价格竞争力
    SALES_PERFORMANCE = "sales_performance"             # 销量表现
    INVENTORY_HEALTH = "inventory_health"               # 库存健康度
    CUSTOMER_SATISFACTION = "customer_satisfaction"     # 客户满意度
    MARKET_POTENTIAL = "market_potential"               # 市场潜力
    RISK_LEVEL = "risk_level"                          # 风险水平


class RiskType(Enum):
    """风险类型"""
    PRICE_RISK = "price_risk"           # 价格风险
    SALES_RISK = "sales_risk"           # 销量风险
    INVENTORY_RISK = "inventory_risk"   # 库存风险
    COMPETITIVE_RISK = "competitive_risk" # 竞争风险
    MARKET_RISK = "market_risk"         # 市场风险
    OPERATIONAL_RISK = "operational_risk" # 运营风险


class OpportunityType(Enum):
    """机会类型"""
    PRICE_OPPORTUNITY = "price_opportunity"         # 价格机会
    MARKET_OPPORTUNITY = "market_opportunity"       # 市场机会
    GROWTH_OPPORTUNITY = "growth_opportunity"       # 增长机会
    COMPETITIVE_OPPORTUNITY = "competitive_opportunity" # 竞争机会


@dataclass
class ScoreComponent:
    """评分组件"""
    category: ScoreCategory
    score: float  # 0-100
    weight: float  # 权重
    description: str
    factors: List[str]
    improvement_suggestions: List[str]


@dataclass
class ComprehensiveScore:
    """综合评分"""
    product_id: str
    overall_score: float  # 0-100
    grade: str  # A+, A, B+, B, C+, C, D
    components: List[ScoreComponent]
    strengths: List[str]
    weaknesses: List[str]
    score_trend: str  # improving, declining, stable
    benchmark_comparison: Dict[str, float]
    calculated_at: datetime = field(default_factory=datetime.now)


@dataclass
class RiskAssessment:
    """风险评估"""
    product_id: str
    overall_risk_level: str  # very_low, low, medium, high, very_high
    risk_score: float  # 0-100
    risks: List[Dict[str, Any]]
    mitigation_strategies: List[str]
    risk_trend: str  # increasing, decreasing, stable
    early_warning_indicators: List[str]


@dataclass
class MarketOpportunity:
    """市场机会"""
    opportunity_type: OpportunityType
    description: str
    potential_impact: float  # 0-1
    confidence_level: float  # 0-1
    time_to_realize: str  # short_term, medium_term, long_term
    required_actions: List[str]
    success_probability: float  # 0-1


@dataclass
class CompetitiveComparison:
    """竞品对比"""
    target_product_id: str
    competitor_products: List[str]
    comparison_metrics: Dict[str, Dict[str, float]]
    competitive_advantages: List[str]
    competitive_disadvantages: List[str]
    market_positioning: str
    strategic_recommendations: List[str]


@dataclass
class ComprehensiveReport:
    """综合分析报告"""
    report_id: str
    product_id: str
    report_type: str
    comprehensive_score: ComprehensiveScore
    risk_assessment: RiskAssessment
    market_opportunities: List[MarketOpportunity]
    competitive_comparison: Optional[CompetitiveComparison]
    key_insights: List[str]
    action_recommendations: List[str]
    executive_summary: str
    generated_at: datetime = field(default_factory=datetime.now)
    valid_until: datetime = field(default_factory=lambda: datetime.now() + timedelta(days=7))


class ComprehensiveAnalyzer:
    """综合分析报告引擎"""
    
    def __init__(self):
        self.price_analyzer = PriceAnalyzer()
        self.sales_analyzer = AdvancedSalesAnalyzer()
        self.prediction_engine = PredictionEngine()
        
        # 评分权重配置
        self.score_weights = {
            ProductType.COMPETITOR: {
                ScoreCategory.PRICE_COMPETITIVENESS: 0.25,
                ScoreCategory.SALES_PERFORMANCE: 0.30,
                ScoreCategory.INVENTORY_HEALTH: 0.15,
                ScoreCategory.CUSTOMER_SATISFACTION: 0.20,
                ScoreCategory.MARKET_POTENTIAL: 0.10
            },
            ProductType.SUPPLIER: {
                ScoreCategory.PRICE_COMPETITIVENESS: 0.30,
                ScoreCategory.SALES_PERFORMANCE: 0.25,
                ScoreCategory.INVENTORY_HEALTH: 0.20,
                ScoreCategory.CUSTOMER_SATISFACTION: 0.15,
                ScoreCategory.MARKET_POTENTIAL: 0.10
            },
            ProductType.OTHER: {
                ScoreCategory.PRICE_COMPETITIVENESS: 0.20,
                ScoreCategory.SALES_PERFORMANCE: 0.25,
                ScoreCategory.INVENTORY_HEALTH: 0.20,
                ScoreCategory.CUSTOMER_SATISFACTION: 0.25,
                ScoreCategory.MARKET_POTENTIAL: 0.10
            }
        }
        
        # 风险评估阈值
        self.risk_thresholds = {
            RiskType.PRICE_RISK: {
                "very_low": 0.1, "low": 0.3, "medium": 0.5, "high": 0.7, "very_high": 0.9
            },
            RiskType.SALES_RISK: {
                "very_low": 0.15, "low": 0.35, "medium": 0.55, "high": 0.75, "very_high": 0.95
            },
            RiskType.COMPETITIVE_RISK: {
                "very_low": 0.2, "low": 0.4, "medium": 0.6, "high": 0.8, "very_high": 1.0
            }
        }
        
        # 报告缓存
        self.report_cache: Dict[str, ComprehensiveReport] = {}
    
    async def generate_comprehensive_report(self, product: Product,
                                          competitor_products: Optional[List[Product]] = None,
                                          report_type: str = "standard") -> ComprehensiveReport:
        """
        生成综合分析报告
        
        Args:
            product: 目标商品
            competitor_products: 竞品列表
            report_type: 报告类型
        
        Returns:
            ComprehensiveReport: 综合分析报告
        """
        try:
            logger.info(f"开始生成综合分析报告: {product.id}")
            
            # 检查缓存
            cache_key = f"{product.id}_{report_type}_{len(competitor_products or [])}"
            if cache_key in self.report_cache:
                cached_report = self.report_cache[cache_key]
                if datetime.now() < cached_report.valid_until:
                    logger.info(f"返回缓存的报告: {product.id}")
                    return cached_report
            
            # 1. 计算综合评分
            comprehensive_score = await self._calculate_comprehensive_score(product)
            
            # 2. 风险评估
            risk_assessment = await self._assess_risks(product)
            
            # 3. 市场机会识别
            market_opportunities = await self._identify_market_opportunities(product)
            
            # 4. 竞品对比分析
            competitive_comparison = None
            if competitor_products:
                competitive_comparison = await self._analyze_competitive_comparison(
                    product, competitor_products
                )
            
            # 5. 生成关键洞察
            key_insights = await self._generate_key_insights(
                product, comprehensive_score, risk_assessment, market_opportunities
            )
            
            # 6. 生成行动建议
            action_recommendations = await self._generate_action_recommendations(
                product, comprehensive_score, risk_assessment, market_opportunities
            )
            
            # 7. 生成执行摘要
            executive_summary = self._generate_executive_summary(
                product, comprehensive_score, risk_assessment, len(market_opportunities)
            )
            
            # 创建综合报告
            report = ComprehensiveReport(
                report_id=f"report_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                product_id=product.id,
                report_type=report_type,
                comprehensive_score=comprehensive_score,
                risk_assessment=risk_assessment,
                market_opportunities=market_opportunities,
                competitive_comparison=competitive_comparison,
                key_insights=key_insights,
                action_recommendations=action_recommendations,
                executive_summary=executive_summary
            )
            
            # 缓存报告
            self.report_cache[cache_key] = report
            
            logger.info(f"综合分析报告生成完成: {product.id}")
            return report
            
        except Exception as e:
            logger.error(f"综合分析报告生成失败: {e}")
            return self._create_empty_report(product, report_type)
    
    async def _calculate_comprehensive_score(self, product: Product) -> ComprehensiveScore:
        """计算综合评分"""
        try:
            components = []
            weights = self.score_weights.get(product.product_type, 
                                           self.score_weights[ProductType.OTHER])
            
            # 1. 价格竞争力评分
            price_score = await self._calculate_price_competitiveness_score(product)
            components.append(price_score)
            
            # 2. 销量表现评分
            sales_score = await self._calculate_sales_performance_score(product)
            components.append(sales_score)
            
            # 3. 库存健康度评分
            inventory_score = self._calculate_inventory_health_score(product)
            components.append(inventory_score)
            
            # 4. 客户满意度评分
            satisfaction_score = self._calculate_customer_satisfaction_score(product)
            components.append(satisfaction_score)
            
            # 5. 市场潜力评分
            potential_score = await self._calculate_market_potential_score(product)
            components.append(potential_score)
            
            # 计算加权总分
            total_weighted_score = 0
            total_weight = 0
            
            for component in components:
                weight = weights.get(component.category, 0.2)
                component.weight = weight
                total_weighted_score += component.score * weight
                total_weight += weight
            
            overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
            
            # 确定等级
            grade = self._determine_grade(overall_score)
            
            # 识别优势和劣势
            strengths, weaknesses = self._identify_strengths_weaknesses(components)
            
            # 评分趋势（简化实现）
            score_trend = "stable"  # 实际应基于历史评分数据
            
            # 基准对比
            benchmark_comparison = self._calculate_benchmark_comparison(
                product, overall_score
            )
            
            return ComprehensiveScore(
                product_id=product.id,
                overall_score=overall_score,
                grade=grade,
                components=components,
                strengths=strengths,
                weaknesses=weaknesses,
                score_trend=score_trend,
                benchmark_comparison=benchmark_comparison
            )
            
        except Exception as e:
            logger.error(f"综合评分计算失败: {e}")
            return self._create_empty_score(product)
    
    async def _calculate_price_competitiveness_score(self, product: Product) -> ScoreComponent:
        """计算价格竞争力评分"""
        if not product.price or product.price.current_price <= 0:
            return ScoreComponent(
                category=ScoreCategory.PRICE_COMPETITIVENESS,
                score=50.0,
                weight=0.0,
                description="价格信息不足",
                factors=["缺少价格数据"],
                improvement_suggestions=["完善价格信息"]
            )
        
        # 基于价格分析结果计算评分
        try:
            price_analysis = await self.price_analyzer.analyze_price_trend(product)
            
            score = 50.0  # 基础分
            factors = []
            suggestions = []
            
            # 价格趋势影响
            if price_analysis.price_trend.value == "falling":
                score += 15  # 价格下降有利于竞争力
                factors.append("价格呈下降趋势")
            elif price_analysis.price_trend.value == "rising":
                score -= 10  # 价格上涨不利于竞争力
                factors.append("价格呈上升趋势")
                suggestions.append("关注价格上涨对竞争力的影响")
            
            # 价格波动影响
            if price_analysis.volatility < 0.1:
                score += 10  # 价格稳定
                factors.append("价格稳定性良好")
            elif price_analysis.volatility > 0.3:
                score -= 15  # 价格波动大
                factors.append("价格波动较大")
                suggestions.append("稳定价格策略")
            
            # 价格区间评估
            current_price = product.price.current_price
            if current_price < 100:
                score += 10  # 低价商品竞争力强
                factors.append("价格具有竞争优势")
            elif current_price > 1000:
                score -= 5   # 高价商品需要更多价值支撑
                factors.append("高价商品需要价值匹配")
                suggestions.append("强化产品价值传递")
            
            score = max(0, min(100, score))
            
            return ScoreComponent(
                category=ScoreCategory.PRICE_COMPETITIVENESS,
                score=score,
                weight=0.0,  # 将在上层设置
                description=f"价格竞争力评分 {score:.1f}分",
                factors=factors,
                improvement_suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"价格竞争力评分计算失败: {e}")
            return ScoreComponent(
                category=ScoreCategory.PRICE_COMPETITIVENESS,
                score=50.0,
                weight=0.0,
                description="价格分析异常",
                factors=["价格分析失败"],
                improvement_suggestions=["检查价格数据质量"]
            )
    
    async def _calculate_sales_performance_score(self, product: Product) -> ScoreComponent:
        """计算销量表现评分"""
        if not product.metrics or product.metrics.sales_count <= 0:
            return ScoreComponent(
                category=ScoreCategory.SALES_PERFORMANCE,
                score=30.0,
                weight=0.0,
                description="销量数据不足",
                factors=["缺少销量数据"],
                improvement_suggestions=["完善销量统计"]
            )
        
        try:
            sales_analysis = await self.sales_analyzer.base_analyzer.analyze_sales_trend(product)
            
            score = 40.0  # 基础分
            factors = []
            suggestions = []
            
            # 销量水平评估
            current_sales = product.metrics.sales_count
            if current_sales >= 10000:
                score += 30  # 高销量
                factors.append("销量表现优秀")
            elif current_sales >= 5000:
                score += 20  # 中等销量
                factors.append("销量表现良好")
            elif current_sales >= 1000:
                score += 10  # 一般销量
                factors.append("销量表现一般")
                suggestions.append("提升销量推广策略")
            else:
                score += 0   # 低销量
                factors.append("销量表现较差")
                suggestions.append("加强营销推广")
            
            # 销量趋势影响
            if sales_analysis.sales_trend.value == "growing":
                score += 15  # 增长趋势
                factors.append("销量呈增长趋势")
            elif sales_analysis.sales_trend.value == "declining":
                score -= 20  # 下降趋势
                factors.append("销量呈下降趋势")
                suggestions.append("分析销量下降原因")
            
            # 销量增长率影响
            if sales_analysis.sales_growth_rate > 20:
                score += 10  # 高增长
                factors.append("销量增长率较高")
            elif sales_analysis.sales_growth_rate < -10:
                score -= 15  # 负增长
                factors.append("销量出现负增长")
                suggestions.append("制定销量提升计划")
            
            score = max(0, min(100, score))
            
            return ScoreComponent(
                category=ScoreCategory.SALES_PERFORMANCE,
                score=score,
                weight=0.0,
                description=f"销量表现评分 {score:.1f}分",
                factors=factors,
                improvement_suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"销量表现评分计算失败: {e}")
            return ScoreComponent(
                category=ScoreCategory.SALES_PERFORMANCE,
                score=40.0,
                weight=0.0,
                description="销量分析异常",
                factors=["销量分析失败"],
                improvement_suggestions=["检查销量数据质量"]
            )
    
    def _calculate_inventory_health_score(self, product: Product) -> ScoreComponent:
        """计算库存健康度评分"""
        if not product.metrics or not hasattr(product.metrics, 'stock_quantity'):
            return ScoreComponent(
                category=ScoreCategory.INVENTORY_HEALTH,
                score=60.0,
                weight=0.0,
                description="库存信息不足",
                factors=["缺少库存数据"],
                improvement_suggestions=["完善库存监控"]
            )
        
        score = 50.0  # 基础分
        factors = []
        suggestions = []
        
        stock_quantity = getattr(product.metrics, 'stock_quantity', 0)
        sales_count = product.metrics.sales_count or 0
        
        # 库存充足度评估
        if stock_quantity is not None and sales_count is not None and sales_count > 0:
            if stock_quantity > sales_count * 0.5:  # 库存超过销量的50%
                score += 20
                factors.append("库存充足")
            elif stock_quantity > sales_count * 0.2:  # 库存超过销量的20%
                score += 10
                factors.append("库存适中")
            elif stock_quantity > 0:
                score -= 10
                factors.append("库存偏低")
                suggestions.append("及时补充库存")
            else:
                score -= 30
                factors.append("库存不足")
                suggestions.append("紧急补货")
        elif stock_quantity is not None:
            if stock_quantity > 0:
                score += 5
                factors.append("有库存")
            else:
                score -= 20
                factors.append("库存不足")
                suggestions.append("紧急补货")
        
        # 库存周转评估（简化）
        if (sales_count is not None and stock_quantity is not None and
            sales_count > 0 and stock_quantity > 0):
            turnover_ratio = sales_count / stock_quantity
            if turnover_ratio > 2:  # 高周转
                score += 15
                factors.append("库存周转率高")
            elif turnover_ratio < 0.5:  # 低周转
                score -= 10
                factors.append("库存周转率低")
                suggestions.append("优化库存管理")
        
        score = max(0, min(100, score))
        
        return ScoreComponent(
            category=ScoreCategory.INVENTORY_HEALTH,
            score=score,
            weight=0.0,
            description=f"库存健康度评分 {score:.1f}分",
            factors=factors,
            improvement_suggestions=suggestions
        )
    
    def _calculate_customer_satisfaction_score(self, product: Product) -> ScoreComponent:
        """计算客户满意度评分"""
        if not product.metrics or not hasattr(product.metrics, 'rating'):
            return ScoreComponent(
                category=ScoreCategory.CUSTOMER_SATISFACTION,
                score=70.0,
                weight=0.0,
                description="客户评价信息不足",
                factors=["缺少评价数据"],
                improvement_suggestions=["收集客户反馈"]
            )
        
        rating = getattr(product.metrics, 'rating', 0)
        
        if rating <= 0:
            score = 50.0
            factors = ["无客户评价"]
            suggestions = ["鼓励客户评价"]
        elif rating >= 4.5:
            score = 95.0
            factors = ["客户满意度极高"]
            suggestions = ["保持优质服务"]
        elif rating >= 4.0:
            score = 85.0
            factors = ["客户满意度高"]
            suggestions = ["继续提升服务质量"]
        elif rating >= 3.5:
            score = 70.0
            factors = ["客户满意度中等"]
            suggestions = ["改善产品和服务"]
        elif rating >= 3.0:
            score = 55.0
            factors = ["客户满意度偏低"]
            suggestions = ["重点改善客户体验"]
        else:
            score = 30.0
            factors = ["客户满意度低"]
            suggestions = ["全面改善产品质量"]
        
        return ScoreComponent(
            category=ScoreCategory.CUSTOMER_SATISFACTION,
            score=score,
            weight=0.0,
            description=f"客户满意度评分 {score:.1f}分",
            factors=factors,
            improvement_suggestions=suggestions
        )
    
    async def _calculate_market_potential_score(self, product: Product) -> ScoreComponent:
        """计算市场潜力评分"""
        score = 60.0  # 基础分
        factors = []
        suggestions = []
        
        try:
            # 基于产品类型评估市场潜力
            if product.product_type == ProductType.COMPETITOR:
                score += 10
                factors.append("竞品市场活跃")
            elif product.product_type == ProductType.SUPPLIER:
                score += 5
                factors.append("供应商市场稳定")
            
            # 基于价格区间评估市场潜力
            if product.price and product.price.current_price:
                price = product.price.current_price
                if 50 <= price <= 500:  # 中等价位市场潜力大
                    score += 15
                    factors.append("价格区间市场潜力大")
                elif price > 1000:  # 高端市场
                    score += 5
                    factors.append("高端市场定位")
                    suggestions.append("关注高端用户需求")
            
            # 基于销量趋势评估潜力
            if product.metrics and product.metrics.sales_count:
                sales = product.metrics.sales_count
                if sales > 5000:
                    score += 10
                    factors.append("销量基础良好")
                elif sales < 500:
                    score -= 10
                    factors.append("销量基础薄弱")
                    suggestions.append("扩大市场推广")
            
            score = max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"市场潜力评分计算失败: {e}")
            score = 60.0
            factors = ["市场潜力分析异常"]
            suggestions = ["完善市场分析数据"]
        
        return ScoreComponent(
            category=ScoreCategory.MARKET_POTENTIAL,
            score=score,
            weight=0.0,
            description=f"市场潜力评分 {score:.1f}分",
            factors=factors,
            improvement_suggestions=suggestions
        )

    def _determine_grade(self, score: float) -> str:
        """确定评分等级"""
        if score >= 90:
            return "A+"
        elif score >= 85:
            return "A"
        elif score >= 80:
            return "B+"
        elif score >= 75:
            return "B"
        elif score >= 70:
            return "C+"
        elif score >= 60:
            return "C"
        else:
            return "D"

    def _identify_strengths_weaknesses(self, components: List[ScoreComponent]) -> Tuple[List[str], List[str]]:
        """识别优势和劣势"""
        strengths = []
        weaknesses = []

        # 按分数排序
        sorted_components = sorted(components, key=lambda x: x.score, reverse=True)

        # 前两名作为优势
        for component in sorted_components[:2]:
            if component.score >= 70:
                strengths.append(f"{component.category.value}: {component.score:.1f}分")

        # 后两名作为劣势
        for component in sorted_components[-2:]:
            if component.score < 60:
                weaknesses.append(f"{component.category.value}: {component.score:.1f}分")

        return strengths, weaknesses

    def _calculate_benchmark_comparison(self, product: Product, score: float) -> Dict[str, float]:
        """计算基准对比"""
        # 简化的基准对比实现
        benchmarks = {
            "行业平均": 65.0,
            "同类商品": 70.0,
            "市场领先": 85.0
        }

        comparison = {}
        for benchmark_name, benchmark_score in benchmarks.items():
            comparison[benchmark_name] = score - benchmark_score

        return comparison

    async def _assess_risks(self, product: Product) -> RiskAssessment:
        """评估风险"""
        try:
            risks = []
            overall_risk_score = 0

            # 1. 价格风险评估
            price_risk = await self._assess_price_risk(product)
            if price_risk:
                risks.append(price_risk)
                overall_risk_score += price_risk["risk_score"]

            # 2. 销量风险评估
            sales_risk = await self._assess_sales_risk(product)
            if sales_risk:
                risks.append(sales_risk)
                overall_risk_score += sales_risk["risk_score"]

            # 3. 库存风险评估
            inventory_risk = self._assess_inventory_risk(product)
            if inventory_risk:
                risks.append(inventory_risk)
                overall_risk_score += inventory_risk["risk_score"]

            # 4. 竞争风险评估
            competitive_risk = self._assess_competitive_risk(product)
            if competitive_risk:
                risks.append(competitive_risk)
                overall_risk_score += competitive_risk["risk_score"]

            # 计算平均风险分数
            avg_risk_score = overall_risk_score / len(risks) if risks else 0

            # 确定风险级别
            overall_risk_level = self._determine_risk_level(avg_risk_score)

            # 生成缓解策略
            mitigation_strategies = self._generate_mitigation_strategies(risks)

            # 早期预警指标
            early_warning_indicators = self._identify_early_warning_indicators(product, risks)

            return RiskAssessment(
                product_id=product.id,
                overall_risk_level=overall_risk_level,
                risk_score=avg_risk_score,
                risks=risks,
                mitigation_strategies=mitigation_strategies,
                risk_trend="stable",  # 简化实现
                early_warning_indicators=early_warning_indicators
            )

        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return self._create_empty_risk_assessment(product)

    async def _assess_price_risk(self, product: Product) -> Optional[Dict[str, Any]]:
        """评估价格风险"""
        if not product.price or product.price.current_price <= 0:
            return None

        try:
            price_analysis = await self.price_analyzer.analyze_price_trend(product)

            risk_score = 0
            risk_factors = []

            # 价格波动风险
            if price_analysis.volatility > 0.3:
                risk_score += 30
                risk_factors.append("价格波动过大")

            # 价格趋势风险
            if price_analysis.price_trend.value == "rising":
                if price_analysis.price_change_percent > 20:
                    risk_score += 25
                    risk_factors.append("价格快速上涨")

            # 价格预警风险
            if price_analysis.price_alerts:
                critical_alerts = [alert for alert in price_analysis.price_alerts
                                 if alert.get('level') == 'critical']
                if critical_alerts:
                    risk_score += 20
                    risk_factors.append("存在价格预警")

            if risk_score > 0:
                return {
                    "risk_type": RiskType.PRICE_RISK.value,
                    "risk_score": min(100, risk_score),
                    "description": "价格相关风险",
                    "factors": risk_factors,
                    "impact": "可能影响产品竞争力和销量",
                    "probability": min(1.0, risk_score / 100)
                }

            return None

        except Exception as e:
            logger.error(f"价格风险评估失败: {e}")
            return None

    async def _assess_sales_risk(self, product: Product) -> Optional[Dict[str, Any]]:
        """评估销量风险"""
        if not product.metrics or product.metrics.sales_count <= 0:
            return {
                "risk_type": RiskType.SALES_RISK.value,
                "risk_score": 60,
                "description": "销量数据不足风险",
                "factors": ["缺少销量数据"],
                "impact": "无法准确评估市场表现",
                "probability": 0.8
            }

        try:
            sales_analysis = await self.sales_analyzer.base_analyzer.analyze_sales_trend(product)

            risk_score = 0
            risk_factors = []

            # 销量下降风险
            if sales_analysis.sales_trend.value == "declining":
                risk_score += 40
                risk_factors.append("销量呈下降趋势")

                if sales_analysis.sales_growth_rate < -20:
                    risk_score += 20
                    risk_factors.append("销量下降幅度大")

            # 销量停滞风险
            elif sales_analysis.sales_trend.value == "stable":
                if sales_analysis.sales_growth_rate < 5:
                    risk_score += 15
                    risk_factors.append("销量增长停滞")

            # 低销量风险
            if product.metrics.sales_count < 1000:
                risk_score += 25
                risk_factors.append("销量基数较低")

            if risk_score > 0:
                return {
                    "risk_type": RiskType.SALES_RISK.value,
                    "risk_score": min(100, risk_score),
                    "description": "销量相关风险",
                    "factors": risk_factors,
                    "impact": "可能影响收入和市场地位",
                    "probability": min(1.0, risk_score / 100)
                }

            return None

        except Exception as e:
            logger.error(f"销量风险评估失败: {e}")
            return None

    def _assess_inventory_risk(self, product: Product) -> Optional[Dict[str, Any]]:
        """评估库存风险"""
        if not product.metrics or not hasattr(product.metrics, 'stock_quantity'):
            return {
                "risk_type": RiskType.INVENTORY_RISK.value,
                "risk_score": 40,
                "description": "库存信息不足风险",
                "factors": ["缺少库存数据"],
                "impact": "无法有效管理库存",
                "probability": 0.6
            }

        stock_quantity = getattr(product.metrics, 'stock_quantity', 0)
        sales_count = product.metrics.sales_count or 0

        risk_score = 0
        risk_factors = []

        # 库存不足风险
        if stock_quantity is not None and stock_quantity <= 0:
            risk_score += 50
            risk_factors.append("库存为零")
        elif (stock_quantity is not None and sales_count is not None and
              sales_count > 0 and stock_quantity < sales_count * 0.1):
            risk_score += 30
            risk_factors.append("库存严重不足")
        elif (stock_quantity is not None and sales_count is not None and
              sales_count > 0 and stock_quantity < sales_count * 0.2):
            risk_score += 15
            risk_factors.append("库存偏低")

        # 库存积压风险
        if (stock_quantity is not None and sales_count is not None and
            sales_count > 0 and stock_quantity > sales_count * 2):
            risk_score += 20
            risk_factors.append("库存积压风险")

        if risk_score > 0:
            return {
                "risk_type": RiskType.INVENTORY_RISK.value,
                "risk_score": min(100, risk_score),
                "description": "库存管理风险",
                "factors": risk_factors,
                "impact": "可能导致缺货或积压",
                "probability": min(1.0, risk_score / 100)
            }

        return None

    def _assess_competitive_risk(self, product: Product) -> Optional[Dict[str, Any]]:
        """评估竞争风险"""
        risk_score = 0
        risk_factors = []

        # 基于产品类型评估竞争风险
        if product.product_type == ProductType.COMPETITOR:
            risk_score += 20
            risk_factors.append("竞品市场竞争激烈")

        # 基于价格评估竞争风险
        if product.price and product.price.current_price:
            price = product.price.current_price
            if price > 1000:  # 高价商品竞争风险高
                risk_score += 15
                risk_factors.append("高价商品面临价格竞争")

        # 基于客户满意度评估竞争风险
        if product.metrics and hasattr(product.metrics, 'rating'):
            rating = getattr(product.metrics, 'rating', 0)
            if rating < 3.5:
                risk_score += 25
                risk_factors.append("客户满意度低，竞争劣势明显")

        if risk_score > 0:
            return {
                "risk_type": RiskType.COMPETITIVE_RISK.value,
                "risk_score": min(100, risk_score),
                "description": "市场竞争风险",
                "factors": risk_factors,
                "impact": "可能失去市场份额",
                "probability": min(1.0, risk_score / 100)
            }

        return None

    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险级别"""
        if risk_score >= 80:
            return "very_high"
        elif risk_score >= 60:
            return "high"
        elif risk_score >= 40:
            return "medium"
        elif risk_score >= 20:
            return "low"
        else:
            return "very_low"

    def _generate_mitigation_strategies(self, risks: List[Dict[str, Any]]) -> List[str]:
        """生成风险缓解策略"""
        strategies = []

        for risk in risks:
            risk_type = risk["risk_type"]

            if risk_type == RiskType.PRICE_RISK.value:
                strategies.extend([
                    "建立价格监控预警机制",
                    "制定灵活的价格调整策略",
                    "加强成本控制"
                ])
            elif risk_type == RiskType.SALES_RISK.value:
                strategies.extend([
                    "加强市场推广活动",
                    "优化产品定位和营销策略",
                    "拓展销售渠道"
                ])
            elif risk_type == RiskType.INVENTORY_RISK.value:
                strategies.extend([
                    "优化库存管理系统",
                    "建立安全库存机制",
                    "加强供应链协调"
                ])
            elif risk_type == RiskType.COMPETITIVE_RISK.value:
                strategies.extend([
                    "加强产品差异化",
                    "提升客户服务质量",
                    "建立品牌护城河"
                ])

        # 去重
        return list(set(strategies))

    def _identify_early_warning_indicators(self, product: Product,
                                         risks: List[Dict[str, Any]]) -> List[str]:
        """识别早期预警指标"""
        indicators = []

        # 基于风险类型设置预警指标
        risk_types = [risk["risk_type"] for risk in risks]

        if RiskType.PRICE_RISK.value in risk_types:
            indicators.extend([
                "价格波动率超过30%",
                "价格连续上涨超过20%",
                "竞品价格大幅下调"
            ])

        if RiskType.SALES_RISK.value in risk_types:
            indicators.extend([
                "销量连续3期下降",
                "销量增长率低于-10%",
                "市场份额持续下滑"
            ])

        if RiskType.INVENTORY_RISK.value in risk_types:
            indicators.extend([
                "库存周转率低于0.5",
                "库存量低于安全库存",
                "缺货率超过5%"
            ])

        return list(set(indicators))

    def _create_empty_risk_assessment(self, product: Product) -> RiskAssessment:
        """创建空的风险评估"""
        return RiskAssessment(
            product_id=product.id,
            overall_risk_level="medium",
            risk_score=50.0,
            risks=[],
            mitigation_strategies=["完善数据收集"],
            risk_trend="stable",
            early_warning_indicators=["建立监控体系"]
        )

    async def _identify_market_opportunities(self, product: Product) -> List[MarketOpportunity]:
        """识别市场机会"""
        opportunities = []

        try:
            # 1. 价格机会识别
            price_opportunities = await self._identify_price_opportunities(product)
            opportunities.extend(price_opportunities)

            # 2. 市场机会识别
            market_opportunities = await self._identify_market_growth_opportunities(product)
            opportunities.extend(market_opportunities)

            # 3. 增长机会识别
            growth_opportunities = await self._identify_growth_opportunities(product)
            opportunities.extend(growth_opportunities)

            # 4. 竞争机会识别
            competitive_opportunities = self._identify_competitive_opportunities(product)
            opportunities.extend(competitive_opportunities)

            # 按潜在影响排序
            opportunities.sort(key=lambda x: x.potential_impact, reverse=True)

            return opportunities[:10]  # 返回前10个机会

        except Exception as e:
            logger.error(f"市场机会识别失败: {e}")
            return []

    async def _identify_price_opportunities(self, product: Product) -> List[MarketOpportunity]:
        """识别价格机会"""
        opportunities = []

        if not product.price or product.price.current_price <= 0:
            return opportunities

        try:
            price_analysis = await self.price_analyzer.analyze_price_trend(product)

            # 价格下降机会
            if price_analysis.price_trend.value == "falling":
                if price_analysis.price_change_percent < -10:
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.PRICE_OPPORTUNITY,
                        description="价格下降创造竞争优势机会",
                        potential_impact=0.7,
                        confidence_level=0.8,
                        time_to_realize="short_term",
                        required_actions=[
                            "加大营销推广力度",
                            "强调价格优势",
                            "扩大市场份额"
                        ],
                        success_probability=0.75
                    ))

            # 价格稳定机会
            elif price_analysis.price_trend.value == "stable" and price_analysis.volatility < 0.1:
                opportunities.append(MarketOpportunity(
                    opportunity_type=OpportunityType.PRICE_OPPORTUNITY,
                    description="价格稳定有利于建立客户信任",
                    potential_impact=0.5,
                    confidence_level=0.9,
                    time_to_realize="medium_term",
                    required_actions=[
                        "强化品牌价值传递",
                        "建立长期客户关系",
                        "提升服务质量"
                    ],
                    success_probability=0.8
                ))

        except Exception as e:
            logger.error(f"价格机会识别失败: {e}")

        return opportunities

    async def _identify_market_growth_opportunities(self, product: Product) -> List[MarketOpportunity]:
        """识别市场增长机会"""
        opportunities = []

        try:
            # 基于产品类型识别机会
            if product.product_type == ProductType.COMPETITOR:
                # 竞品市场机会
                if product.metrics and product.metrics.sales_count > 5000:
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.MARKET_OPPORTUNITY,
                        description="高销量商品具有市场扩张潜力",
                        potential_impact=0.8,
                        confidence_level=0.7,
                        time_to_realize="medium_term",
                        required_actions=[
                            "拓展新的销售渠道",
                            "开发相关产品线",
                            "加强品牌建设"
                        ],
                        success_probability=0.7
                    ))

            # 基于价格区间识别机会
            if product.price and product.price.current_price:
                price = product.price.current_price
                if 100 <= price <= 500:  # 中等价位市场机会大
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.MARKET_OPPORTUNITY,
                        description="中等价位市场需求旺盛",
                        potential_impact=0.6,
                        confidence_level=0.8,
                        time_to_realize="short_term",
                        required_actions=[
                            "优化产品性价比",
                            "加强市场推广",
                            "提升产品质量"
                        ],
                        success_probability=0.75
                    ))

            # 基于客户满意度识别机会
            if product.metrics and hasattr(product.metrics, 'rating'):
                rating = getattr(product.metrics, 'rating', 0)
                if rating >= 4.5:
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.MARKET_OPPORTUNITY,
                        description="高客户满意度支持市场扩张",
                        potential_impact=0.7,
                        confidence_level=0.9,
                        time_to_realize="medium_term",
                        required_actions=[
                            "利用口碑营销",
                            "推出客户推荐计划",
                            "扩大产品曝光度"
                        ],
                        success_probability=0.8
                    ))

        except Exception as e:
            logger.error(f"市场增长机会识别失败: {e}")

        return opportunities

    async def _identify_growth_opportunities(self, product: Product) -> List[MarketOpportunity]:
        """识别增长机会"""
        opportunities = []

        try:
            if product.metrics and product.metrics.sales_count > 0:
                sales_analysis = await self.sales_analyzer.base_analyzer.analyze_sales_trend(product)

                # 销量增长机会
                if sales_analysis.sales_trend.value == "growing":
                    if sales_analysis.sales_growth_rate > 20:
                        opportunities.append(MarketOpportunity(
                            opportunity_type=OpportunityType.GROWTH_OPPORTUNITY,
                            description="强劲增长趋势提供扩张机会",
                            potential_impact=0.9,
                            confidence_level=0.8,
                            time_to_realize="short_term",
                            required_actions=[
                                "增加库存准备",
                                "扩大生产能力",
                                "加强供应链管理"
                            ],
                            success_probability=0.85
                        ))

                # 销量稳定机会
                elif sales_analysis.sales_trend.value == "stable":
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.GROWTH_OPPORTUNITY,
                        description="稳定销量基础上寻求突破",
                        potential_impact=0.6,
                        confidence_level=0.7,
                        time_to_realize="medium_term",
                        required_actions=[
                            "产品创新升级",
                            "开拓新市场",
                            "优化营销策略"
                        ],
                        success_probability=0.6
                    ))

        except Exception as e:
            logger.error(f"增长机会识别失败: {e}")

        return opportunities

    def _identify_competitive_opportunities(self, product: Product) -> List[MarketOpportunity]:
        """识别竞争机会"""
        opportunities = []

        try:
            # 基于产品优势识别竞争机会
            if product.metrics and hasattr(product.metrics, 'rating'):
                rating = getattr(product.metrics, 'rating', 0)
                if rating >= 4.0:
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.COMPETITIVE_OPPORTUNITY,
                        description="高评分产品具有竞争优势",
                        potential_impact=0.6,
                        confidence_level=0.8,
                        time_to_realize="short_term",
                        required_actions=[
                            "突出产品优势",
                            "对比竞品营销",
                            "建立品牌差异化"
                        ],
                        success_probability=0.7
                    ))

            # 基于价格优势识别机会
            if product.price and product.price.current_price:
                price = product.price.current_price
                if price < 200:  # 低价优势
                    opportunities.append(MarketOpportunity(
                        opportunity_type=OpportunityType.COMPETITIVE_OPPORTUNITY,
                        description="价格优势吸引价格敏感客户",
                        potential_impact=0.5,
                        confidence_level=0.7,
                        time_to_realize="short_term",
                        required_actions=[
                            "强调性价比优势",
                            "针对价格敏感市场",
                            "优化成本结构"
                        ],
                        success_probability=0.65
                    ))

        except Exception as e:
            logger.error(f"竞争机会识别失败: {e}")

        return opportunities

    async def _analyze_competitive_comparison(self, product: Product,
                                           competitors: List[Product]) -> CompetitiveComparison:
        """分析竞品对比"""
        try:
            comparison_metrics = {}

            # 收集对比指标
            all_products = [product] + competitors

            for p in all_products:
                metrics = {}

                # 价格指标
                if p.price and p.price.current_price:
                    metrics["price"] = p.price.current_price
                else:
                    metrics["price"] = 0

                # 销量指标
                if p.metrics and p.metrics.sales_count:
                    metrics["sales"] = p.metrics.sales_count
                else:
                    metrics["sales"] = 0

                # 评分指标
                if p.metrics and hasattr(p.metrics, 'rating'):
                    metrics["rating"] = getattr(p.metrics, 'rating', 0)
                else:
                    metrics["rating"] = 0

                # 库存指标
                if p.metrics and hasattr(p.metrics, 'stock_quantity'):
                    metrics["stock"] = getattr(p.metrics, 'stock_quantity', 0)
                else:
                    metrics["stock"] = 0

                comparison_metrics[p.id] = metrics

            # 分析竞争优势和劣势
            advantages, disadvantages = self._analyze_competitive_position(
                product, competitors, comparison_metrics
            )

            # 确定市场定位
            market_positioning = self._determine_market_positioning(
                product, comparison_metrics
            )

            # 生成战略建议
            strategic_recommendations = self._generate_competitive_strategies(
                product, advantages, disadvantages, market_positioning
            )

            return CompetitiveComparison(
                target_product_id=product.id,
                competitor_products=[c.id for c in competitors],
                comparison_metrics=comparison_metrics,
                competitive_advantages=advantages,
                competitive_disadvantages=disadvantages,
                market_positioning=market_positioning,
                strategic_recommendations=strategic_recommendations
            )

        except Exception as e:
            logger.error(f"竞品对比分析失败: {e}")
            return CompetitiveComparison(
                target_product_id=product.id,
                competitor_products=[],
                comparison_metrics={},
                competitive_advantages=[],
                competitive_disadvantages=[],
                market_positioning="unknown",
                strategic_recommendations=["数据不足，无法进行对比分析"]
            )

    def _analyze_competitive_position(self, product: Product, competitors: List[Product],
                                    metrics: Dict[str, Dict[str, float]]) -> Tuple[List[str], List[str]]:
        """分析竞争地位"""
        advantages = []
        disadvantages = []

        product_metrics = metrics.get(product.id, {})

        # 价格对比
        product_price = product_metrics.get("price", 0)
        competitor_prices = [metrics.get(c.id, {}).get("price", 0) for c in competitors if metrics.get(c.id, {}).get("price", 0) > 0]

        if competitor_prices:
            avg_competitor_price = statistics.mean(competitor_prices)
            if product_price > 0:
                if product_price < avg_competitor_price * 0.9:
                    advantages.append(f"价格优势：比竞品平均低 {(1 - product_price/avg_competitor_price)*100:.1f}%")
                elif product_price > avg_competitor_price * 1.1:
                    disadvantages.append(f"价格劣势：比竞品平均高 {(product_price/avg_competitor_price - 1)*100:.1f}%")

        # 销量对比
        product_sales = product_metrics.get("sales", 0)
        competitor_sales = [metrics.get(c.id, {}).get("sales", 0) for c in competitors if metrics.get(c.id, {}).get("sales", 0) > 0]

        if competitor_sales:
            max_competitor_sales = max(competitor_sales)
            avg_competitor_sales = statistics.mean(competitor_sales)

            if product_sales > max_competitor_sales:
                advantages.append("销量领先：销量超过所有竞品")
            elif product_sales > avg_competitor_sales * 1.2:
                advantages.append("销量优势：销量明显高于竞品平均")
            elif product_sales < avg_competitor_sales * 0.5:
                disadvantages.append("销量劣势：销量明显低于竞品平均")

        # 评分对比
        product_rating = product_metrics.get("rating", 0)
        competitor_ratings = [metrics.get(c.id, {}).get("rating", 0) for c in competitors if metrics.get(c.id, {}).get("rating", 0) > 0]

        if competitor_ratings:
            avg_competitor_rating = statistics.mean(competitor_ratings)
            if product_rating > avg_competitor_rating + 0.3:
                advantages.append(f"评分优势：评分 {product_rating:.1f}，高于竞品平均")
            elif product_rating < avg_competitor_rating - 0.3:
                disadvantages.append(f"评分劣势：评分 {product_rating:.1f}，低于竞品平均")

        return advantages, disadvantages

    def _determine_market_positioning(self, product: Product,
                                    metrics: Dict[str, Dict[str, float]]) -> str:
        """确定市场定位"""
        product_metrics = metrics.get(product.id, {})

        price = product_metrics.get("price", 0)
        sales = product_metrics.get("sales", 0)
        rating = product_metrics.get("rating", 0)

        # 基于价格和质量定位
        if price > 1000 and rating >= 4.5:
            return "高端优质"
        elif price > 1000 and rating < 4.0:
            return "高端待改进"
        elif price < 200 and sales > 5000:
            return "大众爆款"
        elif price < 200:
            return "经济实惠"
        elif 200 <= price <= 1000 and rating >= 4.0:
            return "中端优质"
        elif 200 <= price <= 1000:
            return "中端市场"
        else:
            return "市场定位不明确"

    def _generate_competitive_strategies(self, product: Product, advantages: List[str],
                                       disadvantages: List[str], positioning: str) -> List[str]:
        """生成竞争策略"""
        strategies = []

        # 基于优势的策略
        if "价格优势" in str(advantages):
            strategies.append("强化价格优势营销，吸引价格敏感客户")

        if "销量" in str(advantages):
            strategies.append("利用销量优势建立市场领导地位")

        if "评分优势" in str(advantages):
            strategies.append("突出产品质量和客户满意度")

        # 基于劣势的改进策略
        if "价格劣势" in str(disadvantages):
            strategies.append("优化成本结构或强化产品价值")

        if "销量劣势" in str(disadvantages):
            strategies.append("加强市场推广和渠道建设")

        if "评分劣势" in str(disadvantages):
            strategies.append("改善产品质量和客户服务")

        # 基于定位的策略
        if positioning == "高端优质":
            strategies.append("维护高端品牌形象，提供优质服务")
        elif positioning == "大众爆款":
            strategies.append("扩大生产规模，保持价格优势")
        elif positioning == "经济实惠":
            strategies.append("强调性价比，扩大市场覆盖")

        return strategies if strategies else ["制定差异化竞争策略"]

    async def _generate_key_insights(self, product: Product, score: ComprehensiveScore,
                                   risk: RiskAssessment, opportunities: List[MarketOpportunity]) -> List[str]:
        """生成关键洞察"""
        insights = []

        # 评分洞察
        if score.overall_score >= 85:
            insights.append(f"商品综合表现优秀（{score.grade}级），具有强竞争力")
        elif score.overall_score >= 70:
            insights.append(f"商品综合表现良好（{score.grade}级），有提升空间")
        else:
            insights.append(f"商品综合表现需要改进（{score.grade}级），存在明显短板")

        # 风险洞察
        if risk.overall_risk_level in ["high", "very_high"]:
            insights.append(f"商品面临{risk.overall_risk_level}风险，需要重点关注")
        elif risk.overall_risk_level == "very_low":
            insights.append("商品风险水平较低，运营相对稳定")

        # 机会洞察
        high_impact_opportunities = [op for op in opportunities if op.potential_impact >= 0.7]
        if high_impact_opportunities:
            insights.append(f"识别到{len(high_impact_opportunities)}个高潜力市场机会")

        # 优势洞察
        if score.strengths:
            top_strength = score.strengths[0]
            insights.append(f"核心优势：{top_strength}")

        # 劣势洞察
        if score.weaknesses:
            top_weakness = score.weaknesses[0]
            insights.append(f"主要短板：{top_weakness}")

        return insights

    async def _generate_action_recommendations(self, product: Product, score: ComprehensiveScore,
                                             risk: RiskAssessment, opportunities: List[MarketOpportunity]) -> List[str]:
        """生成行动建议"""
        recommendations = []

        # 基于评分的建议
        low_score_components = [c for c in score.components if c.score < 60]
        for component in low_score_components:
            recommendations.extend(component.improvement_suggestions[:2])

        # 基于风险的建议
        recommendations.extend(risk.mitigation_strategies[:3])

        # 基于机会的建议
        top_opportunities = sorted(opportunities, key=lambda x: x.potential_impact, reverse=True)[:3]
        for opportunity in top_opportunities:
            recommendations.extend(opportunity.required_actions[:2])

        # 去重并限制数量
        unique_recommendations = list(set(recommendations))
        return unique_recommendations[:10]

    def _generate_executive_summary(self, product: Product, score: ComprehensiveScore,
                                  risk: RiskAssessment, opportunity_count: int) -> str:
        """生成执行摘要"""
        summary_parts = []

        # 基本信息
        summary_parts.append(f"商品 {product.title[:30]}... 综合分析报告")

        # 综合评分
        summary_parts.append(f"综合评分：{score.overall_score:.1f}分（{score.grade}级）")

        # 风险评估
        risk_level_cn = {
            "very_low": "极低", "low": "低", "medium": "中等",
            "high": "高", "very_high": "极高"
        }
        summary_parts.append(f"风险水平：{risk_level_cn.get(risk.overall_risk_level, '未知')}")

        # 机会数量
        summary_parts.append(f"识别市场机会：{opportunity_count}个")

        # 核心建议
        if score.overall_score >= 80:
            summary_parts.append("建议：保持优势，寻求突破")
        elif score.overall_score >= 60:
            summary_parts.append("建议：补强短板，提升竞争力")
        else:
            summary_parts.append("建议：全面改进，重点突破")

        return "；".join(summary_parts) + "。"

    def _create_empty_score(self, product: Product) -> ComprehensiveScore:
        """创建空的综合评分"""
        return ComprehensiveScore(
            product_id=product.id,
            overall_score=50.0,
            grade="C",
            components=[],
            strengths=[],
            weaknesses=["数据不足"],
            score_trend="unknown",
            benchmark_comparison={}
        )

    def _create_empty_report(self, product: Product, report_type: str) -> ComprehensiveReport:
        """创建空的综合报告"""
        return ComprehensiveReport(
            report_id=f"empty_report_{product.id}",
            product_id=product.id,
            report_type=report_type,
            comprehensive_score=self._create_empty_score(product),
            risk_assessment=self._create_empty_risk_assessment(product),
            market_opportunities=[],
            competitive_comparison=None,
            key_insights=["数据不足，无法生成完整分析"],
            action_recommendations=["完善商品数据收集"],
            executive_summary="数据不足，无法生成综合分析报告"
        )

    async def batch_generate_reports(self, products: List[Product],
                                   report_type: str = "standard") -> Dict[str, ComprehensiveReport]:
        """批量生成综合报告"""
        logger.info(f"开始批量生成综合报告: {len(products)} 个商品")

        results = {}

        # 并发生成报告
        tasks = [
            self.generate_comprehensive_report(product, None, report_type)
            for product in products
        ]

        reports = await asyncio.gather(*tasks, return_exceptions=True)

        for i, report in enumerate(reports):
            if isinstance(report, Exception):
                logger.error(f"商品 {products[i].id} 报告生成失败: {report}")
                results[products[i].id] = self._create_empty_report(products[i], report_type)
            else:
                results[products[i].id] = report

        logger.info(f"批量报告生成完成: {len(results)} 个结果")
        return results

    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        return {
            "cached_reports": len(self.report_cache),
            "score_categories": [category.value for category in ScoreCategory],
            "risk_types": [risk_type.value for risk_type in RiskType],
            "opportunity_types": [op_type.value for op_type in OpportunityType],
            "supported_product_types": list(self.score_weights.keys()),
            "grade_levels": ["A+", "A", "B+", "B", "C+", "C", "D"],
            "risk_levels": ["very_low", "low", "medium", "high", "very_high"]
        }
