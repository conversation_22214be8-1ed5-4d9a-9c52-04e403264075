/**
 * 数据分析API服务
 */

import { api } from './api';
import {
  PriceTrend,
  PriceTrendPoint
} from '../types';

export interface PriceTrendParams {
  product_id?: string;
  category?: string;
  platform?: string;
  start_date?: string;
  end_date?: string;
  interval?: 'hour' | 'day' | 'week' | 'month';
}

export interface SalesAnalysisParams {
  product_id?: string;
  category?: string;
  platform?: string;
  start_date?: string;
  end_date?: string;
  metrics?: string[];
}

export interface ComprehensiveReportParams {
  report_type?: 'daily' | 'weekly' | 'monthly';
  start_date?: string;
  end_date?: string;
  categories?: string[];
  platforms?: string[];
}

export interface SalesAnalysisData {
  daily_sales?: Array<{
    date: string;
    sales_volume: number;
    sales_amount: number;
    platform: string;
    category?: string;
  }>;
  summary?: {
    total_sales: number;
    avg_daily_sales: number;
    growth_rate: number;
  };
}

export interface ComprehensiveReport {
  summary: {
    total_products: number;
    total_sales: number;
    avg_price: number;
    top_categories: string[];
  };
  trends: PriceTrend[];
  recommendations: string[];
}

export interface AnalyticsSummary {
  total_products: number;
  average_price: number;
  price_change_percentage: number;
  min_price: number;
  max_price: number;
  total_sales: number;
  sales_change_percentage: number;
  active_monitors: number;
  last_updated: string;
}

class AnalyticsApi {
  /**
   * 获取统计数据 - 调整为匹配后端端点
   */
  async getStatistics(params?: {
    platform?: string;
    category?: string;
    days?: number;
  }): Promise<{ data: any }> {
    const response = await api.get('/api/v1/analytics/statistics', { params });
    return response;
  }

  /**
   * 获取价格趋势分析 - 调整为匹配后端端点
   */
  async getPriceTrend(params: {
    product_id: string;
    days?: number;
    interval?: string
  }): Promise<{ data: PriceTrendPoint[] }> {
    const response = await api.get(`/api/v1/analytics/price-trends/${params.product_id}`, {
      params: { days: params.days, interval: params.interval }
    });
    return response;
  }

  /**
   * 生成报表 - 调整为匹配后端端点
   */
  async generateReport(params: {
    filters: any;
    report_type?: string;
    format?: string;
  }): Promise<{ data: any }> {
    const response = await api.post('/api/v1/analytics/reports/generate', params.filters, {
      params: {
        report_type: params.report_type,
        format: params.format
      }
    });
    return response;
  }

  /**
   * 数据搜索 - 调整为匹配后端端点
   */
  async searchData(params: {
    keyword?: string;
    platform?: string;
    category?: string;
    price_min?: number;
    price_max?: number;
    sort_by?: string;
    sort_order?: string;
    skip?: number;
    limit?: number;
  }): Promise<{ data: any }> {
    const response = await api.get('/api/v1/analytics/search', { params });
    return response;
  }

  /**
   * 获取价格分布数据
   */
  async getPriceDistribution(params?: {
    category?: string;
    platform?: string;
    date_range?: string;
  }): Promise<{ data: Array<{ price_range: string; count: number; percentage: number }> }> {
    const response = await api.get('/api/v1/analytics/price-distribution', { params });
    return response;
  }

  /**
   * 获取平台对比数据
   */
  async getPlatformComparison(params?: {
    categories?: string[];
    start_date?: string;
    end_date?: string;
  }): Promise<{ data: Array<{ platform: string; avg_price: number; product_count: number; sales_volume: number }> }> {
    const response = await api.get('/api/v1/analytics/platform-comparison', { params });
    return response;
  }

  /**
   * 获取分类分析数据
   */
  async getCategoryAnalysis(params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<{ data: Array<{ category: string; avg_price: number; product_count: number; price_trend: number }> }> {
    const response = await api.get('/api/v1/analytics/category-analysis', { params });
    return response;
  }

  /**
   * 导出分析报告
   */
  async exportReport(params: {
    report_type: 'price_trend' | 'sales_analysis' | 'comprehensive';
    format: 'excel' | 'pdf' | 'csv';
    start_date?: string;
    end_date?: string;
    filters?: Record<string, any>;
  }): Promise<Blob> {
    try {
      // 使用原生fetch来处理blob响应
      const token = localStorage.getItem('token');
      const response = await fetch('/api/v1/analytics/export-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 从响应头获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      let filename = `analytics_report.${params.format}`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return blob;
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  }

  /**
   * 获取实时数据更新
   */
  async getRealTimeData(): Promise<{ data: {
    active_monitors: number;
    recent_updates: number;
    price_alerts: number;
    system_status: 'healthy' | 'warning' | 'error';
  } }> {
    const response = await api.get('/api/v1/analytics/realtime');
    return response;
  }
}

export const analyticsApi = new AnalyticsApi();
