"""
缓存工具函数
"""

import hashlib
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from app.core.cache import get_cache_manager


class CacheKeyBuilder:
    """缓存键构建器"""
    
    @staticmethod
    def product_trend(product_id: str, trend_type: str, days: int, interval: str = "1d") -> str:
        """商品趋势缓存键"""
        return f"trend:{trend_type}:{product_id}:{days}:{interval}"
    
    @staticmethod
    def product_analysis(product_id: str, analysis_type: str, days: int) -> str:
        """商品分析缓存键"""
        return f"analysis:{analysis_type}:{product_id}:{days}"
    
    @staticmethod
    def supplier_costs(product_id: str) -> str:
        """供货商成本缓存键"""
        return f"costs:product:{product_id}"
    
    @staticmethod
    def platform_config(platform: str) -> str:
        """平台配置缓存键"""
        return f"config:platform:{platform}"
    
    @staticmethod
    def user_session(user_id: str) -> str:
        """用户会话缓存键"""
        return f"session:user:{user_id}"
    
    @staticmethod
    def api_rate_limit(api_key: str, endpoint: str) -> str:
        """API限流缓存键"""
        return f"rate_limit:{api_key}:{endpoint}"
    
    @staticmethod
    def translation_cache(text: str, target_lang: str = "zh") -> str:
        """翻译缓存键"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"translation:{target_lang}:{text_hash}"
    
    @staticmethod
    def search_results(query: str, filters: Dict[str, Any]) -> str:
        """搜索结果缓存键"""
        filters_str = json.dumps(filters, sort_keys=True)
        filters_hash = hashlib.md5(filters_str.encode()).hexdigest()
        query_hash = hashlib.md5(query.encode()).hexdigest()
        return f"search:{query_hash}:{filters_hash}"


class CacheHelper:
    """缓存助手类"""
    
    def __init__(self):
        self.key_builder = CacheKeyBuilder()
    
    async def get_or_set(self, key: str, fetch_func, ttl: int = 3600, 
                        cache_type: str = "default") -> Any:
        """获取缓存或设置缓存"""
        cache = await get_cache_manager()
        
        # 尝试从缓存获取
        cached_value = await cache.get(key)
        if cached_value is not None:
            return cached_value
        
        # 执行获取函数
        if callable(fetch_func):
            value = await fetch_func() if hasattr(fetch_func, '__call__') else fetch_func
        else:
            value = fetch_func
        
        # 设置缓存
        if value is not None:
            await cache.set(key, value, ttl, cache_type)
        
        return value
    
    async def invalidate_product_cache(self, product_id: str):
        """清除商品相关缓存"""
        cache = await get_cache_manager()
        
        patterns = [
            f"trend:*:{product_id}:*",
            f"analysis:*:{product_id}:*",
            f"costs:product:{product_id}",
            f"profit:*:{product_id}:*"
        ]
        
        for pattern in patterns:
            await cache.clear_pattern(pattern)
    
    async def invalidate_platform_cache(self, platform: str):
        """清除平台相关缓存"""
        cache = await get_cache_manager()
        await cache.clear_pattern(f"config:platform:{platform}")
    
    async def invalidate_user_cache(self, user_id: str):
        """清除用户相关缓存"""
        cache = await get_cache_manager()
        patterns = [
            f"session:user:{user_id}",
            f"user:*:{user_id}:*"
        ]
        
        for pattern in patterns:
            await cache.clear_pattern(pattern)
    
    async def cache_product_trend(self, product_id: str, trend_type: str, 
                                 days: int, interval: str, data: Any) -> bool:
        """缓存商品趋势数据"""
        cache = await get_cache_manager()
        key = self.key_builder.product_trend(product_id, trend_type, days, interval)
        
        # 根据趋势类型设置不同的TTL
        ttl_map = {
            "price": 1800,  # 30分钟
            "sales": 1800,  # 30分钟
            "inventory": 900,  # 15分钟
            "rating": 3600,  # 1小时
        }
        ttl = ttl_map.get(trend_type, 1800)
        
        return await cache.set(key, data, ttl, f"{trend_type}_trend")
    
    async def get_product_trend(self, product_id: str, trend_type: str, 
                               days: int, interval: str = "1d") -> Optional[Any]:
        """获取商品趋势数据"""
        cache = await get_cache_manager()
        key = self.key_builder.product_trend(product_id, trend_type, days, interval)
        return await cache.get(key)
    
    async def cache_translation(self, text: str, translated_text: str, 
                               target_lang: str = "zh") -> bool:
        """缓存翻译结果"""
        cache = await get_cache_manager()
        key = self.key_builder.translation_cache(text, target_lang)
        
        # 翻译结果缓存7天
        return await cache.set(key, translated_text, 7 * 24 * 3600, "translation")
    
    async def get_translation(self, text: str, target_lang: str = "zh") -> Optional[str]:
        """获取翻译结果"""
        cache = await get_cache_manager()
        key = self.key_builder.translation_cache(text, target_lang)
        return await cache.get(key)
    
    async def cache_search_results(self, query: str, filters: Dict[str, Any], 
                                  results: List[Any]) -> bool:
        """缓存搜索结果"""
        cache = await get_cache_manager()
        key = self.key_builder.search_results(query, filters)
        
        # 搜索结果缓存10分钟
        return await cache.set(key, results, 600, "search")
    
    async def get_search_results(self, query: str, filters: Dict[str, Any]) -> Optional[List[Any]]:
        """获取搜索结果"""
        cache = await get_cache_manager()
        key = self.key_builder.search_results(query, filters)
        return await cache.get(key)
    
    async def check_rate_limit(self, api_key: str, endpoint: str, 
                              limit: int, window: int = 3600) -> Dict[str, Any]:
        """检查API限流"""
        cache = await get_cache_manager()
        key = self.key_builder.api_rate_limit(api_key, endpoint)
        
        # 获取当前计数
        current_count = await cache.get(key) or 0
        
        if current_count >= limit:
            return {
                "allowed": False,
                "current_count": current_count,
                "limit": limit,
                "reset_time": datetime.utcnow() + timedelta(seconds=window)
            }
        
        # 增加计数
        new_count = current_count + 1
        await cache.set(key, new_count, window, "rate_limit")
        
        return {
            "allowed": True,
            "current_count": new_count,
            "limit": limit,
            "remaining": limit - new_count
        }
    
    async def warm_up_cache(self, product_ids: List[str]):
        """预热缓存"""
        # 这里可以预加载一些常用的数据到缓存中
        # 比如热门商品的基础信息、平台配置等
        pass
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        cache = await get_cache_manager()
        return await cache.get_info()


# 全局缓存助手实例
cache_helper = CacheHelper()


# 便捷函数
async def cache_get(key: str, use_local: bool = True) -> Optional[Any]:
    """获取缓存"""
    cache = await get_cache_manager()
    return await cache.get(key, use_local)


async def cache_set(key: str, value: Any, ttl: int = 3600, 
                   cache_type: str = "default") -> bool:
    """设置缓存"""
    cache = await get_cache_manager()
    return await cache.set(key, value, ttl, cache_type)


async def cache_delete(key: str) -> bool:
    """删除缓存"""
    cache = await get_cache_manager()
    return await cache.delete(key)


async def cache_exists(key: str) -> bool:
    """检查缓存是否存在"""
    cache = await get_cache_manager()
    return await cache.exists(key)


async def cache_clear_pattern(pattern: str) -> int:
    """清除匹配模式的缓存"""
    cache = await get_cache_manager()
    return await cache.clear_pattern(pattern)
