#!/bin/bash

# Moniit 系统恢复脚本
# 支持从备份恢复数据库、配置文件、上传文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
BACKUP_DIR="./backups"
DB_CONTAINER="moniit-timescaledb-dev"
DB_NAME="moniit"
DB_USER="moniit"

# 显示帮助信息
show_help() {
    echo "Moniit 系统恢复脚本"
    echo ""
    echo "用法: $0 [选项] <备份路径>"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE      恢复类型 (full|db|config|files) [默认: full]"
    echo "  -f, --force          强制恢复（不询问确认）"
    echo "  -b, --backup-dir DIR 备份目录 [默认: ./backups]"
    echo "  -l, --list           列出可用备份"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "恢复类型:"
    echo "  full    - 完整恢复（数据库+配置+文件）"
    echo "  db      - 仅恢复数据库"
    echo "  config  - 仅恢复配置文件"
    echo "  files   - 仅恢复上传文件"
    echo ""
    echo "示例:"
    echo "  $0 moniit_backup_20231201_120000"
    echo "  $0 -t db moniit_backup_20231201_120000.tar.gz"
    echo "  $0 -f /path/to/backup"
    echo "  $0 -l"
}

# 列出可用备份
list_backups() {
    log_info "可用备份:"
    echo ""
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return
    fi
    
    local count=0
    
    # 列出备份目录
    echo "备份目录:"
    while IFS= read -r -d '' backup; do
        local size=$(du -sh "$backup" 2>/dev/null | cut -f1)
        local name=$(basename "$backup")
        local date=$(echo "$name" | sed 's/moniit_backup_//' | sed 's/_/ /')
        echo "  $name ($size) - $date"
        ((count++))
    done < <(find "$BACKUP_DIR" -name "moniit_backup_*" -type d -print0 | sort -z)
    
    echo ""
    echo "压缩备份:"
    while IFS= read -r -d '' backup; do
        local size=$(du -sh "$backup" 2>/dev/null | cut -f1)
        local name=$(basename "$backup")
        local date=$(echo "$name" | sed 's/moniit_backup_//' | sed 's/.tar.gz//' | sed 's/_/ /')
        echo "  $name ($size) - $date"
        ((count++))
    done < <(find "$BACKUP_DIR" -name "moniit_backup_*.tar.gz" -type f -print0 | sort -z)
    
    if [ $count -eq 0 ]; then
        log_warning "未找到任何备份文件"
    else
        echo ""
        log_info "找到 $count 个备份"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 验证备份文件
validate_backup() {
    local backup_path=$1
    
    log_info "验证备份文件: $backup_path"
    
    # 检查备份是否存在
    if [ ! -e "$backup_path" ]; then
        log_error "备份文件不存在: $backup_path"
        return 1
    fi
    
    # 如果是压缩文件，先解压
    if [[ "$backup_path" == *.tar.gz ]]; then
        log_info "解压备份文件..."
        local extract_dir="${backup_path%.tar.gz}"
        
        if [ -d "$extract_dir" ]; then
            log_warning "解压目录已存在，将被覆盖: $extract_dir"
            rm -rf "$extract_dir"
        fi
        
        tar -xzf "$backup_path" -C "$(dirname "$backup_path")"
        BACKUP_PATH="$extract_dir"
    else
        BACKUP_PATH="$backup_path"
    fi
    
    # 验证备份内容
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "db" ]; then
        if [ ! -f "${BACKUP_PATH}/database.sql" ]; then
            log_error "数据库备份文件不存在: ${BACKUP_PATH}/database.sql"
            return 1
        fi
    fi
    
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "config" ]; then
        if [ ! -d "${BACKUP_PATH}/config" ]; then
            log_warning "配置备份目录不存在: ${BACKUP_PATH}/config"
        fi
    fi
    
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "files" ]; then
        if [ ! -d "${BACKUP_PATH}/files" ]; then
            log_warning "文件备份目录不存在: ${BACKUP_PATH}/files"
        fi
    fi
    
    log_success "备份验证通过"
    return 0
}

# 确认恢复操作
confirm_restore() {
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    echo ""
    log_warning "警告: 恢复操作将覆盖现有数据!"
    log_warning "恢复类型: $RESTORE_TYPE"
    log_warning "备份路径: $BACKUP_PATH"
    echo ""
    
    read -p "确定要继续恢复吗? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复操作已取消"
        exit 0
    fi
}

# 停止相关服务
stop_services() {
    log_info "停止相关服务..."
    
    # 停止应用服务但保持数据库运行
    docker-compose stop backend frontend worker beat flower 2>/dev/null || true
    
    log_success "服务停止完成"
}

# 启动相关服务
start_services() {
    log_info "启动相关服务..."
    
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 恢复数据库
restore_database() {
    log_info "恢复数据库..."
    
    local db_backup_file="${BACKUP_PATH}/database.sql"
    
    if [ ! -f "$db_backup_file" ]; then
        log_error "数据库备份文件不存在: $db_backup_file"
        return 1
    fi
    
    # 确保数据库容器运行
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        log_info "启动数据库容器..."
        docker-compose up -d db
        sleep 10
    fi
    
    # 创建数据库备份（以防万一）
    log_info "创建当前数据库备份..."
    local current_backup="./current_db_backup_$(date +%Y%m%d_%H%M%S).sql"
    docker exec "$DB_CONTAINER" pg_dump -U "$DB_USER" -d "$DB_NAME" > "$current_backup" || true
    
    # 恢复数据库
    log_info "执行数据库恢复..."
    docker exec -i "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" < "$db_backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库恢复完成"
        log_info "当前数据库备份保存在: $current_backup"
    else
        log_error "数据库恢复失败"
        return 1
    fi
}

# 恢复配置文件
restore_config() {
    log_info "恢复配置文件..."
    
    local config_dir="${BACKUP_PATH}/config"
    
    if [ ! -d "$config_dir" ]; then
        log_warning "配置备份目录不存在，跳过配置恢复"
        return 0
    fi
    
    # 备份当前配置
    local current_config_backup="./current_config_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$current_config_backup"
    
    local config_files=(
        ".env"
        "docker-compose.yml"
        "docker-compose.dev.yml"
        "docker-compose.prod.yml"
        "config/"
        "nginx/"
    )
    
    for file in "${config_files[@]}"; do
        if [ -e "$file" ]; then
            cp -r "$file" "$current_config_backup/" 2>/dev/null || true
        fi
    done
    
    # 恢复配置文件
    cp -r "$config_dir"/* ./
    
    log_success "配置文件恢复完成"
    log_info "当前配置备份保存在: $current_config_backup"
}

# 恢复文件
restore_files() {
    log_info "恢复文件..."
    
    local files_dir="${BACKUP_PATH}/files"
    
    if [ ! -d "$files_dir" ]; then
        log_warning "文件备份目录不存在，跳过文件恢复"
        return 0
    fi
    
    # 备份当前文件
    local current_files_backup="./current_files_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$current_files_backup"
    
    if [ -d "uploads" ]; then
        cp -r uploads "$current_files_backup/" 2>/dev/null || true
    fi
    
    if [ -d "data" ]; then
        cp -r data "$current_files_backup/" 2>/dev/null || true
    fi
    
    # 恢复文件
    if [ -d "${files_dir}/uploads" ]; then
        rm -rf uploads 2>/dev/null || true
        cp -r "${files_dir}/uploads" ./
        log_info "上传文件恢复完成"
    fi
    
    if [ -d "${files_dir}/data" ]; then
        rm -rf data 2>/dev/null || true
        cp -r "${files_dir}/data" ./
        log_info "数据文件恢复完成"
    fi
    
    log_success "文件恢复完成")
    log_info "当前文件备份保存在: $current_files_backup"
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 等待服务启动
    sleep 10
    
    # 检查数据库连接
    if docker exec "$DB_CONTAINER" pg_isready -U "$DB_USER" -d "$DB_NAME" &>/dev/null; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 检查应用健康状态
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health &>/dev/null; then
            log_success "应用服务正常"
            break
        fi
        
        log_info "等待应用启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_warning "应用服务检查超时，请手动验证"
    fi
    
    log_success "恢复验证完成"
}

# 主函数
main() {
    # 默认参数
    RESTORE_TYPE="full"
    FORCE=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                RESTORE_TYPE="$2"
                shift 2
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -b|--backup-dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -l|--list)
                list_backups
                exit 0
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
            *)
                BACKUP_PATH="$1"
                shift
                ;;
        esac
    done
    
    # 检查备份路径
    if [ -z "$BACKUP_PATH" ]; then
        log_error "请指定备份路径"
        show_help
        exit 1
    fi
    
    # 如果备份路径不是绝对路径，则相对于备份目录
    if [[ "$BACKUP_PATH" != /* ]]; then
        BACKUP_PATH="${BACKUP_DIR}/${BACKUP_PATH}"
    fi
    
    # 验证恢复类型
    if [[ "$RESTORE_TYPE" != "full" && "$RESTORE_TYPE" != "db" && "$RESTORE_TYPE" != "config" && "$RESTORE_TYPE" != "files" ]]; then
        log_error "无效的恢复类型: $RESTORE_TYPE"
        exit 1
    fi
    
    log_info "开始 Moniit 系统恢复 (类型: $RESTORE_TYPE)"
    
    # 执行恢复
    check_dependencies
    validate_backup "$BACKUP_PATH"
    confirm_restore
    stop_services
    
    case $RESTORE_TYPE in
        "full")
            restore_database
            restore_config
            restore_files
            ;;
        "db")
            restore_database
            ;;
        "config")
            restore_config
            ;;
        "files")
            restore_files
            ;;
    esac
    
    start_services
    verify_restore
    
    log_success "Moniit 系统恢复完成!"
    log_info "请验证系统功能是否正常"
}

# 执行主函数
main "$@"
