/**
 * 监控任务相关API
 */

import { api } from './api';
import { MonitorTask, MonitorTaskForm, MonitorSearchParams, PaginatedResponse, ApiResponse, TaskStatus } from '../types';

// 监控任务查询参数
interface MonitorQueryParams {
  page?: number;
  page_size?: number;
  search?: MonitorSearchParams;
}

// 批量操作参数
interface BatchOperationParams {
  task_ids: string[];
  operation: 'pause' | 'resume' | 'delete' | 'run';
}

// 任务执行结果
interface TaskExecutionResult {
  task_id: string;
  status: TaskStatus;
  result?: any;
  error?: string;
  execution_time: number;
  timestamp: string;
}

// 任务统计信息
interface TaskStats {
  total_tasks: number;
  active_tasks: number;
  paused_tasks: number;
  failed_tasks: number;
  success_rate: number;
  avg_execution_time: number;
}

export const monitorApi = {
  // 获取监控任务列表
  getTasks: (params?: MonitorQueryParams): Promise<ApiResponse<PaginatedResponse<MonitorTask>>> => {
    return api.get('/api/v1/monitor/tasks', { params });
  },

  // 获取任务详情
  getTaskById: (taskId: string): Promise<ApiResponse<MonitorTask>> => {
    return api.get(`/api/v1/monitor/tasks/${taskId}`);
  },

  // 创建监控任务
  createTask: (taskData: MonitorTaskForm): Promise<ApiResponse<MonitorTask>> => {
    return api.post('/api/v1/monitor/tasks', taskData);
  },

  // 更新监控任务
  updateTask: (taskId: string, taskData: Partial<MonitorTaskForm>): Promise<ApiResponse<MonitorTask>> => {
    return api.put(`/api/v1/monitor/tasks/${taskId}`, taskData);
  },

  // 删除监控任务
  deleteTask: (taskId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/api/v1/monitor/tasks/${taskId}`);
  },

  // 执行监控任务
  runTask: (taskId: string): Promise<ApiResponse<TaskExecutionResult>> => {
    return api.post(`/api/v1/monitor/tasks/${taskId}/run`);
  },

  // 暂停监控任务
  pauseTask: (taskId: string): Promise<ApiResponse<MonitorTask>> => {
    return api.post(`/api/v1/monitor/tasks/${taskId}/pause`);
  },

  // 恢复监控任务
  resumeTask: (taskId: string): Promise<ApiResponse<MonitorTask>> => {
    return api.post(`/api/v1/monitor/tasks/${taskId}/resume`);
  },

  // 批量操作任务
  batchOperation: (params: BatchOperationParams): Promise<ApiResponse<any>> => {
    return api.post('/api/v1/monitor/tasks/batch-operation', params);
  },

  // 获取任务执行历史
  getTaskHistory: (taskId: string, limit?: number): Promise<ApiResponse<TaskExecutionResult[]>> => {
    return api.get(`/api/v1/monitor/tasks/${taskId}/history`, {
      params: { limit }
    });
  },

  // 获取任务执行日志
  getTaskLogs: (taskId: string, limit?: number): Promise<ApiResponse<any[]>> => {
    return api.get(`/api/v1/monitor/tasks/${taskId}/logs`, {
      params: { limit }
    });
  },

  // 获取任务统计信息
  getTaskStats: (taskId?: string): Promise<ApiResponse<TaskStats>> => {
    const url = taskId ? `/api/v1/monitor/tasks/${taskId}/stats` : '/api/v1/monitor/tasks/stats';
    return api.get(url);
  },

  // 测试任务配置
  testTask: (taskData: MonitorTaskForm): Promise<ApiResponse<{ success: boolean; result?: any; error?: string }>> => {
    return api.post('/api/v1/monitor/tasks/test', taskData);
  },

  // 验证任务URL
  validateTaskUrl: (url: string): Promise<ApiResponse<{ valid: boolean; error?: string }>> => {
    return api.post('/api/v1/monitor/tasks/validate-url', { url });
  },

  // 验证CSS选择器
  validateSelector: (url: string, selector: string): Promise<ApiResponse<{ valid: boolean; matches: number; error?: string }>> => {
    return api.post('/api/v1/monitor/tasks/validate-selector', { url, selector });
  },

  // 获取任务模板
  getTaskTemplates: (): Promise<ApiResponse<any[]>> => {
    return api.get('/monitor/tasks/templates');
  },

  // 从模板创建任务
  createTaskFromTemplate: (templateId: string, taskData: Partial<MonitorTaskForm>): Promise<ApiResponse<MonitorTask>> => {
    return api.post(`/monitor/tasks/templates/${templateId}/create`, taskData);
  },

  // 获取监控概览
  getMonitorOverview: (): Promise<ApiResponse<{
    total_tasks: number;
    active_tasks: number;
    recent_executions: TaskExecutionResult[];
    system_status: string;
  }>> => {
    return api.get('/monitor/overview');
  },

  // 获取实时任务状态
  getRealtimeTaskStatus: (): Promise<ApiResponse<Record<string, TaskStatus>>> => {
    return api.get('/monitor/tasks/realtime-status');
  },

  // 获取任务性能指标
  getTaskMetrics: (taskId: string, timeRange?: string): Promise<ApiResponse<any>> => {
    return api.get(`/monitor/tasks/${taskId}/metrics`, {
      params: { time_range: timeRange }
    });
  },

  // 设置任务告警
  setTaskAlert: (taskId: string, alertConfig: any): Promise<ApiResponse<any>> => {
    return api.post(`/monitor/tasks/${taskId}/alerts`, alertConfig);
  },

  // 获取任务告警配置
  getTaskAlerts: (taskId: string): Promise<ApiResponse<any[]>> => {
    return api.get(`/monitor/tasks/${taskId}/alerts`);
  },

  // 删除任务告警
  deleteTaskAlert: (taskId: string, alertId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/monitor/tasks/${taskId}/alerts/${alertId}`);
  },

  // 导出任务配置
  exportTasks: (taskIds?: string[]): Promise<void> => {
    const params = taskIds ? { task_ids: taskIds.join(',') } : {};
    const filename = `monitor_tasks_${new Date().toISOString().split('T')[0]}.json`;
    return api.get('/monitor/tasks/export', { params }).then(response => {
      const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    });
  },

  // 导入任务配置
  importTasks: (file: File): Promise<ApiResponse<{ success_count: number; error_count: number; errors: any[] }>> => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/monitor/tasks/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
};
