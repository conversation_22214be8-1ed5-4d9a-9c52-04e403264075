/**
 * 监控任务列表页面
 */

import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Tag,
  Space,
  Input,
  Select,
  DatePicker,
  message,
  Modal,
  Spin,
  Tooltip,
  Dropdown
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  MoreOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchMonitorTasksAsync,
  pauseTaskAsync,
  resumeTaskAsync,
  deleteTaskAsync,
  batchOperationAsync,
  selectMonitorTasks,
  selectMonitorLoading,
  selectMonitorPagination
} from '../../store/slices/monitorSlice';
import { MonitorTask, MonitorSearchParams, TaskStatus } from '../../types';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const MonitorListPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const tasks = useAppSelector(selectMonitorTasks);
  const loading = useAppSelector(selectMonitorLoading);
  const pagination = useAppSelector(selectMonitorPagination);

  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [searchParams, setSearchParams] = useState<MonitorSearchParams>({});
  const [batchLoading, setBatchLoading] = useState(false);

  useEffect(() => {
    loadTasks();
  }, []);

  const loadTasks = () => {
    dispatch(fetchMonitorTasksAsync({
      page: pagination.page,
      page_size: pagination.pageSize,
      search: searchParams,
    }));
  };

  const handleSearch = (params: MonitorSearchParams) => {
    setSearchParams(params);
    dispatch(fetchMonitorTasksAsync({
      page: 1,
      page_size: pagination.pageSize,
      search: params,
    }));
  };

  const handleTableChange = (paginationConfig: any) => {
    dispatch(fetchMonitorTasksAsync({
      page: paginationConfig.current,
      page_size: paginationConfig.pageSize,
      search: searchParams,
    }));
  };

  const handleTaskAction = async (taskId: string, action: 'pause' | 'resume' | 'delete') => {
    try {
      switch (action) {
        case 'pause':
          await dispatch(pauseTaskAsync(taskId)).unwrap();
          message.success('任务已暂停');
          break;
        case 'resume':
          await dispatch(resumeTaskAsync(taskId)).unwrap();
          message.success('任务已恢复');
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: '确定要删除这个监控任务吗？此操作不可恢复。',
            onOk: async () => {
              await dispatch(deleteTaskAsync(taskId)).unwrap();
              message.success('任务已删除');
              loadTasks();
            },
          });
          return;
      }
      loadTasks();
    } catch (error: any) {
      message.error(`操作失败：${error.message}`);
    }
  };

  const handleBatchOperation = async (operation: 'pause' | 'resume' | 'delete') => {
    if (selectedTasks.length === 0) {
      message.warning('请选择要操作的任务');
      return;
    }

    if (operation === 'delete') {
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedTasks.length} 个任务吗？此操作不可恢复。`,
        onOk: async () => {
          try {
            setBatchLoading(true);
            await dispatch(batchOperationAsync({ taskIds: selectedTasks, operation })).unwrap();
            message.success('批量删除成功');
            setSelectedTasks([]);
            loadTasks();
          } catch (error: any) {
            message.error(`批量操作失败：${error.message}`);
          } finally {
            setBatchLoading(false);
          }
        },
      });
    } else {
      try {
        setBatchLoading(true);
        await dispatch(batchOperationAsync({ taskIds: selectedTasks, operation })).unwrap();
        const operationText = operation === 'pause' ? '暂停' : '恢复';
        message.success(`批量${operationText}成功`);
        setSelectedTasks([]);
        loadTasks();
      } catch (error: any) {
        message.error(`批量操作失败：${error.message}`);
      } finally {
        setBatchLoading(false);
      }
    }
  };

  const columns: ColumnsType<MonitorTask> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: MonitorTask) => (
        <Button
          type="link"
          onClick={() => navigate(`/monitor/${record.task_id}`)}
          style={{ padding: 0 }}
        >
          {name}
        </Button>
      ),
    },
    {
      title: '商品',
      dataIndex: 'product_title',
      key: 'product_title',
      ellipsis: true,
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform: string) => (
        <Tag>{platform || '未知'}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors: Record<string, string> = {
          active: 'green',
          paused: 'orange',
          failed: 'red',
          pending: 'blue',
        };
        const labels: Record<string, string> = {
          active: '运行中',
          paused: '已暂停',
          failed: '失败',
          pending: '等待中',
        };
        return <Tag color={colors[status] || 'default'}>{labels[status] || status}</Tag>;
      },
    },
    {
      title: '监控频率',
      dataIndex: 'interval_minutes',
      key: 'interval_minutes',
      render: (interval: number) => `${interval} 分钟`,
    },
    {
      title: '最后运行',
      dataIndex: 'last_run_at',
      key: 'last_run_at',
      render: (time: string) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '下次运行',
      dataIndex: 'next_run_at',
      key: 'next_run_at',
      render: (time: string) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/monitor/${record.task_id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/monitor/${record.task_id}/edit`)}
            />
          </Tooltip>
          {record.status === TaskStatus.ACTIVE ? (
            <Tooltip title="暂停">
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleTaskAction(record.task_id, 'pause')}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动">
              <Button
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleTaskAction(record.task_id, 'resume')}
              />
            </Tooltip>
          )}
          <Dropdown
            menu={{
              items: [
                {
                  key: 'delete',
                  label: '删除',
                  danger: true,
                  onClick: () => handleTaskAction(record.task_id, 'delete'),
                },
              ],
            }}
          >
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面头部 */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>监控管理</h2>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadTasks}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/monitor/new')}
          >
            新增监控任务
          </Button>
        </Space>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-4">
        <div className="row g-3">
          <div className="col-md-4">
            <Search
              placeholder="搜索任务名称或商品"
              onSearch={(value) => handleSearch({ ...searchParams, search: value })}
              allowClear
            />
          </div>
          <div className="col-md-3">
            <Select
              placeholder="选择状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleSearch({ ...searchParams, status: value })}
            >
              <Option value="active">运行中</Option>
              <Option value="paused">已暂停</Option>
              <Option value="failed">失败</Option>
              <Option value="pending">等待中</Option>
            </Select>
          </div>
          <div className="col-md-3">
            <Select
              placeholder="选择平台"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleSearch({ ...searchParams, platform: value })}
            >
              <Option value="taobao">淘宝</Option>
              <Option value="tmall">天猫</Option>
              <Option value="jd">京东</Option>
              <Option value="pdd">拼多多</Option>
            </Select>
          </div>
          <div className="col-md-2">
            <Button
              block
              onClick={() => {
                setSearchParams({});
                handleSearch({});
              }}
            >
              重置
            </Button>
          </div>
        </div>
      </Card>

      {/* 批量操作 */}
      {selectedTasks.length > 0 && (
        <Card className="mb-4">
          <Space>
            <span>已选择 {selectedTasks.length} 个任务</span>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={() => handleBatchOperation('resume')}
              loading={batchLoading}
            >
              批量启动
            </Button>
            <Button
              icon={<PauseCircleOutlined />}
              onClick={() => handleBatchOperation('pause')}
              loading={batchLoading}
            >
              批量暂停
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleBatchOperation('delete')}
              loading={batchLoading}
            >
              批量删除
            </Button>
          </Space>
        </Card>
      )}

      {/* 任务列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="task_id"
          loading={loading}
          rowSelection={{
            selectedRowKeys: selectedTasks,
            onChange: (selectedRowKeys) => setSelectedTasks(selectedRowKeys as string[]),
          }}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>
    </div>
  );
};

export default MonitorListPage;
