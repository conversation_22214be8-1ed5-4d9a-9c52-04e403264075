#!/bin/bash
# 开发环境快速启动脚本（支持热重载）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs uploads config/local
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，从模板创建..."
        cp .env.example .env
        
        # 设置开发环境默认值
        sed -i 's/DATABASE_URL=.*/DATABASE_URL=postgresql:\/\/postgres:dev_password@localhost:5433\/ecommerce_monitor_dev/' .env
        sed -i 's/REDIS_URL=.*/REDIS_URL=redis:\/\/localhost:6380\/0/' .env
        sed -i 's/DEBUG=.*/DEBUG=true/' .env
        sed -i 's/ENVIRONMENT=.*/ENVIRONMENT=development/' .env
        
        log_success "开发环境配置创建完成"
    fi
    
    log_success "配置文件检查通过"
}

# 构建开发镜像
build_dev_images() {
    log_info "构建开发环境镜像..."
    
    docker-compose -f docker-compose.dev.yml build --no-cache
    
    log_success "开发镜像构建完成"
}

# 启动开发环境
start_dev_services() {
    log_info "启动开发环境服务..."
    
    # 先启动数据库和Redis
    docker-compose -f docker-compose.dev.yml up -d db redis
    
    log_info "等待数据库启动..."
    sleep 15
    
    # 检查数据库连接
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres -d ecommerce_monitor_dev; then
            log_success "数据库连接成功"
            break
        fi
        
        log_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库启动超时"
        exit 1
    fi
    
    # 启动所有服务
    docker-compose -f docker-compose.dev.yml up -d
    
    log_success "开发环境服务启动完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待应用启动
    sleep 10
    
    # 运行迁移
    docker-compose -f docker-compose.dev.yml exec app alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 开发环境启动完成！"
    echo ""
    echo "=== 服务访问地址 ==="
    echo "🌐 API服务: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/docs"
    echo "📖 ReDoc文档: http://localhost:8000/redoc"
    echo "💾 数据库: localhost:5433"
    echo "🔴 Redis: localhost:6380"
    echo "🌸 Flower监控: http://localhost:5556"
    echo ""
    echo "=== 开发特性 ==="
    echo "🔥 热重载: 修改代码自动重启"
    echo "📁 代码挂载: ./app 目录已挂载"
    echo "📝 配置挂载: ./config 目录已挂载"
    echo "📊 日志挂载: ./logs 目录已挂载"
    echo ""
    echo "=== 常用命令 ==="
    echo "查看日志: docker-compose -f docker-compose.dev.yml logs -f app"
    echo "进入容器: docker-compose -f docker-compose.dev.yml exec app bash"
    echo "停止服务: docker-compose -f docker-compose.dev.yml down"
    echo "重启服务: docker-compose -f docker-compose.dev.yml restart app"
    echo ""
    echo "=== 数据库操作 ==="
    echo "连接数据库: docker-compose -f docker-compose.dev.yml exec db psql -U postgres -d ecommerce_monitor_dev"
    echo "运行迁移: docker-compose -f docker-compose.dev.yml exec app alembic upgrade head"
    echo "创建迁移: docker-compose -f docker-compose.dev.yml exec app alembic revision --autogenerate -m '描述'"
    echo ""
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    echo ""
    echo "=== 服务状态 ==="
    docker-compose -f docker-compose.dev.yml ps
    
    echo ""
    echo "=== 健康检查 ==="
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，可能还在启动中..."
    fi
}

# 主函数
main() {
    echo "电商监控系统开发环境启动脚本 v1.0"
    echo "======================================="
    
    check_dependencies
    create_directories
    check_config
    build_dev_images
    start_dev_services
    run_migrations
    check_services
    show_access_info
    
    log_success "开发环境启动完成！"
}

# 错误处理
trap 'log_error "启动过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
