"""
认证相关数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Set
import uuid


class UserRole(Enum):
    """用户角色"""
    ADMIN = "admin"           # 管理员
    MANAGER = "manager"       # 管理者
    OPERATOR = "operator"     # 操作员
    VIEWER = "viewer"         # 查看者
    GUEST = "guest"          # 访客


class Permission(Enum):
    """权限类型"""
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    
    # 商品管理权限
    PRODUCT_CREATE = "product:create"
    PRODUCT_READ = "product:read"
    PRODUCT_UPDATE = "product:update"
    PRODUCT_DELETE = "product:delete"
    PRODUCT_IMPORT = "product:import"
    PRODUCT_EXPORT = "product:export"
    
    # 翻译服务权限
    TRANSLATION_USE = "translation:use"
    TRANSLATION_BATCH = "translation:batch"
    TRANSLATION_CONFIG = "translation:config"
    TRANSLATION_MONITOR = "translation:monitor"
    
    # 数据分析权限
    ANALYTICS_VIEW = "analytics:view"
    ANALYTICS_EXPORT = "analytics:export"
    
    # 日志审计权限
    AUDIT_VIEW = "audit:view"
    AUDIT_EXPORT = "audit:export"


@dataclass
class User:
    """用户模型"""
    user_id: str
    username: str
    email: str
    password_hash: str
    salt: str
    role: UserRole
    permissions: Set[Permission] = field(default_factory=set)
    
    # 用户信息
    full_name: str = ""
    phone: str = ""
    department: str = ""
    
    # 状态信息
    is_active: bool = True
    is_verified: bool = False
    is_locked: bool = False
    failed_login_attempts: int = 0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_login_at: Optional[datetime] = None
    password_changed_at: datetime = field(default_factory=datetime.now)
    
    # 安全设置
    require_password_change: bool = False
    two_factor_enabled: bool = False
    two_factor_secret: str = ""
    
    # 会话限制
    max_concurrent_sessions: int = 3
    session_timeout_minutes: int = 480  # 8小时
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.user_id:
            self.user_id = str(uuid.uuid4())
        
        # 根据角色设置默认权限
        self._set_default_permissions()
    
    def _set_default_permissions(self):
        """根据角色设置默认权限"""
        role_permissions = {
            UserRole.ADMIN: {
                Permission.SYSTEM_ADMIN,
                Permission.SYSTEM_CONFIG,
                Permission.SYSTEM_MONITOR,
                Permission.USER_CREATE,
                Permission.USER_READ,
                Permission.USER_UPDATE,
                Permission.USER_DELETE,
                Permission.PRODUCT_CREATE,
                Permission.PRODUCT_READ,
                Permission.PRODUCT_UPDATE,
                Permission.PRODUCT_DELETE,
                Permission.PRODUCT_IMPORT,
                Permission.PRODUCT_EXPORT,
                Permission.TRANSLATION_USE,
                Permission.TRANSLATION_BATCH,
                Permission.TRANSLATION_CONFIG,
                Permission.TRANSLATION_MONITOR,
                Permission.ANALYTICS_VIEW,
                Permission.ANALYTICS_EXPORT,
                Permission.AUDIT_VIEW,
                Permission.AUDIT_EXPORT
            },
            UserRole.MANAGER: {
                Permission.SYSTEM_MONITOR,
                Permission.USER_READ,
                Permission.USER_UPDATE,
                Permission.PRODUCT_CREATE,
                Permission.PRODUCT_READ,
                Permission.PRODUCT_UPDATE,
                Permission.PRODUCT_DELETE,
                Permission.PRODUCT_IMPORT,
                Permission.PRODUCT_EXPORT,
                Permission.TRANSLATION_USE,
                Permission.TRANSLATION_BATCH,
                Permission.TRANSLATION_CONFIG,
                Permission.ANALYTICS_VIEW,
                Permission.ANALYTICS_EXPORT,
                Permission.AUDIT_VIEW
            },
            UserRole.OPERATOR: {
                Permission.PRODUCT_CREATE,
                Permission.PRODUCT_READ,
                Permission.PRODUCT_UPDATE,
                Permission.PRODUCT_IMPORT,
                Permission.PRODUCT_EXPORT,
                Permission.TRANSLATION_USE,
                Permission.TRANSLATION_BATCH,
                Permission.ANALYTICS_VIEW
            },
            UserRole.VIEWER: {
                Permission.PRODUCT_READ,
                Permission.TRANSLATION_USE,
                Permission.ANALYTICS_VIEW
            },
            UserRole.GUEST: {
                Permission.PRODUCT_READ
            }
        }
        
        if not self.permissions:
            self.permissions = role_permissions.get(self.role, set())
    
    def has_permission(self, permission: Permission) -> bool:
        """检查是否有指定权限"""
        return permission in self.permissions
    
    def has_any_permission(self, permissions: List[Permission]) -> bool:
        """检查是否有任意一个权限"""
        return any(perm in self.permissions for perm in permissions)
    
    def has_all_permissions(self, permissions: List[Permission]) -> bool:
        """检查是否有所有权限"""
        return all(perm in self.permissions for perm in permissions)
    
    def add_permission(self, permission: Permission):
        """添加权限"""
        self.permissions.add(permission)
        self.updated_at = datetime.now()
    
    def remove_permission(self, permission: Permission):
        """移除权限"""
        self.permissions.discard(permission)
        self.updated_at = datetime.now()
    
    def lock_account(self):
        """锁定账户"""
        self.is_locked = True
        self.updated_at = datetime.now()
    
    def unlock_account(self):
        """解锁账户"""
        self.is_locked = False
        self.failed_login_attempts = 0
        self.updated_at = datetime.now()
    
    def increment_failed_login(self):
        """增加失败登录次数"""
        self.failed_login_attempts += 1
        self.updated_at = datetime.now()
        
        # 超过5次失败登录自动锁定
        if self.failed_login_attempts >= 5:
            self.lock_account()
    
    def reset_failed_login(self):
        """重置失败登录次数"""
        self.failed_login_attempts = 0
        self.updated_at = datetime.now()
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login_at = datetime.now()
        self.reset_failed_login()
    
    def is_password_expired(self, max_age_days: int = 90) -> bool:
        """检查密码是否过期"""
        if not self.password_changed_at:
            return True
        
        expiry_date = self.password_changed_at + timedelta(days=max_age_days)
        return datetime.now() > expiry_date
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "permissions": [p.value for p in self.permissions],
            "full_name": self.full_name,
            "phone": self.phone,
            "department": self.department,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "is_locked": self.is_locked,
            "failed_login_attempts": self.failed_login_attempts,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "password_changed_at": self.password_changed_at.isoformat(),
            "require_password_change": self.require_password_change,
            "two_factor_enabled": self.two_factor_enabled,
            "max_concurrent_sessions": self.max_concurrent_sessions,
            "session_timeout_minutes": self.session_timeout_minutes
        }


@dataclass
class Session:
    """会话模型"""
    session_id: str
    user_id: str
    access_token: str
    refresh_token: str
    
    # 会话信息
    ip_address: str = ""
    user_agent: str = ""
    device_info: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed_at: datetime = field(default_factory=datetime.now)
    expires_at: datetime = field(default_factory=lambda: datetime.now() + timedelta(hours=8))
    
    # 状态信息
    is_active: bool = True
    is_revoked: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.now() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查会话是否有效"""
        return self.is_active and not self.is_revoked and not self.is_expired()
    
    def extend_session(self, hours: int = 8):
        """延长会话时间"""
        self.expires_at = datetime.now() + timedelta(hours=hours)
        self.last_accessed_at = datetime.now()
    
    def revoke(self):
        """撤销会话"""
        self.is_revoked = True
        self.is_active = False
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "device_info": self.device_info,
            "created_at": self.created_at.isoformat(),
            "last_accessed_at": self.last_accessed_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "is_active": self.is_active,
            "is_revoked": self.is_revoked
        }
