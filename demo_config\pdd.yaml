base_config:
  base_url: https://www.pinduoduo.com
  cookies: null
  headers: null
  javascript_required: true
  name: 拼多多商城 - 定制版
  proxy_required: true
  rate_limit:
    concurrent_requests: 1
    delay_between_requests: 4.0
    requests_per_hour: null
    requests_per_minute: 20
  selectors:
    description: null
    images: null
    min_order: null
    price: .goods-price
    rating: null
    sales_count: .goods-sales
    seller_info: null
    stock: null
    title: .goods-title
enabled: true
platform: !!python/object/apply:app.services.task_middleware.config_manager.Platform
- pdd
product_types:
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - competitor
  : additional_selectors: null
    custom_query_template: null
    monitoring_frequency: 3600
    priority_boost: 1
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - other
  : additional_selectors: null
    custom_query_template: null
    monitoring_frequency: 7200
    priority_boost: 0
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - supplier
  : additional_selectors:
      bulk_price: .bulk-price
    custom_query_template: 提取拼多多供货商信息
    monitoring_frequency: 5400
    priority_boost: 0
