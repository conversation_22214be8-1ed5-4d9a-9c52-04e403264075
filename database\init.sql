-- 电商商品监控系统数据库初始化脚本

-- 创建TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建数据库（如果不存在）
-- 注意：这个脚本在Docker容器中运行时，数据库已经存在

-- 设置时区
SET timezone = 'UTC';

-- 创建自定义类型
DO $$ BEGIN
    CREATE TYPE product_status AS ENUM ('active', 'inactive', 'deleted');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE task_status AS ENUM ('pending', 'running', 'completed', 'failed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE alert_severity AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建函数：更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建函数：计算总成本
CREATE OR REPLACE FUNCTION calculate_total_cost()
RETURNS TRIGGER AS $$
BEGIN
    NEW.total_cost = NEW.unit_cost + COALESCE(NEW.shipping_cost, 0) + COALESCE(NEW.other_costs, 0);
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建函数：生成商品历史数据的分区
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I 
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- 创建函数：自动创建分区
CREATE OR REPLACE FUNCTION auto_create_partitions()
RETURNS void AS $$
DECLARE
    current_month date;
    i integer;
BEGIN
    current_month := date_trunc('month', CURRENT_DATE);
    
    -- 创建当前月份及未来11个月的分区
    FOR i IN 0..11 LOOP
        PERFORM create_monthly_partition('product_history', current_month + (i || ' months')::interval);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_old_data(retention_days integer DEFAULT 730)
RETURNS integer AS $$
DECLARE
    deleted_count integer;
    cutoff_date timestamp;
BEGIN
    cutoff_date := CURRENT_TIMESTAMP - (retention_days || ' days')::interval;
    
    -- 删除过期的商品历史数据
    DELETE FROM product_history WHERE time < cutoff_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 删除过期的监控任务
    DELETE FROM monitoring_tasks 
    WHERE status IN ('completed', 'failed') 
    AND completed_at < cutoff_date;
    
    -- 删除过期的已解决预警
    DELETE FROM alerts 
    WHERE is_resolved = true 
    AND resolved_at < cutoff_date;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建统计函数
CREATE OR REPLACE FUNCTION get_product_stats(p_product_id uuid, days integer DEFAULT 30)
RETURNS TABLE(
    avg_price numeric,
    max_price numeric,
    min_price numeric,
    avg_sales integer,
    total_sales bigint,
    avg_stock integer,
    min_stock integer,
    avg_rating numeric,
    data_points bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        AVG(ph.price)::numeric as avg_price,
        MAX(ph.price)::numeric as max_price,
        MIN(ph.price)::numeric as min_price,
        AVG(ph.sales_count)::integer as avg_sales,
        SUM(ph.sales_count)::bigint as total_sales,
        AVG(ph.stock_quantity)::integer as avg_stock,
        MIN(ph.stock_quantity)::integer as min_stock,
        AVG(ph.rating)::numeric as avg_rating,
        COUNT(*)::bigint as data_points
    FROM product_history ph
    WHERE ph.product_id = p_product_id
    AND ph.time >= CURRENT_TIMESTAMP - (days || ' days')::interval;
END;
$$ LANGUAGE plpgsql;

-- 创建价格变化检测函数
CREATE OR REPLACE FUNCTION detect_price_change()
RETURNS TRIGGER AS $$
DECLARE
    prev_price numeric;
    price_change_threshold numeric := 0.05; -- 5%的价格变化阈值
BEGIN
    -- 获取上一次的价格
    SELECT price INTO prev_price
    FROM product_history
    WHERE product_id = NEW.product_id
    AND time < NEW.time
    AND price IS NOT NULL
    ORDER BY time DESC
    LIMIT 1;
    
    -- 如果价格变化超过阈值，设置变化类型和数值
    IF prev_price IS NOT NULL AND NEW.price IS NOT NULL THEN
        IF ABS(NEW.price - prev_price) / prev_price > price_change_threshold THEN
            NEW.change_type := 'price_change';
            NEW.change_value := NEW.price - prev_price;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建库存预警函数
CREATE OR REPLACE FUNCTION check_stock_alert()
RETURNS TRIGGER AS $$
DECLARE
    low_stock_threshold integer := 10;
BEGIN
    -- 检查库存是否过低
    IF NEW.stock_quantity IS NOT NULL AND NEW.stock_quantity <= low_stock_threshold THEN
        INSERT INTO alerts (product_id, alert_type, severity, title, message, data)
        VALUES (
            NEW.product_id,
            'low_stock',
            'medium',
            '库存不足预警',
            format('商品库存仅剩 %s 件，建议及时补货', NEW.stock_quantity),
            json_build_object('stock_quantity', NEW.stock_quantity, 'threshold', low_stock_threshold)
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建数据质量评分函数
CREATE OR REPLACE FUNCTION calculate_data_quality_score()
RETURNS TRIGGER AS $$
DECLARE
    score numeric := 1.0;
BEGIN
    -- 根据数据完整性计算质量评分
    IF NEW.price IS NULL THEN score := score - 0.3; END IF;
    IF NEW.sales_count IS NULL THEN score := score - 0.2; END IF;
    IF NEW.stock_quantity IS NULL THEN score := score - 0.2; END IF;
    IF NEW.rating IS NULL THEN score := score - 0.1; END IF;
    IF NEW.title IS NULL THEN score := score - 0.2; END IF;
    
    NEW.data_quality_score := GREATEST(score, 0.0);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建索引优化函数
CREATE OR REPLACE FUNCTION optimize_indexes()
RETURNS void AS $$
BEGIN
    -- 重建索引以优化性能
    REINDEX INDEX CONCURRENTLY idx_product_history_product_time;
    REINDEX INDEX CONCURRENTLY idx_product_history_price;
    REINDEX INDEX CONCURRENTLY idx_product_history_sales;
    
    -- 更新表统计信息
    ANALYZE product_history;
    ANALYZE products;
    ANALYZE suppliers;
    ANALYZE product_costs;
END;
$$ LANGUAGE plpgsql;

-- 创建备份函数
CREATE OR REPLACE FUNCTION create_backup_tables()
RETURNS void AS $$
BEGIN
    -- 创建产品备份表
    DROP TABLE IF EXISTS products_backup;
    CREATE TABLE products_backup AS SELECT * FROM products;
    
    -- 创建供货商备份表
    DROP TABLE IF EXISTS suppliers_backup;
    CREATE TABLE suppliers_backup AS SELECT * FROM suppliers;
    
    -- 创建成本备份表
    DROP TABLE IF EXISTS product_costs_backup;
    CREATE TABLE product_costs_backup AS SELECT * FROM product_costs;
    
    RAISE NOTICE '备份表创建完成';
END;
$$ LANGUAGE plpgsql;
