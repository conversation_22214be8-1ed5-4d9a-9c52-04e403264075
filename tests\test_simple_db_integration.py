"""
简化的数据库集成测试
使用已生成的TimescaleDB测试数据进行真实的数据库集成测试
"""

import pytest
import asyncio
import asyncpg
import os
from datetime import datetime, timedelta
from decimal import Decimal


class TestSimpleDatabaseIntegration:
    """简化的数据库集成测试"""
    
    @pytest.mark.asyncio
    async def test_database_connection(self):
        """测试数据库连接"""
        db_url = "postgresql://moniit:moniit123@localhost:5432/moniit"

        try:
            conn = await asyncpg.connect(db_url)

            # 测试基本查询
            result = await conn.fetchval("SELECT 1")
            assert result == 1

            # 测试版本查询
            version = await conn.fetchval("SELECT version()")
            assert "PostgreSQL" in version

            await conn.close()
            print("✅ 数据库连接测试通过")

        except Exception as e:
            pytest.fail(f"数据库连接失败: {e}")
    
    @pytest.mark.asyncio
    async def test_test_data_exists(self):
        """测试测试数据是否存在"""
        db_url = "postgresql://moniit:moniit123@localhost:5432/moniit"

        try:
            conn = await asyncpg.connect(db_url)

            # 检查测试表是否存在
            tables_exist = await conn.fetchval("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_name IN ('test_products', 'test_price_history')
            """)

            assert tables_exist >= 2, "测试表应该存在"

            # 检查是否有测试数据
            product_count = await conn.fetchval("SELECT COUNT(*) FROM test_products")
            price_count = await conn.fetchval("SELECT COUNT(*) FROM test_price_history")

            assert product_count > 0, "应该有测试商品数据"
            assert price_count > 0, "应该有测试价格数据"

            print(f"✅ 测试数据检查通过: {product_count} 个商品, {price_count} 条价格记录")

            await conn.close()

        except Exception as e:
            pytest.fail(f"数据检查失败: {e}")
    
    @pytest.mark.asyncio
    async def test_price_trend_analysis_with_real_data(self):
        """使用真实数据库数据测试价格趋势分析"""
        db_url = "postgresql://moniit:moniit123@localhost:5432/moniit"

        try:
            conn = await asyncpg.connect(db_url)

            # 获取测试商品
            products = await conn.fetch("SELECT id, name FROM test_products LIMIT 3")

            assert len(products) > 0, "应该有测试商品数据"

            for product in products:
                product_id = product['id']
                product_name = product['name']

                # 获取价格历史数据
                price_records = await conn.fetch("""
                    SELECT price, recorded_at
                    FROM test_price_history
                    WHERE product_id = $1
                    ORDER BY recorded_at DESC
                    LIMIT 50
                """, product_id)

                assert len(price_records) > 0, f"商品 {product_name} 应该有价格数据"

                # 提取价格数据进行趋势分析
                prices = [float(record['price']) for record in price_records]
                timestamps = [record['recorded_at'] for record in price_records]

                # 计算简单的线性趋势
                trend_result = self._calculate_linear_trend(prices)

                assert "slope" in trend_result
                assert "r_squared" in trend_result
                assert "trend_direction" in trend_result
                assert 0 <= trend_result["r_squared"] <= 1

                print(f"✅ {product_name} 趋势分析:")
                print(f"   斜率: {trend_result['slope']:.4f}")
                print(f"   R²: {trend_result['r_squared']:.4f}")
                print(f"   趋势: {trend_result['trend_direction']}")

            await conn.close()

        except Exception as e:
            pytest.fail(f"价格趋势分析测试失败: {e}")
    
    @pytest.mark.asyncio
    async def test_price_statistics_analysis(self):
        """测试价格统计分析"""
        db_url = os.getenv("DATABASE_URL", "postgresql://moniit:moniit123@localhost:5432/moniit")
        
        try:
            conn = await asyncpg.connect(db_url)
            
            # 获取价格统计数据
            stats = await conn.fetch("""
                SELECT 
                    p.name,
                    COUNT(ph.id) as record_count,
                    MIN(ph.price) as min_price,
                    MAX(ph.price) as max_price,
                    AVG(ph.price) as avg_price,
                    STDDEV(ph.price) as price_stddev
                FROM test_products p
                JOIN test_price_history ph ON p.id = ph.product_id
                GROUP BY p.id, p.name
                ORDER BY avg_price DESC
            """)
            
            assert len(stats) > 0, "应该有价格统计数据"
            
            for stat in stats:
                name = stat['name']
                count = stat['record_count']
                min_price = float(stat['min_price'])
                max_price = float(stat['max_price'])
                avg_price = float(stat['avg_price'])
                stddev = float(stat['price_stddev']) if stat['price_stddev'] else 0
                
                # 验证统计数据的合理性
                assert count > 0, f"{name} 应该有价格记录"
                assert min_price > 0, f"{name} 最低价格应该大于0"
                assert max_price >= min_price, f"{name} 最高价格应该不小于最低价格"
                assert avg_price >= min_price and avg_price <= max_price, f"{name} 平均价格应该在合理范围内"
                
                # 计算变异系数（波动率指标）
                cv = stddev / avg_price if avg_price > 0 else 0
                
                print(f"✅ {name} 价格统计:")
                print(f"   记录数: {count}")
                print(f"   价格范围: ¥{min_price:.2f} - ¥{max_price:.2f}")
                print(f"   平均价格: ¥{avg_price:.2f}")
                print(f"   波动率: {cv:.2%}")
            
            await conn.close()
            
        except Exception as e:
            pytest.skip(f"价格统计分析测试失败: {e}")
    
    @pytest.mark.asyncio
    async def test_time_series_query_performance(self):
        """测试时序查询性能"""
        db_url = os.getenv("DATABASE_URL", "postgresql://moniit:moniit123@localhost:5432/moniit")
        
        try:
            conn = await asyncpg.connect(db_url)
            
            # 获取一个测试商品
            product = await conn.fetchrow("SELECT id FROM test_products LIMIT 1")
            if not product:
                await conn.close()
                pytest.skip("没有找到测试商品")
            
            product_id = product['id']
            
            # 测试不同时间范围的查询性能
            time_ranges = [
                ("最近7天", 7),
                ("最近30天", 30),
                ("全部数据", None)
            ]
            
            for range_name, days in time_ranges:
                start_time = datetime.now()
                
                if days:
                    # 有时间限制的查询
                    records = await conn.fetch("""
                        SELECT price, recorded_at 
                        FROM test_price_history 
                        WHERE product_id = $1 
                          AND recorded_at >= NOW() - INTERVAL '%s days'
                        ORDER BY recorded_at DESC
                    """ % days, product_id)
                else:
                    # 全部数据查询
                    records = await conn.fetch("""
                        SELECT price, recorded_at 
                        FROM test_price_history 
                        WHERE product_id = $1 
                        ORDER BY recorded_at DESC
                    """, product_id)
                
                end_time = datetime.now()
                query_time = (end_time - start_time).total_seconds() * 1000  # 毫秒
                
                assert len(records) >= 0, f"{range_name} 查询应该返回结果"
                
                print(f"✅ {range_name} 查询性能:")
                print(f"   记录数: {len(records)}")
                print(f"   查询时间: {query_time:.2f}ms")
                
                # 性能断言（根据实际情况调整）
                if len(records) < 1000:
                    assert query_time < 1000, f"{range_name} 查询时间应该小于1秒"
            
            await conn.close()
            
        except Exception as e:
            pytest.skip(f"时序查询性能测试失败: {e}")
    
    @pytest.mark.asyncio
    async def test_price_anomaly_detection_with_real_data(self):
        """使用真实数据测试价格异常检测"""
        db_url = os.getenv("DATABASE_URL", "postgresql://moniit:moniit123@localhost:5432/moniit")
        
        try:
            conn = await asyncpg.connect(db_url)
            
            # 获取一个有足够数据的商品
            product = await conn.fetchrow("""
                SELECT p.id, p.name, COUNT(ph.id) as record_count
                FROM test_products p
                JOIN test_price_history ph ON p.id = ph.product_id
                GROUP BY p.id, p.name
                HAVING COUNT(ph.id) >= 20
                LIMIT 1
            """)
            
            if not product:
                await conn.close()
                pytest.skip("没有找到足够数据的测试商品")
            
            product_id = product['id']
            product_name = product['name']
            
            # 获取价格数据
            price_records = await conn.fetch("""
                SELECT price FROM test_price_history 
                WHERE product_id = $1 
                ORDER BY recorded_at
            """, product_id)
            
            prices = [float(record['price']) for record in price_records]
            
            # 执行异常检测
            anomalies = self._detect_price_anomalies(prices)
            
            assert "anomaly_count" in anomalies
            assert "anomaly_indices" in anomalies
            assert "threshold_used" in anomalies
            assert anomalies["anomaly_count"] >= 0
            
            print(f"✅ {product_name} 异常检测:")
            print(f"   总记录数: {len(prices)}")
            print(f"   异常数量: {anomalies['anomaly_count']}")
            print(f"   异常比例: {anomalies['anomaly_count']/len(prices):.2%}")
            print(f"   检测阈值: ¥{anomalies['threshold_used']:.2f}")
            
            await conn.close()
            
        except Exception as e:
            pytest.skip(f"价格异常检测测试失败: {e}")
    
    def _calculate_linear_trend(self, prices):
        """计算线性趋势"""
        n = len(prices)
        if n < 2:
            return {"slope": 0, "r_squared": 0, "trend_direction": "stable"}
        
        x_values = list(range(n))
        sum_x = sum(x_values)
        sum_y = sum(prices)
        sum_xy = sum(x_values[i] * prices[i] for i in range(n))
        sum_x2 = sum(x * x for x in x_values)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # 计算R²
        y_mean = sum_y / n
        ss_tot = sum((prices[i] - y_mean) ** 2 for i in range(n))
        ss_res = sum((prices[i] - (slope * i + intercept)) ** 2 for i in range(n))
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        trend_direction = "increasing" if slope > 0.1 else "decreasing" if slope < -0.1 else "stable"
        
        return {
            "slope": slope,
            "intercept": intercept,
            "r_squared": max(0, min(1, r_squared)),
            "trend_direction": trend_direction
        }
    
    def _detect_price_anomalies(self, prices):
        """检测价格异常"""
        if len(prices) < 10:
            return {"anomaly_count": 0, "anomaly_indices": [], "threshold_used": 0}
        
        mean_price = sum(prices) / len(prices)
        std_price = (sum((p - mean_price)**2 for p in prices) / len(prices))**0.5
        threshold = 2 * std_price  # 2倍标准差作为阈值
        
        anomaly_indices = []
        for i, price in enumerate(prices):
            if abs(price - mean_price) > threshold:
                anomaly_indices.append(i)
        
        return {
            "anomaly_count": len(anomaly_indices),
            "anomaly_indices": anomaly_indices,
            "threshold_used": threshold
        }
