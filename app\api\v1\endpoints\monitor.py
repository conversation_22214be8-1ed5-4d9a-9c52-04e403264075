"""
监控管理API端点

解决前后端路径不匹配问题，创建完整的监控管理API
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from enum import Enum

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.models.database import Product as DBProduct
from app.models.schemas import BaseSchema
from pydantic import BaseModel, Field

logger = get_logger(__name__)
router = APIRouter()


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class MonitorTaskCreate(BaseModel):
    """创建监控任务请求"""
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    product_ids: List[str] = Field(..., description="商品ID列表")
    schedule_type: str = Field("interval", description="调度类型")
    schedule_config: Dict[str, Any] = Field(..., description="调度配置")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="任务优先级")
    is_active: bool = Field(True, description="是否激活")


class MonitorTaskUpdate(BaseModel):
    """更新监控任务请求"""
    name: Optional[str] = Field(None, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    product_ids: Optional[List[str]] = Field(None, description="商品ID列表")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="调度配置")
    priority: Optional[TaskPriority] = Field(None, description="任务优先级")
    is_active: Optional[bool] = Field(None, description="是否激活")


class MonitorTask(BaseModel):
    """监控任务响应"""
    id: str
    name: str
    description: Optional[str]
    product_count: int
    status: TaskStatus
    priority: TaskPriority
    schedule_type: str
    schedule_config: Dict[str, Any]
    is_active: bool
    last_run_at: Optional[datetime]
    next_run_at: Optional[datetime]
    success_count: int
    error_count: int
    created_at: datetime
    updated_at: datetime


# 模拟的任务存储（实际应用中应该使用数据库）
MOCK_TASKS: Dict[str, Dict[str, Any]] = {}


@router.get("/tasks", summary="获取监控任务列表")
async def get_monitor_tasks(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[TaskStatus] = Query(None, description="任务状态筛选"),
    priority: Optional[TaskPriority] = Query(None, description="优先级筛选"),
    is_active: Optional[bool] = Query(None, description="是否激活筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取监控任务列表"""
    logger.info(f"获取监控任务列表 - skip: {skip}, limit: {limit}")
    
    try:
        # 筛选任务
        filtered_tasks = []
        for task_id, task_data in MOCK_TASKS.items():
            # 状态筛选
            if status and task_data["status"] != status:
                continue
            
            # 优先级筛选
            if priority and task_data["priority"] != priority:
                continue
            
            # 激活状态筛选
            if is_active is not None and task_data["is_active"] != is_active:
                continue
            
            # 搜索筛选
            if search and search.lower() not in task_data["name"].lower():
                continue
            
            filtered_tasks.append({
                "id": task_id,
                **task_data
            })
        
        # 分页
        total = len(filtered_tasks)
        tasks = filtered_tasks[skip:skip + limit]
        
        logger.info(f"成功获取监控任务列表 - 总数: {total}, 返回: {len(tasks)}")
        return {
            "items": tasks,
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"获取监控任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取监控任务列表失败: {str(e)}")


@router.post("/tasks", summary="创建监控任务")
async def create_monitor_task(
    task_data: MonitorTaskCreate,
    db: AsyncSession = Depends(get_db_session)
):
    """创建监控任务"""
    logger.info(f"创建监控任务 - 名称: {task_data.name}")
    
    try:
        # 验证商品ID是否存在
        valid_product_ids = []
        for product_id in task_data.product_ids:
            try:
                product_uuid = UUID(product_id)
                query = select(DBProduct).where(DBProduct.id == product_uuid)
                result = await db.execute(query)
                product = result.scalar_one_or_none()
                if product:
                    valid_product_ids.append(product_id)
            except ValueError:
                continue
        
        if not valid_product_ids:
            raise HTTPException(status_code=400, detail="没有找到有效的商品ID")
        
        # 创建任务
        task_id = str(uuid4())
        now = datetime.now()
        
        # 计算下次运行时间
        next_run_at = now + timedelta(hours=1)  # 默认1小时后运行
        if task_data.schedule_config.get("interval_hours"):
            next_run_at = now + timedelta(hours=task_data.schedule_config["interval_hours"])
        
        new_task = {
            "name": task_data.name,
            "description": task_data.description,
            "product_ids": valid_product_ids,
            "product_count": len(valid_product_ids),
            "status": TaskStatus.PENDING,
            "priority": task_data.priority,
            "schedule_type": task_data.schedule_type,
            "schedule_config": task_data.schedule_config,
            "is_active": task_data.is_active,
            "last_run_at": None,
            "next_run_at": next_run_at,
            "success_count": 0,
            "error_count": 0,
            "created_at": now,
            "updated_at": now
        }
        
        MOCK_TASKS[task_id] = new_task
        
        logger.info(f"监控任务创建成功 - ID: {task_id}")
        return {
            "id": task_id,
            **new_task
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建监控任务失败: {str(e)}")


@router.get("/tasks/{task_id}", summary="获取监控任务详情")
async def get_monitor_task(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取监控任务详情"""
    logger.info(f"获取监控任务详情 - ID: {task_id}")
    
    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")
        
        task_data = MOCK_TASKS[task_id]
        
        # 获取商品详情
        product_details = []
        for product_id in task_data["product_ids"]:
            try:
                product_uuid = UUID(product_id)
                query = select(DBProduct).where(DBProduct.id == product_uuid)
                result = await db.execute(query)
                product = result.scalar_one_or_none()
                if product:
                    product_details.append({
                        "id": str(product.id),
                        "title": product.title,
                        "url": product.url,
                        "platform": product.platform,
                        "status": product.status
                    })
            except ValueError:
                continue
        
        logger.info(f"成功获取监控任务详情 - ID: {task_id}")
        return {
            "id": task_id,
            **task_data,
            "products": product_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取监控任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取监控任务详情失败: {str(e)}")


@router.put("/tasks/{task_id}", summary="更新监控任务")
async def update_monitor_task(
    task_id: str,
    task_data: MonitorTaskUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """更新监控任务"""
    logger.info(f"更新监控任务 - ID: {task_id}")
    
    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")
        
        task = MOCK_TASKS[task_id]
        update_data = task_data.model_dump(exclude_unset=True)
        
        # 如果更新商品ID列表，需要验证
        if "product_ids" in update_data:
            valid_product_ids = []
            for product_id in update_data["product_ids"]:
                try:
                    product_uuid = UUID(product_id)
                    query = select(DBProduct).where(DBProduct.id == product_uuid)
                    result = await db.execute(query)
                    product = result.scalar_one_or_none()
                    if product:
                        valid_product_ids.append(product_id)
                except ValueError:
                    continue
            
            update_data["product_ids"] = valid_product_ids
            update_data["product_count"] = len(valid_product_ids)
        
        # 更新任务数据
        for field, value in update_data.items():
            task[field] = value
        
        task["updated_at"] = datetime.now()
        
        logger.info(f"监控任务更新成功 - ID: {task_id}")
        return {
            "id": task_id,
            **task
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新监控任务失败: {str(e)}")


@router.delete("/tasks/{task_id}", summary="删除监控任务")
async def delete_monitor_task(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """删除监控任务"""
    logger.info(f"删除监控任务 - ID: {task_id}")
    
    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")
        
        # 如果任务正在运行，先停止
        task = MOCK_TASKS[task_id]
        if task["status"] == TaskStatus.RUNNING:
            task["status"] = TaskStatus.CANCELLED
        
        # 删除任务
        del MOCK_TASKS[task_id]
        
        logger.info(f"监控任务删除成功 - ID: {task_id}")
        return {
            "message": "监控任务删除成功",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除监控任务失败: {str(e)}")


@router.post("/tasks/{task_id}/start", summary="启动监控任务")
async def start_monitor_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session)
):
    """启动监控任务"""
    logger.info(f"启动监控任务 - ID: {task_id}")

    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")

        task = MOCK_TASKS[task_id]

        if task["status"] == TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="任务已在运行中")

        if not task["is_active"]:
            raise HTTPException(status_code=400, detail="任务未激活，无法启动")

        # 更新任务状态
        task["status"] = TaskStatus.RUNNING
        task["last_run_at"] = datetime.now()
        task["updated_at"] = datetime.now()

        # 添加后台任务（模拟任务执行）
        background_tasks.add_task(simulate_task_execution, task_id)

        logger.info(f"监控任务启动成功 - ID: {task_id}")
        return {
            "message": "监控任务启动成功",
            "task_id": task_id,
            "status": task["status"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动监控任务失败: {str(e)}")


@router.post("/tasks/{task_id}/pause", summary="暂停监控任务")
async def pause_monitor_task(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """暂停监控任务"""
    logger.info(f"暂停监控任务 - ID: {task_id}")

    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")

        task = MOCK_TASKS[task_id]

        if task["status"] != TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="任务未在运行中")

        # 更新任务状态
        task["status"] = TaskStatus.PENDING
        task["updated_at"] = datetime.now()

        logger.info(f"监控任务暂停成功 - ID: {task_id}")
        return {
            "message": "监控任务暂停成功",
            "task_id": task_id,
            "status": task["status"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停监控任务失败: {str(e)}")


@router.post("/tasks/{task_id}/stop", summary="停止监控任务")
async def stop_monitor_task(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """停止监控任务"""
    logger.info(f"停止监控任务 - ID: {task_id}")

    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")

        task = MOCK_TASKS[task_id]

        # 更新任务状态
        task["status"] = TaskStatus.CANCELLED
        task["updated_at"] = datetime.now()

        logger.info(f"监控任务停止成功 - ID: {task_id}")
        return {
            "message": "监控任务停止成功",
            "task_id": task_id,
            "status": task["status"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止监控任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止监控任务失败: {str(e)}")


@router.get("/tasks/{task_id}/logs", summary="获取任务执行日志")
async def get_task_logs(
    task_id: str,
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    level: Optional[str] = Query(None, description="日志级别筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取任务执行日志"""
    logger.info(f"获取任务执行日志 - ID: {task_id}")

    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")

        # 模拟日志数据
        logs = [
            {
                "id": str(uuid4()),
                "timestamp": datetime.now() - timedelta(minutes=i),
                "level": "INFO" if i % 3 != 0 else "ERROR",
                "message": f"任务执行日志 {i+1}",
                "details": {"step": f"step_{i+1}", "duration": f"{i*0.5}s"}
            }
            for i in range(min(limit, 20))
        ]

        # 级别筛选
        if level:
            logs = [log for log in logs if log["level"] == level.upper()]

        logger.info(f"成功获取任务执行日志 - ID: {task_id}, 记录数: {len(logs)}")
        return {
            "task_id": task_id,
            "logs": logs,
            "total": len(logs)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务执行日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务执行日志失败: {str(e)}")


@router.get("/tasks/{task_id}/status", summary="获取任务实时状态")
async def get_task_status(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取任务实时状态"""
    logger.info(f"获取任务实时状态 - ID: {task_id}")

    try:
        if task_id not in MOCK_TASKS:
            raise HTTPException(status_code=404, detail="监控任务不存在")

        task = MOCK_TASKS[task_id]

        # 计算运行时长
        running_duration = None
        if task["status"] == TaskStatus.RUNNING and task["last_run_at"]:
            running_duration = (datetime.now() - task["last_run_at"]).total_seconds()

        # 计算成功率
        total_runs = task["success_count"] + task["error_count"]
        success_rate = (task["success_count"] / total_runs * 100) if total_runs > 0 else 0

        status_info = {
            "task_id": task_id,
            "status": task["status"],
            "is_active": task["is_active"],
            "last_run_at": task["last_run_at"],
            "next_run_at": task["next_run_at"],
            "running_duration": running_duration,
            "success_count": task["success_count"],
            "error_count": task["error_count"],
            "success_rate": round(success_rate, 2),
            "product_count": task["product_count"]
        }

        logger.info(f"成功获取任务实时状态 - ID: {task_id}")
        return status_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务实时状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务实时状态失败: {str(e)}")


async def simulate_task_execution(task_id: str):
    """模拟任务执行"""
    import asyncio
    import random

    try:
        # 模拟任务执行时间
        await asyncio.sleep(random.uniform(5, 15))

        if task_id in MOCK_TASKS:
            task = MOCK_TASKS[task_id]

            # 模拟执行结果
            if random.random() > 0.2:  # 80%成功率
                task["status"] = TaskStatus.COMPLETED
                task["success_count"] += 1
            else:
                task["status"] = TaskStatus.FAILED
                task["error_count"] += 1

            # 更新下次运行时间
            if task["schedule_config"].get("interval_hours"):
                task["next_run_at"] = datetime.now() + timedelta(
                    hours=task["schedule_config"]["interval_hours"]
                )

            task["updated_at"] = datetime.now()

    except Exception as e:
        logger.error(f"模拟任务执行失败: {str(e)}")
        if task_id in MOCK_TASKS:
            MOCK_TASKS[task_id]["status"] = TaskStatus.FAILED
            MOCK_TASKS[task_id]["error_count"] += 1
