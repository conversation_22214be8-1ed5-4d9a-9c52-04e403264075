"""
商品状态跟踪系统测试

测试生命周期管理、监控调度等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import Product, ProductType, ProductStatus, ProductPrice, ProductSpecs, ProductMetrics
from app.services.product_management.lifecycle_manager import (
    ProductLifecycleManager, LifecycleEvent, MonitoringPriority, LifecycleRule, MonitoringConfig
)
from app.services.product_management.monitoring_scheduler import MonitoringScheduler, SchedulerStatus


class TestProductLifecycleManager:
    """商品生命周期管理器测试"""
    
    @pytest.fixture
    def lifecycle_manager(self):
        """创建生命周期管理器实例"""
        return ProductLifecycleManager()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 256GB",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            status=ProductStatus.NEW,
            data_quality_score=0.9,
            specs=ProductSpecs(brand="Apple"),
            price=ProductPrice(current_price=8999.00),
            metrics=ProductMetrics(sales_count=15000)
        )
    
    def test_default_rules_initialization(self, lifecycle_manager):
        """测试默认生命周期规则初始化"""
        assert len(lifecycle_manager.lifecycle_rules) > 0
        
        # 检查特定规则
        rule_names = [rule.name for rule in lifecycle_manager.lifecycle_rules]
        assert "新商品自动激活" in rule_names
        assert "低质量商品降级" in rule_names
        assert "竞品商品升级" in rule_names
        assert "失败商品暂停" in rule_names
    
    @pytest.mark.asyncio
    async def test_track_lifecycle_event(self, lifecycle_manager, sample_product):
        """测试跟踪生命周期事件"""
        # 跟踪创建事件
        success = await lifecycle_manager.track_lifecycle_event(
            product=sample_product,
            event=LifecycleEvent.CREATED,
            metadata={"source": "test"}
        )
        
        assert success is True
        
        # 检查变更历史
        assert len(sample_product.change_history) > 0
        last_change = sample_product.change_history[-1]
        assert last_change.change_type == "lifecycle_event"
        assert last_change.new_value == "created"
        
        # 检查生命周期指标
        assert sample_product.id in lifecycle_manager.lifecycle_metrics
        metrics = lifecycle_manager.lifecycle_metrics[sample_product.id]
        assert metrics.total_events == 1
        assert metrics.last_activity is not None
    
    @pytest.mark.asyncio
    async def test_activate_monitoring(self, lifecycle_manager, sample_product):
        """测试激活监控"""
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 检查监控配置
        assert sample_product.id in lifecycle_manager.monitoring_configs
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        
        assert config.enabled is True
        assert config.priority == MonitoringPriority.HIGH  # 竞品应该是高优先级
        assert config.frequency_minutes == 60  # 高优先级的频率
        assert config.next_monitor_time is not None
        
        # 检查商品状态
        assert sample_product.status == ProductStatus.ACTIVE
    
    @pytest.mark.asyncio
    async def test_pause_monitoring(self, lifecycle_manager, sample_product):
        """测试暂停监控"""
        # 先激活监控
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 然后暂停
        await lifecycle_manager._pause_monitoring(sample_product)
        
        # 检查监控配置
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        assert config.enabled is False
        
        # 检查商品状态
        assert sample_product.status == ProductStatus.PAUSED
    
    def test_determine_initial_priority(self, lifecycle_manager):
        """测试确定初始监控优先级"""
        # 竞品商品
        competitor_product = Product(
            url="https://example.com/1",
            title="竞品商品",
            product_type=ProductType.COMPETITOR,
            data_quality_score=0.8
        )
        priority = lifecycle_manager._determine_initial_priority(competitor_product)
        assert priority == MonitoringPriority.HIGH
        
        # 供货商商品
        supplier_product = Product(
            url="https://example.com/2",
            title="供货商商品",
            product_type=ProductType.SUPPLIER,
            data_quality_score=0.8
        )
        priority = lifecycle_manager._determine_initial_priority(supplier_product)
        assert priority == MonitoringPriority.NORMAL
        
        # 高质量商品
        high_quality_product = Product(
            url="https://example.com/3",
            title="高质量商品",
            product_type=ProductType.OTHER,
            data_quality_score=0.95
        )
        priority = lifecycle_manager._determine_initial_priority(high_quality_product)
        assert priority == MonitoringPriority.HIGH
        
        # 低质量商品
        low_quality_product = Product(
            url="https://example.com/4",
            title="低质量商品",
            product_type=ProductType.OTHER,
            data_quality_score=0.3
        )
        priority = lifecycle_manager._determine_initial_priority(low_quality_product)
        assert priority == MonitoringPriority.LOW
    
    def test_get_frequency_by_priority(self, lifecycle_manager):
        """测试根据优先级获取监控频率"""
        frequencies = {
            MonitoringPriority.CRITICAL: 15,
            MonitoringPriority.HIGH: 60,
            MonitoringPriority.NORMAL: 240,
            MonitoringPriority.LOW: 720,
            MonitoringPriority.MINIMAL: 1440,
        }
        
        for priority, expected_frequency in frequencies.items():
            frequency = lifecycle_manager._get_frequency_by_priority(priority)
            assert frequency == expected_frequency
    
    @pytest.mark.asyncio
    async def test_raise_monitoring_priority(self, lifecycle_manager, sample_product):
        """测试提高监控优先级"""
        # 先激活监控（初始为HIGH）
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 提高优先级
        await lifecycle_manager._raise_monitoring_priority(sample_product)
        
        # 检查优先级是否提高
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        assert config.priority == MonitoringPriority.CRITICAL
        assert config.frequency_minutes == 15  # CRITICAL的频率
    
    @pytest.mark.asyncio
    async def test_lower_monitoring_priority(self, lifecycle_manager, sample_product):
        """测试降低监控优先级"""
        # 先激活监控（初始为HIGH）
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 降低优先级
        await lifecycle_manager._lower_monitoring_priority(sample_product)
        
        # 检查优先级是否降低
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        assert config.priority == MonitoringPriority.NORMAL
        assert config.frequency_minutes == 240  # NORMAL的频率
    
    @pytest.mark.asyncio
    async def test_get_products_to_monitor(self, lifecycle_manager, sample_product):
        """测试获取需要监控的商品列表"""
        # 激活监控
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 设置下次监控时间为过去时间
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        config.next_monitor_time = datetime.now() - timedelta(minutes=1)
        
        # 获取需要监控的商品
        products_to_monitor = await lifecycle_manager.get_products_to_monitor()
        
        assert len(products_to_monitor) == 1
        assert products_to_monitor[0][0] == sample_product.id
        assert products_to_monitor[0][1] == config
    
    @pytest.mark.asyncio
    async def test_record_monitoring_failure(self, lifecycle_manager, sample_product):
        """测试记录监控失败"""
        # 先激活监控
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 记录失败
        await lifecycle_manager.record_monitoring_failure(sample_product.id)
        
        # 检查失败计数
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        assert config.failure_count == 1
        
        # 检查指标
        metrics = lifecycle_manager.lifecycle_metrics[sample_product.id]
        assert metrics.monitoring_failures == 1
    
    @pytest.mark.asyncio
    async def test_record_monitoring_success(self, lifecycle_manager, sample_product):
        """测试记录监控成功"""
        # 先激活监控
        await lifecycle_manager._activate_monitoring(sample_product)
        
        # 先记录一些失败
        config = lifecycle_manager.monitoring_configs[sample_product.id]
        config.failure_count = 3
        
        # 记录成功
        await lifecycle_manager.record_monitoring_success(sample_product.id)
        
        # 检查失败计数被重置
        assert config.failure_count == 0
        assert config.last_monitored is not None
        assert config.next_monitor_time is not None
    
    def test_get_lifecycle_statistics(self, lifecycle_manager, sample_product):
        """测试获取生命周期统计"""
        # 添加一些数据
        lifecycle_manager.monitoring_configs[sample_product.id] = MonitoringConfig(
            product_id=sample_product.id,
            priority=MonitoringPriority.HIGH,
            frequency_minutes=60,
            enabled=True
        )
        
        stats = lifecycle_manager.get_lifecycle_statistics()
        
        assert "total_products" in stats
        assert "active_monitoring" in stats
        assert "priority_distribution" in stats
        assert stats["total_products"] == 1
        assert stats["active_monitoring"] == 1
    
    def test_lifecycle_rules_management(self, lifecycle_manager):
        """测试生命周期规则管理"""
        # 添加自定义规则
        custom_rule = LifecycleRule(
            id="test_rule",
            name="测试规则",
            trigger_event=LifecycleEvent.UPDATED,
            conditions={"test": True},
            actions=["test_action"],
            priority=5
        )
        
        lifecycle_manager.add_lifecycle_rule(custom_rule)
        
        # 检查规则是否添加
        rule_ids = [rule.id for rule in lifecycle_manager.lifecycle_rules]
        assert "test_rule" in rule_ids
        
        # 获取规则列表
        rules = lifecycle_manager.get_lifecycle_rules()
        assert len(rules) > 0
        
        # 移除规则
        success = lifecycle_manager.remove_lifecycle_rule("test_rule")
        assert success is True
        
        # 检查规则是否移除
        rule_ids = [rule.id for rule in lifecycle_manager.lifecycle_rules]
        assert "test_rule" not in rule_ids


class TestMonitoringScheduler:
    """监控调度器测试"""
    
    @pytest.fixture
    def lifecycle_manager(self):
        """创建生命周期管理器实例"""
        return ProductLifecycleManager()
    
    @pytest.fixture
    def scheduler(self, lifecycle_manager):
        """创建监控调度器实例"""
        return MonitoringScheduler(lifecycle_manager)
    
    def test_scheduler_initialization(self, scheduler):
        """测试调度器初始化"""
        assert scheduler.status == SchedulerStatus.STOPPED
        assert scheduler.max_concurrent_tasks == 10
        assert scheduler.task_timeout_minutes == 30
        assert len(scheduler.pending_tasks) == 0
        assert len(scheduler.running_tasks) == 0
        assert len(scheduler.completed_tasks) == 0
    
    @pytest.mark.asyncio
    async def test_scheduler_start_stop(self, scheduler):
        """测试调度器启动和停止"""
        # 启动调度器
        await scheduler.start()
        assert scheduler.status == SchedulerStatus.RUNNING
        assert scheduler.scheduler_task is not None
        
        # 停止调度器
        await scheduler.stop()
        assert scheduler.status == SchedulerStatus.STOPPED
    
    @pytest.mark.asyncio
    async def test_scheduler_pause_resume(self, scheduler):
        """测试调度器暂停和恢复"""
        # 启动调度器
        await scheduler.start()
        
        # 暂停调度器
        await scheduler.pause()
        assert scheduler.status == SchedulerStatus.PAUSED
        
        # 恢复调度器
        await scheduler.resume()
        assert scheduler.status == SchedulerStatus.RUNNING
        
        # 停止调度器
        await scheduler.stop()
    
    @pytest.mark.asyncio
    async def test_force_monitor_product(self, scheduler):
        """测试强制监控商品"""
        product_id = "test_product_123"
        
        task_id = await scheduler.force_monitor_product(product_id, MonitoringPriority.HIGH)
        
        assert task_id is not None
        assert len(scheduler.pending_tasks) == 1
        
        task = scheduler.pending_tasks[0]
        assert task.product_id == product_id
        assert task.priority == MonitoringPriority.HIGH
        assert task.status == "pending"
    
    def test_get_scheduler_status(self, scheduler):
        """测试获取调度器状态"""
        status = scheduler.get_scheduler_status()
        
        assert "status" in status
        assert "uptime_seconds" in status
        assert "pending_tasks" in status
        assert "running_tasks" in status
        assert "metrics" in status
        
        assert status["status"] == "stopped"
        assert status["pending_tasks"] == 0
        assert status["running_tasks"] == 0
    
    def test_get_task_history(self, scheduler):
        """测试获取任务历史"""
        # 添加一些模拟任务
        from app.services.product_management.monitoring_scheduler import MonitoringTask
        
        task = MonitoringTask(
            id="test_task_1",
            product_id="product_1",
            priority=MonitoringPriority.HIGH,
            scheduled_time=datetime.now(),
            status="completed"
        )
        scheduler.completed_tasks.append(task)
        
        history = scheduler.get_task_history(limit=10)
        
        assert len(history) == 1
        assert history[0]["id"] == "test_task_1"
        assert history[0]["product_id"] == "product_1"
        assert history[0]["status"] == "completed"
    
    def test_monitoring_callbacks(self, scheduler):
        """测试监控回调"""
        callback_called = False
        callback_args = None
        
        def test_callback(product_id, success, error_message):
            nonlocal callback_called, callback_args
            callback_called = True
            callback_args = (product_id, success, error_message)
        
        # 添加回调
        scheduler.add_monitoring_callback(test_callback)
        assert len(scheduler.monitoring_callbacks) == 1
        
        # 移除回调
        scheduler.remove_monitoring_callback(test_callback)
        assert len(scheduler.monitoring_callbacks) == 0
    
    def test_priority_ordering(self, scheduler):
        """测试优先级排序"""
        priorities = [
            MonitoringPriority.LOW,
            MonitoringPriority.CRITICAL,
            MonitoringPriority.NORMAL,
            MonitoringPriority.HIGH,
            MonitoringPriority.MINIMAL
        ]
        
        expected_order = [0, 1, 2, 3, 4]  # CRITICAL, HIGH, NORMAL, LOW, MINIMAL
        
        actual_order = [scheduler._get_priority_order(p) for p in priorities]
        
        # 检查排序是否正确
        sorted_priorities = sorted(zip(actual_order, priorities))
        expected_sorted = [
            (0, MonitoringPriority.CRITICAL),
            (1, MonitoringPriority.HIGH),
            (2, MonitoringPriority.NORMAL),
            (3, MonitoringPriority.LOW),
            (4, MonitoringPriority.MINIMAL)
        ]
        
        assert sorted_priorities == expected_sorted


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
