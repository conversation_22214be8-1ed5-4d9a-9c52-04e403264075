"""
报表引擎

提供基于商品分类的专业报表生成功能
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics
from jinja2 import Template, Environment, DictLoader

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.comprehensive_analyzer import ComprehensiveAnalyzer
from app.services.profit_analysis.cost_manager import CostManager
from app.services.profit_analysis.profit_calculator import ProfitCalculator
from app.services.alert_system.alert_engine import AlertEngine

logger = get_logger(__name__)


class ReportType(Enum):
    """报表类型"""
    COMPETITOR_ANALYSIS = "competitor_analysis"     # 竞品分析报表
    SUPPLIER_COMPARISON = "supplier_comparison"     # 供应商对比报表
    PROFIT_ANALYSIS = "profit_analysis"             # 利润分析报表
    ALERT_SUMMARY = "alert_summary"                 # 预警汇总报表
    MARKET_OVERVIEW = "market_overview"             # 市场概览报表
    PERFORMANCE_DASHBOARD = "performance_dashboard" # 性能仪表板


class ReportPeriod(Enum):
    """报表周期"""
    DAILY = "daily"         # 日报
    WEEKLY = "weekly"       # 周报
    MONTHLY = "monthly"     # 月报
    QUARTERLY = "quarterly" # 季报
    YEARLY = "yearly"       # 年报
    CUSTOM = "custom"       # 自定义


class ReportFormat(Enum):
    """报表格式"""
    HTML = "html"           # HTML格式
    PDF = "pdf"             # PDF格式
    EXCEL = "excel"         # Excel格式
    JSON = "json"           # JSON格式
    CSV = "csv"             # CSV格式


@dataclass
class ReportConfig:
    """报表配置"""
    report_id: str
    report_name: str
    report_type: ReportType
    report_period: ReportPeriod
    enabled: bool = True
    auto_generate: bool = False
    recipients: List[str] = field(default_factory=list)
    template_name: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ReportData:
    """报表数据"""
    report_id: str
    report_type: ReportType
    title: str
    subtitle: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    summary: Dict[str, Any]
    sections: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ReportResult:
    """报表结果"""
    report_id: str
    config: ReportConfig
    data: ReportData
    content: str
    format: ReportFormat
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    generation_time: float = 0.0
    status: str = "completed"
    error_message: Optional[str] = None


class ReportEngine:
    """报表引擎"""
    
    def __init__(self, comprehensive_analyzer: ComprehensiveAnalyzer,
                 cost_manager: CostManager, profit_calculator: ProfitCalculator,
                 alert_engine: AlertEngine):
        self.comprehensive_analyzer = comprehensive_analyzer
        self.cost_manager = cost_manager
        self.profit_calculator = profit_calculator
        self.alert_engine = alert_engine
        
        # 报表存储
        self.report_configs: Dict[str, ReportConfig] = {}
        self.report_results: List[ReportResult] = []
        
        # 模板环境
        self.template_env = Environment(loader=DictLoader({}))
        
        # 初始化默认模板
        self._initialize_default_templates()
        
        # 初始化默认配置
        self._initialize_default_configs()
    
    def _initialize_default_templates(self):
        """初始化默认报表模板"""
        templates = {
            "competitor_analysis": """
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .chart { margin: 15px 0; }
        .table { border-collapse: collapse; width: 100%; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f2f2f2; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #fff; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p>{{ subtitle }}</p>
        <p>生成时间: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        <p>报表周期: {{ period_start.strftime('%Y-%m-%d') }} 至 {{ period_end.strftime('%Y-%m-%d') }}</p>
    </div>
    
    <div class="summary">
        <h2>执行摘要</h2>
        {% for key, value in summary.items() %}
        <div class="metric">
            <strong>{{ key }}</strong>: {{ value }}
        </div>
        {% endfor %}
    </div>
    
    {% for section in sections %}
    <div class="section">
        <h2>{{ section.title }}</h2>
        <p>{{ section.description }}</p>
        
        {% if section.charts %}
        {% for chart in section.charts %}
        <div class="chart">
            <h3>{{ chart.title }}</h3>
            <p>{{ chart.description }}</p>
            <!-- 图表占位符 -->
            <div style="height: 300px; background-color: #f9f9f9; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                图表: {{ chart.type }} - {{ chart.title }}
            </div>
        </div>
        {% endfor %}
        {% endif %}
        
        {% if section.tables %}
        {% for table in section.tables %}
        <div>
            <h3>{{ table.title }}</h3>
            <table class="table">
                <thead>
                    <tr>
                        {% for header in table.headers %}
                        <th>{{ header }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in table.rows %}
                    <tr>
                        {% for cell in row %}
                        <td>{{ cell }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endfor %}
        {% endif %}
    </div>
    {% endfor %}
    
    <div class="section">
        <h2>报表说明</h2>
        <p>本报表基于系统监控数据自动生成，数据截止到 {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }}。</p>
        <p>如有疑问，请联系系统管理员。</p>
    </div>
</body>
</html>
            """.strip(),
            
            "supplier_comparison": """
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .comparison-table { border-collapse: collapse; width: 100%; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 12px; text-align: center; }
        .comparison-table th { background-color: #4CAF50; color: white; }
        .best { background-color: #d4edda; }
        .worst { background-color: #f8d7da; }
        .summary { background-color: #fff3cd; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p>{{ subtitle }}</p>
        <p>生成时间: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
    </div>
    
    <div class="summary">
        <h2>供应商对比摘要</h2>
        {% for key, value in summary.items() %}
        <p><strong>{{ key }}</strong>: {{ value }}</p>
        {% endfor %}
    </div>
    
    {% for section in sections %}
    <div class="section">
        <h2>{{ section.title }}</h2>
        <p>{{ section.description }}</p>
        
        {% if section.tables %}
        {% for table in section.tables %}
        <table class="comparison-table">
            <thead>
                <tr>
                    {% for header in table.headers %}
                    <th>{{ header }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for row in table.rows %}
                <tr>
                    {% for cell in row %}
                    <td>{{ cell }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endfor %}
        {% endif %}
    </div>
    {% endfor %}
</body>
</html>
            """.strip(),
            
            "profit_analysis": """
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .profit-metric { display: inline-block; margin: 10px; padding: 15px; background-color: #e8f5e8; border-radius: 5px; text-align: center; }
        .loss-metric { display: inline-block; margin: 10px; padding: 15px; background-color: #fde8e8; border-radius: 5px; text-align: center; }
        .chart-placeholder { height: 300px; background-color: #f9f9f9; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p>{{ subtitle }}</p>
        <p>生成时间: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
    </div>
    
    <div class="section">
        <h2>利润概览</h2>
        {% for key, value in summary.items() %}
        <div class="{% if 'profit' in key.lower() and value > 0 %}profit-metric{% else %}loss-metric{% endif %}">
            <h3>{{ key }}</h3>
            <p>{{ value }}</p>
        </div>
        {% endfor %}
    </div>
    
    {% for section in sections %}
    <div class="section">
        <h2>{{ section.title }}</h2>
        <p>{{ section.description }}</p>
        
        {% if section.charts %}
        {% for chart in section.charts %}
        <div class="chart-placeholder">
            利润图表: {{ chart.title }}
        </div>
        {% endfor %}
        {% endif %}
    </div>
    {% endfor %}
</body>
</html>
            """.strip()
        }
        
        # 更新模板环境
        self.template_env = Environment(loader=DictLoader(templates))
    
    def _initialize_default_configs(self):
        """初始化默认报表配置"""
        default_configs = [
            ReportConfig(
                report_id="daily_competitor_analysis",
                report_name="每日竞品分析报表",
                report_type=ReportType.COMPETITOR_ANALYSIS,
                report_period=ReportPeriod.DAILY,
                template_name="competitor_analysis",
                auto_generate=True,
                recipients=["<EMAIL>", "<EMAIL>"]
            ),
            ReportConfig(
                report_id="weekly_supplier_comparison",
                report_name="每周供应商对比报表",
                report_type=ReportType.SUPPLIER_COMPARISON,
                report_period=ReportPeriod.WEEKLY,
                template_name="supplier_comparison",
                auto_generate=True,
                recipients=["<EMAIL>"]
            ),
            ReportConfig(
                report_id="monthly_profit_analysis",
                report_name="每月利润分析报表",
                report_type=ReportType.PROFIT_ANALYSIS,
                report_period=ReportPeriod.MONTHLY,
                template_name="profit_analysis",
                auto_generate=True,
                recipients=["<EMAIL>", "<EMAIL>"]
            )
        ]
        
        for config in default_configs:
            self.report_configs[config.report_id] = config

    async def generate_report(self, report_id: str, period_start: Optional[datetime] = None,
                            period_end: Optional[datetime] = None,
                            format: ReportFormat = ReportFormat.HTML) -> ReportResult:
        """
        生成报表

        Args:
            report_id: 报表ID
            period_start: 开始时间
            period_end: 结束时间
            format: 报表格式

        Returns:
            ReportResult: 报表结果
        """
        start_time = datetime.now()

        try:
            logger.info(f"开始生成报表: {report_id}")

            # 获取报表配置
            config = self.report_configs.get(report_id)
            if not config:
                raise ValueError(f"报表配置不存在: {report_id}")

            # 确定报表周期
            if not period_start or not period_end:
                period_start, period_end = self._get_report_period(config.report_period)

            # 收集报表数据
            report_data = await self._collect_report_data(config, period_start, period_end)

            # 生成报表内容
            content = await self._generate_report_content(config, report_data, format)

            # 计算生成时间
            generation_time = (datetime.now() - start_time).total_seconds()

            # 创建报表结果
            result = ReportResult(
                report_id=f"{report_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                config=config,
                data=report_data,
                content=content,
                format=format,
                generation_time=generation_time,
                status="completed"
            )

            # 保存结果
            self.report_results.append(result)

            logger.info(f"报表生成完成: {report_id}, 耗时: {generation_time:.2f}秒")
            return result

        except Exception as e:
            generation_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"报表生成失败: {report_id}, {e}")

            return ReportResult(
                report_id=f"{report_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                config=config if 'config' in locals() else None,
                data=None,
                content="",
                format=format,
                generation_time=generation_time,
                status="failed",
                error_message=str(e)
            )

    def _get_report_period(self, period: ReportPeriod) -> Tuple[datetime, datetime]:
        """获取报表周期"""
        now = datetime.now()

        if period == ReportPeriod.DAILY:
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1) - timedelta(microseconds=1)
        elif period == ReportPeriod.WEEKLY:
            days_since_monday = now.weekday()
            start = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=7) - timedelta(microseconds=1)
        elif period == ReportPeriod.MONTHLY:
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end = start.replace(year=now.year + 1, month=1) - timedelta(microseconds=1)
            else:
                end = start.replace(month=now.month + 1) - timedelta(microseconds=1)
        elif period == ReportPeriod.QUARTERLY:
            quarter = (now.month - 1) // 3 + 1
            start = now.replace(month=(quarter - 1) * 3 + 1, day=1, hour=0, minute=0, second=0, microsecond=0)
            if quarter == 4:
                end = start.replace(year=now.year + 1, month=1) - timedelta(microseconds=1)
            else:
                end = start.replace(month=quarter * 3 + 1) - timedelta(microseconds=1)
        elif period == ReportPeriod.YEARLY:
            start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            end = start.replace(year=now.year + 1) - timedelta(microseconds=1)
        else:  # CUSTOM
            start = now - timedelta(days=30)
            end = now

        return start, end

    async def _collect_report_data(self, config: ReportConfig,
                                 period_start: datetime, period_end: datetime) -> ReportData:
        """收集报表数据"""
        try:
            if config.report_type == ReportType.COMPETITOR_ANALYSIS:
                return await self._collect_competitor_analysis_data(config, period_start, period_end)
            elif config.report_type == ReportType.SUPPLIER_COMPARISON:
                return await self._collect_supplier_comparison_data(config, period_start, period_end)
            elif config.report_type == ReportType.PROFIT_ANALYSIS:
                return await self._collect_profit_analysis_data(config, period_start, period_end)
            elif config.report_type == ReportType.ALERT_SUMMARY:
                return await self._collect_alert_summary_data(config, period_start, period_end)
            else:
                return await self._collect_default_data(config, period_start, period_end)

        except Exception as e:
            logger.error(f"收集报表数据失败: {config.report_id}, {e}")
            # 返回空数据结构
            return ReportData(
                report_id=config.report_id,
                report_type=config.report_type,
                title=config.report_name,
                subtitle="数据收集失败",
                generated_at=datetime.now(),
                period_start=period_start,
                period_end=period_end,
                summary={"错误": "数据收集失败"},
                sections=[],
                charts=[],
                tables=[]
            )

    async def _collect_competitor_analysis_data(self, config: ReportConfig,
                                              period_start: datetime, period_end: datetime) -> ReportData:
        """收集竞品分析数据"""
        # 模拟竞品数据收集
        summary = {
            "监控竞品数量": 25,
            "价格变化商品": 8,
            "新增竞品": 3,
            "平均价格下降": "5.2%",
            "最大威胁竞品": "iPhone 15 Pro"
        }

        sections = [
            {
                "title": "价格变化分析",
                "description": "分析竞品价格变化趋势和影响",
                "charts": [
                    {
                        "title": "竞品价格变化趋势",
                        "type": "line",
                        "description": "显示主要竞品的价格变化趋势"
                    }
                ],
                "tables": [
                    {
                        "title": "价格变化明细",
                        "headers": ["商品名称", "原价格", "现价格", "变化幅度", "影响评估"],
                        "rows": [
                            ["iPhone 15 Pro", "¥8,999", "¥8,499", "-5.6%", "高"],
                            ["Samsung S24", "¥7,299", "¥6,999", "-4.1%", "中"],
                            ["华为 Mate 60", "¥6,999", "¥6,799", "-2.9%", "低"]
                        ]
                    }
                ]
            },
            {
                "title": "市场份额分析",
                "description": "分析竞品市场份额变化",
                "charts": [
                    {
                        "title": "市场份额分布",
                        "type": "pie",
                        "description": "显示各竞品的市场份额分布"
                    }
                ]
            }
        ]

        return ReportData(
            report_id=config.report_id,
            report_type=config.report_type,
            title=config.report_name,
            subtitle=f"竞品分析报表 - {period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            generated_at=datetime.now(),
            period_start=period_start,
            period_end=period_end,
            summary=summary,
            sections=sections,
            charts=[],
            tables=[]
        )

    async def _collect_supplier_comparison_data(self, config: ReportConfig,
                                              period_start: datetime, period_end: datetime) -> ReportData:
        """收集供应商对比数据"""
        # 模拟供应商对比数据
        summary = {
            "供应商总数": 12,
            "活跃供应商": 8,
            "最优供应商": "供应商A",
            "平均成本节省": "8.5%",
            "质量评分最高": "供应商B (4.8分)"
        }

        sections = [
            {
                "title": "供应商综合对比",
                "description": "基于成本、质量、交付等维度的综合对比",
                "tables": [
                    {
                        "title": "供应商评分对比",
                        "headers": ["供应商", "成本评分", "质量评分", "交付评分", "综合评分", "排名"],
                        "rows": [
                            ["供应商A", "9.2", "4.6", "4.8", "9.1", "1"],
                            ["供应商B", "8.8", "4.8", "4.7", "8.9", "2"],
                            ["供应商C", "8.5", "4.5", "4.6", "8.6", "3"],
                            ["供应商D", "8.2", "4.3", "4.4", "8.2", "4"]
                        ]
                    }
                ]
            },
            {
                "title": "成本分析",
                "description": "供应商成本对比和趋势分析",
                "charts": [
                    {
                        "title": "供应商成本对比",
                        "type": "bar",
                        "description": "显示各供应商的成本水平对比"
                    }
                ]
            }
        ]

        return ReportData(
            report_id=config.report_id,
            report_type=config.report_type,
            title=config.report_name,
            subtitle=f"供应商对比报表 - {period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            generated_at=datetime.now(),
            period_start=period_start,
            period_end=period_end,
            summary=summary,
            sections=sections,
            charts=[],
            tables=[]
        )

    async def _collect_profit_analysis_data(self, config: ReportConfig,
                                          period_start: datetime, period_end: datetime) -> ReportData:
        """收集利润分析数据"""
        # 模拟利润分析数据
        summary = {
            "总收入": "¥1,250,000",
            "总成本": "¥980,000",
            "总利润": "¥270,000",
            "利润率": "21.6%",
            "最高利润商品": "高端手机类",
            "利润增长": "+12.3%"
        }

        sections = [
            {
                "title": "利润概览",
                "description": "整体利润情况分析",
                "charts": [
                    {
                        "title": "利润趋势图",
                        "type": "line",
                        "description": "显示利润变化趋势"
                    },
                    {
                        "title": "利润构成",
                        "type": "pie",
                        "description": "显示不同商品类别的利润贡献"
                    }
                ]
            },
            {
                "title": "商品利润排行",
                "description": "各商品类别的利润表现",
                "tables": [
                    {
                        "title": "利润排行榜",
                        "headers": ["商品类别", "收入", "成本", "利润", "利润率", "排名"],
                        "rows": [
                            ["高端手机", "¥450,000", "¥320,000", "¥130,000", "28.9%", "1"],
                            ["中端手机", "¥380,000", "¥290,000", "¥90,000", "23.7%", "2"],
                            ["配件类", "¥250,000", "¥200,000", "¥50,000", "20.0%", "3"],
                            ["其他", "¥170,000", "¥170,000", "¥0", "0.0%", "4"]
                        ]
                    }
                ]
            }
        ]

        return ReportData(
            report_id=config.report_id,
            report_type=config.report_type,
            title=config.report_name,
            subtitle=f"利润分析报表 - {period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            generated_at=datetime.now(),
            period_start=period_start,
            period_end=period_end,
            summary=summary,
            sections=sections,
            charts=[],
            tables=[]
        )

    async def _collect_alert_summary_data(self, config: ReportConfig,
                                        period_start: datetime, period_end: datetime) -> ReportData:
        """收集预警汇总数据"""
        try:
            # 获取预警汇总
            alert_summary = await self.alert_engine.get_alert_summary()

            summary = {
                "总预警数": alert_summary.total_alerts,
                "活跃预警": alert_summary.active_alerts,
                "紧急预警": alert_summary.critical_alerts,
                "重要预警": alert_summary.high_alerts,
                "处理率": f"{((alert_summary.total_alerts - alert_summary.active_alerts) / max(alert_summary.total_alerts, 1) * 100):.1f}%"
            }

            sections = [
                {
                    "title": "预警分布",
                    "description": "预警类型和级别分布情况",
                    "charts": [
                        {
                            "title": "预警级别分布",
                            "type": "pie",
                            "description": "显示不同级别预警的分布"
                        }
                    ]
                }
            ]

        except Exception as e:
            logger.error(f"获取预警数据失败: {e}")
            summary = {"错误": "预警数据获取失败"}
            sections = []

        return ReportData(
            report_id=config.report_id,
            report_type=config.report_type,
            title=config.report_name,
            subtitle=f"预警汇总报表 - {period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            generated_at=datetime.now(),
            period_start=period_start,
            period_end=period_end,
            summary=summary,
            sections=sections,
            charts=[],
            tables=[]
        )

    async def _collect_default_data(self, config: ReportConfig,
                                  period_start: datetime, period_end: datetime) -> ReportData:
        """收集默认数据"""
        return ReportData(
            report_id=config.report_id,
            report_type=config.report_type,
            title=config.report_name,
            subtitle=f"报表 - {period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            generated_at=datetime.now(),
            period_start=period_start,
            period_end=period_end,
            summary={"状态": "数据收集中"},
            sections=[],
            charts=[],
            tables=[]
        )

    async def _generate_report_content(self, config: ReportConfig,
                                     report_data: ReportData, format: ReportFormat) -> str:
        """生成报表内容"""
        try:
            if format == ReportFormat.HTML:
                return await self._generate_html_content(config, report_data)
            elif format == ReportFormat.JSON:
                return await self._generate_json_content(config, report_data)
            elif format == ReportFormat.CSV:
                return await self._generate_csv_content(config, report_data)
            else:
                # 默认返回HTML
                return await self._generate_html_content(config, report_data)

        except Exception as e:
            logger.error(f"生成报表内容失败: {config.report_id}, {e}")
            return f"<html><body><h1>报表生成失败</h1><p>错误: {e}</p></body></html>"

    async def _generate_html_content(self, config: ReportConfig, report_data: ReportData) -> str:
        """生成HTML内容"""
        try:
            template_name = config.template_name or "competitor_analysis"
            template = self.template_env.get_template(template_name)

            # 准备模板变量
            template_vars = {
                "title": report_data.title,
                "subtitle": report_data.subtitle,
                "generated_at": report_data.generated_at,
                "period_start": report_data.period_start,
                "period_end": report_data.period_end,
                "summary": report_data.summary,
                "sections": report_data.sections,
                "charts": report_data.charts,
                "tables": report_data.tables
            }

            return template.render(**template_vars)

        except Exception as e:
            logger.error(f"生成HTML内容失败: {e}")
            return f"<html><body><h1>模板渲染失败</h1><p>错误: {e}</p></body></html>"

    async def _generate_json_content(self, config: ReportConfig, report_data: ReportData) -> str:
        """生成JSON内容"""
        try:
            data = {
                "report_id": report_data.report_id,
                "report_type": report_data.report_type.value,
                "title": report_data.title,
                "subtitle": report_data.subtitle,
                "generated_at": report_data.generated_at.isoformat(),
                "period_start": report_data.period_start.isoformat(),
                "period_end": report_data.period_end.isoformat(),
                "summary": report_data.summary,
                "sections": report_data.sections,
                "charts": report_data.charts,
                "tables": report_data.tables,
                "metadata": report_data.metadata
            }

            return json.dumps(data, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"生成JSON内容失败: {e}")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    async def _generate_csv_content(self, config: ReportConfig, report_data: ReportData) -> str:
        """生成CSV内容"""
        try:
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # 写入标题
            writer.writerow([report_data.title])
            writer.writerow([report_data.subtitle])
            writer.writerow([f"生成时间: {report_data.generated_at.strftime('%Y-%m-%d %H:%M:%S')}"])
            writer.writerow([])

            # 写入摘要
            writer.writerow(["摘要"])
            for key, value in report_data.summary.items():
                writer.writerow([key, value])
            writer.writerow([])

            # 写入表格数据
            for section in report_data.sections:
                if section.get("tables"):
                    for table in section["tables"]:
                        writer.writerow([table["title"]])
                        writer.writerow(table["headers"])
                        for row in table["rows"]:
                            writer.writerow(row)
                        writer.writerow([])

            return output.getvalue()

        except Exception as e:
            logger.error(f"生成CSV内容失败: {e}")
            return f"错误,{e}\n"

    def add_report_config(self, config: ReportConfig) -> bool:
        """添加报表配置"""
        try:
            if config.report_id in self.report_configs:
                logger.warning(f"报表配置已存在: {config.report_id}")
                return False

            self.report_configs[config.report_id] = config
            logger.info(f"报表配置已添加: {config.report_id}")
            return True

        except Exception as e:
            logger.error(f"添加报表配置失败: {config.report_id}, {e}")
            return False

    def update_report_config(self, report_id: str, updates: Dict[str, Any]) -> bool:
        """更新报表配置"""
        try:
            if report_id not in self.report_configs:
                logger.error(f"报表配置不存在: {report_id}")
                return False

            config = self.report_configs[report_id]

            # 更新配置属性
            for key, value in updates.items():
                if hasattr(config, key):
                    if key == "report_type":
                        config.report_type = ReportType(value)
                    elif key == "report_period":
                        config.report_period = ReportPeriod(value)
                    else:
                        setattr(config, key, value)

            logger.info(f"报表配置已更新: {report_id}")
            return True

        except Exception as e:
            logger.error(f"更新报表配置失败: {report_id}, {e}")
            return False

    def delete_report_config(self, report_id: str) -> bool:
        """删除报表配置"""
        try:
            if report_id in self.report_configs:
                del self.report_configs[report_id]
                logger.info(f"报表配置已删除: {report_id}")
                return True
            else:
                logger.error(f"报表配置不存在: {report_id}")
                return False

        except Exception as e:
            logger.error(f"删除报表配置失败: {report_id}, {e}")
            return False

    def get_report_configs(self) -> List[ReportConfig]:
        """获取所有报表配置"""
        return list(self.report_configs.values())

    def get_report_results(self, limit: Optional[int] = None) -> List[ReportResult]:
        """获取报表结果"""
        results = sorted(self.report_results, key=lambda x: x.data.generated_at if x.data else datetime.min, reverse=True)

        if limit:
            results = results[:limit]

        return results

    def get_report_statistics(self) -> Dict[str, Any]:
        """获取报表统计信息"""
        try:
            total_reports = len(self.report_results)
            successful_reports = len([r for r in self.report_results if r.status == "completed"])

            # 按类型统计
            type_distribution = {}
            for result in self.report_results:
                if result.config:
                    report_type = result.config.report_type.value
                    type_distribution[report_type] = type_distribution.get(report_type, 0) + 1

            # 按格式统计
            format_distribution = {}
            for result in self.report_results:
                format_name = result.format.value
                format_distribution[format_name] = format_distribution.get(format_name, 0) + 1

            # 成功率
            success_rate = (successful_reports / total_reports * 100) if total_reports > 0 else 0

            # 平均生成时间
            generation_times = [r.generation_time for r in self.report_results if r.generation_time > 0]
            avg_generation_time = statistics.mean(generation_times) if generation_times else 0

            return {
                "total_reports": total_reports,
                "successful_reports": successful_reports,
                "failed_reports": total_reports - successful_reports,
                "success_rate": success_rate,
                "avg_generation_time": avg_generation_time,
                "total_configs": len(self.report_configs),
                "auto_generate_configs": len([c for c in self.report_configs.values() if c.auto_generate]),
                "type_distribution": type_distribution,
                "format_distribution": format_distribution,
                "available_types": [t.value for t in ReportType],
                "available_periods": [p.value for p in ReportPeriod],
                "available_formats": [f.value for f in ReportFormat]
            }

        except Exception as e:
            logger.error(f"获取报表统计失败: {e}")
            return {
                "total_reports": 0,
                "successful_reports": 0,
                "failed_reports": 0,
                "success_rate": 0,
                "avg_generation_time": 0,
                "total_configs": 0,
                "auto_generate_configs": 0,
                "type_distribution": {},
                "format_distribution": {},
                "available_types": [t.value for t in ReportType],
                "available_periods": [p.value for p in ReportPeriod],
                "available_formats": [f.value for f in ReportFormat]
            }
