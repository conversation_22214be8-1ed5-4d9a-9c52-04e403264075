"""Initial migration

Revision ID: 001_initial_migration
Revises: 
Create Date: 2025-08-10 18:20:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_initial_migration'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('products',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('url', sa.String(length=2000), nullable=False),
    sa.Column('platform', sa.String(length=50), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=True),
    sa.Column('title_translated', sa.String(length=500), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('monitoring_frequency', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('last_monitored_at', sa.DateTime(), nullable=True),
    sa.CheckConstraint('monitoring_frequency > 0', name='check_monitoring_frequency_positive'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('url', name='uq_products_url')
    )
    op.create_index('idx_products_active', 'products', ['is_active'], unique=False)
    op.create_index('idx_products_category', 'products', ['category'], unique=False)
    op.create_index('idx_products_platform', 'products', ['platform'], unique=False)
    op.create_index('idx_products_status', 'products', ['status'], unique=False)
    
    op.create_table('suppliers',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('contact_person', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('email', sa.String(length=200), nullable=True),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('payment_terms', sa.String(length=200), nullable=True),
    sa.Column('delivery_time', sa.Integer(), nullable=True),
    sa.Column('min_order_quantity', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('rating', sa.DECIMAL(precision=3, scale=2), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.CheckConstraint('delivery_time > 0', name='check_delivery_time_positive'),
    sa.CheckConstraint('min_order_quantity > 0', name='check_min_order_quantity_positive'),
    sa.CheckConstraint('rating >= 0 AND rating <= 5', name='check_rating_range'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', name='uq_suppliers_name')
    )
    op.create_index('idx_suppliers_active', 'suppliers', ['is_active', 'name'], unique=False)
    op.create_index('idx_suppliers_rating', 'suppliers', ['rating'], unique=False)
    
    op.create_table('alerts',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('product_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('alert_type', sa.String(length=50), nullable=False),
    sa.Column('severity', sa.String(length=20), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('is_read', sa.Boolean(), nullable=False),
    sa.Column('is_resolved', sa.Boolean(), nullable=False),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.Column('resolved_by', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_alerts_product', 'alerts', ['product_id', 'created_at'], unique=False)
    op.create_index('idx_alerts_severity', 'alerts', ['severity', 'created_at'], unique=False)
    op.create_index('idx_alerts_type', 'alerts', ['alert_type', 'created_at'], unique=False)
    op.create_index('idx_alerts_unread', 'alerts', ['is_read', 'created_at'], unique=False)
    op.create_index('idx_alerts_unresolved', 'alerts', ['is_resolved', 'created_at'], unique=False)
    
    op.create_table('monitoring_tasks',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('product_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('task_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('scheduled_at', sa.DateTime(), nullable=False),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('max_retries', sa.Integer(), nullable=False),
    sa.Column('result_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.CheckConstraint('priority >= 1 AND priority <= 10', name='check_priority_range'),
    sa.CheckConstraint('retry_count >= 0', name='check_retry_count_non_negative'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_monitoring_tasks_priority', 'monitoring_tasks', ['priority', 'scheduled_at'], unique=False)
    op.create_index('idx_monitoring_tasks_product', 'monitoring_tasks', ['product_id', 'scheduled_at'], unique=False)
    op.create_index('idx_monitoring_tasks_status', 'monitoring_tasks', ['status', 'scheduled_at'], unique=False)
    
    op.create_table('product_costs',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('product_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('supplier_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('unit_cost', sa.DECIMAL(precision=12, scale=4), nullable=False),
    sa.Column('currency', sa.String(length=10), nullable=False),
    sa.Column('shipping_cost', sa.DECIMAL(precision=12, scale=4), nullable=True),
    sa.Column('other_costs', sa.DECIMAL(precision=12, scale=4), nullable=True),
    sa.Column('total_cost', sa.DECIMAL(precision=12, scale=4), nullable=False),
    sa.Column('min_quantity', sa.Integer(), nullable=True),
    sa.Column('max_quantity', sa.Integer(), nullable=True),
    sa.Column('valid_from', sa.DateTime(), nullable=False),
    sa.Column('valid_until', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.CheckConstraint('max_quantity IS NULL OR min_quantity IS NULL OR max_quantity >= min_quantity', name='check_quantity_range'),
    sa.CheckConstraint('min_quantity IS NULL OR min_quantity > 0', name='check_min_quantity_positive'),
    sa.CheckConstraint('total_cost > 0', name='check_total_cost_positive'),
    sa.CheckConstraint('unit_cost > 0', name='check_unit_cost_positive'),
    sa.CheckConstraint('valid_until IS NULL OR valid_until > valid_from', name='check_valid_date_range'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_product_costs_active', 'product_costs', ['is_active', 'valid_from'], unique=False)
    op.create_index('idx_product_costs_product', 'product_costs', ['product_id', 'valid_from'], unique=False)
    op.create_index('idx_product_costs_supplier', 'product_costs', ['supplier_id', 'valid_from'], unique=False)
    op.create_index('idx_product_costs_valid', 'product_costs', ['product_id', 'valid_from', 'valid_until'], unique=False)
    
    op.create_table('product_history',
    sa.Column('time', sa.DateTime(), nullable=False),
    sa.Column('product_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('platform', sa.String(length=50), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=True),
    sa.Column('title_translated', sa.String(length=500), nullable=True),
    sa.Column('price', sa.DECIMAL(precision=12, scale=4), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('sales_count', sa.Integer(), nullable=True),
    sa.Column('stock_quantity', sa.Integer(), nullable=True),
    sa.Column('rating', sa.DECIMAL(precision=3, scale=2), nullable=True),
    sa.Column('review_count', sa.Integer(), nullable=True),
    sa.Column('change_type', sa.String(length=50), nullable=True),
    sa.Column('change_value', sa.DECIMAL(precision=12, scale=4), nullable=True),
    sa.Column('data_quality_score', sa.DECIMAL(precision=3, scale=2), nullable=True),
    sa.Column('raw_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('time', 'product_id')
    )
    op.create_index('idx_product_history_comprehensive', 'product_history', ['product_id', 'time', 'price', 'sales_count', 'stock_quantity', 'rating'], unique=False)
    op.create_index('idx_product_history_platform_time', 'product_history', ['platform', 'time'], unique=False)
    op.create_index('idx_product_history_price', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_price_change', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_product_time', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_rating', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_reviews', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_sales', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_sales_change', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_stock', 'product_history', ['product_id', 'time'], unique=False)
    op.create_index('idx_product_history_stock_change', 'product_history', ['product_id', 'time'], unique=False)
    
    # 创建TimescaleDB超表
    op.execute("SELECT create_hypertable('product_history', 'time');")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_product_history_stock_change', table_name='product_history')
    op.drop_index('idx_product_history_stock', table_name='product_history')
    op.drop_index('idx_product_history_sales_change', table_name='product_history')
    op.drop_index('idx_product_history_sales', table_name='product_history')
    op.drop_index('idx_product_history_reviews', table_name='product_history')
    op.drop_index('idx_product_history_rating', table_name='product_history')
    op.drop_index('idx_product_history_product_time', table_name='product_history')
    op.drop_index('idx_product_history_price_change', table_name='product_history')
    op.drop_index('idx_product_history_price', table_name='product_history')
    op.drop_index('idx_product_history_platform_time', table_name='product_history')
    op.drop_index('idx_product_history_comprehensive', table_name='product_history')
    op.drop_table('product_history')
    op.drop_index('idx_product_costs_valid', table_name='product_costs')
    op.drop_index('idx_product_costs_supplier', table_name='product_costs')
    op.drop_index('idx_product_costs_product', table_name='product_costs')
    op.drop_index('idx_product_costs_active', table_name='product_costs')
    op.drop_table('product_costs')
    op.drop_index('idx_monitoring_tasks_status', table_name='monitoring_tasks')
    op.drop_index('idx_monitoring_tasks_product', table_name='monitoring_tasks')
    op.drop_index('idx_monitoring_tasks_priority', table_name='monitoring_tasks')
    op.drop_table('monitoring_tasks')
    op.drop_index('idx_alerts_unresolved', table_name='alerts')
    op.drop_index('idx_alerts_unread', table_name='alerts')
    op.drop_index('idx_alerts_type', table_name='alerts')
    op.drop_index('idx_alerts_severity', table_name='alerts')
    op.drop_index('idx_alerts_product', table_name='alerts')
    op.drop_table('alerts')
    op.drop_index('idx_suppliers_rating', table_name='suppliers')
    op.drop_index('idx_suppliers_active', table_name='suppliers')
    op.drop_table('suppliers')
    op.drop_index('idx_products_status', table_name='products')
    op.drop_index('idx_products_platform', table_name='products')
    op.drop_index('idx_products_category', table_name='products')
    op.drop_index('idx_products_active', table_name='products')
    op.drop_table('products')
    # ### end Alembic commands ###
