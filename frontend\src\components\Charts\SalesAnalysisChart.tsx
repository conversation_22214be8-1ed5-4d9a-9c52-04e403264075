/**
 * 销量分析图表组件
 */

import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin, Empty, Radio } from 'antd';
import { analyticsApi } from '../../services/analyticsApi';

interface SalesAnalysisChartProps {
  category?: string;
  platform?: string;
  startDate?: string;
  endDate?: string;
  height?: number;
}

interface SalesData {
  date: string;
  sales_volume: number;
  sales_amount: number;
  platform: string;
  category?: string;
}

const SalesAnalysisChart: React.FC<SalesAnalysisChartProps> = ({
  category,
  platform,
  startDate,
  endDate,
  height = 400,
}) => {
  const [data, setData] = useState<SalesData[]>([]);
  const [loading, setLoading] = useState(false);
  const [chartType, setChartType] = useState<'volume' | 'amount'>('volume');

  useEffect(() => {
    loadData();
  }, [category, platform, startDate, endDate]);

  const loadData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getSalesAnalysis({
        category,
        platform,
        start_date: startDate,
        end_date: endDate,
        metrics: ['sales_volume', 'sales_amount'],
      });
      setData(response.data.daily_sales || []);
    } catch (error) {
      console.error('Failed to load sales analysis data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const getOption = () => {
    if (!data || data.length === 0) {
      return {};
    }

    // 按平台分组数据
    const groupedData: { [key: string]: SalesData[] } = {};
    data.forEach(item => {
      const key = item.platform || 'Unknown Platform';
      if (!groupedData[key]) {
        groupedData[key] = [];
      }
      groupedData[key].push(item);
    });

    const series = Object.keys(groupedData).map((platformName, index) => {
      const platformData = groupedData[platformName];
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa8c16'];
      
      return {
        name: platformName,
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: colors[index % colors.length],
          borderRadius: [2, 2, 0, 0],
        },
        data: platformData.map(item => [
          item.date, 
          chartType === 'volume' ? item.sales_volume : item.sales_amount
        ]),
      };
    });

    const yAxisName = chartType === 'volume' ? '销量' : '销售额 (¥)';
    const formatter = chartType === 'volume' ? '{value}' : '¥{value}';

    return {
      title: {
        text: chartType === 'volume' ? '销量趋势分析' : '销售额趋势分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
          params.forEach((param: any) => {
            const value = chartType === 'volume' 
              ? param.value[1] 
              : `¥${param.value[1].toLocaleString()}`;
            result += `
              <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                <span style="margin-right: 8px;">${param.seriesName}:</span>
                <span style="font-weight: bold;">${value}</span>
              </div>
            `;
          });
          return result;
        },
      },
      legend: {
        data: Object.keys(groupedData),
        bottom: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        axisLine: {
          lineStyle: {
            color: '#d9d9d9',
          },
        },
        axisLabel: {
          color: '#666',
        },
      },
      yAxis: {
        type: 'value',
        name: yAxisName,
        nameTextStyle: {
          color: '#666',
        },
        axisLine: {
          lineStyle: {
            color: '#d9d9d9',
          },
        },
        axisLabel: {
          color: '#666',
          formatter,
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series,
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
        {
          start: 0,
          end: 100,
          height: 30,
          bottom: 50,
        },
      ],
    };
  };

  if (loading) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="暂无数据" />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 16, textAlign: 'center' }}>
        <Radio.Group 
          value={chartType} 
          onChange={(e) => setChartType(e.target.value)}
          buttonStyle="solid"
        >
          <Radio.Button value="volume">销量</Radio.Button>
          <Radio.Button value="amount">销售额</Radio.Button>
        </Radio.Group>
      </div>
      <ReactECharts
        option={getOption()}
        style={{ height }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

export default SalesAnalysisChart;
