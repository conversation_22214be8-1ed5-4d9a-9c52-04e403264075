[{"metric_id": "volume_translations_count_1755711418.33174", "metric_type": "volume", "name": "translations_count", "value": 150, "unit": "count", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "volume_characters_translated_1755711418.33174", "metric_type": "volume", "name": "characters_translated", "value": 12500, "unit": "chars", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "cost_translation_cost_1755711418.33174", "metric_type": "cost", "name": "translation_cost", "value": 25.75, "unit": "USD", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "cost_provider_cost_openai_1755711418.33174", "metric_type": "cost", "name": "provider_cost_openai", "value": 15.5, "unit": "USD", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "cost_provider_cost_claude_1755711418.33174", "metric_type": "cost", "name": "provider_cost_claude", "value": 10.25, "unit": "USD", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 8.5, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 9.2, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 7.8, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 8.9, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 9.1, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 8.3, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 7.9, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "quality_quality_score_1755711418.33174", "metric_type": "quality", "name": "quality_score", "value": 8.7, "unit": "score", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.3, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 1.8, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 3.1, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.7, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.1, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.9, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.4, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "performance_processing_time_1755711418.33174", "metric_type": "performance", "name": "processing_time", "value": 2.6, "unit": "seconds", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}, {"metric_id": "error_error_count_1755711418.33174", "metric_type": "error", "name": "error_count", "value": 3, "unit": "count", "timestamp": "2025-08-21T01:36:58.331740", "tags": {}, "metadata": {}}]