<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端代理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端代理测试</h1>
        <p>测试前端是否可以通过代理访问后端API</p>
        
        <div class="test-section">
            <h3>1. 健康检查测试</h3>
            <p>测试前端是否可以访问后端健康检查端点</p>
            <button onclick="testHealth()">测试 /health</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. API路径测试</h3>
            <p>测试不同的API路径访问方式</p>
            <button onclick="testApiPath('/api/v1/auth/login')">测试相对路径</button>
            <button onclick="testApiPath('http://localhost:3000/api/v1/auth/login')">测试代理路径</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 登录功能测试</h3>
            <p>测试登录API是否正常工作</p>
            <button onclick="testLogin()">测试登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const credentials = {
            username: 'admin',
            password: '0)q=Y6(ZeTi8'
        };

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试健康检查...';
            
            try {
                const response = await fetch('/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 健康检查成功!\n状态码: ${response.status}\n响应: ${data}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 健康检查失败!\n状态码: ${response.status}\n响应: ${data}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!\n错误: ${error.message}`;
            }
        }

        async function testApiPath(url) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = `正在测试路径: ${url}`;
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.text();
                
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.textContent = `路径: ${url}\n状态码: ${response.status}\n响应: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `路径: ${url}\n❌ 网络错误!\n错误: ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试登录...';
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录成功!\n用户: ${data.user.username}\n邮箱: ${data.user.email}\n角色: ${data.user.role}\n令牌: ${data.access_token.substring(0, 20)}...`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败!\n状态码: ${response.status}\n错误: ${data.message || data.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!\n错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
