"""
缓存管理模块
实现简化的两层缓存架构：本地缓存 + Redis缓存
"""

import json
import time
import logging
from typing import Any, Dict, Optional, Union
from datetime import datetime, timedelta

import redis.asyncio as redis
from redis.asyncio import Redis

logger = logging.getLogger(__name__)


class CacheStats:
    """缓存统计"""
    
    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.errors = 0
    
    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "hits": self.hits,
            "misses": self.misses,
            "sets": self.sets,
            "deletes": self.deletes,
            "errors": self.errors,
            "hit_rate": round(self.hit_rate, 3),
            "total_requests": self.hits + self.misses
        }


class LocalCache:
    """本地内存缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self._cache:
            return None
        
        entry = self._cache[key]
        if entry["expires"] <= time.time():
            del self._cache[key]
            return None
        
        return entry["value"]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            # 如果缓存已满，删除最旧的条目
            if len(self._cache) >= self.max_size:
                oldest_key = min(self._cache.keys(), 
                               key=lambda k: self._cache[k]["created"])
                del self._cache[oldest_key]
            
            ttl = ttl or self.default_ttl
            self._cache[key] = {
                "value": value,
                "expires": time.time() + ttl,
                "created": time.time()
            }
            return True
        except Exception as e:
            logger.error(f"本地缓存设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
    
    def size(self) -> int:
        """缓存大小"""
        return len(self._cache)
    
    def cleanup_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry["expires"] <= current_time
        ]
        for key in expired_keys:
            del self._cache[key]


class SimpleCacheManager:
    """简化的缓存管理器"""
    
    def __init__(self, redis_url: str, local_cache_size: int = 1000):
        self.redis_url = redis_url
        self.redis_client: Optional[Redis] = None
        self.local_cache = LocalCache(max_size=local_cache_size)
        self.stats = CacheStats()
        
        # 缓存配置
        self.cache_config = {
            "default_ttl": 3600,  # 1小时
            "price_trend_ttl": 1800,  # 30分钟
            "sales_trend_ttl": 1800,  # 30分钟
            "product_config_ttl": 3600,  # 1小时
            "supplier_info_ttl": 7200,  # 2小时
            "local_cache_ttl": 300,  # 5分钟本地缓存
        }
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis连接成功")
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
    
    async def get(self, key: str, use_local: bool = True) -> Optional[Any]:
        """获取缓存值"""
        try:
            # L1: 本地缓存
            if use_local:
                local_value = self.local_cache.get(key)
                if local_value is not None:
                    self.stats.hits += 1
                    return local_value
            
            # L2: Redis缓存
            if self.redis_client:
                redis_value = await self.redis_client.get(key)
                if redis_value:
                    self.stats.hits += 1
                    
                    # 反序列化
                    try:
                        value = json.loads(redis_value)
                    except json.JSONDecodeError:
                        value = redis_value
                    
                    # 更新本地缓存
                    if use_local:
                        self.local_cache.set(
                            key, value, self.cache_config["local_cache_ttl"]
                        )
                    
                    return value
            
            self.stats.misses += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败 {key}: {e}")
            self.stats.errors += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, 
                  cache_type: str = "default") -> bool:
        """设置缓存值"""
        try:
            # 获取TTL配置
            if ttl is None:
                ttl = self.cache_config.get(f"{cache_type}_ttl", 
                                          self.cache_config["default_ttl"])
            
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, default=str)
            else:
                serialized_value = str(value)
            
            # 设置Redis缓存
            success = False
            if self.redis_client:
                await self.redis_client.setex(key, ttl, serialized_value)
                success = True
            
            # 设置本地缓存
            local_ttl = min(ttl, self.cache_config["local_cache_ttl"])
            self.local_cache.set(key, value, local_ttl)
            
            self.stats.sets += 1
            return success
            
        except Exception as e:
            logger.error(f"缓存设置失败 {key}: {e}")
            self.stats.errors += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            # 删除Redis缓存
            redis_deleted = False
            if self.redis_client:
                redis_deleted = await self.redis_client.delete(key) > 0
            
            # 删除本地缓存
            local_deleted = self.local_cache.delete(key)
            
            self.stats.deletes += 1
            return redis_deleted or local_deleted
            
        except Exception as e:
            logger.error(f"缓存删除失败 {key}: {e}")
            self.stats.errors += 1
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            # 检查本地缓存
            if self.local_cache.get(key) is not None:
                return True
            
            # 检查Redis缓存
            if self.redis_client:
                return await self.redis_client.exists(key) > 0
            
            return False
            
        except Exception as e:
            logger.error(f"缓存存在检查失败 {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            deleted_count = 0
            
            if self.redis_client:
                # 获取匹配的键
                keys = await self.redis_client.keys(pattern)
                if keys:
                    deleted_count = await self.redis_client.delete(*keys)
            
            # 清理本地缓存中匹配的键
            import fnmatch
            local_keys = [
                key for key in self.local_cache._cache.keys()
                if fnmatch.fnmatch(key, pattern)
            ]
            for key in local_keys:
                self.local_cache.delete(key)
            
            return deleted_count + len(local_keys)
            
        except Exception as e:
            logger.error(f"模式清除失败 {pattern}: {e}")
            return 0
    
    async def get_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        info = {
            "stats": self.stats.to_dict(),
            "local_cache": {
                "size": self.local_cache.size(),
                "max_size": self.local_cache.max_size
            },
            "redis": {
                "connected": self.redis_client is not None
            }
        }
        
        # 获取Redis信息
        if self.redis_client:
            try:
                redis_info = await self.redis_client.info()
                info["redis"].update({
                    "version": redis_info.get("redis_version"),
                    "used_memory": redis_info.get("used_memory_human"),
                    "connected_clients": redis_info.get("connected_clients"),
                    "total_commands_processed": redis_info.get("total_commands_processed")
                })
            except Exception as e:
                logger.error(f"获取Redis信息失败: {e}")
        
        return info
    
    def cleanup_local_cache(self):
        """清理本地缓存"""
        self.local_cache.cleanup_expired()
    
    def get_cache_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args]
        return ":".join(key_parts)


# 全局缓存管理器实例
cache_manager: Optional[SimpleCacheManager] = None


async def get_cache_manager() -> SimpleCacheManager:
    """获取缓存管理器实例"""
    global cache_manager
    if cache_manager is None:
        raise RuntimeError("缓存管理器未初始化")
    return cache_manager


async def init_cache_manager(redis_url: str, local_cache_size: int = 1000):
    """初始化缓存管理器"""
    global cache_manager
    cache_manager = SimpleCacheManager(redis_url, local_cache_size)
    await cache_manager.connect()
    return cache_manager


async def close_cache_manager():
    """关闭缓存管理器"""
    global cache_manager
    if cache_manager:
        await cache_manager.disconnect()
        cache_manager = None


# 缓存装饰器
def cached(ttl: int = 3600, cache_type: str = "default", use_local: bool = True):
    """缓存装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"func:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cache = await get_cache_manager()
            cached_result = await cache.get(cache_key, use_local)
            
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            await cache.set(cache_key, result, ttl, cache_type)
            
            return result
        
        return wrapper
    return decorator
