"""
任务调度管理服务

管理Celery任务的调度、监控和状态跟踪
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from celery import group, chain, chord
from celery.result import AsyncResult, GroupResult

from app.core.celery_app import celery_app
from app.core.logging import get_logger
from app.tasks.crawl_tasks import submit_crawl_batch, batch_crawl_with_strategy
from app.tasks.analysis_tasks import analyze_crawl_results, price_trend_analysis
from app.tasks.notification_tasks import send_notification, price_alert

logger = get_logger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    STARTED = "started"
    SUCCESS = "success"
    FAILURE = "failure"
    RETRY = "retry"
    REVOKED = "revoked"


@dataclass
class ScheduledTask:
    """调度任务信息"""
    task_id: str
    task_name: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int = 0


@dataclass
class TaskBatch:
    """任务批次"""
    batch_id: str
    task_ids: List[str]
    batch_type: str
    created_at: datetime
    status: TaskStatus
    progress: float = 0.0
    results: List[Dict[str, Any]] = None


class TaskSchedulerService:
    """任务调度管理服务"""
    
    def __init__(self):
        self.active_tasks: Dict[str, ScheduledTask] = {}
        self.task_batches: Dict[str, TaskBatch] = {}
    
    def submit_single_crawl_task(self, urls: List[str], platform: str, 
                                product_type: str, priority: str = "medium",
                                batch_name: Optional[str] = None) -> str:
        """
        提交单个爬取任务
        
        Args:
            urls: URL列表
            platform: 平台类型
            product_type: 商品类型
            priority: 任务优先级
            batch_name: 批次名称
        
        Returns:
            str: 任务ID
        """
        try:
            # 根据优先级选择队列
            queue_map = {
                "high": "crawl.high",
                "medium": "crawl.medium", 
                "low": "crawl.low"
            }
            queue = queue_map.get(priority, "crawl.medium")
            
            # 提交任务
            task_result = submit_crawl_batch.apply_async(
                args=[urls, platform, product_type, priority, batch_name],
                queue=queue,
                priority={"high": 9, "medium": 5, "low": 1}.get(priority, 5)
            )
            
            # 记录任务信息
            scheduled_task = ScheduledTask(
                task_id=task_result.id,
                task_name=f"crawl_{platform}_{product_type}",
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            self.active_tasks[task_result.id] = scheduled_task
            
            logger.info(f"提交爬取任务: {task_result.id}, 队列: {queue}")
            return task_result.id
            
        except Exception as e:
            logger.error(f"提交爬取任务失败: {e}")
            raise
    
    def submit_batch_crawl_tasks(self, task_configs: List[Dict[str, Any]], 
                                strategy: str = "batch_optimize") -> str:
        """
        提交批量爬取任务
        
        Args:
            task_configs: 任务配置列表
            strategy: 调度策略
        
        Returns:
            str: 批次ID
        """
        try:
            batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 提交批量任务
            task_result = batch_crawl_with_strategy.apply_async(
                args=[task_configs, strategy],
                queue="crawl.medium"
            )
            
            # 创建任务批次
            task_batch = TaskBatch(
                batch_id=batch_id,
                task_ids=[task_result.id],
                batch_type="crawl_batch",
                created_at=datetime.now(),
                status=TaskStatus.PENDING
            )
            self.task_batches[batch_id] = task_batch
            
            # 记录任务信息
            scheduled_task = ScheduledTask(
                task_id=task_result.id,
                task_name=f"batch_crawl_{strategy}",
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            self.active_tasks[task_result.id] = scheduled_task
            
            logger.info(f"提交批量爬取任务: {batch_id}, 策略: {strategy}")
            return batch_id
            
        except Exception as e:
            logger.error(f"提交批量爬取任务失败: {e}")
            raise
    
    def submit_analysis_workflow(self, crawl_task_ids: List[str]) -> str:
        """
        提交分析工作流
        
        Args:
            crawl_task_ids: 爬取任务ID列表
        
        Returns:
            str: 工作流ID
        """
        try:
            workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建分析工作流：爬取结果分析 -> 价格趋势分析 -> 发送通知
            workflow = chain(
                analyze_crawl_results.s(crawl_task_ids),
                price_trend_analysis.s([]),  # 会从前一个任务获取URL
                send_notification.s("分析工作流完成", "success", ["system"], ["admin"])
            )
            
            # 执行工作流
            workflow_result = workflow.apply_async(queue="analysis")
            
            # 记录工作流信息
            scheduled_task = ScheduledTask(
                task_id=workflow_result.id,
                task_name="analysis_workflow",
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            self.active_tasks[workflow_result.id] = scheduled_task
            
            logger.info(f"提交分析工作流: {workflow_id}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"提交分析工作流失败: {e}")
            raise
    
    def submit_parallel_analysis(self, task_groups: List[List[str]]) -> str:
        """
        提交并行分析任务
        
        Args:
            task_groups: 任务组列表
        
        Returns:
            str: 并行任务组ID
        """
        try:
            group_id = f"parallel_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建并行任务组
            analysis_group = group(
                analyze_crawl_results.s(task_ids) for task_ids in task_groups
            )
            
            # 执行并行任务
            group_result = analysis_group.apply_async()
            
            # 记录任务组信息
            task_batch = TaskBatch(
                batch_id=group_id,
                task_ids=[str(task.id) for task in group_result.children],
                batch_type="parallel_analysis",
                created_at=datetime.now(),
                status=TaskStatus.PENDING
            )
            self.task_batches[group_id] = task_batch
            
            logger.info(f"提交并行分析任务: {group_id}, 组数: {len(task_groups)}")
            return group_id
            
        except Exception as e:
            logger.error(f"提交并行分析任务失败: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Optional[ScheduledTask]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            Optional[ScheduledTask]: 任务信息
        """
        try:
            # 从内存获取任务信息
            if task_id in self.active_tasks:
                scheduled_task = self.active_tasks[task_id]
                
                # 更新任务状态
                celery_result = AsyncResult(task_id, app=celery_app)
                
                if celery_result.state == "PENDING":
                    scheduled_task.status = TaskStatus.PENDING
                elif celery_result.state == "STARTED":
                    scheduled_task.status = TaskStatus.STARTED
                    if not scheduled_task.started_at:
                        scheduled_task.started_at = datetime.now()
                elif celery_result.state == "SUCCESS":
                    scheduled_task.status = TaskStatus.SUCCESS
                    scheduled_task.completed_at = datetime.now()
                    scheduled_task.result = celery_result.result
                elif celery_result.state == "FAILURE":
                    scheduled_task.status = TaskStatus.FAILURE
                    scheduled_task.completed_at = datetime.now()
                    scheduled_task.error = str(celery_result.info)
                elif celery_result.state == "RETRY":
                    scheduled_task.status = TaskStatus.RETRY
                    scheduled_task.retry_count += 1
                
                return scheduled_task
            
            return None
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return None
    
    def get_batch_status(self, batch_id: str) -> Optional[TaskBatch]:
        """
        获取批次状态
        
        Args:
            batch_id: 批次ID
        
        Returns:
            Optional[TaskBatch]: 批次信息
        """
        try:
            if batch_id not in self.task_batches:
                return None
            
            task_batch = self.task_batches[batch_id]
            
            # 更新批次状态
            completed_tasks = 0
            failed_tasks = 0
            results = []
            
            for task_id in task_batch.task_ids:
                task_status = self.get_task_status(task_id)
                if task_status:
                    if task_status.status == TaskStatus.SUCCESS:
                        completed_tasks += 1
                        results.append(task_status.result)
                    elif task_status.status == TaskStatus.FAILURE:
                        failed_tasks += 1
            
            # 计算进度
            total_tasks = len(task_batch.task_ids)
            task_batch.progress = (completed_tasks + failed_tasks) / total_tasks if total_tasks > 0 else 0
            task_batch.results = results
            
            # 更新批次状态
            if completed_tasks + failed_tasks == total_tasks:
                task_batch.status = TaskStatus.SUCCESS if failed_tasks == 0 else TaskStatus.FAILURE
            elif completed_tasks + failed_tasks > 0:
                task_batch.status = TaskStatus.STARTED
            
            return task_batch
            
        except Exception as e:
            logger.error(f"获取批次状态失败: {e}")
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否取消成功
        """
        try:
            celery_app.control.revoke(task_id, terminate=True)
            
            if task_id in self.active_tasks:
                self.active_tasks[task_id].status = TaskStatus.REVOKED
                self.active_tasks[task_id].completed_at = datetime.now()
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False
    
    def retry_failed_task(self, task_id: str) -> Optional[str]:
        """
        重试失败任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            Optional[str]: 新任务ID
        """
        try:
            if task_id not in self.active_tasks:
                return None
            
            original_task = self.active_tasks[task_id]
            if original_task.status != TaskStatus.FAILURE:
                return None
            
            # 获取原始任务参数（这里简化处理）
            # 实际实现中需要保存原始参数
            logger.info(f"重试任务: {task_id}")
            
            # 这里返回原任务ID，实际应该创建新任务
            return task_id
            
        except Exception as e:
            logger.error(f"重试任务失败: {e}")
            return None
    
    def get_active_tasks(self) -> List[ScheduledTask]:
        """
        获取活跃任务列表
        
        Returns:
            List[ScheduledTask]: 活跃任务列表
        """
        active_tasks = []
        
        for task_id, task in self.active_tasks.items():
            if task.status in [TaskStatus.PENDING, TaskStatus.STARTED, TaskStatus.RETRY]:
                # 更新任务状态
                updated_task = self.get_task_status(task_id)
                if updated_task:
                    active_tasks.append(updated_task)
        
        return active_tasks
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                "total_tasks": len(self.active_tasks),
                "total_batches": len(self.task_batches),
                "status_breakdown": {
                    "pending": 0,
                    "started": 0,
                    "success": 0,
                    "failure": 0,
                    "retry": 0,
                    "revoked": 0
                },
                "active_tasks": 0,
                "completed_tasks": 0
            }
            
            for task in self.active_tasks.values():
                current_status = self.get_task_status(task.task_id)
                if current_status:
                    status_key = current_status.status.value
                    stats["status_breakdown"][status_key] += 1
                    
                    if current_status.status in [TaskStatus.PENDING, TaskStatus.STARTED, TaskStatus.RETRY]:
                        stats["active_tasks"] += 1
                    elif current_status.status in [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.REVOKED]:
                        stats["completed_tasks"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}
