"""
预测引擎

基于历史数据进行价格和销量预测
"""

import asyncio
import statistics
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.trend_calculator import TrendCalculator, TrendDirection

logger = get_logger(__name__)


class PredictionMethod(Enum):
    """预测方法"""
    LINEAR_REGRESSION = "linear_regression"
    MOVING_AVERAGE = "moving_average"
    EXPONENTIAL_SMOOTHING = "exponential_smoothing"
    SEASONAL_DECOMPOSITION = "seasonal_decomposition"
    ENSEMBLE = "ensemble"


class PredictionConfidence(Enum):
    """预测置信度"""
    VERY_HIGH = "very_high"    # 90%+
    HIGH = "high"              # 80-90%
    MEDIUM = "medium"          # 60-80%
    LOW = "low"                # 40-60%
    VERY_LOW = "very_low"      # <40%


@dataclass
class PredictionPoint:
    """预测数据点"""
    timestamp: datetime
    predicted_value: float
    confidence_interval: Tuple[float, float]
    confidence_level: float


@dataclass
class PredictionResult:
    """预测结果"""
    product_id: str
    product_type: ProductType
    prediction_method: PredictionMethod
    prediction_horizon_days: int
    predictions: List[PredictionPoint]
    overall_confidence: PredictionConfidence
    accuracy_metrics: Dict[str, float]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class PricePrediction:
    """价格预测"""
    current_price: float
    predicted_prices: List[float]
    price_change_percent: float
    trend_direction: str
    confidence_score: float
    risk_factors: List[str]


@dataclass
class SalesPrediction:
    """销量预测"""
    current_sales: int
    predicted_sales: List[int]
    sales_growth_rate: float
    seasonal_factors: List[float]
    market_factors: List[str]


class PredictionEngine:
    """预测引擎"""
    
    def __init__(self):
        self.trend_calculator = TrendCalculator()
        self.prediction_cache: Dict[str, PredictionResult] = {}
        self.model_weights = {
            PredictionMethod.LINEAR_REGRESSION: 0.3,
            PredictionMethod.MOVING_AVERAGE: 0.2,
            PredictionMethod.EXPONENTIAL_SMOOTHING: 0.3,
            PredictionMethod.SEASONAL_DECOMPOSITION: 0.2
        }
    
    async def predict_price_trend(self, product: Product, 
                                historical_prices: List[float],
                                historical_timestamps: List[datetime],
                                prediction_days: int = 30,
                                method: PredictionMethod = PredictionMethod.ENSEMBLE) -> PricePrediction:
        """
        预测价格趋势
        
        Args:
            product: 商品对象
            historical_prices: 历史价格数据
            historical_timestamps: 历史时间戳
            prediction_days: 预测天数
            method: 预测方法
        
        Returns:
            PricePrediction: 价格预测结果
        """
        try:
            logger.info(f"开始价格趋势预测: {product.id}")
            
            if len(historical_prices) < 3:
                return self._create_empty_price_prediction(product)
            
            current_price = historical_prices[-1]
            
            # 根据方法进行预测
            if method == PredictionMethod.ENSEMBLE:
                predicted_prices = await self._ensemble_price_prediction(
                    historical_prices, historical_timestamps, prediction_days
                )
            elif method == PredictionMethod.LINEAR_REGRESSION:
                predicted_prices = self._linear_regression_prediction(
                    historical_prices, prediction_days
                )
            elif method == PredictionMethod.MOVING_AVERAGE:
                predicted_prices = self._moving_average_prediction(
                    historical_prices, prediction_days
                )
            elif method == PredictionMethod.EXPONENTIAL_SMOOTHING:
                predicted_prices = self._exponential_smoothing_prediction(
                    historical_prices, prediction_days
                )
            else:
                predicted_prices = self._linear_regression_prediction(
                    historical_prices, prediction_days
                )
            
            # 计算价格变化百分比
            if predicted_prices:
                final_predicted_price = predicted_prices[-1]
                price_change_percent = ((final_predicted_price - current_price) / current_price * 100
                                      if current_price > 0 else 0)
            else:
                price_change_percent = 0
            
            # 分析趋势方向
            trend_result = self.trend_calculator.calculate_linear_trend(historical_prices)
            trend_direction = trend_result.direction.value
            
            # 计算置信度
            confidence_score = self._calculate_prediction_confidence(
                historical_prices, predicted_prices, method
            )
            
            # 识别风险因素
            risk_factors = self._identify_price_risk_factors(
                product, historical_prices, predicted_prices
            )
            
            return PricePrediction(
                current_price=current_price,
                predicted_prices=predicted_prices,
                price_change_percent=price_change_percent,
                trend_direction=trend_direction,
                confidence_score=confidence_score,
                risk_factors=risk_factors
            )
            
        except Exception as e:
            logger.error(f"价格趋势预测失败: {e}")
            return self._create_empty_price_prediction(product)
    
    async def predict_sales_trend(self, product: Product,
                                historical_sales: List[int],
                                historical_timestamps: List[datetime],
                                prediction_days: int = 30,
                                method: PredictionMethod = PredictionMethod.ENSEMBLE) -> SalesPrediction:
        """
        预测销量趋势
        
        Args:
            product: 商品对象
            historical_sales: 历史销量数据
            historical_timestamps: 历史时间戳
            prediction_days: 预测天数
            method: 预测方法
        
        Returns:
            SalesPrediction: 销量预测结果
        """
        try:
            logger.info(f"开始销量趋势预测: {product.id}")
            
            if len(historical_sales) < 3:
                return self._create_empty_sales_prediction(product)
            
            current_sales = historical_sales[-1]
            
            # 转换为浮点数进行计算
            sales_float = [float(s) for s in historical_sales]
            
            # 根据方法进行预测
            if method == PredictionMethod.ENSEMBLE:
                predicted_sales_float = await self._ensemble_sales_prediction(
                    sales_float, historical_timestamps, prediction_days
                )
            elif method == PredictionMethod.SEASONAL_DECOMPOSITION:
                predicted_sales_float = self._seasonal_sales_prediction(
                    sales_float, prediction_days
                )
            else:
                predicted_sales_float = self._linear_regression_prediction(
                    sales_float, prediction_days
                )
            
            # 转换回整数
            predicted_sales = [max(0, int(round(s))) for s in predicted_sales_float]
            
            # 计算销量增长率
            if predicted_sales:
                final_predicted_sales = predicted_sales[-1]
                sales_growth_rate = ((final_predicted_sales - current_sales) / current_sales * 100
                                   if current_sales > 0 else 0)
            else:
                sales_growth_rate = 0
            
            # 分析季节性因素
            seasonality_result = self.trend_calculator.detect_seasonality(sales_float)
            seasonal_factors = seasonality_result.seasonal_pattern if seasonality_result.has_seasonality else []
            
            # 识别市场因素
            market_factors = self._identify_market_factors(product, historical_sales)
            
            return SalesPrediction(
                current_sales=current_sales,
                predicted_sales=predicted_sales,
                sales_growth_rate=sales_growth_rate,
                seasonal_factors=seasonal_factors,
                market_factors=market_factors
            )
            
        except Exception as e:
            logger.error(f"销量趋势预测失败: {e}")
            return self._create_empty_sales_prediction(product)
    
    def _linear_regression_prediction(self, historical_data: List[float], 
                                    prediction_days: int) -> List[float]:
        """线性回归预测"""
        if len(historical_data) < 2:
            return []
        
        # 计算线性趋势
        x_values = list(range(len(historical_data)))
        trend_result = self.trend_calculator.calculate_linear_trend(historical_data)
        
        # 生成预测值
        predictions = []
        for i in range(prediction_days):
            future_x = len(historical_data) + i
            predicted_value = historical_data[-1] + trend_result.slope * (i + 1)
            predictions.append(max(0, predicted_value))  # 确保非负
        
        return predictions
    
    def _moving_average_prediction(self, historical_data: List[float],
                                 prediction_days: int,
                                 window: int = 7) -> List[float]:
        """移动平均预测"""
        if len(historical_data) < window:
            window = len(historical_data)
        
        # 计算最近窗口的平均值
        recent_average = statistics.mean(historical_data[-window:])
        
        # 简单预测：使用最近平均值
        predictions = [recent_average] * prediction_days
        
        return predictions
    
    def _exponential_smoothing_prediction(self, historical_data: List[float],
                                        prediction_days: int,
                                        alpha: float = 0.3) -> List[float]:
        """指数平滑预测"""
        if not historical_data:
            return []
        
        # 计算指数移动平均
        ema_result = self.trend_calculator.calculate_moving_average(
            historical_data, period=7, method="exponential"
        )
        
        # 使用最后的EMA值作为预测基础
        last_ema = ema_result.values[-1] if ema_result.values else historical_data[-1]
        
        # 计算趋势
        if len(historical_data) >= 2:
            trend = historical_data[-1] - historical_data[-2]
        else:
            trend = 0
        
        # 生成预测
        predictions = []
        current_value = last_ema
        
        for i in range(prediction_days):
            # 应用趋势衰减
            trend_factor = math.exp(-i * 0.1)  # 趋势衰减
            predicted_value = current_value + trend * trend_factor
            predictions.append(max(0, predicted_value))
            current_value = predicted_value
        
        return predictions
    
    def _seasonal_sales_prediction(self, historical_sales: List[float],
                                 prediction_days: int) -> List[float]:
        """季节性销量预测"""
        # 检测季节性
        seasonality_result = self.trend_calculator.detect_seasonality(historical_sales)
        
        if not seasonality_result.has_seasonality:
            # 如果没有季节性，使用线性回归
            return self._linear_regression_prediction(historical_sales, prediction_days)
        
        # 使用季节性模式进行预测
        seasonal_pattern = seasonality_result.seasonal_pattern
        seasonal_period = seasonality_result.seasonal_period
        
        if not seasonal_pattern or not seasonal_period:
            return self._linear_regression_prediction(historical_sales, prediction_days)
        
        # 计算基础趋势
        detrended_data = seasonality_result.detrended_data
        base_trend = statistics.mean(detrended_data[-min(7, len(detrended_data)):])
        
        # 生成预测
        predictions = []
        for i in range(prediction_days):
            seasonal_index = i % seasonal_period
            seasonal_factor = seasonal_pattern[seasonal_index]
            predicted_value = base_trend + seasonal_factor
            predictions.append(max(0, predicted_value))
        
        return predictions
    
    async def _ensemble_price_prediction(self, historical_prices: List[float],
                                       historical_timestamps: List[datetime],
                                       prediction_days: int) -> List[float]:
        """集成价格预测"""
        predictions_dict = {}
        
        # 线性回归预测
        linear_pred = self._linear_regression_prediction(historical_prices, prediction_days)
        predictions_dict[PredictionMethod.LINEAR_REGRESSION] = linear_pred
        
        # 移动平均预测
        ma_pred = self._moving_average_prediction(historical_prices, prediction_days)
        predictions_dict[PredictionMethod.MOVING_AVERAGE] = ma_pred
        
        # 指数平滑预测
        exp_pred = self._exponential_smoothing_prediction(historical_prices, prediction_days)
        predictions_dict[PredictionMethod.EXPONENTIAL_SMOOTHING] = exp_pred
        
        # 加权平均
        ensemble_predictions = []
        for i in range(prediction_days):
            weighted_sum = 0
            total_weight = 0
            
            for method, predictions in predictions_dict.items():
                if i < len(predictions):
                    weight = self.model_weights.get(method, 0.25)
                    weighted_sum += predictions[i] * weight
                    total_weight += weight
            
            if total_weight > 0:
                ensemble_predictions.append(weighted_sum / total_weight)
            else:
                ensemble_predictions.append(historical_prices[-1])
        
        return ensemble_predictions
    
    async def _ensemble_sales_prediction(self, historical_sales: List[float],
                                       historical_timestamps: List[datetime],
                                       prediction_days: int) -> List[float]:
        """集成销量预测"""
        predictions_dict = {}
        
        # 线性回归预测
        linear_pred = self._linear_regression_prediction(historical_sales, prediction_days)
        predictions_dict[PredictionMethod.LINEAR_REGRESSION] = linear_pred
        
        # 季节性预测
        seasonal_pred = self._seasonal_sales_prediction(historical_sales, prediction_days)
        predictions_dict[PredictionMethod.SEASONAL_DECOMPOSITION] = seasonal_pred
        
        # 指数平滑预测
        exp_pred = self._exponential_smoothing_prediction(historical_sales, prediction_days)
        predictions_dict[PredictionMethod.EXPONENTIAL_SMOOTHING] = exp_pred
        
        # 加权平均
        ensemble_predictions = []
        for i in range(prediction_days):
            weighted_sum = 0
            total_weight = 0
            
            for method, predictions in predictions_dict.items():
                if i < len(predictions):
                    weight = self.model_weights.get(method, 0.33)
                    weighted_sum += predictions[i] * weight
                    total_weight += weight
            
            if total_weight > 0:
                ensemble_predictions.append(weighted_sum / total_weight)
            else:
                ensemble_predictions.append(historical_sales[-1])
        
        return ensemble_predictions
    
    def _calculate_prediction_confidence(self, historical_data: List[float],
                                       predicted_data: List[float],
                                       method: PredictionMethod) -> float:
        """计算预测置信度"""
        if len(historical_data) < 3:
            return 0.0
        
        try:
            # 基于历史数据的稳定性
            volatility = self.trend_calculator._calculate_volatility(historical_data)
            stability_score = max(0, 1 - volatility * 2)  # 波动率越低，稳定性越高
            
            # 基于趋势强度
            trend_result = self.trend_calculator.calculate_linear_trend(historical_data)
            trend_score = abs(trend_result.correlation)
            
            # 基于数据量
            data_score = min(1.0, len(historical_data) / 30)  # 30天数据为满分
            
            # 方法置信度权重
            method_weights = {
                PredictionMethod.LINEAR_REGRESSION: 0.7,
                PredictionMethod.MOVING_AVERAGE: 0.5,
                PredictionMethod.EXPONENTIAL_SMOOTHING: 0.8,
                PredictionMethod.SEASONAL_DECOMPOSITION: 0.6,
                PredictionMethod.ENSEMBLE: 0.9
            }
            
            method_score = method_weights.get(method, 0.5)
            
            # 综合置信度
            confidence = (stability_score * 0.3 + 
                         trend_score * 0.3 + 
                         data_score * 0.2 + 
                         method_score * 0.2)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def _identify_price_risk_factors(self, product: Product,
                                   historical_prices: List[float],
                                   predicted_prices: List[float]) -> List[str]:
        """识别价格风险因素"""
        risk_factors = []
        
        try:
            # 高波动率风险
            volatility = self.trend_calculator._calculate_volatility(historical_prices)
            if volatility > 0.15:
                risk_factors.append("价格波动率较高")
            
            # 急剧变化风险
            if predicted_prices and historical_prices:
                price_change = abs(predicted_prices[-1] - historical_prices[-1]) / historical_prices[-1]
                if price_change > 0.2:
                    risk_factors.append("预测价格变化幅度较大")
            
            # 商品类型风险
            if product.product_type == ProductType.COMPETITOR:
                risk_factors.append("竞品价格可能受市场竞争影响")
            elif product.product_type == ProductType.SUPPLIER:
                risk_factors.append("供应商价格可能受成本波动影响")
            
            # 数据不足风险
            if len(historical_prices) < 7:
                risk_factors.append("历史数据不足，预测准确性有限")
            
        except Exception as e:
            logger.error(f"风险因素识别失败: {e}")
            risk_factors.append("风险评估异常")
        
        return risk_factors
    
    def _identify_market_factors(self, product: Product, 
                               historical_sales: List[int]) -> List[str]:
        """识别市场因素"""
        market_factors = []
        
        try:
            # 销量趋势因素
            if len(historical_sales) >= 2:
                recent_trend = historical_sales[-1] - historical_sales[-2]
                if recent_trend > 0:
                    market_factors.append("销量呈上升趋势")
                elif recent_trend < 0:
                    market_factors.append("销量呈下降趋势")
                else:
                    market_factors.append("销量保持稳定")
            
            # 销量水平因素
            if historical_sales:
                avg_sales = statistics.mean(historical_sales)
                if avg_sales > 10000:
                    market_factors.append("高销量商品，市场需求旺盛")
                elif avg_sales > 1000:
                    market_factors.append("中等销量商品，市场表现良好")
                else:
                    market_factors.append("低销量商品，市场需求有限")
            
            # 商品类型因素
            if product.product_type == ProductType.COMPETITOR:
                market_factors.append("竞品市场，需关注竞争动态")
            elif product.product_type == ProductType.SUPPLIER:
                market_factors.append("供应商市场，批发需求影响较大")
            
        except Exception as e:
            logger.error(f"市场因素识别失败: {e}")
            market_factors.append("市场分析异常")
        
        return market_factors
    
    def _create_empty_price_prediction(self, product: Product) -> PricePrediction:
        """创建空的价格预测"""
        current_price = product.price.current_price if product.price else 0.0
        
        return PricePrediction(
            current_price=current_price,
            predicted_prices=[],
            price_change_percent=0.0,
            trend_direction="unknown",
            confidence_score=0.0,
            risk_factors=["数据不足，无法进行预测"]
        )
    
    def _create_empty_sales_prediction(self, product: Product) -> SalesPrediction:
        """创建空的销量预测"""
        current_sales = product.metrics.sales_count if product.metrics else 0
        
        return SalesPrediction(
            current_sales=current_sales,
            predicted_sales=[],
            sales_growth_rate=0.0,
            seasonal_factors=[],
            market_factors=["数据不足，无法进行预测"]
        )
    
    async def batch_predict_prices(self, products: List[Product],
                                 historical_data: Dict[str, Tuple[List[float], List[datetime]]],
                                 prediction_days: int = 30) -> Dict[str, PricePrediction]:
        """批量价格预测"""
        logger.info(f"开始批量价格预测: {len(products)} 个商品")
        
        results = {}
        
        for product in products:
            if product.id in historical_data:
                prices, timestamps = historical_data[product.id]
                prediction = await self.predict_price_trend(
                    product, prices, timestamps, prediction_days
                )
                results[product.id] = prediction
            else:
                results[product.id] = self._create_empty_price_prediction(product)
        
        logger.info(f"批量价格预测完成: {len(results)} 个结果")
        return results
    
    def get_prediction_statistics(self) -> Dict[str, Any]:
        """获取预测统计信息"""
        return {
            "cached_predictions": len(self.prediction_cache),
            "model_weights": self.model_weights,
            "supported_methods": [method.value for method in PredictionMethod],
            "confidence_levels": [level.value for level in PredictionConfidence]
        }
