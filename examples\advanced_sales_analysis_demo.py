"""
高级销量趋势分析引擎演示

展示销量异常检测、市场份额分析、季节性分析等高级功能
"""

import asyncio
import sys
import random
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.analytics.advanced_sales_analyzer import (
    AdvancedSalesAnalyzer, SalesAnomalyType, MarketPosition
)
from app.services.analytics.sales_anomaly_detector import (
    SalesAnomalyDetector, DetectionConfig, DetectionMethod, AlertChannel
)
from app.services.analytics.sales_seasonality_analyzer import SalesSeasonalityAnalyzer


async def demo_advanced_sales_analysis():
    """演示高级销量分析功能"""
    print("=== 高级销量分析演示 ===")
    
    advanced_analyzer = AdvancedSalesAnalyzer()
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=8999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=25000, rating=4.9)
        ),
        Product(
            url="https://item.jd.com/345678.html",
            title="Samsung Galaxy S24 Ultra 512GB 钛金灰",
            platform="jd",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=9999.00),
            specs=ProductSpecs(brand="Samsung"),
            metrics=ProductMetrics(sales_count=18000, rating=4.8)
        ),
        Product(
            url="https://item.taobao.com/item.htm?id=789012",
            title="Huawei Mate 60 Pro 512GB 雅川青",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="Huawei"),
            metrics=ProductMetrics(sales_count=22000, rating=4.7)
        )
    ]
    
    print(f"\n1. 添加销量历史数据:")
    
    # 为每个商品添加销量历史数据
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        
        base_sales = product.metrics.sales_count
        base_time = datetime.now() - timedelta(days=30)
        
        # 不同的销量模式
        if i == 0:  # iPhone - 正常增长但有异常
            pattern = "正常增长+异常"
            for j in range(15):
                if j == 12:  # 第13天出现异常下降
                    sales = int(base_sales * 0.3)  # 突然下降70%
                else:
                    sales = int(base_sales * (0.8 + j * 0.02))  # 正常增长
                timestamp = base_time + timedelta(days=j*2)
                await advanced_analyzer.base_analyzer.add_sales_data(product.id, sales, timestamp)
        elif i == 1:  # Samsung - 波动较大
            pattern = "高波动"
            for j in range(15):
                fluctuation = random.uniform(-0.3, 0.4)  # 大幅波动
                sales = int(base_sales * (1 + fluctuation))
                timestamp = base_time + timedelta(days=j*2)
                await advanced_analyzer.base_analyzer.add_sales_data(product.id, sales, timestamp)
        else:  # Huawei - 逐渐下降
            pattern = "逐渐下降"
            for j in range(15):
                sales = int(base_sales * (1.1 - j * 0.04))  # 逐渐下降
                timestamp = base_time + timedelta(days=j*2)
                await advanced_analyzer.base_analyzer.add_sales_data(product.id, sales, timestamp)
        
        print(f"     模拟模式: {pattern}")
        print(f"     数据点数: 15个")
    
    print(f"\n2. 销量异常检测:")
    
    # 检测每个商品的销量异常
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1} 异常检测:")
        
        anomalies = await advanced_analyzer.detect_sales_anomalies(product, days=30)
        
        print(f"     检测到异常数: {len(anomalies)}")
        
        for anomaly in anomalies:
            print(f"     - 异常类型: {anomaly.anomaly_type.value}")
            print(f"       预警级别: {anomaly.alert_level.value}")
            print(f"       描述: {anomaly.description}")
            print(f"       严重程度: {anomaly.severity_score:.2f}")
            print(f"       偏差百分比: {anomaly.deviation_percent:.1f}%")
            print(f"       建议:")
            for rec in anomaly.recommendations[:2]:  # 只显示前2个建议
                print(f"         • {rec}")
    
    print(f"\n3. 市场份额分析:")
    
    # 分析iPhone的市场份额
    iphone = test_products[0]
    competitors = test_products[1:]  # Samsung和Huawei作为竞品
    
    market_analysis = await advanced_analyzer.analyze_market_share(
        iphone, competitors, "smartphone"
    )
    
    print(f"   目标商品: {iphone.title[:30]}...")
    print(f"   商品类别: {market_analysis.category}")
    print(f"   当前市场份额: {market_analysis.current_market_share:.1f}%")
    print(f"   市场份额趋势: {market_analysis.market_share_trend}")
    print(f"   市场地位: {market_analysis.market_position.value}")
    print(f"   市场规模估算: {market_analysis.market_size_estimate:,} 件")
    print(f"   增长机会: {market_analysis.growth_opportunity:.1f}%")
    
    if market_analysis.competitive_threats:
        print(f"   竞争威胁:")
        for threat in market_analysis.competitive_threats:
            print(f"     - {threat}")
    
    print(f"   市场洞察:")
    for insight in market_analysis.market_insights:
        print(f"     • {insight}")
    
    return test_products, advanced_analyzer


async def demo_sales_growth_analysis(products, advanced_analyzer):
    """演示销量增长分析"""
    print("\n=== 销量增长分析演示 ===")
    
    # 分析每个商品的销量增长
    for i, product in enumerate(products):
        print(f"\n{i+1}. 商品增长分析: {product.title[:40]}...")
        
        growth_analysis = await advanced_analyzer.analyze_sales_growth(product, days=30)
        
        print(f"   日增长率: {growth_analysis.growth_rate_daily:+.2f}%")
        print(f"   周增长率: {growth_analysis.growth_rate_weekly:+.2f}%")
        print(f"   月增长率: {growth_analysis.growth_rate_monthly:+.2f}%")
        print(f"   增长加速度: {growth_analysis.growth_acceleration:+.2f}%")
        print(f"   增长可持续性: {growth_analysis.growth_sustainability:.2f}")
        
        print(f"   增长驱动因素:")
        for driver in growth_analysis.growth_drivers[:3]:  # 显示前3个
            print(f"     • {driver}")
        
        print(f"   增长障碍:")
        for barrier in growth_analysis.growth_barriers[:3]:  # 显示前3个
            print(f"     • {barrier}")
        
        if growth_analysis.growth_forecast:
            print(f"   增长预测 (未来7天):")
            for j, (date, sales) in enumerate(growth_analysis.growth_forecast[:7]):
                print(f"     {date.strftime('%m-%d')}: {sales:,} 件")


async def demo_competitive_analysis(products, advanced_analyzer):
    """演示竞争分析"""
    print("\n=== 竞争格局分析演示 ===")
    
    # 分析iPhone的竞争格局
    iphone = products[0]
    competitors = products[1:]
    
    competitive_analysis = await advanced_analyzer.analyze_competitive_landscape(
        iphone, competitors
    )
    
    print(f"\n目标商品: {iphone.title[:30]}...")
    print(f"直接竞品数: {len(competitive_analysis.direct_competitors)}")
    print(f"竞争优势: {competitive_analysis.competitive_advantage}")
    print(f"市场差异化: {competitive_analysis.market_differentiation:.2f}")
    print(f"价格竞争力: {competitive_analysis.price_competitiveness:.2f}")
    print(f"销量竞争力: {competitive_analysis.sales_competitiveness:.2f}")
    print(f"威胁级别: {competitive_analysis.threat_level}")
    
    print(f"\n战略建议:")
    for recommendation in competitive_analysis.strategic_recommendations:
        print(f"  • {recommendation}")


async def demo_anomaly_detector():
    """演示异常检测器"""
    print("\n=== 销量异常检测器演示 ===")
    
    anomaly_detector = SalesAnomalyDetector()
    
    print(f"\n1. 配置异常检测:")
    
    # 配置商品检测
    test_product_ids = ["product_001", "product_002", "product_003"]
    
    for i, product_id in enumerate(test_product_ids):
        config = DetectionConfig(
            product_id=product_id,
            enabled=True,
            sensitivity=0.6 + i * 0.1,  # 不同的敏感度
            detection_methods=[DetectionMethod.STATISTICAL, DetectionMethod.RULE_BASED],
            alert_channels=[AlertChannel.LOG, AlertChannel.EMAIL],
            alert_cooldown_minutes=30
        )
        
        anomaly_detector.configure_product_detection(product_id, config)
        print(f"   配置商品: {product_id} (敏感度: {config.sensitivity:.1f})")
    
    print(f"\n2. 检测器状态:")
    stats = anomaly_detector.get_detection_statistics()
    print(f"   监控商品数: {stats['total_products']}")
    print(f"   运行状态: {'运行中' if stats['running'] else '已停止'}")
    print(f"   检测间隔: {stats['detection_interval']} 秒")
    
    print(f"\n3. 模拟预警管理:")
    
    # 模拟创建预警记录
    from app.services.analytics.sales_anomaly_detector import AlertRecord
    from app.services.analytics.advanced_sales_analyzer import SalesAnomaly
    
    from app.services.analytics.advanced_sales_analyzer import SalesAlertLevel

    anomaly = SalesAnomaly(
        anomaly_type=SalesAnomalyType.SUDDEN_DROP,
        alert_level=SalesAlertLevel.HIGH,  # 修正为正确的预警级别
        detected_at=datetime.now(),
        description="模拟销量突然下降异常",
        severity_score=0.8,
        affected_period=(datetime.now() - timedelta(hours=1), datetime.now()),
        baseline_value=1000,
        anomaly_value=300,
        deviation_percent=70,
        recommendations=["立即调查原因", "检查库存状态", "联系运营团队"]
    )
    
    # 这里简化演示，实际中会通过检测流程生成
    print(f"   模拟异常: {anomaly.description}")
    print(f"   严重程度: {anomaly.severity_score:.1f}")
    print(f"   偏差: {anomaly.deviation_percent:.0f}%")


async def demo_seasonality_analysis():
    """演示季节性分析"""
    print("\n=== 销量季节性分析演示 ===")
    
    seasonality_analyzer = SalesSeasonalityAnalyzer()
    
    # 创建测试商品
    test_product = Product(
        url="https://item.taobao.com/item.htm?id=season_test",
        title="季节性商品测试 - 羽绒服",
        product_type=ProductType.COMPETITOR,
        metrics=ProductMetrics(sales_count=5000)
    )
    
    print(f"\n1. 创建季节性销量数据:")
    print(f"   商品: {test_product.title}")
    
    # 创建有明显季节性的销量数据
    sales_data = []
    base_date = datetime.now() - timedelta(days=120)  # 4个月数据
    
    for i in range(120):
        date = base_date + timedelta(days=i)
        
        # 模拟羽绒服的季节性：冬季销量高，夏季销量低
        month = date.month
        if month in [12, 1, 2]:  # 冬季
            base_sales = 3000
        elif month in [6, 7, 8]:  # 夏季
            base_sales = 500
        else:  # 春秋季
            base_sales = 1500
        
        # 添加周模式：周末销量更高
        if date.weekday() in [5, 6]:
            weekend_boost = 1.3
        else:
            weekend_boost = 1.0
        
        # 添加随机波动
        random_factor = random.uniform(0.8, 1.2)
        
        sales = int(base_sales * weekend_boost * random_factor)
        sales_data.append((date, sales))
    
    print(f"   数据点数: {len(sales_data)}")
    print(f"   时间跨度: {sales_data[0][0].strftime('%Y-%m-%d')} 到 {sales_data[-1][0].strftime('%Y-%m-%d')}")
    
    print(f"\n2. 综合季节性分析:")
    
    seasonality_result = await seasonality_analyzer.analyze_comprehensive_seasonality(
        test_product, sales_data, "clothing"
    )
    
    # 基础季节性
    basic = seasonality_result["basic_seasonality"]
    print(f"   有季节性: {basic.has_seasonality}")
    print(f"   季节性强度: {basic.seasonal_strength:.2f}")
    print(f"   峰值季节: {', '.join(basic.peak_seasons) if basic.peak_seasons else '无'}")
    print(f"   低谷季节: {', '.join(basic.low_seasons) if basic.low_seasons else '无'}")
    
    # 多周期分析
    multi_period = seasonality_result["multi_period_analysis"]
    if "weekly" in multi_period and multi_period["weekly"]["has_pattern"]:
        weekly = multi_period["weekly"]
        print(f"   周模式: {weekly['peak_day']} 销量最高，{weekly['low_day']} 销量最低")
    
    if "monthly" in multi_period and multi_period["monthly"]["has_pattern"]:
        monthly = multi_period["monthly"]
        print(f"   月模式: {monthly['peak_month']} 销量最高，{monthly['low_month']} 销量最低")
    
    # 季节性洞察
    insights = seasonality_result["insights"]
    print(f"\n3. 季节性洞察 ({len(insights)} 个):")
    for insight in insights:
        print(f"   • {insight.description}")
        print(f"     影响分数: {insight.impact_score:.2f}")
        if insight.recommendations:
            print(f"     建议: {insight.recommendations[0]}")
    
    # 季节性策略
    strategies = seasonality_result["strategies"]
    if strategies:
        print(f"\n4. 季节性策略 ({len(strategies)} 个):")
        for strategy in strategies:
            print(f"   • {strategy.season} - {strategy.strategy_type}")
            print(f"     预期影响: {strategy.expected_impact:.1%}")
            print(f"     实施时间: {strategy.implementation_timeline}")
            if strategy.recommendations:
                print(f"     关键建议: {strategy.recommendations[0]}")
    
    # 季节性预测
    forecast = seasonality_result["forecast"]
    if forecast.predicted_sales:
        print(f"\n5. 季节性预测 (未来7天):")
        for i in range(min(7, len(forecast.predicted_sales))):
            date = forecast.period[0] + timedelta(days=i)
            sales = forecast.predicted_sales[i]
            adjustment = forecast.seasonal_adjustment[i]
            print(f"   {date.strftime('%m-%d')}: {sales:,} 件 (季节因子: {adjustment:.2f})")
    
    # 关键事件
    if forecast.key_events:
        print(f"\n6. 预测期间关键事件:")
        for event in forecast.key_events:
            print(f"   • {event}")


async def main():
    """主演示函数"""
    print("🚀 高级销量趋势分析引擎演示")
    print("=" * 60)
    
    # 1. 高级销量分析演示
    products, advanced_analyzer = await demo_advanced_sales_analysis()
    
    # 2. 销量增长分析演示
    await demo_sales_growth_analysis(products, advanced_analyzer)
    
    # 3. 竞争分析演示
    await demo_competitive_analysis(products, advanced_analyzer)
    
    # 4. 异常检测器演示
    await demo_anomaly_detector()
    
    # 5. 季节性分析演示
    await demo_seasonality_analysis()
    
    print("\n" + "=" * 60)
    print("✅ 高级销量趋势分析引擎演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 异常检测：6种异常类型，5级预警，智能检测算法")
    print(f"- 市场份额分析：市场地位评估，竞争威胁识别，增长机会计算")
    print(f"- 销量增长分析：多周期增长率，增长可持续性，驱动因素识别")
    print(f"- 竞争格局分析：竞争优势评估，差异化分析，战略建议")
    print(f"- 季节性分析：多周期模式，节假日影响，季节性预测")
    print(f"- 预警系统：实时监控，多渠道预警，预警管理")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: 4个")
    print(f"- 销量数据点: 165个")
    print(f"- 异常检测: 6种类型")
    print(f"- 分析维度: 20+个指标")
    print(f"- 预测方法: 多种算法")
    print(f"- 季节性模式: 3种周期")
    
    print(f"\n🔧 技术特性:")
    print(f"- 深度分析：销量异常、市场份额、增长趋势、竞争格局")
    print(f"- 智能检测：统计方法、规则引擎、机器学习、混合方法")
    print(f"- 实时监控：异步检测、多渠道预警、预警管理")
    print(f"- 季节性分析：多周期检测、节假日影响、季节性预测")
    print(f"- 竞争分析：市场地位、差异化评估、威胁识别")
    print(f"- 增长分析：多维度增长率、可持续性评估、驱动因素")
    
    print(f"\n🏗️ 架构优势:")
    print(f"- 模块化设计：高级分析器 + 异常检测器 + 季节性分析器")
    print(f"- 多算法融合：统计分析 + 规则引擎 + 机器学习方法")
    print(f"- 实时处理：异步检测 + 并发分析 + 实时预警")
    print(f"- 业务导向：市场洞察 + 战略建议 + 可行性分析")
    print(f"- 扩展性强：易于添加新的检测方法和分析维度")


if __name__ == "__main__":
    asyncio.run(main())
