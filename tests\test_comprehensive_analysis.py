"""
综合分析报告引擎测试

测试综合评分、风险评估、机会识别、排名对比等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
from app.services.analytics.comprehensive_analyzer import (
    ComprehensiveAnalyzer, ScoreCategory, RiskType, OpportunityType
)
from app.services.analytics.product_ranking_system import (
    ProductRankingSystem, RankingCriteria, RankingCategory
)


class TestComprehensiveAnalyzer:
    """综合分析器测试"""
    
    @pytest.fixture
    def comprehensive_analyzer(self):
        """创建综合分析器实例"""
        return ComprehensiveAnalyzer()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="综合分析测试商品 - Apple iPhone 15",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=8000, rating=4.6)
        )
    
    @pytest.fixture
    def competitor_products(self):
        """竞品列表"""
        return [
            Product(
                url="https://item.jd.com/competitor1.html",
                title="Samsung Galaxy S24",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=7999.00),
                metrics=ProductMetrics(sales_count=6000, rating=4.4)
            ),
            Product(
                url="https://item.taobao.com/competitor2.html",
                title="Huawei Mate 60",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=5999.00),
                metrics=ProductMetrics(sales_count=9000, rating=4.5)
            )
        ]
    
    @pytest.mark.asyncio
    async def test_generate_comprehensive_report(self, comprehensive_analyzer, sample_product):
        """测试生成综合分析报告"""
        report = await comprehensive_analyzer.generate_comprehensive_report(sample_product)
        
        assert report.product_id == sample_product.id
        assert report.report_type == "standard"
        assert isinstance(report.comprehensive_score.overall_score, float)
        assert 0 <= report.comprehensive_score.overall_score <= 100
        assert report.comprehensive_score.grade in ["A+", "A", "B+", "B", "C+", "C", "D"]
        assert isinstance(report.risk_assessment.overall_risk_level, str)
        assert isinstance(report.market_opportunities, list)
        assert isinstance(report.key_insights, list)
        assert isinstance(report.action_recommendations, list)
        assert isinstance(report.executive_summary, str)
    
    @pytest.mark.asyncio
    async def test_calculate_comprehensive_score(self, comprehensive_analyzer, sample_product):
        """测试计算综合评分"""
        score = await comprehensive_analyzer._calculate_comprehensive_score(sample_product)
        
        assert score.product_id == sample_product.id
        assert 0 <= score.overall_score <= 100
        assert score.grade in ["A+", "A", "B+", "B", "C+", "C", "D"]
        assert len(score.components) == 5  # 5个评分组件
        
        # 检查评分组件
        component_categories = [c.category for c in score.components]
        expected_categories = [
            ScoreCategory.PRICE_COMPETITIVENESS,
            ScoreCategory.SALES_PERFORMANCE,
            ScoreCategory.INVENTORY_HEALTH,
            ScoreCategory.CUSTOMER_SATISFACTION,
            ScoreCategory.MARKET_POTENTIAL
        ]
        
        for category in expected_categories:
            assert category in component_categories
        
        # 检查权重总和
        total_weight = sum(c.weight for c in score.components)
        assert abs(total_weight - 1.0) < 0.01  # 权重总和应该接近1
    
    @pytest.mark.asyncio
    async def test_assess_risks(self, comprehensive_analyzer, sample_product):
        """测试风险评估"""
        risk_assessment = await comprehensive_analyzer._assess_risks(sample_product)
        
        assert risk_assessment.product_id == sample_product.id
        assert risk_assessment.overall_risk_level in ["very_low", "low", "medium", "high", "very_high"]
        assert 0 <= risk_assessment.risk_score <= 100
        assert isinstance(risk_assessment.risks, list)
        assert isinstance(risk_assessment.mitigation_strategies, list)
        assert isinstance(risk_assessment.early_warning_indicators, list)
    
    @pytest.mark.asyncio
    async def test_identify_market_opportunities(self, comprehensive_analyzer, sample_product):
        """测试市场机会识别"""
        opportunities = await comprehensive_analyzer._identify_market_opportunities(sample_product)
        
        assert isinstance(opportunities, list)
        assert len(opportunities) <= 10  # 最多返回10个机会
        
        for opportunity in opportunities:
            assert opportunity.opportunity_type in [op_type for op_type in OpportunityType]
            assert isinstance(opportunity.description, str)
            assert 0 <= opportunity.potential_impact <= 1
            assert 0 <= opportunity.confidence_level <= 1
            assert opportunity.time_to_realize in ["short_term", "medium_term", "long_term"]
            assert isinstance(opportunity.required_actions, list)
            assert 0 <= opportunity.success_probability <= 1
    
    @pytest.mark.asyncio
    async def test_analyze_competitive_comparison(self, comprehensive_analyzer, sample_product, competitor_products):
        """测试竞品对比分析"""
        comparison = await comprehensive_analyzer._analyze_competitive_comparison(
            sample_product, competitor_products
        )
        
        assert comparison.target_product_id == sample_product.id
        assert len(comparison.competitor_products) == len(competitor_products)
        assert isinstance(comparison.comparison_metrics, dict)
        assert isinstance(comparison.competitive_advantages, list)
        assert isinstance(comparison.competitive_disadvantages, list)
        assert isinstance(comparison.market_positioning, str)
        assert isinstance(comparison.strategic_recommendations, list)
        
        # 检查对比指标
        assert sample_product.id in comparison.comparison_metrics
        for competitor in competitor_products:
            assert competitor.id in comparison.comparison_metrics
    
    @pytest.mark.asyncio
    async def test_batch_generate_reports(self, comprehensive_analyzer, sample_product, competitor_products):
        """测试批量生成报告"""
        all_products = [sample_product] + competitor_products
        
        reports = await comprehensive_analyzer.batch_generate_reports(all_products)
        
        assert len(reports) == len(all_products)
        
        for product in all_products:
            assert product.id in reports
            report = reports[product.id]
            assert isinstance(report.comprehensive_score.overall_score, float)
            assert isinstance(report.risk_assessment.overall_risk_level, str)
    
    def test_determine_grade(self, comprehensive_analyzer):
        """测试评分等级确定"""
        assert comprehensive_analyzer._determine_grade(95) == "A+"
        assert comprehensive_analyzer._determine_grade(87) == "A"
        assert comprehensive_analyzer._determine_grade(82) == "B+"
        assert comprehensive_analyzer._determine_grade(77) == "B"
        assert comprehensive_analyzer._determine_grade(72) == "C+"
        assert comprehensive_analyzer._determine_grade(65) == "C"
        assert comprehensive_analyzer._determine_grade(50) == "D"
    
    def test_get_analysis_statistics(self, comprehensive_analyzer):
        """测试获取分析统计信息"""
        stats = comprehensive_analyzer.get_analysis_statistics()
        
        assert "cached_reports" in stats
        assert "score_categories" in stats
        assert "risk_types" in stats
        assert "opportunity_types" in stats
        assert "supported_product_types" in stats
        assert "grade_levels" in stats
        assert "risk_levels" in stats


class TestProductRankingSystem:
    """商品排名系统测试"""
    
    @pytest.fixture
    def ranking_system(self):
        """创建排名系统实例"""
        return ProductRankingSystem()
    
    @pytest.fixture
    def test_products(self):
        """测试商品列表"""
        products = []
        for i in range(5):
            product = Product(
                url=f"https://example.com/product_{i}",
                title=f"测试商品 {i}",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=100.0 + i * 50),
                metrics=ProductMetrics(sales_count=1000 + i * 500, rating=4.0 + i * 0.1)
            )
            products.append(product)
        
        return products
    
    @pytest.mark.asyncio
    async def test_generate_product_ranking(self, ranking_system, test_products):
        """测试生成商品排名"""
        ranking_result = await ranking_system.generate_product_ranking(
            test_products, 
            RankingCriteria.OVERALL_SCORE,
            RankingCategory.ALL_PRODUCTS
        )
        
        assert ranking_result.criteria == RankingCriteria.OVERALL_SCORE
        assert ranking_result.category == RankingCategory.ALL_PRODUCTS
        assert ranking_result.total_products <= len(test_products)
        assert isinstance(ranking_result.rankings, list)
        assert isinstance(ranking_result.category_stats, dict)
        assert isinstance(ranking_result.insights, list)
        
        # 检查排名顺序
        if len(ranking_result.rankings) > 1:
            for i in range(len(ranking_result.rankings) - 1):
                assert ranking_result.rankings[i].rank <= ranking_result.rankings[i + 1].rank
                assert ranking_result.rankings[i].score >= ranking_result.rankings[i + 1].score
    
    def test_filter_products_by_category(self, ranking_system, test_products):
        """测试按类别过滤商品"""
        # 测试全部商品
        all_filtered = ranking_system._filter_products_by_category(
            test_products, RankingCategory.ALL_PRODUCTS
        )
        assert len(all_filtered) == len(test_products)
        
        # 测试按产品类型过滤
        type_filtered = ranking_system._filter_products_by_category(
            test_products, RankingCategory.BY_PRODUCT_TYPE
        )
        assert all(p.product_type == ProductType.COMPETITOR for p in type_filtered)
    
    @pytest.mark.asyncio
    async def test_compare_products(self, ranking_system, test_products):
        """测试商品对比"""
        # 选择前3个商品进行对比
        products_to_compare = test_products[:3]
        
        comparison = await ranking_system.compare_products(products_to_compare)
        
        assert len(comparison.product_ids) == len(products_to_compare)
        assert isinstance(comparison.comparison_metrics, list)
        assert comparison.overall_winner in [p.id for p in products_to_compare]
        assert isinstance(comparison.detailed_analysis, dict)
        assert isinstance(comparison.recommendations, list)
        
        # 检查对比指标
        for metric in comparison.comparison_metrics:
            assert isinstance(metric.metric_name, str)
            assert isinstance(metric.product_values, dict)
            assert metric.best_product in [p.id for p in products_to_compare]
            assert metric.worst_product in [p.id for p in products_to_compare]
            assert isinstance(metric.average_value, float)
            assert isinstance(metric.variance, float)
    
    @pytest.mark.asyncio
    async def test_compare_products_insufficient_data(self, ranking_system):
        """测试商品对比数据不足的情况"""
        single_product = [Product(
            url="https://example.com/single",
            title="单个商品",
            product_type=ProductType.COMPETITOR
        )]
        
        # 应该抛出异常或返回错误结果
        comparison = await ranking_system.compare_products(single_product)
        assert comparison.comparison_id == "error_comparison"
    
    def test_calculate_category_stats(self, ranking_system):
        """测试计算类别统计"""
        from app.services.analytics.product_ranking_system import RankingItem
        
        # 创建测试排名项目
        ranking_items = [
            RankingItem(
                product_id=f"product_{i}",
                product_title=f"商品 {i}",
                rank=i+1,
                score=80.0 + i * 5,
                grade="B+",
                key_metrics={},
                strengths=[],
                weaknesses=[],
                trend="stable"
            )
            for i in range(5)
        ]
        
        stats = ranking_system._calculate_category_stats(ranking_items)
        
        assert "average_score" in stats
        assert "median_score" in stats
        assert "max_score" in stats
        assert "min_score" in stats
        assert "score_std" in stats
        assert "top_10_percent_threshold" in stats
        
        # 验证统计值
        scores = [item.score for item in ranking_items]
        assert abs(stats["average_score"] - sum(scores) / len(scores)) < 0.01
        assert stats["max_score"] == max(scores)
        assert stats["min_score"] == min(scores)
    
    def test_get_ranking_statistics(self, ranking_system):
        """测试获取排名统计信息"""
        stats = ranking_system.get_ranking_statistics()
        
        assert "cached_rankings" in stats
        assert "cached_comparisons" in stats
        assert "ranking_criteria" in stats
        assert "ranking_categories" in stats
        assert "price_ranges" in stats
        assert "sales_ranges" in stats
        
        # 验证枚举值
        assert len(stats["ranking_criteria"]) == len(RankingCriteria)
        assert len(stats["ranking_categories"]) == len(RankingCategory)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
