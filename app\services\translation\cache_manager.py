"""
缓存管理器

管理翻译缓存，提高翻译效率和降低成本
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import pickle
import os

from app.core.logging import get_logger

logger = get_logger(__name__)


class CacheType(Enum):
    """缓存类型"""
    MEMORY = "memory"           # 内存缓存
    FILE = "file"               # 文件缓存
    REDIS = "redis"             # Redis缓存
    DATABASE = "database"       # 数据库缓存


class CacheStatus(Enum):
    """缓存状态"""
    ACTIVE = "active"           # 活跃
    EXPIRED = "expired"         # 已过期
    INVALID = "invalid"         # 无效


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CacheStats:
    """缓存统计"""
    total_entries: int = 0
    active_entries: int = 0
    expired_entries: int = 0
    total_size_bytes: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    last_cleanup: Optional[datetime] = None


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_type: CacheType = CacheType.MEMORY, 
                 cache_dir: str = "cache", max_size_mb: int = 100):
        self.cache_type = cache_type
        self.cache_dir = cache_dir
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
        # 内存缓存存储
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # 缓存统计
        self.stats = CacheStats()
        
        # 缓存配置
        self.cache_config = {
            "default_expire_seconds": 24 * 3600,  # 24小时
            "max_entries": 10000,                  # 最大条目数
            "cleanup_interval": 3600,              # 清理间隔（秒）
            "eviction_policy": "lru",              # 淘汰策略：lru, lfu, fifo
            "compression_enabled": True,           # 启用压缩
            "persistence_enabled": True            # 启用持久化
        }
        
        # 确保缓存目录存在
        if self.cache_type == CacheType.FILE:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        # 启动后台清理任务
        self._cleanup_task = None
        try:
            self._start_cleanup_task()
        except RuntimeError:
            # 如果没有运行的事件循环，跳过启动清理任务
            logger.warning("没有运行的事件循环，跳过启动清理任务")
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
        
        Returns:
            Optional[Any]: 缓存值，如果不存在或已过期则返回None
        """
        try:
            if self.cache_type == CacheType.MEMORY:
                return await self._get_from_memory(key)
            elif self.cache_type == CacheType.FILE:
                return await self._get_from_file(key)
            else:
                logger.warning(f"不支持的缓存类型: {self.cache_type}")
                return None
                
        except Exception as e:
            logger.error(f"获取缓存失败: {key}, {e}")
            self.stats.miss_count += 1
            return None
    
    async def set(self, key: str, value: Any, expire_seconds: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            expire_seconds: 过期时间（秒）
        
        Returns:
            bool: 是否设置成功
        """
        try:
            if self.cache_type == CacheType.MEMORY:
                return await self._set_to_memory(key, value, expire_seconds)
            elif self.cache_type == CacheType.FILE:
                return await self._set_to_file(key, value, expire_seconds)
            else:
                logger.warning(f"不支持的缓存类型: {self.cache_type}")
                return False
                
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
        
        Returns:
            bool: 是否删除成功
        """
        try:
            if self.cache_type == CacheType.MEMORY:
                return await self._delete_from_memory(key)
            elif self.cache_type == CacheType.FILE:
                return await self._delete_from_file(key)
            else:
                logger.warning(f"不支持的缓存类型: {self.cache_type}")
                return False
                
        except Exception as e:
            logger.error(f"删除缓存失败: {key}, {e}")
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            if self.cache_type == CacheType.MEMORY:
                self.memory_cache.clear()
                logger.info("内存缓存已清空")
            elif self.cache_type == CacheType.FILE:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith('.cache'):
                        os.remove(os.path.join(self.cache_dir, filename))
                logger.info("文件缓存已清空")
            
            # 重置统计
            self.stats = CacheStats()
            return True
            
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    async def _get_from_memory(self, key: str) -> Optional[Any]:
        """从内存获取缓存"""
        if key not in self.memory_cache:
            self.stats.miss_count += 1
            return None
        
        entry = self.memory_cache[key]
        
        # 检查是否过期
        if entry.expires_at and datetime.now() > entry.expires_at:
            del self.memory_cache[key]
            self.stats.miss_count += 1
            self.stats.expired_entries += 1
            return None
        
        # 更新访问信息
        entry.access_count += 1
        entry.last_accessed = datetime.now()
        
        self.stats.hit_count += 1
        return entry.value
    
    async def _set_to_memory(self, key: str, value: Any, expire_seconds: Optional[int]) -> bool:
        """设置内存缓存"""
        # 计算过期时间
        expires_at = None
        if expire_seconds:
            expires_at = datetime.now() + timedelta(seconds=expire_seconds)
        elif self.cache_config["default_expire_seconds"]:
            expires_at = datetime.now() + timedelta(seconds=self.cache_config["default_expire_seconds"])
        
        # 计算大小
        size_bytes = len(str(value).encode('utf-8'))
        
        # 检查是否需要淘汰
        await self._evict_if_needed(size_bytes)
        
        # 创建缓存条目
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            expires_at=expires_at,
            size_bytes=size_bytes
        )
        
        # 如果是更新现有条目，先减去旧的大小
        if key in self.memory_cache:
            old_entry = self.memory_cache[key]
            self.stats.total_size_bytes -= old_entry.size_bytes
        else:
            self.stats.total_entries += 1
            self.stats.active_entries += 1
        
        self.memory_cache[key] = entry
        self.stats.total_size_bytes += size_bytes
        
        return True
    
    async def _delete_from_memory(self, key: str) -> bool:
        """从内存删除缓存"""
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            del self.memory_cache[key]
            
            self.stats.total_entries -= 1
            self.stats.active_entries -= 1
            self.stats.total_size_bytes -= entry.size_bytes
            
            return True
        
        return False
    
    async def _get_from_file(self, key: str) -> Optional[Any]:
        """从文件获取缓存"""
        file_path = self._get_file_path(key)
        
        if not os.path.exists(file_path):
            self.stats.miss_count += 1
            return None
        
        try:
            with open(file_path, 'rb') as f:
                entry_data = pickle.load(f)
            
            entry = CacheEntry(**entry_data)
            
            # 检查是否过期
            if entry.expires_at and datetime.now() > entry.expires_at:
                os.remove(file_path)
                self.stats.miss_count += 1
                self.stats.expired_entries += 1
                return None
            
            # 更新访问信息
            entry.access_count += 1
            entry.last_accessed = datetime.now()
            
            # 保存更新的访问信息
            with open(file_path, 'wb') as f:
                pickle.dump(asdict(entry), f)
            
            self.stats.hit_count += 1
            return entry.value
            
        except Exception as e:
            logger.error(f"读取文件缓存失败: {file_path}, {e}")
            self.stats.miss_count += 1
            return None
    
    async def _set_to_file(self, key: str, value: Any, expire_seconds: Optional[int]) -> bool:
        """设置文件缓存"""
        file_path = self._get_file_path(key)
        
        # 计算过期时间
        expires_at = None
        if expire_seconds:
            expires_at = datetime.now() + timedelta(seconds=expire_seconds)
        elif self.cache_config["default_expire_seconds"]:
            expires_at = datetime.now() + timedelta(seconds=self.cache_config["default_expire_seconds"])
        
        # 创建缓存条目
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            expires_at=expires_at,
            size_bytes=0  # 文件大小稍后计算
        )
        
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(asdict(entry), f)
            
            # 计算文件大小
            entry.size_bytes = os.path.getsize(file_path)
            
            # 更新统计
            if not os.path.exists(file_path + '.exists'):
                self.stats.total_entries += 1
                self.stats.active_entries += 1
                # 创建存在标记文件
                with open(file_path + '.exists', 'w') as f:
                    f.write('')
            
            self.stats.total_size_bytes += entry.size_bytes
            
            return True
            
        except Exception as e:
            logger.error(f"写入文件缓存失败: {file_path}, {e}")
            return False
    
    async def _delete_from_file(self, key: str) -> bool:
        """从文件删除缓存"""
        file_path = self._get_file_path(key)
        exists_path = file_path + '.exists'
        
        try:
            if os.path.exists(file_path):
                # 获取文件大小
                size_bytes = os.path.getsize(file_path)
                
                # 删除文件
                os.remove(file_path)
                if os.path.exists(exists_path):
                    os.remove(exists_path)
                
                # 更新统计
                self.stats.total_entries -= 1
                self.stats.active_entries -= 1
                self.stats.total_size_bytes -= size_bytes
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除文件缓存失败: {file_path}, {e}")
            return False
    
    def _get_file_path(self, key: str) -> str:
        """获取缓存文件路径"""
        # 使用MD5哈希作为文件名
        hash_key = hashlib.md5(key.encode('utf-8')).hexdigest()
        return os.path.join(self.cache_dir, f"{hash_key}.cache")
    
    async def _evict_if_needed(self, new_size: int):
        """如果需要则淘汰缓存"""
        if self.cache_type != CacheType.MEMORY:
            return
        
        # 检查大小限制
        if self.stats.total_size_bytes + new_size > self.max_size_bytes:
            await self._evict_entries()
        
        # 检查条目数限制
        if len(self.memory_cache) >= self.cache_config["max_entries"]:
            await self._evict_entries()
    
    async def _evict_entries(self):
        """淘汰缓存条目"""
        if not self.memory_cache:
            return
        
        eviction_policy = self.cache_config["eviction_policy"]
        entries_to_evict = []
        
        if eviction_policy == "lru":
            # 最近最少使用
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].last_accessed or x[1].created_at
            )
        elif eviction_policy == "lfu":
            # 最少使用频率
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].access_count
            )
        else:  # fifo
            # 先进先出
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].created_at
            )
        
        # 淘汰25%的条目
        evict_count = max(1, len(sorted_entries) // 4)
        entries_to_evict = sorted_entries[:evict_count]
        
        for key, entry in entries_to_evict:
            del self.memory_cache[key]
            self.stats.total_entries -= 1
            self.stats.active_entries -= 1
            self.stats.total_size_bytes -= entry.size_bytes
            self.stats.eviction_count += 1
        
        logger.info(f"淘汰了 {len(entries_to_evict)} 个缓存条目")
    
    async def cleanup_expired(self):
        """清理过期缓存"""
        try:
            current_time = datetime.now()
            expired_keys = []
            
            if self.cache_type == CacheType.MEMORY:
                for key, entry in self.memory_cache.items():
                    if entry.expires_at and current_time > entry.expires_at:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    await self._delete_from_memory(key)
                    self.stats.expired_entries += 1
            
            elif self.cache_type == CacheType.FILE:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith('.cache'):
                        file_path = os.path.join(self.cache_dir, filename)
                        try:
                            with open(file_path, 'rb') as f:
                                entry_data = pickle.load(f)
                            
                            entry = CacheEntry(**entry_data)
                            if entry.expires_at and current_time > entry.expires_at:
                                os.remove(file_path)
                                exists_path = file_path + '.exists'
                                if os.path.exists(exists_path):
                                    os.remove(exists_path)
                                expired_keys.append(entry.key)
                                self.stats.expired_entries += 1
                        except:
                            # 如果文件损坏，直接删除
                            os.remove(file_path)
            
            self.stats.last_cleanup = current_time
            
            if expired_keys:
                logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cache_config["cleanup_interval"])
                    await self.cleanup_expired()
                except Exception as e:
                    logger.error(f"清理任务异常: {e}")

        # 只有在有运行的事件循环时才启动任务
        try:
            loop = asyncio.get_running_loop()
            self._cleanup_task = asyncio.create_task(cleanup_loop())
        except RuntimeError:
            # 没有运行的事件循环，不启动清理任务
            self._cleanup_task = None
    
    def stop_cleanup_task(self):
        """停止清理任务"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # 计算命中率
            total_requests = self.stats.hit_count + self.stats.miss_count
            hit_rate = (self.stats.hit_count / total_requests * 100) if total_requests > 0 else 0
            
            # 计算平均大小
            avg_entry_size = (self.stats.total_size_bytes / 
                            max(self.stats.active_entries, 1))
            
            return {
                "cache_type": self.cache_type.value,
                "total_entries": self.stats.total_entries,
                "active_entries": self.stats.active_entries,
                "expired_entries": self.stats.expired_entries,
                "total_size_bytes": self.stats.total_size_bytes,
                "total_size_mb": self.stats.total_size_bytes / (1024 * 1024),
                "max_size_mb": self.max_size_bytes / (1024 * 1024),
                "hit_count": self.stats.hit_count,
                "miss_count": self.stats.miss_count,
                "hit_rate": hit_rate,
                "eviction_count": self.stats.eviction_count,
                "avg_entry_size_bytes": avg_entry_size,
                "last_cleanup": self.stats.last_cleanup.isoformat() if self.stats.last_cleanup else None,
                "config": {
                    "default_expire_seconds": self.cache_config["default_expire_seconds"],
                    "max_entries": self.cache_config["max_entries"],
                    "cleanup_interval": self.cache_config["cleanup_interval"],
                    "eviction_policy": self.cache_config["eviction_policy"]
                }
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {
                "cache_type": self.cache_type.value,
                "total_entries": 0,
                "active_entries": 0,
                "expired_entries": 0,
                "total_size_bytes": 0,
                "hit_count": 0,
                "miss_count": 0,
                "hit_rate": 0,
                "eviction_count": 0
            }
