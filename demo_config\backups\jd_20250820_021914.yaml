base_config:
  base_url: https://www.jd.com
  cookies: null
  headers:
    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  javascript_required: true
  name: 京东商城
  proxy_required: false
  rate_limit:
    concurrent_requests: 2
    delay_between_requests: 2.5
    requests_per_hour: null
    requests_per_minute: 25
  selectors:
    description: null
    images: null
    min_order: null
    price: .price
    rating: null
    sales_count: .comment-count
    seller_info: null
    stock: .stock
    title: .sku-name
enabled: true
platform: !!python/object/apply:app.services.task_middleware.config_manager.Platform
- jd
product_types:
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - competitor
  : additional_selectors:
      brand: .brand-name
      shop_name: .shop-name
    custom_query_template: 提取京东竞品信息：标题、价格、销量、品牌、店铺名称
    monitoring_frequency: 2400
    priority_boost: 2
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - other
  : additional_selectors: null
    custom_query_template: 提取京东基础商品信息：标题、价格、库存、评论数
    monitoring_frequency: 9600
    priority_boost: 0
  ? !!python/object/apply:app.services.task_middleware.config_manager.ProductType
  - supplier
  : additional_selectors:
      delivery_info: .delivery-info
      seller_type: .seller-type
    custom_query_template: 提取京东供货商信息：标题、价格、卖家类型、配送信息
    monitoring_frequency: 4800
    priority_boost: 1
