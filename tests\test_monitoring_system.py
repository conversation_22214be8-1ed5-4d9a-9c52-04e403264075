"""
监控和日志系统测试

测试健康检查、日志管理、系统监控等功能
"""

import pytest
import asyncio
import tempfile
import shutil
import os
import json
from datetime import datetime, timedelta

from app.monitoring import (
    HealthChecker, HealthStatus, LogManager, LogLevel, LogCategory,
    SystemMonitor, ResourceMetrics, AlertThreshold
)


class TestHealthChecker:
    """健康检查器测试"""
    
    @pytest.fixture
    def health_checker(self):
        """健康检查器"""
        return HealthChecker()
    
    @pytest.mark.asyncio
    async def test_check_system_resources(self, health_checker):
        """测试系统资源检查"""
        result = await health_checker._check_system_resources()
        
        assert result.name == "system"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert result.response_time_ms >= 0
        assert "cpu_percent" in result.details
        assert "memory_percent" in result.details
        assert "disk_percent" in result.details
    
    @pytest.mark.asyncio
    async def test_check_disk_space(self, health_checker):
        """测试磁盘空间检查"""
        result = await health_checker._check_disk_space()
        
        assert result.name == "disk"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "total_gb" in result.details
        assert "used_gb" in result.details
        assert "free_gb" in result.details
        assert "usage_percent" in result.details
    
    @pytest.mark.asyncio
    async def test_check_memory_usage(self, health_checker):
        """测试内存使用检查"""
        result = await health_checker._check_memory_usage()
        
        assert result.name == "memory"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "total_gb" in result.details
        assert "available_gb" in result.details
        assert "usage_percent" in result.details
    
    @pytest.mark.asyncio
    async def test_check_cpu_usage(self, health_checker):
        """测试CPU使用检查"""
        result = await health_checker._check_cpu_usage()
        
        assert result.name == "cpu"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "usage_percent" in result.details
        assert "cpu_count" in result.details
    
    @pytest.mark.asyncio
    async def test_full_health_check(self, health_checker):
        """测试完整健康检查"""
        result = await health_checker.check_health()
        
        assert "status" in result
        assert "timestamp" in result
        assert "components" in result
        assert "summary" in result
        assert "uptime_seconds" in result
        
        # 检查组件结果
        components = result["components"]
        assert len(components) > 0
        
        for component_name, component_data in components.items():
            assert "status" in component_data
            assert "response_time_ms" in component_data
    
    @pytest.mark.asyncio
    async def test_specific_components_check(self, health_checker):
        """测试特定组件检查"""
        components = ["system", "disk", "memory"]
        result = await health_checker.check_health(components)
        
        assert len(result["components"]) == len(components)
        for component in components:
            assert component in result["components"]


class TestLogManager:
    """日志管理器测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def log_manager(self, temp_dir):
        """日志管理器"""
        return LogManager(temp_dir)
    
    def test_log_creation(self, log_manager):
        """测试日志记录"""
        log_manager.info(
            LogCategory.SYSTEM,
            "测试信息日志",
            user_id="test_user",
            extra_data={"key": "value"}
        )
        
        log_manager.error(
            LogCategory.ERROR,
            "测试错误日志",
            user_id="test_user",
            extra_data={"error_code": "E001"}
        )
        
        # 等待异步日志处理
        import time
        time.sleep(1)
        
        # 检查统计
        stats = log_manager.get_log_statistics()
        assert stats["stats"]["total_logs"] >= 2
        assert stats["stats"]["by_level"]["INFO"] >= 1
        assert stats["stats"]["by_level"]["ERROR"] >= 1
    
    def test_search_logs(self, log_manager):
        """测试日志搜索"""
        # 记录一些日志
        log_manager.info(LogCategory.SYSTEM, "系统启动")
        log_manager.warning(LogCategory.AUTH, "登录失败")
        log_manager.error(LogCategory.ERROR, "数据库连接失败")
        
        # 等待异步处理
        import time
        time.sleep(1)
        
        # 搜索所有日志
        all_logs = log_manager.search_logs(limit=100)
        assert len(all_logs) >= 3
        
        # 按分类搜索
        system_logs = log_manager.search_logs(category=LogCategory.SYSTEM, limit=100)
        assert len(system_logs) >= 1
        
        # 按级别搜索
        error_logs = log_manager.search_logs(level=LogLevel.ERROR, limit=100)
        assert len(error_logs) >= 1
        
        # 关键词搜索
        keyword_logs = log_manager.search_logs(keyword="失败", limit=100)
        assert len(keyword_logs) >= 2
    
    def test_log_rotation(self, log_manager):
        """测试日志轮转"""
        # 记录大量日志触发轮转
        for i in range(100):
            log_manager.info(LogCategory.SYSTEM, f"测试日志 {i}")
        
        # 等待异步处理
        import time
        time.sleep(2)
        
        # 手动轮转
        log_manager.rotate_logs()
        
        # 检查日志文件是否存在
        log_files = os.listdir(log_manager.log_dir)
        assert len(log_files) > 0
    
    def test_export_logs(self, log_manager, temp_dir):
        """测试日志导出"""
        # 记录一些日志
        log_manager.info(LogCategory.SYSTEM, "导出测试日志1")
        log_manager.info(LogCategory.SYSTEM, "导出测试日志2")
        
        # 等待异步处理
        import time
        time.sleep(1)
        
        # 导出日志
        export_file = os.path.join(temp_dir, "exported_logs.json")
        success = log_manager.export_logs(export_file, category=LogCategory.SYSTEM)
        
        assert success is True
        assert os.path.exists(export_file)
        
        # 检查导出内容
        with open(export_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            assert len(lines) >= 2


class TestSystemMonitor:
    """系统监控器测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def system_monitor(self, temp_dir):
        """系统监控器"""
        config_file = os.path.join(temp_dir, "test_monitor_config.json")
        return SystemMonitor(config_file, temp_dir)
    
    @pytest.mark.asyncio
    async def test_collect_system_metrics(self, system_monitor):
        """测试收集系统指标"""
        metrics = await system_monitor._collect_system_metrics()
        
        assert isinstance(metrics, ResourceMetrics)
        assert metrics.cpu_percent >= 0
        assert metrics.memory_percent >= 0
        assert metrics.disk_percent >= 0
        assert metrics.network_bytes_sent >= 0
        assert metrics.network_bytes_recv >= 0
        assert metrics.process_count > 0
    
    def test_store_metrics(self, system_monitor):
        """测试存储指标"""
        metrics = ResourceMetrics(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=60.0,
            disk_percent=70.0,
            network_bytes_sent=1000,
            network_bytes_recv=2000,
            process_count=100
        )
        
        system_monitor._store_metrics(metrics)
        
        assert len(system_monitor.metrics_history) == 1
        assert system_monitor.metrics_history[0] == metrics
    
    @pytest.mark.asyncio
    async def test_check_alerts(self, system_monitor):
        """测试告警检查"""
        # 创建高CPU使用率的指标
        high_cpu_metrics = ResourceMetrics(
            timestamp=datetime.now(),
            cpu_percent=95.0,  # 超过阈值
            memory_percent=50.0,
            disk_percent=50.0,
            network_bytes_sent=1000,
            network_bytes_recv=2000,
            process_count=100
        )
        
        # 检查告警
        await system_monitor._check_alerts(high_cpu_metrics)
        
        # 检查是否生成了告警
        assert len(system_monitor.alert_history) > 0
        alert = system_monitor.alert_history[0]
        assert alert["metric_name"] == "cpu_percent"
        assert alert["level"] == "critical"
    
    def test_get_system_status(self, system_monitor):
        """测试获取系统状态"""
        # 添加一些测试数据
        metrics = ResourceMetrics(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=60.0,
            disk_percent=70.0,
            network_bytes_sent=1000,
            network_bytes_recv=2000,
            process_count=100
        )
        system_monitor._store_metrics(metrics)
        
        status = system_monitor.get_system_status()
        
        assert "system_healthy" in status
        assert "uptime_seconds" in status
        assert "latest_metrics" in status
        assert "services" in status
        assert "recent_alerts" in status
        assert "stats" in status
    
    def test_get_metrics_history(self, system_monitor):
        """测试获取指标历史"""
        # 添加测试指标
        for i in range(5):
            metrics = ResourceMetrics(
                timestamp=datetime.now() - timedelta(minutes=i),
                cpu_percent=50.0 + i,
                memory_percent=60.0,
                disk_percent=70.0,
                network_bytes_sent=1000,
                network_bytes_recv=2000,
                process_count=100
            )
            system_monitor._store_metrics(metrics)
        
        # 获取历史数据
        history = system_monitor.get_metrics_history(hours=1)
        
        assert len(history) == 5
        for metric_data in history:
            assert "timestamp" in metric_data
            assert "cpu_percent" in metric_data
            assert "memory_percent" in metric_data
    
    @pytest.mark.asyncio
    async def test_monitoring_lifecycle(self, system_monitor):
        """测试监控生命周期"""
        # 启动监控
        await system_monitor.start_monitoring()
        assert system_monitor.is_running is True
        
        # 等待一下让监控运行
        await asyncio.sleep(2)
        
        # 检查是否收集了指标
        assert len(system_monitor.metrics_history) > 0
        
        # 停止监控
        await system_monitor.stop_monitoring()
        assert system_monitor.is_running is False


class TestAlertThreshold:
    """告警阈值测试"""
    
    def test_alert_threshold_creation(self):
        """测试告警阈值创建"""
        threshold = AlertThreshold(
            metric_name="cpu_percent",
            warning_threshold=80.0,
            critical_threshold=90.0,
            duration_minutes=5,
            enabled=True
        )
        
        assert threshold.metric_name == "cpu_percent"
        assert threshold.warning_threshold == 80.0
        assert threshold.critical_threshold == 90.0
        assert threshold.duration_minutes == 5
        assert threshold.enabled is True


@pytest.mark.asyncio
async def test_complete_monitoring_flow():
    """测试完整监控流程"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建系统监控器
        config_file = os.path.join(temp_dir, "test_config.json")
        monitor = SystemMonitor(config_file, temp_dir)
        
        # 启动监控
        await monitor.start_monitoring()
        
        # 等待收集一些数据
        await asyncio.sleep(3)
        
        # 检查系统状态
        status = monitor.get_system_status()
        assert status["latest_metrics"] is not None
        
        # 获取指标历史
        history = monitor.get_metrics_history(hours=1)
        assert len(history) > 0
        
        # 停止监控
        await monitor.stop_monitoring()


def test_log_manager_integration():
    """测试日志管理器集成"""
    with tempfile.TemporaryDirectory() as temp_dir:
        log_manager = LogManager(temp_dir)
        
        # 记录不同类型的日志
        log_manager.info(LogCategory.SYSTEM, "系统启动")
        log_manager.warning(LogCategory.MONITORING, "CPU使用率较高")
        log_manager.error(LogCategory.ERROR, "服务连接失败")
        log_manager.debug(LogCategory.AUTH, "用户登录调试信息")
        
        # 等待异步处理
        import time
        time.sleep(2)
        
        # 检查统计
        stats = log_manager.get_log_statistics()
        assert stats["stats"]["total_logs"] >= 4
        assert stats["stats"]["by_category"]["system"] >= 1
        assert stats["stats"]["by_category"]["monitoring"] >= 1
        assert stats["stats"]["by_category"]["error"] >= 1
        
        # 搜索日志
        all_logs = log_manager.search_logs(limit=100)
        assert len(all_logs) >= 4
        
        # 按级别过滤
        error_logs = log_manager.search_logs(level=LogLevel.ERROR, limit=100)
        assert len(error_logs) >= 1
        
        # 关键词搜索
        cpu_logs = log_manager.search_logs(keyword="CPU", limit=100)
        assert len(cpu_logs) >= 1
