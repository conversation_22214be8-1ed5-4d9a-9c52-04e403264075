#!/usr/bin/env python3
"""
系统安装脚本

自动安装和配置Moniit系统
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path


class SystemInstaller:
    """系统安装器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / "venv"
        self.data_dir = self.project_root / "data"
        self.logs_dir = self.project_root / "logs"
        self.config_dir = self.project_root / "config"
    
    def install(self):
        """执行完整安装"""
        try:
            print("🚀 开始安装Moniit系统...")
            
            # 检查Python版本
            self.check_python_version()
            
            # 创建目录结构
            self.create_directories()
            
            # 创建虚拟环境
            self.create_virtual_environment()
            
            # 安装依赖
            self.install_dependencies()
            
            # 创建配置文件
            self.create_config_files()
            
            # 初始化数据库
            self.initialize_database()
            
            # 设置权限
            self.set_permissions()
            
            # 创建启动脚本
            self.create_startup_scripts()
            
            print("✅ Moniit系统安装完成!")
            self.show_next_steps()
            
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            sys.exit(1)
    
    def check_python_version(self):
        """检查Python版本"""
        try:
            version = sys.version_info
            if version.major < 3 or (version.major == 3 and version.minor < 8):
                raise Exception("需要Python 3.8或更高版本")
            
            print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
            
        except Exception as e:
            print(f"❌ Python版本检查失败: {e}")
            raise
    
    def create_directories(self):
        """创建目录结构"""
        try:
            print("📁 创建目录结构...")
            
            directories = [
                self.data_dir,
                self.logs_dir,
                self.config_dir,
                self.data_dir / "auth_data",
                self.data_dir / "auth_data" / "users",
                self.data_dir / "auth_data" / "sessions",
                self.data_dir / "translation_data",
                self.data_dir / "monitor_data",
                self.data_dir / "audit_logs",
                self.logs_dir / "app",
                self.logs_dir / "system",
                self.logs_dir / "auth",
                self.logs_dir / "translation",
                self.logs_dir / "monitoring"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
                print(f"  📂 {directory}")
            
            print("✅ 目录结构创建完成")
            
        except Exception as e:
            print(f"❌ 创建目录结构失败: {e}")
            raise
    
    def create_virtual_environment(self):
        """创建虚拟环境"""
        try:
            if self.venv_path.exists():
                print("⚠️  虚拟环境已存在，跳过创建")
                return
            
            print("🐍 创建Python虚拟环境...")
            
            subprocess.run([
                sys.executable, "-m", "venv", str(self.venv_path)
            ], check=True)
            
            print("✅ 虚拟环境创建完成")
            
        except Exception as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            raise
    
    def install_dependencies(self):
        """安装依赖包"""
        try:
            print("📦 安装Python依赖包...")
            
            # 确定pip路径
            if os.name == 'nt':  # Windows
                pip_path = self.venv_path / "Scripts" / "pip.exe"
            else:  # Unix/Linux/macOS
                pip_path = self.venv_path / "bin" / "pip"
            
            # 升级pip
            subprocess.run([
                str(pip_path), "install", "--upgrade", "pip"
            ], check=True)
            
            # 安装基础依赖
            dependencies = [
                "fastapi>=0.104.0",
                "uvicorn[standard]>=0.24.0",
                "pydantic>=2.5.0",
                "sqlalchemy>=2.0.0",
                "alembic>=1.13.0",
                "redis>=5.0.0",
                "psutil>=5.9.0",
                "PyJWT>=2.8.0",
                "passlib[bcrypt]>=1.7.4",
                "python-multipart>=0.0.6",
                "aiofiles>=23.2.1",
                "httpx>=0.25.0",
                "pytest>=7.4.0",
                "pytest-asyncio>=0.21.0"
            ]
            
            for dep in dependencies:
                print(f"  📦 安装 {dep}")
                subprocess.run([
                    str(pip_path), "install", dep
                ], check=True)
            
            print("✅ 依赖包安装完成")
            
        except Exception as e:
            print(f"❌ 安装依赖包失败: {e}")
            raise
    
    def create_config_files(self):
        """创建配置文件"""
        try:
            print("⚙️  创建配置文件...")
            
            # 主配置文件
            main_config = {
                "app": {
                    "name": "Moniit",
                    "version": "1.0.0",
                    "debug": False,
                    "host": "0.0.0.0",
                    "port": 8000
                },
                "database": {
                    "url": "sqlite:///./data/moniit.db",
                    "echo": False
                },
                "redis": {
                    "host": "localhost",
                    "port": 6379,
                    "db": 0
                },
                "auth": {
                    "secret_key": "your-secret-key-change-this",
                    "access_token_expire_hours": 1,
                    "refresh_token_expire_days": 7
                },
                "logging": {
                    "level": "INFO",
                    "file": "./logs/app/moniit.log",
                    "max_size_mb": 100,
                    "backup_count": 10
                }
            }
            
            config_file = self.config_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(main_config, f, ensure_ascii=False, indent=2)
            print(f"  ⚙️  {config_file}")
            
            # 监控配置文件
            monitor_config = {
                "monitor_config": {
                    "enabled": True,
                    "check_interval_seconds": 60,
                    "metrics_retention_hours": 24,
                    "alert_cooldown_minutes": 15,
                    "auto_restart_services": True,
                    "max_restart_attempts": 3,
                    "notification_enabled": True
                },
                "alert_thresholds": [
                    {
                        "metric_name": "cpu_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "memory_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "disk_percent",
                        "warning_threshold": 85.0,
                        "critical_threshold": 95.0,
                        "duration_minutes": 10,
                        "enabled": True
                    }
                ],
                "services": {
                    "moniit_api": {
                        "command": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000",
                        "working_dir": ".",
                        "auto_restart": True,
                        "max_restart_attempts": 3,
                        "restart_delay_seconds": 30,
                        "health_check_url": "http://localhost:8000/health",
                        "health_check_interval": 60
                    }
                }
            }
            
            monitor_config_file = self.project_root / "monitor_config.json"
            with open(monitor_config_file, 'w', encoding='utf-8') as f:
                json.dump(monitor_config, f, ensure_ascii=False, indent=2)
            print(f"  ⚙️  {monitor_config_file}")
            
            # 服务配置文件
            services_config = {
                "api": {
                    "name": "Moniit API服务",
                    "command": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000",
                    "working_dir": ".",
                    "env": {},
                    "auto_restart": True,
                    "health_check": "http://localhost:8000/health",
                    "description": "主API服务"
                }
            }
            
            services_config_file = self.project_root / "services.json"
            with open(services_config_file, 'w', encoding='utf-8') as f:
                json.dump(services_config, f, ensure_ascii=False, indent=2)
            print(f"  ⚙️  {services_config_file}")
            
            print("✅ 配置文件创建完成")
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            raise
    
    def initialize_database(self):
        """初始化数据库"""
        try:
            print("🗄️  初始化数据库...")
            
            # 创建数据库文件
            db_file = self.data_dir / "moniit.db"
            if not db_file.exists():
                db_file.touch()
                print(f"  🗄️  创建数据库文件: {db_file}")
            
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 初始化数据库失败: {e}")
            raise
    
    def set_permissions(self):
        """设置文件权限"""
        try:
            if os.name == 'nt':  # Windows
                print("⚠️  Windows系统，跳过权限设置")
                return
            
            print("🔐 设置文件权限...")
            
            # 设置脚本可执行权限
            scripts = [
                self.project_root / "scripts" / "monitor.py",
                self.project_root / "scripts" / "service_manager.py",
                self.project_root / "scripts" / "install.py"
            ]
            
            for script in scripts:
                if script.exists():
                    os.chmod(script, 0o755)
                    print(f"  🔐 {script}")
            
            print("✅ 文件权限设置完成")
            
        except Exception as e:
            print(f"❌ 设置文件权限失败: {e}")
            raise
    
    def create_startup_scripts(self):
        """创建启动脚本"""
        try:
            print("📜 创建启动脚本...")
            
            # Unix/Linux启动脚本
            if os.name != 'nt':
                start_script = self.project_root / "start.sh"
                with open(start_script, 'w') as f:
                    f.write(f"""#!/bin/bash
# Moniit系统启动脚本

cd {self.project_root}

# 激活虚拟环境
source venv/bin/activate

# 启动服务
echo "🚀 启动Moniit系统..."
python scripts/service_manager.py start

echo "✅ Moniit系统已启动"
echo "🌐 访问地址: http://localhost:8000"
""")
                os.chmod(start_script, 0o755)
                print(f"  📜 {start_script}")
                
                # 停止脚本
                stop_script = self.project_root / "stop.sh"
                with open(stop_script, 'w') as f:
                    f.write(f"""#!/bin/bash
# Moniit系统停止脚本

cd {self.project_root}

# 激活虚拟环境
source venv/bin/activate

# 停止服务
echo "🛑 停止Moniit系统..."
python scripts/service_manager.py stop

echo "✅ Moniit系统已停止"
""")
                os.chmod(stop_script, 0o755)
                print(f"  📜 {stop_script}")
            
            # Windows启动脚本
            else:
                start_script = self.project_root / "start.bat"
                with open(start_script, 'w') as f:
                    f.write(f"""@echo off
REM Moniit系统启动脚本

cd /d {self.project_root}

REM 激活虚拟环境
call venv\\Scripts\\activate.bat

REM 启动服务
echo 🚀 启动Moniit系统...
python scripts\\service_manager.py start

echo ✅ Moniit系统已启动
echo 🌐 访问地址: http://localhost:8000
pause
""")
                print(f"  📜 {start_script}")
                
                # 停止脚本
                stop_script = self.project_root / "stop.bat"
                with open(stop_script, 'w') as f:
                    f.write(f"""@echo off
REM Moniit系统停止脚本

cd /d {self.project_root}

REM 激活虚拟环境
call venv\\Scripts\\activate.bat

REM 停止服务
echo 🛑 停止Moniit系统...
python scripts\\service_manager.py stop

echo ✅ Moniit系统已停止
pause
""")
                print(f"  📜 {stop_script}")
            
            print("✅ 启动脚本创建完成")
            
        except Exception as e:
            print(f"❌ 创建启动脚本失败: {e}")
            raise
    
    def show_next_steps(self):
        """显示后续步骤"""
        print("\n" + "="*60)
        print("🎉 安装完成！后续步骤:")
        print("="*60)
        
        if os.name != 'nt':
            print("1. 启动系统:")
            print("   ./start.sh")
            print("\n2. 停止系统:")
            print("   ./stop.sh")
            print("\n3. 查看服务状态:")
            print("   python scripts/service_manager.py status")
            print("\n4. 查看系统监控:")
            print("   python scripts/monitor.py status")
        else:
            print("1. 启动系统:")
            print("   start.bat")
            print("\n2. 停止系统:")
            print("   stop.bat")
            print("\n3. 查看服务状态:")
            print("   python scripts\\service_manager.py status")
            print("\n4. 查看系统监控:")
            print("   python scripts\\monitor.py status")
        
        print("\n5. 访问Web界面:")
        print("   http://localhost:8000")
        
        print("\n6. 默认管理员账户:")
        print("   用户名: admin")
        print("   密码: 首次启动时会自动生成并显示")
        
        print("\n📚 更多信息请查看项目文档")
        print("="*60)


def main():
    """主函数"""
    try:
        installer = SystemInstaller()
        installer.install()
        
    except KeyboardInterrupt:
        print("\n⏹️  安装已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
