#!/usr/bin/env python3
"""
简单的API测试脚本

测试基本的API功能
"""

import asyncio
import httpx
import json


async def test_basic_api():
    """测试基本API功能"""
    print("🧪 测试基本API功能...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=10.0) as client:
        try:
            # 1. 测试健康检查
            print("  ❤️ 测试健康检查...")
            response = await client.get("/health")
            if response.status_code == 200:
                print(f"    ✅ 健康检查成功 - 状态码: {response.status_code}")
            else:
                print(f"    ❌ 健康检查失败 - 状态码: {response.status_code}")
            
            # 2. 测试API文档
            print("  📚 测试API文档...")
            response = await client.get("/docs")
            if response.status_code == 200:
                print(f"    ✅ API文档访问成功 - 状态码: {response.status_code}")
            else:
                print(f"    ❌ API文档访问失败 - 状态码: {response.status_code}")
            
            # 3. 测试供货商API
            print("  🏪 测试供货商API...")
            response = await client.get("/api/v1/suppliers/")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 供货商API成功 - 状态码: {response.status_code}")
                print(f"    📊 返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"    ❌ 供货商API失败 - 状态码: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"    错误内容: {response.text}")
            
            # 4. 测试系统日志API
            print("  📋 测试系统日志API...")
            response = await client.get("/api/v1/system/logs?lines=5")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 系统日志API成功 - 状态码: {response.status_code}")
                print(f"    📝 日志数量: {data.get('returned_lines', 0)}")
            else:
                print(f"    ❌ 系统日志API失败 - 状态码: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"    错误内容: {response.text}")
                    
        except Exception as e:
            print(f"    ❌ API测试异常: {str(e)}")


async def main():
    """主测试函数"""
    print("🚀 开始简单API测试...\n")
    
    await test_basic_api()
    
    print("\n✅ 简单API测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
