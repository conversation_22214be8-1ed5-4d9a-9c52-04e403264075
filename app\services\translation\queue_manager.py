"""
优先级队列管理器

建立翻译队列和优先级管理，重要商品优先翻译，普通商品排队处理
"""

import asyncio
import heapq
import uuid
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time

from app.core.logging import get_logger
from app.services.translation.translation_engine import (
    TranslationEngine, TranslationRequest, LanguageCode, TextType
)

logger = get_logger(__name__)


class QueuePriority(Enum):
    """队列优先级"""
    LOW = 1         # 低优先级
    NORMAL = 2      # 普通优先级
    HIGH = 3        # 高优先级
    URGENT = 4      # 紧急优先级
    CRITICAL = 5    # 关键优先级


class QueueStatus(Enum):
    """队列状态"""
    ACTIVE = "active"       # 活跃
    PAUSED = "paused"       # 暂停
    STOPPED = "stopped"     # 停止


@dataclass
class QueueItem:
    """队列项目"""
    item_id: str
    request: TranslationRequest
    priority: QueuePriority
    created_at: datetime = field(default_factory=datetime.now)
    scheduled_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """优先级比较（用于堆排序）"""
        # 优先级高的排在前面，时间早的排在前面
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.created_at < other.created_at


@dataclass
class QueueConfig:
    """队列配置"""
    name: str
    max_size: int = 10000
    max_concurrent: int = 5
    retry_enabled: bool = True
    retry_delay_seconds: int = 60
    priority_boost_enabled: bool = True
    priority_boost_threshold_minutes: int = 30
    auto_scaling_enabled: bool = True
    min_workers: int = 1
    max_workers: int = 10


@dataclass
class QueueStats:
    """队列统计"""
    total_items: int = 0
    pending_items: int = 0
    processing_items: int = 0
    completed_items: int = 0
    failed_items: int = 0
    retried_items: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    throughput_per_minute: float = 0.0
    last_processed_at: Optional[datetime] = None


class PriorityQueueManager:
    """优先级队列管理器"""
    
    def __init__(self, translation_engine: TranslationEngine):
        self.translation_engine = translation_engine
        
        # 队列存储
        self.queues: Dict[str, List[QueueItem]] = {}
        self.queue_configs: Dict[str, QueueConfig] = {}
        self.queue_stats: Dict[str, QueueStats] = {}
        self.queue_status: Dict[str, QueueStatus] = {}
        
        # 工作器管理
        self.workers: Dict[str, List[asyncio.Task]] = {}
        self.worker_semaphores: Dict[str, asyncio.Semaphore] = {}
        
        # 全局配置
        self.global_config = {
            "default_queue": "default",
            "auto_create_queues": True,
            "queue_monitoring_enabled": True,
            "monitoring_interval": 30,
            "priority_aging_enabled": True,
            "aging_interval": 300,  # 5分钟
            "load_balancing_enabled": True
        }
        
        # 监控任务
        self.monitoring_task: Optional[asyncio.Task] = None
        self.aging_task: Optional[asyncio.Task] = None
        
        # 创建默认队列
        self.create_queue("default")
        
        # 启动监控
        self._start_monitoring()
    
    def create_queue(self, queue_name: str, config: Optional[QueueConfig] = None) -> bool:
        """
        创建队列
        
        Args:
            queue_name: 队列名称
            config: 队列配置
        
        Returns:
            bool: 是否创建成功
        """
        try:
            if queue_name in self.queues:
                logger.warning(f"队列已存在: {queue_name}")
                return False
            
            # 使用默认配置或提供的配置
            if config is None:
                config = QueueConfig(name=queue_name)
            
            # 初始化队列
            self.queues[queue_name] = []
            self.queue_configs[queue_name] = config
            self.queue_stats[queue_name] = QueueStats()
            self.queue_status[queue_name] = QueueStatus.ACTIVE
            self.workers[queue_name] = []
            self.worker_semaphores[queue_name] = asyncio.Semaphore(config.max_concurrent)
            
            # 启动工作器
            self._start_workers(queue_name)
            
            logger.info(f"创建队列成功: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建队列失败: {queue_name}, {e}")
            return False
    
    def delete_queue(self, queue_name: str, force: bool = False) -> bool:
        """
        删除队列
        
        Args:
            queue_name: 队列名称
            force: 是否强制删除（即使有待处理项目）
        
        Returns:
            bool: 是否删除成功
        """
        try:
            if queue_name not in self.queues:
                logger.warning(f"队列不存在: {queue_name}")
                return False
            
            if queue_name == self.global_config["default_queue"]:
                logger.error(f"不能删除默认队列: {queue_name}")
                return False
            
            # 检查是否有待处理项目
            if not force and len(self.queues[queue_name]) > 0:
                logger.error(f"队列中还有待处理项目，无法删除: {queue_name}")
                return False
            
            # 停止工作器
            self._stop_workers(queue_name)
            
            # 删除队列数据
            del self.queues[queue_name]
            del self.queue_configs[queue_name]
            del self.queue_stats[queue_name]
            del self.queue_status[queue_name]
            del self.workers[queue_name]
            del self.worker_semaphores[queue_name]
            
            logger.info(f"删除队列成功: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除队列失败: {queue_name}, {e}")
            return False
    
    async def enqueue(self, request: TranslationRequest, 
                     priority: QueuePriority = QueuePriority.NORMAL,
                     queue_name: Optional[str] = None,
                     scheduled_at: Optional[datetime] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        添加翻译请求到队列
        
        Args:
            request: 翻译请求
            priority: 优先级
            queue_name: 队列名称
            scheduled_at: 计划执行时间
            metadata: 元数据
        
        Returns:
            str: 队列项目ID
        """
        try:
            # 确定队列名称
            if queue_name is None:
                queue_name = self._select_queue(request, priority)
            
            # 自动创建队列
            if (queue_name not in self.queues and 
                self.global_config["auto_create_queues"]):
                self.create_queue(queue_name)
            
            if queue_name not in self.queues:
                raise ValueError(f"队列不存在: {queue_name}")
            
            # 检查队列大小限制
            config = self.queue_configs[queue_name]
            if len(self.queues[queue_name]) >= config.max_size:
                raise ValueError(f"队列已满: {queue_name}")
            
            # 创建队列项目
            item_id = str(uuid.uuid4())
            queue_item = QueueItem(
                item_id=item_id,
                request=request,
                priority=priority,
                scheduled_at=scheduled_at,
                metadata=metadata or {}
            )
            
            # 添加到队列（使用堆排序）
            heapq.heappush(self.queues[queue_name], queue_item)
            
            # 更新统计
            stats = self.queue_stats[queue_name]
            stats.total_items += 1
            stats.pending_items += 1
            
            logger.info(f"添加翻译请求到队列: {queue_name}, 项目ID: {item_id}, 优先级: {priority.name}")
            return item_id
            
        except Exception as e:
            logger.error(f"添加翻译请求到队列失败: {e}")
            raise
    
    def _select_queue(self, request: TranslationRequest, priority: QueuePriority) -> str:
        """选择合适的队列"""
        # 简单的负载均衡策略
        if self.global_config["load_balancing_enabled"]:
            # 选择待处理项目最少的队列
            min_queue = min(self.queues.keys(), 
                          key=lambda q: len(self.queues[q]) if self.queue_status[q] == QueueStatus.ACTIVE else float('inf'))
            return min_queue
        
        return self.global_config["default_queue"]
    
    def _start_workers(self, queue_name: str):
        """启动队列工作器"""
        try:
            config = self.queue_configs[queue_name]
            
            # 启动最小数量的工作器
            for i in range(config.min_workers):
                worker_task = asyncio.create_task(
                    self._worker_loop(queue_name, f"worker_{i}")
                )
                self.workers[queue_name].append(worker_task)
            
            logger.info(f"启动队列工作器: {queue_name}, 数量: {config.min_workers}")
            
        except RuntimeError:
            # 没有运行的事件循环，跳过启动工作器
            logger.warning(f"没有运行的事件循环，跳过启动工作器: {queue_name}")
        except Exception as e:
            logger.error(f"启动队列工作器失败: {queue_name}, {e}")
    
    def _stop_workers(self, queue_name: str):
        """停止队列工作器"""
        try:
            if queue_name in self.workers:
                for worker in self.workers[queue_name]:
                    worker.cancel()
                self.workers[queue_name].clear()
            
            logger.info(f"停止队列工作器: {queue_name}")
            
        except Exception as e:
            logger.error(f"停止队列工作器失败: {queue_name}, {e}")
    
    async def _worker_loop(self, queue_name: str, worker_id: str):
        """工作器循环"""
        logger.info(f"启动工作器: {queue_name}/{worker_id}")
        
        while True:
            try:
                # 检查队列状态
                if self.queue_status[queue_name] != QueueStatus.ACTIVE:
                    await asyncio.sleep(1)
                    continue
                
                # 获取下一个项目
                queue_item = await self._get_next_item(queue_name)
                if queue_item is None:
                    await asyncio.sleep(1)
                    continue
                
                # 处理项目
                await self._process_queue_item(queue_name, queue_item, worker_id)
                
            except asyncio.CancelledError:
                logger.info(f"工作器被取消: {queue_name}/{worker_id}")
                break
            except Exception as e:
                logger.error(f"工作器异常: {queue_name}/{worker_id}, {e}")
                await asyncio.sleep(5)  # 错误后等待5秒
    
    async def _get_next_item(self, queue_name: str) -> Optional[QueueItem]:
        """获取下一个待处理项目"""
        try:
            queue = self.queues[queue_name]
            
            while queue:
                # 获取优先级最高的项目
                queue_item = heapq.heappop(queue)
                
                # 检查是否到了计划执行时间
                if (queue_item.scheduled_at and 
                    datetime.now() < queue_item.scheduled_at):
                    # 重新放回队列
                    heapq.heappush(queue, queue_item)
                    return None
                
                return queue_item
            
            return None
            
        except Exception as e:
            logger.error(f"获取下一个项目失败: {queue_name}, {e}")
            return None
    
    async def _process_queue_item(self, queue_name: str, queue_item: QueueItem, worker_id: str):
        """处理队列项目"""
        semaphore = self.worker_semaphores[queue_name]
        stats = self.queue_stats[queue_name]
        
        async with semaphore:
            try:
                # 更新统计
                stats.pending_items -= 1
                stats.processing_items += 1
                
                logger.info(f"开始处理翻译请求: {queue_name}/{worker_id}, 项目ID: {queue_item.item_id}")
                
                # 执行翻译
                start_time = time.time()
                result = await self.translation_engine.translate(queue_item.request)
                processing_time = time.time() - start_time
                
                # 更新统计
                stats.processing_items -= 1
                stats.completed_items += 1
                stats.total_processing_time += processing_time
                stats.average_processing_time = (stats.total_processing_time / 
                                               max(stats.completed_items, 1))
                stats.last_processed_at = datetime.now()
                
                logger.info(f"翻译请求处理完成: {queue_name}/{worker_id}, "
                           f"项目ID: {queue_item.item_id}, 耗时: {processing_time:.2f}秒")
                
            except Exception as e:
                logger.error(f"处理翻译请求失败: {queue_name}/{worker_id}, "
                           f"项目ID: {queue_item.item_id}, {e}")
                
                # 更新统计
                stats.processing_items -= 1
                
                # 重试逻辑
                config = self.queue_configs[queue_name]
                if (config.retry_enabled and 
                    queue_item.retry_count < queue_item.max_retries):
                    
                    queue_item.retry_count += 1
                    queue_item.scheduled_at = datetime.now() + timedelta(
                        seconds=config.retry_delay_seconds
                    )
                    
                    # 重新加入队列
                    heapq.heappush(self.queues[queue_name], queue_item)
                    stats.pending_items += 1
                    stats.retried_items += 1
                    
                    logger.info(f"翻译请求重试: {queue_name}, 项目ID: {queue_item.item_id}, "
                               f"重试次数: {queue_item.retry_count}")
                else:
                    stats.failed_items += 1
                    logger.error(f"翻译请求最终失败: {queue_name}, 项目ID: {queue_item.item_id}")
    
    def pause_queue(self, queue_name: str) -> bool:
        """暂停队列"""
        try:
            if queue_name not in self.queues:
                return False
            
            self.queue_status[queue_name] = QueueStatus.PAUSED
            logger.info(f"暂停队列: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"暂停队列失败: {queue_name}, {e}")
            return False
    
    def resume_queue(self, queue_name: str) -> bool:
        """恢复队列"""
        try:
            if queue_name not in self.queues:
                return False
            
            self.queue_status[queue_name] = QueueStatus.ACTIVE
            logger.info(f"恢复队列: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"恢复队列失败: {queue_name}, {e}")
            return False
    
    def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """获取队列信息"""
        try:
            if queue_name not in self.queues:
                return None
            
            config = self.queue_configs[queue_name]
            stats = self.queue_stats[queue_name]
            status = self.queue_status[queue_name]
            
            return {
                "name": queue_name,
                "status": status.value,
                "config": {
                    "max_size": config.max_size,
                    "max_concurrent": config.max_concurrent,
                    "retry_enabled": config.retry_enabled,
                    "retry_delay_seconds": config.retry_delay_seconds,
                    "min_workers": config.min_workers,
                    "max_workers": config.max_workers
                },
                "stats": {
                    "total_items": stats.total_items,
                    "pending_items": stats.pending_items,
                    "processing_items": stats.processing_items,
                    "completed_items": stats.completed_items,
                    "failed_items": stats.failed_items,
                    "retried_items": stats.retried_items,
                    "success_rate": (stats.completed_items / max(stats.total_items, 1) * 100),
                    "average_processing_time": stats.average_processing_time,
                    "throughput_per_minute": stats.throughput_per_minute,
                    "last_processed_at": stats.last_processed_at.isoformat() if stats.last_processed_at else None
                },
                "workers": {
                    "active_workers": len(self.workers[queue_name]),
                    "max_concurrent": config.max_concurrent
                }
            }
            
        except Exception as e:
            logger.error(f"获取队列信息失败: {queue_name}, {e}")
            return None
    
    def get_all_queues_info(self) -> Dict[str, Any]:
        """获取所有队列信息"""
        try:
            queues_info = {}
            
            for queue_name in self.queues.keys():
                queue_info = self.get_queue_info(queue_name)
                if queue_info:
                    queues_info[queue_name] = queue_info
            
            # 全局统计
            total_stats = {
                "total_queues": len(self.queues),
                "active_queues": len([q for q, s in self.queue_status.items() 
                                    if s == QueueStatus.ACTIVE]),
                "total_pending_items": sum(stats.pending_items for stats in self.queue_stats.values()),
                "total_processing_items": sum(stats.processing_items for stats in self.queue_stats.values()),
                "total_completed_items": sum(stats.completed_items for stats in self.queue_stats.values()),
                "total_failed_items": sum(stats.failed_items for stats in self.queue_stats.values()),
                "global_success_rate": 0.0
            }
            
            total_items = sum(stats.total_items for stats in self.queue_stats.values())
            if total_items > 0:
                total_stats["global_success_rate"] = (
                    total_stats["total_completed_items"] / total_items * 100
                )
            
            return {
                "queues": queues_info,
                "global_stats": total_stats,
                "config": self.global_config
            }
            
        except Exception as e:
            logger.error(f"获取所有队列信息失败: {e}")
            return {"queues": {}, "global_stats": {}, "config": {}}
    
    def _start_monitoring(self):
        """启动监控任务"""
        try:
            if self.global_config["queue_monitoring_enabled"]:
                self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            if self.global_config["priority_aging_enabled"]:
                self.aging_task = asyncio.create_task(self._aging_loop())
                
        except RuntimeError:
            # 没有运行的事件循环，跳过启动监控
            logger.warning("没有运行的事件循环，跳过启动监控")
        except Exception as e:
            logger.error(f"启动监控任务失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                await asyncio.sleep(self.global_config["monitoring_interval"])
                await self._update_throughput()
                await self._auto_scale_workers()
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
    
    async def _aging_loop(self):
        """优先级老化循环"""
        while True:
            try:
                await asyncio.sleep(self.global_config["aging_interval"])
                await self._age_priorities()
            except Exception as e:
                logger.error(f"优先级老化循环异常: {e}")
    
    async def _update_throughput(self):
        """更新吞吐量统计"""
        for queue_name, stats in self.queue_stats.items():
            if stats.last_processed_at:
                # 计算最近一分钟的吞吐量（简化实现）
                stats.throughput_per_minute = stats.completed_items / max(
                    (datetime.now() - stats.last_processed_at).total_seconds() / 60, 1
                )
    
    async def _auto_scale_workers(self):
        """自动扩缩容工作器"""
        for queue_name in self.queues.keys():
            config = self.queue_configs[queue_name]
            if not config.auto_scaling_enabled:
                continue
            
            stats = self.queue_stats[queue_name]
            current_workers = len(self.workers[queue_name])
            
            # 简单的扩缩容策略
            if stats.pending_items > current_workers * 2 and current_workers < config.max_workers:
                # 扩容
                worker_task = asyncio.create_task(
                    self._worker_loop(queue_name, f"worker_{current_workers}")
                )
                self.workers[queue_name].append(worker_task)
                logger.info(f"自动扩容工作器: {queue_name}, 新数量: {current_workers + 1}")
            
            elif stats.pending_items < current_workers // 2 and current_workers > config.min_workers:
                # 缩容
                worker = self.workers[queue_name].pop()
                worker.cancel()
                logger.info(f"自动缩容工作器: {queue_name}, 新数量: {current_workers - 1}")
    
    async def _age_priorities(self):
        """优先级老化处理"""
        for queue_name in self.queues.keys():
            config = self.queue_configs[queue_name]
            if not config.priority_boost_enabled:
                continue
            
            queue = self.queues[queue_name]
            threshold = datetime.now() - timedelta(minutes=config.priority_boost_threshold_minutes)
            
            # 提升等待时间过长的项目优先级
            for item in queue:
                if (item.created_at < threshold and 
                    item.priority.value < QueuePriority.CRITICAL.value):
                    old_priority = item.priority
                    item.priority = QueuePriority(min(item.priority.value + 1, QueuePriority.CRITICAL.value))
                    logger.info(f"优先级老化提升: {queue_name}, 项目ID: {item.item_id}, "
                               f"{old_priority.name} -> {item.priority.name}")
            
            # 重新排序队列
            heapq.heapify(queue)
    
    def stop_monitoring(self):
        """停止监控任务"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None
        
        if self.aging_task:
            self.aging_task.cancel()
            self.aging_task = None
