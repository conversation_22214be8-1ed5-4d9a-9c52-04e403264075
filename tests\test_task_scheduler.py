"""
任务调度系统测试

测试Celery任务调度、监控和管理功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.services.task_scheduler_service import (
    TaskSchedulerService, ScheduledTask, TaskBatch, TaskStatus
)


class TestTaskSchedulerService:
    """任务调度服务测试"""
    
    @pytest.fixture
    def scheduler_service(self):
        """创建任务调度服务实例"""
        return TaskSchedulerService()
    
    @pytest.fixture
    def mock_celery_result(self):
        """模拟Celery任务结果"""
        mock_result = Mock()
        mock_result.id = "test-task-123"
        mock_result.state = "PENDING"
        mock_result.result = None
        mock_result.info = None
        return mock_result
    
    def test_submit_single_crawl_task(self, scheduler_service, mock_celery_result):
        """测试提交单个爬取任务"""
        with patch('app.tasks.crawl_tasks.submit_crawl_batch.apply_async') as mock_apply:
            mock_apply.return_value = mock_celery_result
            
            task_id = scheduler_service.submit_single_crawl_task(
                urls=["https://example.com/product1"],
                platform="1688",
                product_type="competitor",
                priority="high"
            )
            
            assert task_id == "test-task-123"
            assert task_id in scheduler_service.active_tasks
            
            scheduled_task = scheduler_service.active_tasks[task_id]
            assert scheduled_task.task_name == "crawl_1688_competitor"
            assert scheduled_task.status == TaskStatus.PENDING
            
            # 验证调用参数
            mock_apply.assert_called_once()
            args, kwargs = mock_apply.call_args
            assert kwargs['queue'] == 'crawl.high'
            assert kwargs['priority'] == 9
    
    def test_submit_batch_crawl_tasks(self, scheduler_service, mock_celery_result):
        """测试提交批量爬取任务"""
        with patch('app.tasks.crawl_tasks.batch_crawl_with_strategy.apply_async') as mock_apply:
            mock_apply.return_value = mock_celery_result
            
            task_configs = [
                {
                    "urls": ["https://example.com/product1"],
                    "platform": "1688",
                    "product_type": "competitor",
                    "priority": "high"
                },
                {
                    "urls": ["https://example.com/product2"],
                    "platform": "taobao",
                    "product_type": "supplier",
                    "priority": "medium"
                }
            ]
            
            batch_id = scheduler_service.submit_batch_crawl_tasks(
                task_configs=task_configs,
                strategy="batch_optimize"
            )
            
            assert batch_id.startswith("batch_")
            assert batch_id in scheduler_service.task_batches
            
            task_batch = scheduler_service.task_batches[batch_id]
            assert task_batch.batch_type == "crawl_batch"
            assert task_batch.status == TaskStatus.PENDING
            assert len(task_batch.task_ids) == 1  # 批量任务作为一个任务提交
            
            mock_apply.assert_called_once()
    
    def test_submit_analysis_workflow(self, scheduler_service):
        """测试提交分析工作流"""
        with patch('app.services.task_scheduler_service.chain') as mock_chain:
            mock_workflow = Mock()
            mock_workflow.apply_async.return_value = Mock(id="workflow-123")
            mock_chain.return_value = mock_workflow

            workflow_id = scheduler_service.submit_analysis_workflow(
                crawl_task_ids=["task1", "task2", "task3"]
            )

            assert workflow_id.startswith("workflow_")
            assert "workflow-123" in scheduler_service.active_tasks

            scheduled_task = scheduler_service.active_tasks["workflow-123"]
            assert scheduled_task.task_name == "analysis_workflow"
            assert scheduled_task.status == TaskStatus.PENDING
    
    def test_submit_parallel_analysis(self, scheduler_service):
        """测试提交并行分析任务"""
        with patch('app.services.task_scheduler_service.group') as mock_group:
            mock_group_result = Mock()
            mock_group_result.children = [Mock(id="child1"), Mock(id="child2")]
            mock_group.return_value.apply_async.return_value = mock_group_result

            task_groups = [
                ["task1", "task2"],
                ["task3", "task4"]
            ]

            group_id = scheduler_service.submit_parallel_analysis(task_groups)

            assert group_id.startswith("parallel_")
            assert group_id in scheduler_service.task_batches

            task_batch = scheduler_service.task_batches[group_id]
            assert task_batch.batch_type == "parallel_analysis"
            assert len(task_batch.task_ids) == 2
    
    def test_get_task_status_pending(self, scheduler_service):
        """测试获取待处理任务状态"""
        # 创建测试任务
        task_id = "test-task-456"
        scheduled_task = ScheduledTask(
            task_id=task_id,
            task_name="test_task",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch('app.services.task_scheduler_service.AsyncResult') as mock_async_result:
            mock_result = Mock()
            mock_result.state = "PENDING"
            mock_async_result.return_value = mock_result
            
            task_status = scheduler_service.get_task_status(task_id)
            
            assert task_status is not None
            assert task_status.task_id == task_id
            assert task_status.status == TaskStatus.PENDING
    
    def test_get_task_status_success(self, scheduler_service):
        """测试获取成功任务状态"""
        task_id = "test-task-789"
        scheduled_task = ScheduledTask(
            task_id=task_id,
            task_name="test_task",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch('app.services.task_scheduler_service.AsyncResult') as mock_async_result:
            mock_result = Mock()
            mock_result.state = "SUCCESS"
            mock_result.result = {"success": True, "data": "test_data"}
            mock_async_result.return_value = mock_result
            
            task_status = scheduler_service.get_task_status(task_id)
            
            assert task_status.status == TaskStatus.SUCCESS
            assert task_status.result == {"success": True, "data": "test_data"}
            assert task_status.completed_at is not None
    
    def test_get_task_status_failure(self, scheduler_service):
        """测试获取失败任务状态"""
        task_id = "test-task-fail"
        scheduled_task = ScheduledTask(
            task_id=task_id,
            task_name="test_task",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch('app.services.task_scheduler_service.AsyncResult') as mock_async_result:
            mock_result = Mock()
            mock_result.state = "FAILURE"
            mock_result.info = Exception("Task failed")
            mock_async_result.return_value = mock_result
            
            task_status = scheduler_service.get_task_status(task_id)
            
            assert task_status.status == TaskStatus.FAILURE
            assert "Task failed" in task_status.error
            assert task_status.completed_at is not None
    
    def test_get_batch_status(self, scheduler_service):
        """测试获取批次状态"""
        batch_id = "test-batch-123"
        task_batch = TaskBatch(
            batch_id=batch_id,
            task_ids=["task1", "task2", "task3"],
            batch_type="test_batch",
            created_at=datetime.now(),
            status=TaskStatus.PENDING
        )
        scheduler_service.task_batches[batch_id] = task_batch
        
        # 模拟任务状态
        for i, task_id in enumerate(task_batch.task_ids):
            scheduled_task = ScheduledTask(
                task_id=task_id,
                task_name=f"test_task_{i}",
                status=TaskStatus.SUCCESS if i < 2 else TaskStatus.PENDING,
                created_at=datetime.now()
            )
            if scheduled_task.status == TaskStatus.SUCCESS:
                scheduled_task.result = {"success": True}
            scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch.object(scheduler_service, 'get_task_status') as mock_get_status:
            def side_effect(task_id):
                return scheduler_service.active_tasks.get(task_id)
            mock_get_status.side_effect = side_effect
            
            batch_status = scheduler_service.get_batch_status(batch_id)
            
            assert batch_status is not None
            assert batch_status.batch_id == batch_id
            assert batch_status.progress == 2/3  # 2 out of 3 tasks completed
            assert len(batch_status.results) == 2  # 2 successful tasks
    
    def test_cancel_task(self, scheduler_service):
        """测试取消任务"""
        task_id = "test-task-cancel"
        scheduled_task = ScheduledTask(
            task_id=task_id,
            task_name="test_task",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch('app.core.celery_app.celery_app.control.revoke') as mock_revoke:
            success = scheduler_service.cancel_task(task_id)
            
            assert success is True
            mock_revoke.assert_called_once_with(task_id, terminate=True)
            
            # 验证任务状态更新
            assert scheduler_service.active_tasks[task_id].status == TaskStatus.REVOKED
            assert scheduler_service.active_tasks[task_id].completed_at is not None
    
    def test_get_active_tasks(self, scheduler_service):
        """测试获取活跃任务列表"""
        # 创建测试任务
        active_task_ids = ["active1", "active2"]
        completed_task_ids = ["completed1"]
        
        for task_id in active_task_ids:
            scheduled_task = ScheduledTask(
                task_id=task_id,
                task_name="active_task",
                status=TaskStatus.STARTED,
                created_at=datetime.now()
            )
            scheduler_service.active_tasks[task_id] = scheduled_task
        
        for task_id in completed_task_ids:
            scheduled_task = ScheduledTask(
                task_id=task_id,
                task_name="completed_task",
                status=TaskStatus.SUCCESS,
                created_at=datetime.now()
            )
            scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch.object(scheduler_service, 'get_task_status') as mock_get_status:
            def side_effect(task_id):
                task = scheduler_service.active_tasks.get(task_id)
                # 保持活跃任务的状态，完成任务返回SUCCESS状态
                if task_id in completed_task_ids:
                    task.status = TaskStatus.SUCCESS
                return task
            mock_get_status.side_effect = side_effect
            
            active_tasks = scheduler_service.get_active_tasks()
            
            # 只返回活跃任务
            assert len(active_tasks) == 2
            active_task_ids_result = [task.task_id for task in active_tasks]
            assert "active1" in active_task_ids_result
            assert "active2" in active_task_ids_result
            assert "completed1" not in active_task_ids_result
    
    def test_get_task_statistics(self, scheduler_service):
        """测试获取任务统计信息"""
        # 创建不同状态的任务
        task_statuses = [
            TaskStatus.PENDING,
            TaskStatus.STARTED,
            TaskStatus.SUCCESS,
            TaskStatus.FAILURE,
            TaskStatus.RETRY
        ]
        
        for i, status in enumerate(task_statuses):
            task_id = f"task_{i}"
            scheduled_task = ScheduledTask(
                task_id=task_id,
                task_name=f"test_task_{i}",
                status=status,
                created_at=datetime.now()
            )
            scheduler_service.active_tasks[task_id] = scheduled_task
        
        with patch.object(scheduler_service, 'get_task_status') as mock_get_status:
            def side_effect(task_id):
                return scheduler_service.active_tasks.get(task_id)
            mock_get_status.side_effect = side_effect
            
            stats = scheduler_service.get_task_statistics()
            
            assert stats["total_tasks"] == 5
            assert stats["status_breakdown"]["pending"] == 1
            assert stats["status_breakdown"]["started"] == 1
            assert stats["status_breakdown"]["success"] == 1
            assert stats["status_breakdown"]["failure"] == 1
            assert stats["status_breakdown"]["retry"] == 1
            assert stats["active_tasks"] == 3  # pending, started, retry
            assert stats["completed_tasks"] == 2  # success, failure
    
    def test_nonexistent_task_status(self, scheduler_service):
        """测试获取不存在任务的状态"""
        task_status = scheduler_service.get_task_status("nonexistent-task")
        assert task_status is None
    
    def test_nonexistent_batch_status(self, scheduler_service):
        """测试获取不存在批次的状态"""
        batch_status = scheduler_service.get_batch_status("nonexistent-batch")
        assert batch_status is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
