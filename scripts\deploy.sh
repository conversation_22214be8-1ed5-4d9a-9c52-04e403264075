#!/bin/bash

# Moniit 系统一键部署脚本
# 支持开发环境和生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Moniit 系统部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV        部署环境 (dev|prod) [默认: dev]"
    echo "  -p, --pull           拉取最新镜像"
    echo "  -b, --build          强制重新构建镜像"
    echo "  -d, --down           停止并删除容器"
    echo "  -c, --clean          清理未使用的镜像和容器"
    echo "  -l, --logs           显示服务日志"
    echo "  -s, --status         显示服务状态"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -e dev            部署开发环境"
    echo "  $0 -e prod -b        重新构建并部署生产环境"
    echo "  $0 -d                停止所有服务"
    echo "  $0 -l                查看服务日志"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi

    log_success "系统要求检查通过"
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."

    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env 文件不存在，从 .env.example 复制"
            cp .env.example .env
            log_warning "请编辑 .env 文件并设置正确的配置值"
        else
            log_error ".env 和 .env.example 文件都不存在"
            exit 1
        fi
    fi

    log_success "环境配置检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    mkdir -p data logs backups uploads
    mkdir -p nginx/ssl

    # 设置权限
    chmod 755 data logs backups uploads

    log_success "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."

    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml build
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml build
    else
        docker-compose build
    fi

    log_success "镜像构建完成"
}

# 拉取镜像
pull_images() {
    log_info "拉取最新镜像..."

    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml pull
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml pull
    else
        docker-compose pull
    fi

    log_success "镜像拉取完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."

    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml up -d
    else
        docker-compose up -d
    fi

    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."

    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml down
    else
        docker-compose down
    fi

    log_success "服务停止完成"
}

# 验证服务状态
verify_services() {
    log_info "验证服务状态..."

    # 等待服务启动
    sleep 10

    # 检查后端服务
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        return 1
    fi

    # 检查前端服务（开发环境）
    if [ "$ENVIRONMENT" = "dev" ]; then
        if curl -f http://localhost:3000 &> /dev/null; then
            log_success "前端服务运行正常"
        else
            log_warning "前端服务可能还在启动中"
        fi
    fi

    # 检查Redis服务
    if docker exec moniit-redis-${ENVIRONMENT:-dev} redis-cli ping &> /dev/null; then
        log_success "Redis服务运行正常"
    else
        log_error "Redis服务启动失败"
        return 1
    fi

    # 检查TimescaleDB数据库服务
    local db_container="moniit-timescaledb-${ENVIRONMENT:-dev}"
    if docker exec "$db_container" pg_isready -U moniit -d moniit &> /dev/null; then
        log_success "TimescaleDB数据库连接正常"
    else
        log_error "TimescaleDB数据库连接失败"
        return 1
    fi

    log_success "服务验证完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态:"

    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml ps
    else
        docker-compose ps
    fi
}

# 显示服务日志
show_logs() {
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml logs -f
    elif [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose logs -f
    fi
}

# 清理资源
clean_resources() {
    log_info "清理未使用的资源..."

    docker system prune -f
    docker volume prune -f
    docker image prune -f

    log_success "资源清理完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "=== 访问地址 ==="
    echo "🌐 前端界面: http://localhost"
    echo "📚 API文档: http://localhost:8000/docs"
    echo "📖 ReDoc文档: http://localhost:8000/redoc"
    echo "🌸 Flower监控: http://localhost:5555"
    echo "💾 数据库: localhost:5432"
    echo "🔴 Redis: localhost:6379"
    echo ""
    echo "=== 常用命令 ==="
    echo "查看日志: docker-compose logs -f"
    echo "停止服务: docker-compose down"
    echo "重启服务: docker-compose restart"
    echo "查看状态: docker-compose ps"
    echo ""
    echo "=== 管理命令 ==="
    echo "make status  - 查看系统状态"
    echo "make logs    - 查看日志"
    echo "make backup  - 备份数据库"
    echo ""
}

# 主函数
main() {
    # 默认参数
    ENVIRONMENT="dev"
    PULL_IMAGES=false
    BUILD_IMAGES=false
    STOP_SERVICES=false
    CLEAN_RESOURCES=false
    SHOW_LOGS=false
    SHOW_STATUS=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -p|--pull)
                PULL_IMAGES=true
                shift
                ;;
            -b|--build)
                BUILD_IMAGES=true
                shift
                ;;
            -d|--down)
                STOP_SERVICES=true
                shift
                ;;
            -c|--clean)
                CLEAN_RESOURCES=true
                shift
                ;;
            -l|--logs)
                SHOW_LOGS=true
                shift
                ;;
            -s|--status)
                SHOW_STATUS=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证环境参数
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
        log_error "无效的环境参数: $ENVIRONMENT (支持: dev, prod)"
        exit 1
    fi

    log_info "开始部署 Moniit 系统 (环境: $ENVIRONMENT)"

    # 执行操作
    if [ "$STOP_SERVICES" = true ]; then
        stop_services
        exit 0
    fi

    if [ "$SHOW_LOGS" = true ]; then
        show_logs
        exit 0
    fi

    if [ "$SHOW_STATUS" = true ]; then
        show_status
        exit 0
    fi

    if [ "$CLEAN_RESOURCES" = true ]; then
        clean_resources
        exit 0
    fi

    # 正常部署流程
    check_requirements
    check_environment
    create_directories

    if [ "$PULL_IMAGES" = true ]; then
        pull_images
    fi

    if [ "$BUILD_IMAGES" = true ]; then
        build_images
    fi

    start_services
    verify_services

    log_success "Moniit 系统部署完成!"
    log_info "访问地址:"
    if [ "$ENVIRONMENT" = "dev" ]; then
        log_info "  前端: http://localhost:3000"
        log_info "  后端: http://localhost:8000"
        log_info "  API文档: http://localhost:8000/docs"
    else
        log_info "  应用: http://localhost"
        log_info "  API文档: http://localhost/docs"
    fi

    show_status
}

# 执行主函数
main "$@"
