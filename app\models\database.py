"""
SQLAlchemy数据库模型
"""

import uuid
from datetime import datetime
from typing import Optional
from decimal import Decimal

from sqlalchemy import (
    Column, String, Integer, DECIMAL, DateTime, Boolean,
    Text, JSON, ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)


class Product(Base, TimestampMixin):
    """商品表"""
    __tablename__ = "products"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(String(2000), nullable=False, comment="商品URL")
    platform = Column(String(50), nullable=False, comment="平台名称")
    title = Column(String(500), nullable=True, comment="商品标题")
    title_translated = Column(String(500), nullable=True, comment="翻译后标题")
    category = Column(String(100), nullable=True, comment="商品分类")
    status = Column(String(20), nullable=False, default="active", comment="状态")
    monitoring_frequency = Column(Integer, nullable=False, default=24, comment="监控频率(小时)")
    last_monitored_at = Column(DateTime(timezone=True), nullable=True, comment="最后监控时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否激活")
    tags = Column(JSONB, nullable=True, comment="标签")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 关系
    history = relationship("ProductHistory", back_populates="product", cascade="all, delete-orphan")
    costs = relationship("ProductCost", back_populates="product", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_products_platform', 'platform'),
        Index('idx_products_category', 'category'),
        Index('idx_products_status', 'status'),
        Index('idx_products_active', 'is_active'),
        UniqueConstraint('url', name='uq_products_url'),
        CheckConstraint('monitoring_frequency > 0', name='ck_products_monitoring_frequency'),
    )


class ProductHistory(Base):
    """商品历史数据表（时序数据）"""
    __tablename__ = "product_history"
    
    time = Column(DateTime(timezone=True), primary_key=True, nullable=False, comment="时间")
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), primary_key=True, nullable=False, comment="商品ID")
    platform = Column(String(50), nullable=False, comment="平台名称")
    
    # 基础信息
    title = Column(String(500), nullable=True, comment="商品标题")
    title_translated = Column(String(500), nullable=True, comment="翻译后标题")
    
    # 价格信息（核心）
    price = Column(DECIMAL(12, 4), nullable=True, comment="当前价格")
    currency = Column(String(10), nullable=True, comment="货币类型")

    # 销量信息（核心）
    sales_count = Column(Integer, nullable=True, comment="销售数量")

    # 库存信息（核心）
    stock_quantity = Column(Integer, nullable=True, comment="库存数量")

    # 质量信息（核心）
    rating = Column(DECIMAL(3, 2), nullable=True, comment="商品评分")
    review_count = Column(Integer, nullable=True, comment="评论数量")

    # 变化追踪
    change_type = Column(String(50), nullable=True, comment="变化类型")
    change_value = Column(DECIMAL(12, 4), nullable=True, comment="变化数值")

    # 数据质量
    data_quality_score = Column(DECIMAL(3, 2), nullable=True, comment="数据质量评分")
    raw_data = Column(JSONB, nullable=True, comment="原始数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # 关系
    product = relationship("Product", back_populates="history")
    
    # 索引 - 优化所有核心商品信息查询
    __table_args__ = (
        # 基础时序索引
        Index('idx_product_history_product_time', 'product_id', 'time'),
        Index('idx_product_history_platform_time', 'platform', 'time'),
        
        # 价格相关索引
        Index('idx_product_history_price', 'product_id', 'time', postgresql_where=Column('price').isnot(None)),
        Index('idx_product_history_price_change', 'product_id', 'time', postgresql_where=Column('change_type') == 'price_change'),
        
        # 销量相关索引
        Index('idx_product_history_sales', 'product_id', 'time', postgresql_where=Column('sales_count').isnot(None)),
        Index('idx_product_history_sales_change', 'product_id', 'time', postgresql_where=Column('change_type') == 'sales_update'),
        
        # 库存相关索引
        Index('idx_product_history_stock', 'product_id', 'time', postgresql_where=Column('stock_quantity').isnot(None)),
        Index('idx_product_history_stock_change', 'product_id', 'time', postgresql_where=Column('change_type') == 'stock_update'),
        
        # 好评率相关索引
        Index('idx_product_history_rating', 'product_id', 'time', postgresql_where=Column('rating').isnot(None)),
        Index('idx_product_history_reviews', 'product_id', 'time', postgresql_where=Column('review_count').isnot(None)),
        
        # 复合查询优化索引
        Index('idx_product_history_comprehensive', 'product_id', 'time', 'price', 'sales_count', 'stock_quantity', 'rating'),
        
        CheckConstraint('rating >= 0 AND rating <= 5', name='ck_product_history_rating'),
        CheckConstraint('stock_quantity >= 0', name='ck_product_history_stock'),
        CheckConstraint('sales_count >= 0', name='ck_product_history_sales'),
    )


class Supplier(Base, TimestampMixin):
    """供货商表"""
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_id = Column(String, nullable=False, comment="供货商ID")
    name = Column(String(200), nullable=False, comment="供货商名称")
    contact_person = Column(String(100), nullable=True, comment="联系人")
    phone = Column(String(50), nullable=True, comment="电话")
    email = Column(String(200), nullable=True, comment="邮箱")
    address = Column(Text, nullable=True, comment="地址")
    website = Column(String, nullable=True, comment="网站")
    payment_terms = Column(String(200), nullable=True, comment="付款条件")
    delivery_time = Column(Integer, nullable=True, comment="交货时间(天)")
    min_order_quantity = Column(Integer, nullable=True, comment="最小订货量")
    is_active = Column(Boolean, nullable=True, default=True, comment="是否激活")
    rating = Column(DECIMAL(3, 2), nullable=True, comment="供货商评分")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 关系
    costs = relationship("ProductCost", back_populates="supplier", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_suppliers_active', 'is_active', 'name'),
        Index('idx_suppliers_rating', 'rating'),
        UniqueConstraint('name', name='uq_suppliers_name'),
        CheckConstraint('rating >= 0 AND rating <= 5', name='ck_suppliers_rating'),
        CheckConstraint('delivery_time > 0', name='ck_suppliers_delivery_time'),
    )


class ProductCost(Base, TimestampMixin):
    """商品成本表"""
    __tablename__ = "product_costs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True, comment="商品ID")
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True, comment="供货商ID")
    unit_cost = Column(DECIMAL(12, 4), nullable=False, comment="单位成本")
    currency = Column(String(10), nullable=False, default="USD", comment="货币类型")
    shipping_cost = Column(DECIMAL(12, 4), nullable=True, comment="运费")
    other_costs = Column(DECIMAL(12, 4), nullable=True, comment="其他费用")
    total_cost = Column(DECIMAL(12, 4), nullable=False, comment="总成本")
    min_quantity = Column(Integer, nullable=True, comment="最小数量")
    max_quantity = Column(Integer, nullable=True, comment="最大数量")
    valid_from = Column(DateTime(timezone=True), nullable=False, comment="有效开始时间")
    valid_until = Column(DateTime(timezone=True), nullable=True, comment="有效结束时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否激活")
    is_preferred = Column(Boolean, nullable=False, default=False, comment="是否首选")
    notes = Column(Text, nullable=True, comment="备注")

    # 关系
    product = relationship("Product", back_populates="costs")
    supplier = relationship("Supplier", back_populates="costs")
    
    # 索引
    __table_args__ = (
        Index('idx_product_costs_product', 'product_id', 'valid_from'),
        Index('idx_product_costs_supplier', 'supplier_id', 'valid_from'),
        Index('idx_product_costs_valid', 'product_id', 'valid_from', 'valid_until'),
        Index('idx_product_costs_active', 'is_active', 'valid_from'),
        CheckConstraint('unit_cost > 0', name='ck_product_costs_unit_cost'),
        CheckConstraint('total_cost > 0', name='ck_product_costs_total_cost'),
        CheckConstraint('valid_from < valid_until OR valid_until IS NULL', name='ck_product_costs_valid_period'),
    )


class MonitoringTask(Base, TimestampMixin):
    """监控任务表"""
    __tablename__ = "monitoring_tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False, comment="商品ID")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    status = Column(String(20), nullable=False, default="pending", comment="任务状态")
    priority = Column(Integer, nullable=False, default=5, comment="优先级")
    scheduled_at = Column(DateTime(timezone=True), nullable=False, comment="计划执行时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    retry_count = Column(Integer, nullable=False, default=0, comment="重试次数")
    max_retries = Column(Integer, nullable=False, default=3, comment="最大重试次数")
    error_message = Column(Text, nullable=True, comment="错误信息")
    result_data = Column(JSONB, nullable=True, comment="结果数据")
    
    # 索引
    __table_args__ = (
        Index('idx_monitoring_tasks_product', 'product_id', 'scheduled_at'),
        Index('idx_monitoring_tasks_status', 'status', 'scheduled_at'),
        Index('idx_monitoring_tasks_priority', 'priority', 'scheduled_at'),
        CheckConstraint('priority >= 1 AND priority <= 10', name='ck_monitoring_tasks_priority'),
        CheckConstraint('retry_count <= max_retries', name='ck_monitoring_tasks_retry'),
    )


class Alert(Base, TimestampMixin):
    """预警表"""
    __tablename__ = "alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=True, comment="商品ID")
    alert_type = Column(String(50), nullable=False, comment="预警类型")
    severity = Column(String(20), nullable=False, comment="严重程度")
    title = Column(String(200), nullable=False, comment="预警标题")
    message = Column(Text, nullable=False, comment="预警消息")
    data = Column(JSONB, nullable=True, comment="相关数据")
    is_read = Column(Boolean, nullable=False, default=False, comment="是否已读")
    is_resolved = Column(Boolean, nullable=False, default=False, comment="是否已解决")
    resolved_at = Column(DateTime(timezone=True), nullable=True, comment="解决时间")
    resolved_by = Column(String(100), nullable=True, comment="解决人")
    
    # 索引
    __table_args__ = (
        Index('idx_alerts_product', 'product_id', 'created_at'),
        Index('idx_alerts_type', 'alert_type', 'created_at'),
        Index('idx_alerts_severity', 'severity', 'created_at'),
        Index('idx_alerts_unread', 'is_read', 'created_at'),
        Index('idx_alerts_unresolved', 'is_resolved', 'created_at'),
    )
