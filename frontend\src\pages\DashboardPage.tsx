/**
 * 仪表板页面
 */

import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, Table, Tag, Button } from 'antd';
import {
  ShoppingOutlined,
  MonitorOutlined,
  AlertOutlined,
  RiseOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';

import { useAppDispatch, useAppSelector } from '../store';
import { fetchDashboardStatsAsync, selectDashboardStats, selectSystemHealth } from '../store/slices/systemSlice';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const stats = useAppSelector(selectDashboardStats);
  const systemHealth = useAppSelector(selectSystemHealth);

  useEffect(() => {
    dispatch(fetchDashboardStatsAsync());
  }, [dispatch]);

  // 模拟最近活动数据
  const recentActivities = [
    {
      id: '1',
      type: 'product',
      title: '新增商品：iPhone 15 Pro',
      time: '2分钟前',
      status: 'success',
    },
    {
      id: '2',
      type: 'monitor',
      title: '监控任务执行成功',
      time: '5分钟前',
      status: 'success',
    },
    {
      id: '3',
      type: 'alert',
      title: '价格变动告警',
      time: '10分钟前',
      status: 'warning',
    },
  ];

  // 模拟热门商品数据
  const popularProducts = [
    {
      id: '1',
      name: 'iPhone 15 Pro',
      category: '电子产品',
      monitors: 5,
      status: 'active',
    },
    {
      id: '2',
      name: 'MacBook Pro',
      category: '电子产品',
      monitors: 3,
      status: 'active',
    },
    {
      id: '3',
      name: 'AirPods Pro',
      category: '电子产品',
      monitors: 2,
      status: 'active',
    },
  ];

  const activityColumns: ColumnsType<any> = [
    {
      title: '活动',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => {
        const colors = {
          success: 'green',
          warning: 'orange',
          error: 'red',
        };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
  ];

  const productColumns: ColumnsType<any> = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '监控数',
      dataIndex: 'monitors',
      key: 'monitors',
      width: 80,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/products/${record.id}`)}
        />
      ),
    },
  ];

  return (
    <div>
      <h2 className="mb-4">仪表板</h2>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-4">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总商品数"
              value={stats?.total_products || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃监控"
              value={stats?.active_monitors || 0}
              prefix={<MonitorOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="价格记录"
              value={stats?.total_price_records || 0}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="近期告警"
              value={stats?.recent_alerts || 0}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} className="mb-4">
        {/* 系统健康状态 */}
        <Col xs={24} lg={12}>
          <Card title="系统健康状态" className="h-100">
            <div className="mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>CPU使用率</span>
                <span>45%</span>
              </div>
              <Progress percent={45} status="active" />
            </div>
            
            <div className="mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>内存使用率</span>
                <span>62%</span>
              </div>
              <Progress percent={62} status="active" />
            </div>
            
            <div className="mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>磁盘使用率</span>
                <span>38%</span>
              </div>
              <Progress percent={38} status="active" />
            </div>

            <div className="text-center mt-3">
              <Tag color="green" style={{ fontSize: 14, padding: '4px 12px' }}>
                系统运行正常
              </Tag>
            </div>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" className="h-100">
            <Table
              columns={activityColumns}
              dataSource={recentActivities}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 热门商品 */}
        <Col xs={24} lg={12}>
          <Card 
            title="热门商品" 
            extra={
              <Button type="link" onClick={() => navigate('/products')}>
                查看全部
              </Button>
            }
          >
            <Table
              columns={productColumns}
              dataSource={popularProducts}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* 监控概览 */}
        <Col xs={24} lg={12}>
          <Card 
            title="监控概览"
            extra={
              <Button type="link" onClick={() => navigate('/monitor')}>
                查看全部
              </Button>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="运行中"
                  value={8}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已暂停"
                  value={2}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
            <Row gutter={16} className="mt-3">
              <Col span={12}>
                <Statistic
                  title="成功率"
                  value={95.6}
                  suffix="%"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均响应"
                  value={1.2}
                  suffix="s"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
