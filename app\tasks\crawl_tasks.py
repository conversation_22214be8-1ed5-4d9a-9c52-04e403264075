"""
爬取任务

异步爬取任务，支持优先级、批量处理和重试机制
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from celery import current_task
from celery.exceptions import Retry

from app.core.celery_app import celery_app
from app.core.logging import get_logger
from app.services.task_middleware import (
    TaskMiddlewareClient, TaskScheduler, PlatformConfigManager
)
from app.services.task_middleware.client import CrawlConfig, TaskPriority
from app.services.task_middleware.config_manager import Platform, ProductType
from app.services.task_middleware.task_scheduler import ScheduleTask, ScheduleStrategy

logger = get_logger(__name__)


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def submit_crawl_batch(self, urls: List[str], platform: str, product_type: str, 
                      priority: str = "medium", batch_name: Optional[str] = None):
    """
    提交批量爬取任务
    
    Args:
        urls: URL列表
        platform: 平台类型
        product_type: 商品类型
        priority: 任务优先级
        batch_name: 批次名称
    
    Returns:
        Dict: 任务结果
    """
    try:
        logger.info(f"开始提交批量爬取任务: {len(urls)} URLs, 平台: {platform}")
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 创建服务实例
            client = TaskMiddlewareClient()
            config_manager = PlatformConfigManager()
            scheduler = TaskScheduler(client, config_manager)
            
            # 创建调度任务
            schedule_task = ScheduleTask(
                urls=urls,
                platform=Platform(platform),
                product_type=ProductType(product_type),
                priority=TaskPriority(priority),
                batch_name=batch_name or f"{platform}_{product_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            # 执行调度
            result = loop.run_until_complete(
                scheduler.schedule_single_task(schedule_task)
            )
            
            # 关闭客户端
            loop.run_until_complete(client.close())
            
            if result.success:
                logger.info(f"批量爬取任务提交成功: {result.schedule_id}")
                return {
                    "success": True,
                    "schedule_id": result.schedule_id,
                    "scheduled_tasks": result.scheduled_tasks,
                    "batch_results": [
                        {
                            "batch_id": br.batch_id,
                            "task_ids": br.task_ids,
                            "valid_tasks": br.valid_tasks
                        } for br in result.batch_results
                    ]
                }
            else:
                raise Exception(f"任务调度失败: {result.message}")
                
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"批量爬取任务失败: {exc}")
        
        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {
            "success": False,
            "error": str(exc),
            "retries": self.request.retries
        }


@celery_app.task(bind=True, max_retries=3)
def batch_crawl_with_strategy(self, task_configs: List[Dict[str, Any]], 
                             strategy: str = "batch_optimize"):
    """
    使用指定策略批量爬取
    
    Args:
        task_configs: 任务配置列表
        strategy: 调度策略
    
    Returns:
        Dict: 批量任务结果
    """
    try:
        logger.info(f"开始批量爬取，策略: {strategy}, 任务数: {len(task_configs)}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            client = TaskMiddlewareClient()
            config_manager = PlatformConfigManager()
            scheduler = TaskScheduler(client, config_manager)
            
            # 创建调度任务列表
            schedule_tasks = []
            for config in task_configs:
                task = ScheduleTask(
                    urls=config["urls"],
                    platform=Platform(config["platform"]),
                    product_type=ProductType(config["product_type"]),
                    priority=TaskPriority(config.get("priority", "medium")),
                    batch_name=config.get("batch_name")
                )
                schedule_tasks.append(task)
            
            # 执行批量调度
            result = loop.run_until_complete(
                scheduler.schedule_batch_tasks(
                    schedule_tasks, 
                    ScheduleStrategy(strategy)
                )
            )
            
            loop.run_until_complete(client.close())
            
            if result.success:
                logger.info(f"批量调度成功: {result.schedule_id}")
                return {
                    "success": True,
                    "schedule_id": result.schedule_id,
                    "scheduled_tasks": result.scheduled_tasks,
                    "failed_tasks": result.failed_tasks,
                    "batch_count": len(result.batch_results)
                }
            else:
                raise Exception(f"批量调度失败: {result.message}")
                
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"批量爬取失败: {exc}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=120)
        
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def check_priority_tasks(self):
    """检查高优先级任务状态"""
    try:
        logger.info("检查高优先级任务状态")
        
        # 这里可以实现检查逻辑
        # 例如：检查Task-Middleware中的高优先级任务状态
        # 如果有失败的任务，可以重新提交
        
        return {
            "success": True,
            "checked_at": datetime.now().isoformat(),
            "message": "高优先级任务检查完成"
        }
        
    except Exception as exc:
        logger.error(f"检查高优先级任务失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def competitor_monitoring(self):
    """竞品监控任务"""
    try:
        logger.info("开始竞品监控")
        
        # 模拟竞品监控逻辑
        # 实际实现中，这里会：
        # 1. 从数据库获取需要监控的竞品URL
        # 2. 按平台分组
        # 3. 提交爬取任务
        
        competitor_urls = [
            "https://detail.1688.com/offer/competitor1.html",
            "https://detail.1688.com/offer/competitor2.html",
        ]
        
        if competitor_urls:
            # 提交竞品爬取任务
            task_result = submit_crawl_batch.delay(
                urls=competitor_urls,
                platform="1688",
                product_type="competitor",
                priority="high",
                batch_name="competitor_monitoring"
            )
            
            return {
                "success": True,
                "task_id": task_result.id,
                "urls_count": len(competitor_urls),
                "message": "竞品监控任务已提交"
            }
        else:
            return {
                "success": True,
                "message": "没有需要监控的竞品"
            }
            
    except Exception as exc:
        logger.error(f"竞品监控失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def supplier_monitoring(self):
    """供货商监控任务"""
    try:
        logger.info("开始供货商监控")
        
        supplier_urls = [
            "https://detail.1688.com/offer/supplier1.html",
            "https://detail.1688.com/offer/supplier2.html",
        ]
        
        if supplier_urls:
            task_result = submit_crawl_batch.delay(
                urls=supplier_urls,
                platform="1688", 
                product_type="supplier",
                priority="medium",
                batch_name="supplier_monitoring"
            )
            
            return {
                "success": True,
                "task_id": task_result.id,
                "urls_count": len(supplier_urls),
                "message": "供货商监控任务已提交"
            }
        else:
            return {
                "success": True,
                "message": "没有需要监控的供货商商品"
            }
            
    except Exception as exc:
        logger.error(f"供货商监控失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }


@celery_app.task(bind=True)
def other_products_monitoring(self):
    """其他商品监控任务"""
    try:
        logger.info("开始其他商品监控")
        
        other_urls = [
            "https://detail.1688.com/offer/other1.html",
            "https://item.taobao.com/item.htm?id=other2",
        ]
        
        if other_urls:
            # 按平台分组
            platform_groups = {}
            for url in other_urls:
                if "1688.com" in url:
                    platform = "1688"
                elif "taobao.com" in url:
                    platform = "taobao"
                else:
                    platform = "1688"  # 默认
                
                if platform not in platform_groups:
                    platform_groups[platform] = []
                platform_groups[platform].append(url)
            
            task_ids = []
            for platform, urls in platform_groups.items():
                task_result = submit_crawl_batch.delay(
                    urls=urls,
                    platform=platform,
                    product_type="other",
                    priority="low",
                    batch_name=f"other_monitoring_{platform}"
                )
                task_ids.append(task_result.id)
            
            return {
                "success": True,
                "task_ids": task_ids,
                "urls_count": len(other_urls),
                "platforms": list(platform_groups.keys()),
                "message": "其他商品监控任务已提交"
            }
        else:
            return {
                "success": True,
                "message": "没有需要监控的其他商品"
            }
            
    except Exception as exc:
        logger.error(f"其他商品监控失败: {exc}")
        return {
            "success": False,
            "error": str(exc)
        }
