# Moniit 前端应用

基于 React + TypeScript + Ant Design 构建的商品监控系统前端应用。

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design 5** - UI组件库
- **Redux Toolkit** - 状态管理
- **React Router 6** - 路由管理
- **Axios** - HTTP客户端
- **ECharts** - 图表库

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 通用组件
│   │   ├── Layout/        # 布局组件
│   │   └── LoadingPage.tsx # 加载页面
│   ├── pages/             # 页面组件
│   │   ├── auth/          # 认证相关页面
│   │   ├── products/      # 商品管理页面
│   │   ├── monitor/       # 监控管理页面
│   │   ├── analytics/     # 数据分析页面
│   │   ├── suppliers/     # 供货商管理页面
│   │   └── system/        # 系统管理页面
│   ├── services/          # API服务
│   │   ├── api.ts         # API客户端配置
│   │   ├── authApi.ts     # 认证API
│   │   ├── productApi.ts  # 商品API
│   │   ├── monitorApi.ts  # 监控API
│   │   ├── supplierApi.ts # 供货商API
│   │   └── systemApi.ts   # 系统API
│   ├── store/             # Redux状态管理
│   │   ├── slices/        # Redux切片
│   │   └── index.ts       # Store配置
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── hooks/             # 自定义Hooks
│   ├── constants/         # 常量定义
│   ├── App.tsx            # 主应用组件
│   ├── index.tsx          # 应用入口
│   └── index.css          # 全局样式
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目说明
```

## 功能特性

### 🔐 用户认证
- 用户登录/登出
- JWT令牌管理
- 权限控制
- 个人信息管理

### 📦 商品管理
- 商品列表展示
- 商品搜索和筛选
- 商品详情查看
- 商品编辑和删除
- 批量操作
- Excel导入/导出

### 📊 监控管理
- 监控任务列表
- 任务状态管理
- 任务执行控制
- 监控历史记录

### 📈 数据分析
- 价格趋势图表
- 数据统计分析
- 报表生成导出

### 🏢 供货商管理
- 供货商信息管理
- 供货商商品关联

### ⚙️ 系统管理
- 系统设置配置
- 用户管理
- 系统监控状态

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd frontend
npm install
```

### 开发运行

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

### 类型检查

```bash
npm run type-check
```

## 环境配置

创建 `.env` 文件配置环境变量：

```env
# API服务地址
REACT_APP_API_URL=http://localhost:8000

# 应用标题
REACT_APP_TITLE=Moniit商品监控系统

# 是否启用调试模式
REACT_APP_DEBUG=true
```

## API集成

### 请求拦截器
- 自动添加认证令牌
- 请求日志记录
- 错误统一处理

### 响应拦截器
- 自动令牌刷新
- 错误消息提示
- 响应数据格式化

### 错误处理
- 网络错误处理
- 认证失败处理
- 业务错误提示

## 状态管理

使用 Redux Toolkit 进行状态管理：

- **authSlice** - 用户认证状态
- **productSlice** - 商品数据状态
- **monitorSlice** - 监控任务状态
- **supplierSlice** - 供货商数据状态
- **systemSlice** - 系统状态
- **uiSlice** - UI界面状态

## 路由配置

- `/` - 仪表板
- `/products` - 商品管理
- `/monitor` - 监控管理
- `/analytics` - 数据分析
- `/suppliers` - 供货商管理
- `/system/settings` - 系统设置
- `/system/users` - 用户管理
- `/profile` - 个人中心

## 组件设计

### 布局组件
- 响应式侧边栏
- 顶部导航栏
- 面包屑导航
- 用户信息展示

### 表格组件
- 数据展示
- 分页控制
- 排序筛选
- 批量操作

### 表单组件
- 数据录入
- 表单验证
- 文件上传
- 数据提交

## 样式规范

### CSS类命名
- 使用 Bootstrap 风格的工具类
- 组件样式使用 CSS Modules
- 全局样式定义在 index.css

### 响应式设计
- 移动端适配
- 平板端适配
- 桌面端优化

## 性能优化

- 组件懒加载
- 图片懒加载
- 虚拟滚动
- 缓存策略

## 部署说明

### 开发环境
```bash
npm start
```

### 生产环境
```bash
npm run build
npm install -g serve
serve -s build
```

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 演示账户

- **管理员**: admin / admin123
- **操作员**: operator / operator123

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 开发团队

- 前端开发：React + TypeScript
- UI设计：Ant Design
- 状态管理：Redux Toolkit
- 构建工具：Create React App
