"""
审计日志系统

记录用户操作、权限检查、系统事件等审计信息
"""

import json
import os
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import uuid

from app.core.logging import get_logger

logger = get_logger(__name__)


class AuditAction(Enum):
    """审计动作类型"""
    # 认证相关
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    
    # 权限相关
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_DENIED = "permission_denied"
    ACCESS_DENIED = "access_denied"
    FUNCTION_ACCESS = "function_access"
    
    # 用户管理
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    USER_ACTIVATED = "user_activated"
    USER_DEACTIVATED = "user_deactivated"
    ROLE_CHANGED = "role_changed"
    PERMISSION_CHANGED = "permission_changed"
    
    # 数据操作
    DATA_CREATE = "data_create"
    DATA_READ = "data_read"
    DATA_UPDATE = "data_update"
    DATA_DELETE = "data_delete"
    DATA_EXPORT = "data_export"
    DATA_IMPORT = "data_import"
    
    # 系统操作
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    SYSTEM_MAINTENANCE = "system_maintenance"
    
    # 翻译服务
    TRANSLATION_REQUEST = "translation_request"
    TRANSLATION_BATCH = "translation_batch"
    TRANSLATION_CONFIG = "translation_config"
    
    # 其他
    FILE_UPLOAD = "file_upload"
    FILE_DOWNLOAD = "file_download"
    API_ACCESS = "api_access"
    CUSTOM = "custom"


class AuditResult(Enum):
    """审计结果"""
    SUCCESS = "success"
    FAILURE = "failure"
    WARNING = "warning"
    INFO = "info"


class AuditLevel(Enum):
    """审计级别"""
    LOW = 1      # 一般操作
    MEDIUM = 2   # 重要操作
    HIGH = 3     # 敏感操作
    CRITICAL = 4 # 关键操作


@dataclass
class AuditEntry:
    """审计日志条目"""
    entry_id: str
    timestamp: datetime
    user_id: str
    username: str
    action: AuditAction
    resource_type: str
    resource_id: str
    
    # 详细信息
    details: Dict[str, Any] = field(default_factory=dict)
    result: AuditResult = AuditResult.SUCCESS
    level: AuditLevel = AuditLevel.LOW
    
    # 网络信息
    ip_address: str = ""
    user_agent: str = ""
    session_id: str = ""
    
    # 错误信息
    error_message: str = ""
    error_code: str = ""
    
    # 性能信息
    duration_ms: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.entry_id:
            self.entry_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "entry_id": self.entry_id,
            "timestamp": self.timestamp.isoformat(),
            "user_id": self.user_id,
            "username": self.username,
            "action": self.action.value,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "details": self.details,
            "result": self.result.value,
            "level": self.level.value,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "session_id": self.session_id,
            "error_message": self.error_message,
            "error_code": self.error_code,
            "duration_ms": self.duration_ms
        }


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, data_dir: str = "audit_logs"):
        self.data_dir = data_dir
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 审计配置
        self.config = {
            "enabled": True,
            "log_level": AuditLevel.LOW,
            "retention_days": 365,  # 保留1年
            "max_file_size_mb": 100,
            "compress_old_logs": True,
            "real_time_alerts": True,
            "sensitive_data_masking": True
        }
        
        # 审计统计
        self.audit_stats = {
            "total_entries": 0,
            "by_action": {},
            "by_result": {},
            "by_level": {},
            "by_user": {},
            "today_entries": 0,
            "failed_operations": 0,
            "security_events": 0
        }
        
        # 当前日志文件
        self.current_log_file = None
        self.current_date = None
        
        # 敏感字段列表（需要脱敏）
        self.sensitive_fields = {
            "password", "password_hash", "salt", "token", "secret",
            "api_key", "private_key", "credit_card", "ssn", "phone"
        }
        
        # 安全事件关键词
        self.security_keywords = {
            "login_failed", "permission_denied", "access_denied",
            "account_locked", "password_reset", "unauthorized"
        }
    
    def log_action(self, user_id: str, action: AuditAction, resource_type: str,
                  resource_id: str, username: str = "", details: Dict[str, Any] = None,
                  result: AuditResult = AuditResult.SUCCESS, level: AuditLevel = None,
                  ip_address: str = "", user_agent: str = "", session_id: str = "",
                  error_message: str = "", error_code: str = "", duration_ms: int = 0):
        """
        记录审计日志
        
        Args:
            user_id: 用户ID
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            username: 用户名
            details: 详细信息
            result: 操作结果
            level: 审计级别
            ip_address: IP地址
            user_agent: 用户代理
            session_id: 会话ID
            error_message: 错误消息
            error_code: 错误代码
            duration_ms: 执行时间（毫秒）
        """
        try:
            if not self.config["enabled"]:
                return
            
            # 自动确定审计级别
            if level is None:
                level = self._determine_audit_level(action, result)
            
            # 检查是否需要记录
            if level.value < self.config["log_level"].value:
                return
            
            # 脱敏处理
            if details and self.config["sensitive_data_masking"]:
                details = self._mask_sensitive_data(details)
            
            # 创建审计条目
            entry = AuditEntry(
                entry_id="",  # 将在__post_init__中生成
                timestamp=datetime.now(),
                user_id=user_id,
                username=username,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details or {},
                result=result,
                level=level,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                error_message=error_message,
                error_code=error_code,
                duration_ms=duration_ms
            )
            
            # 写入日志文件
            self._write_audit_entry(entry)
            
            # 更新统计
            self._update_statistics(entry)
            
            # 实时告警检查
            if self.config["real_time_alerts"]:
                self._check_security_alerts(entry)
            
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
    
    def query_audit_logs(self, start_date: datetime = None, end_date: datetime = None,
                        user_id: str = None, action: AuditAction = None,
                        result: AuditResult = None, level: AuditLevel = None,
                        resource_type: str = None, limit: int = 1000) -> List[AuditEntry]:
        """
        查询审计日志
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            user_id: 用户ID
            action: 操作类型
            result: 操作结果
            level: 审计级别
            resource_type: 资源类型
            limit: 返回条数限制
        
        Returns:
            List[AuditEntry]: 审计日志列表
        """
        try:
            entries = []
            
            # 确定查询日期范围
            if not start_date:
                start_date = datetime.now() - timedelta(days=7)  # 默认查询最近7天
            if not end_date:
                end_date = datetime.now()
            
            # 遍历日期范围内的日志文件
            current_date = start_date.date()
            while current_date <= end_date.date():
                log_file = os.path.join(self.data_dir, f"audit_{current_date.strftime('%Y%m%d')}.json")
                
                if os.path.exists(log_file):
                    entries.extend(self._read_log_file(log_file, user_id, action, result, level, resource_type))
                
                current_date += timedelta(days=1)
            
            # 按时间戳排序
            entries.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 应用限制
            return entries[:limit]
            
        except Exception as e:
            logger.error(f"查询审计日志失败: {e}")
            return []
    
    def get_user_activity(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """
        获取用户活动统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            Dict[str, Any]: 用户活动统计
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            entries = self.query_audit_logs(start_date=start_date, user_id=user_id)
            
            activity = {
                "total_actions": len(entries),
                "by_action": {},
                "by_result": {},
                "by_date": {},
                "recent_actions": [],
                "failed_actions": [],
                "login_history": []
            }
            
            for entry in entries:
                # 按操作类型统计
                action_key = entry.action.value
                activity["by_action"][action_key] = activity["by_action"].get(action_key, 0) + 1
                
                # 按结果统计
                result_key = entry.result.value
                activity["by_result"][result_key] = activity["by_result"].get(result_key, 0) + 1
                
                # 按日期统计
                date_key = entry.timestamp.date().isoformat()
                activity["by_date"][date_key] = activity["by_date"].get(date_key, 0) + 1
                
                # 收集失败操作
                if entry.result == AuditResult.FAILURE:
                    activity["failed_actions"].append({
                        "timestamp": entry.timestamp.isoformat(),
                        "action": entry.action.value,
                        "error_message": entry.error_message
                    })
                
                # 收集登录历史
                if entry.action in [AuditAction.LOGIN, AuditAction.LOGIN_FAILED]:
                    activity["login_history"].append({
                        "timestamp": entry.timestamp.isoformat(),
                        "result": entry.result.value,
                        "ip_address": entry.ip_address,
                        "user_agent": entry.user_agent
                    })
            
            # 最近操作（前10条）
            activity["recent_actions"] = [
                {
                    "timestamp": entry.timestamp.isoformat(),
                    "action": entry.action.value,
                    "resource_type": entry.resource_type,
                    "result": entry.result.value
                }
                for entry in entries[:10]
            ]
            
            return activity
            
        except Exception as e:
            logger.error(f"获取用户活动失败: {e}")
            return {}
    
    def get_security_events(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取安全事件
        
        Args:
            days: 查询天数
        
        Returns:
            List[Dict[str, Any]]: 安全事件列表
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            entries = self.query_audit_logs(start_date=start_date)
            
            security_events = []
            
            for entry in entries:
                # 检查是否为安全事件
                if (entry.result == AuditResult.FAILURE or 
                    any(keyword in entry.action.value for keyword in self.security_keywords) or
                    entry.level in [AuditLevel.HIGH, AuditLevel.CRITICAL]):
                    
                    security_events.append({
                        "timestamp": entry.timestamp.isoformat(),
                        "user_id": entry.user_id,
                        "username": entry.username,
                        "action": entry.action.value,
                        "result": entry.result.value,
                        "level": entry.level.value,
                        "ip_address": entry.ip_address,
                        "error_message": entry.error_message,
                        "details": entry.details
                    })
            
            return security_events
            
        except Exception as e:
            logger.error(f"获取安全事件失败: {e}")
            return []
    
    def cleanup_old_logs(self):
        """清理过期日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config["retention_days"])
            
            for filename in os.listdir(self.data_dir):
                if filename.startswith("audit_") and filename.endswith(".json"):
                    file_path = os.path.join(self.data_dir, filename)
                    file_stat = os.stat(file_path)
                    file_date = datetime.fromtimestamp(file_stat.st_mtime)
                    
                    if file_date < cutoff_date:
                        os.remove(file_path)
                        logger.info(f"删除过期审计日志: {filename}")
            
        except Exception as e:
            logger.error(f"清理过期日志失败: {e}")
    
    def _determine_audit_level(self, action: AuditAction, result: AuditResult) -> AuditLevel:
        """确定审计级别"""
        # 关键操作
        critical_actions = {
            AuditAction.USER_DELETED, AuditAction.SYSTEM_CONFIG_CHANGE,
            AuditAction.SYSTEM_BACKUP, AuditAction.SYSTEM_RESTORE
        }
        
        # 高级操作
        high_actions = {
            AuditAction.USER_CREATED, AuditAction.ROLE_CHANGED,
            AuditAction.PERMISSION_CHANGED, AuditAction.ACCOUNT_LOCKED,
            AuditAction.PASSWORD_RESET
        }
        
        # 中级操作
        medium_actions = {
            AuditAction.LOGIN, AuditAction.LOGOUT, AuditAction.PASSWORD_CHANGE,
            AuditAction.DATA_DELETE, AuditAction.DATA_EXPORT
        }
        
        if action in critical_actions:
            return AuditLevel.CRITICAL
        elif action in high_actions or result == AuditResult.FAILURE:
            return AuditLevel.HIGH
        elif action in medium_actions:
            return AuditLevel.MEDIUM
        else:
            return AuditLevel.LOW
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏敏感数据"""
        try:
            masked_data = {}
            
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                    if isinstance(value, str) and len(value) > 4:
                        masked_data[key] = value[:2] + "*" * (len(value) - 4) + value[-2:]
                    else:
                        masked_data[key] = "***"
                else:
                    masked_data[key] = value
            
            return masked_data
            
        except Exception as e:
            logger.error(f"脱敏数据失败: {e}")
            return data
    
    def _write_audit_entry(self, entry: AuditEntry):
        """写入审计条目"""
        try:
            # 确定日志文件
            today = datetime.now().date()
            if self.current_date != today:
                self.current_date = today
                self.current_log_file = os.path.join(
                    self.data_dir, 
                    f"audit_{today.strftime('%Y%m%d')}.json"
                )
            
            # 写入文件
            with open(self.current_log_file, 'a', encoding='utf-8') as f:
                json.dump(entry.to_dict(), f, ensure_ascii=False)
                f.write('\n')
            
        except Exception as e:
            logger.error(f"写入审计条目失败: {e}")
    
    def _read_log_file(self, log_file: str, user_id: str = None, action: AuditAction = None,
                      result: AuditResult = None, level: AuditLevel = None,
                      resource_type: str = None) -> List[AuditEntry]:
        """读取日志文件"""
        try:
            entries = []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        
                        # 应用过滤条件
                        if user_id and data.get("user_id") != user_id:
                            continue
                        if action and data.get("action") != action.value:
                            continue
                        if result and data.get("result") != result.value:
                            continue
                        if level and data.get("level") != level.value:
                            continue
                        if resource_type and data.get("resource_type") != resource_type:
                            continue
                        
                        # 重建审计条目
                        entry = AuditEntry(
                            entry_id=data["entry_id"],
                            timestamp=datetime.fromisoformat(data["timestamp"]),
                            user_id=data["user_id"],
                            username=data["username"],
                            action=AuditAction(data["action"]),
                            resource_type=data["resource_type"],
                            resource_id=data["resource_id"],
                            details=data.get("details", {}),
                            result=AuditResult(data["result"]),
                            level=AuditLevel(data["level"]),
                            ip_address=data.get("ip_address", ""),
                            user_agent=data.get("user_agent", ""),
                            session_id=data.get("session_id", ""),
                            error_message=data.get("error_message", ""),
                            error_code=data.get("error_code", ""),
                            duration_ms=data.get("duration_ms", 0)
                        )
                        
                        entries.append(entry)
                        
                    except Exception as e:
                        logger.error(f"解析审计条目失败: {e}")
                        continue
            
            return entries
            
        except Exception as e:
            logger.error(f"读取日志文件失败: {e}")
            return []
    
    def _update_statistics(self, entry: AuditEntry):
        """更新统计信息"""
        try:
            self.audit_stats["total_entries"] += 1
            
            # 按操作统计
            action_key = entry.action.value
            self.audit_stats["by_action"][action_key] = self.audit_stats["by_action"].get(action_key, 0) + 1
            
            # 按结果统计
            result_key = entry.result.value
            self.audit_stats["by_result"][result_key] = self.audit_stats["by_result"].get(result_key, 0) + 1
            
            # 按级别统计
            level_key = entry.level.value
            self.audit_stats["by_level"][level_key] = self.audit_stats["by_level"].get(level_key, 0) + 1
            
            # 按用户统计
            user_key = entry.user_id
            self.audit_stats["by_user"][user_key] = self.audit_stats["by_user"].get(user_key, 0) + 1
            
            # 今日统计
            if entry.timestamp.date() == datetime.now().date():
                self.audit_stats["today_entries"] += 1
            
            # 失败操作统计
            if entry.result == AuditResult.FAILURE:
                self.audit_stats["failed_operations"] += 1
            
            # 安全事件统计
            if (entry.level in [AuditLevel.HIGH, AuditLevel.CRITICAL] or
                any(keyword in entry.action.value for keyword in self.security_keywords)):
                self.audit_stats["security_events"] += 1
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def _check_security_alerts(self, entry: AuditEntry):
        """检查安全告警"""
        try:
            # 这里可以实现实时安全告警逻辑
            # 例如：连续登录失败、权限异常访问等
            
            if entry.result == AuditResult.FAILURE and entry.action == AuditAction.LOGIN_FAILED:
                logger.warning(f"安全告警: 用户{entry.username}登录失败 - IP: {entry.ip_address}")
            
            if entry.action == AuditAction.PERMISSION_DENIED:
                logger.warning(f"安全告警: 用户{entry.username}权限被拒绝 - 资源: {entry.resource_type}")
            
        except Exception as e:
            logger.error(f"检查安全告警失败: {e}")
    
    def get_audit_statistics(self) -> Dict[str, Any]:
        """获取审计统计信息"""
        return {
            "stats": self.audit_stats.copy(),
            "config": self.config.copy()
        }
