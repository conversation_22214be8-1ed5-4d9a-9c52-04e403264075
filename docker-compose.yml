# Moniit 系统 Docker Compose 配置
# 支持开发环境热重载和生产环境部署

version: '3.8'

services:
  # 后端API服务 - 开发模式
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-backend-dev
    restart: unless-stopped
    ports:
      - "8002:8000"
      - "5678:5678"  # 调试端口
    environment:
      - MONIIT_ENV=development
      - DATABASE_URL=postgresql+asyncpg://moniit:moniit123@db:5432/moniit
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-for-development-only
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    volumes:
      # 挂载整个应用目录支持热重载
      - .:/app
      # 排除一些不需要挂载的目录
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/venv
      - /app/.venv
    depends_on:
      - redis
      - db
    networks:
      - moniit-dev-network
    command: >
      sh -c "
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # 前端Web服务 - 开发模式
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: moniit-frontend-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
      - REACT_APP_API_URL=http://localhost:8002
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=true
    volumes:
      # 挂载源代码目录支持热重载
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
      # 排除node_modules避免冲突
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - moniit-dev-network
    stdin_open: true
    tty: true

  # Celery工作进程 - 开发模式
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-worker-dev
    command: celery -A app.core.celery worker --loglevel=debug --concurrency=2
    environment:
      - MONIIT_ENV=development
      - DATABASE_URL=*************************************/moniit
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-for-development-only
      - DEBUG=true
    depends_on:
      - redis
      - db
    volumes:
      # 挂载整个应用目录支持热重载
      - .:/app
      # 排除一些不需要挂载的目录
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/venv
      - /app/.venv
    networks:
      - moniit-dev-network
    restart: unless-stopped
    profiles:
      - worker

  # Celery Beat调度器 - 开发模式
  beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-beat-dev
    command: celery -A app.core.celery beat --loglevel=debug
    environment:
      - MONIIT_ENV=development
      - DATABASE_URL=*************************************/moniit
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-for-development-only
      - DEBUG=true
    depends_on:
      - redis
      - db
    volumes:
      # 挂载整个应用目录支持热重载
      - .:/app
      # 排除一些不需要挂载的目录
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/venv
      - /app/.venv
    networks:
      - moniit-dev-network
    restart: unless-stopped
    profiles:
      - worker

  # Flower监控界面 - 开发模式
  flower:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-flower-dev
    command: celery -A app.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - MONIIT_ENV=development
      - DATABASE_URL=*************************************/moniit
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-for-development-only
      - DEBUG=true
    depends_on:
      - redis
      - db
    volumes:
      - .:/app
    networks:
      - moniit-dev-network
    restart: unless-stopped
    profiles:
      - worker

  # Redis缓存服务 - 开发模式
  redis:
    image: redis:7-alpine
    container_name: moniit-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - moniit-dev-redis-data:/data
    networks:
      - moniit-dev-network

  # TimescaleDB数据库服务 - 开发模式
  db:
    image: timescale/timescaledb:latest-pg15
    container_name: moniit-timescaledb-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=moniit
      - POSTGRES_USER=moniit
      - POSTGRES_PASSWORD=moniit123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - moniit-dev-postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/init-timescale.sql:/docker-entrypoint-initdb.d/02-timescale.sql:ro
    networks:
      - moniit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U moniit -d moniit"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据库管理工具 - 开发环境
  adminer:
    image: adminer:latest
    container_name: moniit-adminer-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=db
    networks:
      - moniit-dev-network
    profiles:
      - tools

  # Redis管理工具 - 开发环境
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: moniit-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - moniit-dev-network
    profiles:
      - tools

  # 邮件测试工具 - 开发环境
  mailhog:
    image: mailhog/mailhog:latest
    container_name: moniit-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - moniit-dev-network
    profiles:
      - tools

# 网络配置
networks:
  moniit-dev-network:
    driver: bridge

# 数据卷配置
volumes:
  moniit-dev-redis-data:
    driver: local
  moniit-dev-postgres-data:
    driver: local
