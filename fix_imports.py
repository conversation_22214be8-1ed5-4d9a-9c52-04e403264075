#!/usr/bin/env python3
"""
修复前端项目中的@/导入路径
"""

import os
import re
from pathlib import Path

def calculate_relative_path(from_file, to_path):
    """计算相对路径"""
    from_dir = Path(from_file).parent
    src_dir = Path("frontend/src")
    
    # 计算从当前文件到src目录的相对路径
    try:
        rel_path = os.path.relpath(src_dir / to_path, from_dir)
        # 确保使用正斜杠
        rel_path = rel_path.replace('\\', '/')
        # 如果不以./或../开头，添加./
        if not rel_path.startswith('.'):
            rel_path = './' + rel_path
        return rel_path
    except ValueError:
        # 如果无法计算相对路径，返回原路径
        return to_path

def fix_imports_in_file(file_path):
    """修复单个文件中的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 匹配@/开头的导入
        pattern = r"from ['\"]@/([^'\"]+)['\"]"
        
        def replace_import(match):
            import_path = match.group(1)
            relative_path = calculate_relative_path(file_path, import_path)
            return f"from '{relative_path}'"
        
        content = re.sub(pattern, replace_import, content)
        
        # 匹配import ... from '@/...'
        pattern2 = r"import ([^'\"]+) from ['\"]@/([^'\"]+)['\"]"
        
        def replace_import2(match):
            import_statement = match.group(1)
            import_path = match.group(2)
            relative_path = calculate_relative_path(file_path, import_path)
            return f"import {import_statement} from '{relative_path}'"
        
        content = re.sub(pattern2, replace_import2, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed imports in: {file_path}")
            return True
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    frontend_src = Path("frontend/src")
    
    if not frontend_src.exists():
        print("frontend/src directory not found!")
        return
    
    # 查找所有.ts和.tsx文件
    files_to_fix = []
    for ext in ['*.ts', '*.tsx']:
        files_to_fix.extend(frontend_src.rglob(ext))
    
    fixed_count = 0
    for file_path in files_to_fix:
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    print(f"\nFixed imports in {fixed_count} files.")

if __name__ == "__main__":
    main()
