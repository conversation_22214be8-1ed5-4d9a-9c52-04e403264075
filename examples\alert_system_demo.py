"""
智能预警系统演示

展示预警引擎、规则管理、通知发送、预警处理等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.alert_system.alert_engine import (
    AlertEngine, AlertLevel, AlertType, AlertCategory
)
from app.services.alert_system.rule_manager import RuleManager
from app.services.alert_system.notification_sender import (
    NotificationSender, NotificationChannel, NotificationConfig
)
from app.services.alert_system.alert_processor import AlertProcessor


async def demo_alert_engine():
    """演示预警引擎功能"""
    print("=== 智能预警引擎演示 ===")
    
    # 创建模拟依赖
    mock_comprehensive_analyzer = Mock()
    mock_comprehensive_analyzer.generate_comprehensive_report = Mock(return_value=None)

    mock_cost_manager = Mock()
    mock_cost_manager.get_cost_alerts = Mock(return_value=[])

    mock_profit_calculator = Mock()
    mock_profit_calculator.calculate_profit = Mock(return_value=None)
    
    # 创建预警引擎
    alert_engine = AlertEngine(
        mock_comprehensive_analyzer,
        mock_cost_manager,
        mock_profit_calculator
    )
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=111111",
            title="Apple iPhone 15 Pro 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=7599.00),  # 价格下降10.6%
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=18000, rating=4.8)  # 销量增长50%
        ),
        Product(
            url="https://item.jd.com/item.htm?id=222222",
            title="Samsung Galaxy S24 256GB 钛金灰",
            platform="jd",
            product_type=ProductType.SUPPLIER,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="Samsung"),
            metrics=ProductMetrics(sales_count=12000, rating=4.7)  # 库存充足
        ),
        Product(
            url="https://item.tmall.com/item.htm?id=333333",
            title="华为 Mate 60 Pro 512GB 雅川青",
            platform="tmall",
            product_type=ProductType.SUPPLIER,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="华为"),
            metrics=ProductMetrics(sales_count=8000, rating=4.6)  # 库存较低
        )
    ]
    
    print(f"\n1. 预警引擎配置:")
    print(f"   默认规则数: {len(alert_engine.alert_rules)}")
    print(f"   预警配置: 最大预警数 {alert_engine.alert_config['max_alerts_per_product']}")
    print(f"   智能过滤: 预警疲劳阈值 {alert_engine.filter_config['alert_fatigue_threshold']}")
    
    # 处理商品预警
    print(f"\n2. 商品预警处理:")
    print(f"   处理商品数: {len(test_products)}")
    
    alerts = await alert_engine.process_products(test_products)
    
    print(f"   生成预警数: {len(alerts)}")
    
    if alerts:
        print(f"\n   预警详情:")
        for i, alert in enumerate(alerts, 1):
            print(f"     预警{i}: {alert.alert_id}")
            print(f"       商品: {alert.title}")
            print(f"       类型: {alert.alert_type.value}")
            print(f"       分类: {alert.alert_category.value}")
            print(f"       级别: {alert.alert_level.value}")
            print(f"       描述: {alert.description}")
            print(f"       当前值: {alert.current_value}")
            if alert.change_percentage:
                print(f"       变化: {alert.change_percentage:+.1f}%")
            print(f"       建议: {', '.join(alert.recommendations[:2])}")
            print(f"       创建时间: {alert.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 预警管理操作
    print(f"\n3. 预警管理操作:")
    
    if alerts:
        test_alert = alerts[0]
        
        # 确认预警
        result = await alert_engine.acknowledge_alert(test_alert.alert_id)
        print(f"   确认预警: {'成功' if result else '失败'}")
        
        # 解决预警
        result = await alert_engine.resolve_alert(test_alert.alert_id)
        print(f"   解决预警: {'成功' if result else '失败'}")
    
    # 获取预警汇总
    print(f"\n4. 预警汇总信息:")
    summary = await alert_engine.get_alert_summary()
    
    print(f"   总预警数: {summary.total_alerts}")
    print(f"   活跃预警: {summary.active_alerts}")
    print(f"   紧急预警: {summary.critical_alerts}")
    print(f"   重要预警: {summary.high_alerts}")
    print(f"   一般预警: {summary.medium_alerts}")
    print(f"   低级预警: {summary.low_alerts}")
    
    print(f"\n   预警分类分布:")
    for category, count in summary.category_distribution.items():
        print(f"     {category.value}: {count} 个")
    
    print(f"\n   预警类型分布:")
    for alert_type, count in summary.type_distribution.items():
        print(f"     {alert_type.value}: {count} 个")
    
    if summary.top_affected_products:
        print(f"\n   受影响最多的商品:")
        for product_id in summary.top_affected_products[:3]:
            print(f"     • {product_id}")
    
    # 规则统计
    print(f"\n5. 预警规则统计:")
    rule_stats = alert_engine.get_rule_statistics()
    
    print(f"   总规则数: {rule_stats['total_rules']}")
    print(f"   启用规则: {rule_stats['enabled_rules']}")
    print(f"   禁用规则: {rule_stats['disabled_rules']}")
    
    print(f"\n   规则类型分布:")
    for alert_type, count in rule_stats['type_distribution'].items():
        print(f"     {alert_type}: {count} 个")
    
    print(f"\n   规则分类分布:")
    for category, count in rule_stats['category_distribution'].items():
        print(f"     {category}: {count} 个")
    
    return alert_engine, alerts


async def demo_rule_manager():
    """演示规则管理器功能"""
    print("\n=== 规则管理器演示 ===")
    
    rule_manager = RuleManager()
    
    print(f"\n1. 规则模板管理:")
    templates = rule_manager.get_templates()
    print(f"   可用模板数: {len(templates)}")
    
    print(f"\n   模板列表:")
    for template in templates[:3]:  # 显示前3个模板
        print(f"     • {template.template_name}")
        print(f"       ID: {template.template_id}")
        print(f"       类型: {template.alert_type.value}")
        print(f"       分类: {template.alert_category.value}")
        print(f"       默认级别: {template.default_level.value}")
        print(f"       适用商品: {', '.join([pt.value for pt in template.applicable_product_types])}")
        print(f"       描述: {template.description}")
    
    # 从模板创建规则
    print(f"\n2. 从模板创建规则:")
    template = templates[0]
    
    custom_rule = rule_manager.create_rule_from_template(
        template_id=template.template_id,
        rule_name="演示自定义价格预警规则",
        custom_conditions={
            "price_drop_percentage": 0.08,  # 8%价格下降
            "time_window_hours": 12
        },
        alert_level=AlertLevel.CRITICAL
    )
    
    if custom_rule:
        print(f"   规则创建成功:")
        print(f"     规则ID: {custom_rule.rule_id}")
        print(f"     规则名称: {custom_rule.rule_name}")
        print(f"     预警类型: {custom_rule.alert_type.value}")
        print(f"     预警级别: {custom_rule.alert_level.value}")
        print(f"     触发条件: {custom_rule.conditions}")
    
    # 创建完全自定义规则
    print(f"\n3. 创建自定义规则:")
    
    custom_rule_data = {
        "rule_name": "演示库存预警规则",
        "alert_type": "inventory_shortage",
        "alert_category": "inventory_alert",
        "conditions": {
            "stock_level_threshold": 50,
            "days_of_supply": 5
        },
        "product_types": ["supplier"],
        "alert_level": "high",
        "enabled": True,
        "description": "当供应商商品库存低于50件且供应天数少于5天时触发预警"
    }
    
    custom_rule2 = rule_manager.create_custom_rule(custom_rule_data)
    
    if custom_rule2:
        print(f"   自定义规则创建成功:")
        print(f"     规则ID: {custom_rule2.rule_id}")
        print(f"     规则名称: {custom_rule2.rule_name}")
        print(f"     触发条件: {custom_rule2.conditions}")
        print(f"     适用商品类型: {', '.join([pt.value for pt in custom_rule2.product_types])}")
    
    # 规则管理操作
    print(f"\n4. 规则管理操作:")
    
    if custom_rule2:
        rule_id = custom_rule2.rule_id
        
        # 更新规则
        updates = {
            "alert_level": "critical",
            "conditions": {
                "stock_level_threshold": 30,  # 更严格的阈值
                "days_of_supply": 3
            }
        }
        
        result = rule_manager.update_rule(rule_id, updates)
        print(f"   更新规则: {'成功' if result else '失败'}")
        
        if result:
            updated_rule = rule_manager.get_rule(rule_id)
            print(f"     新预警级别: {updated_rule.alert_level.value}")
            print(f"     新触发条件: {updated_rule.conditions}")
    
    # 规则统计
    print(f"\n5. 规则管理统计:")
    rule_stats = rule_manager.get_rule_statistics()
    
    print(f"   自定义规则数: {rule_stats['total_rules']}")
    print(f"   启用规则数: {rule_stats['enabled_rules']}")
    print(f"   可用模板数: {rule_stats['total_templates']}")
    
    print(f"\n   规则类型分布:")
    for alert_type, count in rule_stats['type_distribution'].items():
        print(f"     {alert_type}: {count} 个")
    
    return rule_manager


async def demo_notification_system():
    """演示通知系统功能"""
    print("\n=== 通知推送系统演示 ===")
    
    notification_sender = NotificationSender()
    
    print(f"\n1. 通知系统配置:")
    print(f"   可用模板数: {len(notification_sender.notification_templates)}")
    print(f"   支持渠道: {', '.join([ch.value for ch in NotificationChannel])}")
    
    # 配置用户通知偏好
    print(f"\n2. 用户通知配置:")
    
    user_configs = [
        NotificationConfig(
            user_id="admin",
            enabled_channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            alert_level_filters=[AlertLevel.CRITICAL, AlertLevel.HIGH, AlertLevel.MEDIUM],
            alert_type_filters=list(AlertType),
            max_notifications_per_hour=20,
            email_address="<EMAIL>"
        ),
        NotificationConfig(
            user_id="manager",
            enabled_channels=[NotificationChannel.EMAIL, NotificationChannel.WECHAT],
            alert_level_filters=[AlertLevel.CRITICAL, AlertLevel.HIGH],
            alert_type_filters=[AlertType.PRICE_ANOMALY, AlertType.INVENTORY_SHORTAGE, AlertType.PROFIT_MARGIN_DROP],
            max_notifications_per_hour=15,
            email_address="<EMAIL>",
            wechat_id="manager_wechat"
        ),
        NotificationConfig(
            user_id="analyst",
            enabled_channels=[NotificationChannel.IN_APP],
            alert_level_filters=[AlertLevel.CRITICAL, AlertLevel.HIGH, AlertLevel.MEDIUM, AlertLevel.LOW],
            alert_type_filters=list(AlertType),
            max_notifications_per_hour=30
        )
    ]
    
    for config in user_configs:
        notification_sender.add_user_config(config)
        print(f"   用户 {config.user_id}:")
        print(f"     通知渠道: {', '.join([ch.value for ch in config.enabled_channels])}")
        print(f"     接收级别: {', '.join([level.value for level in config.alert_level_filters])}")
        print(f"     每小时限制: {config.max_notifications_per_hour} 条")
    
    # 模拟发送通知
    print(f"\n3. 通知发送演示:")
    
    # 创建测试预警
    from app.services.alert_system.alert_engine import Alert
    
    test_alert = Alert(
        alert_id="demo_alert_001",
        rule_id="demo_rule",
        product_id="demo_product",
        alert_type=AlertType.PRICE_ANOMALY,
        alert_category=AlertCategory.COMPETITOR_ALERT,
        alert_level=AlertLevel.HIGH,
        title="iPhone 15 Pro 价格异常",
        description="竞品价格下降10.6%，超过预警阈值10%",
        current_value=7599.00,
        threshold_value=7649.00,
        change_percentage=-10.6,
        affected_metrics={
            "current_price": 7599.00,
            "previous_price": 8499.00
        },
        recommendations=[
            "分析价格下降原因",
            "评估对竞争力的影响",
            "考虑调整定价策略"
        ]
    )
    
    # 发送通知
    user_ids = ["admin", "manager", "analyst"]
    notification_records = await notification_sender.send_alert_notification(test_alert, user_ids)
    
    print(f"   发送通知数: {len(notification_records)}")
    
    if notification_records:
        print(f"\n   通知详情:")
        for record in notification_records:
            print(f"     通知ID: {record.notification_id}")
            print(f"     用户: {record.user_id}")
            print(f"     渠道: {record.channel.value}")
            print(f"     状态: {record.status.value}")
            print(f"     优先级: {record.priority.value}")
            print(f"     主题: {record.subject}")
            print(f"     创建时间: {record.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if record.sent_at:
                print(f"     发送时间: {record.sent_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 通知统计
    print(f"\n4. 通知系统统计:")
    notification_stats = notification_sender.get_notification_statistics()
    
    print(f"   总通知数: {notification_stats['total_notifications']}")
    print(f"   配置用户数: {notification_stats['total_users']}")
    print(f"   成功率: {notification_stats['success_rate']:.1f}%")
    print(f"   最近24小时: {notification_stats['recent_24h_count']} 条")
    
    print(f"\n   通知状态分布:")
    for status, count in notification_stats['status_distribution'].items():
        print(f"     {status}: {count} 条")
    
    print(f"\n   通知渠道分布:")
    for channel, count in notification_stats['channel_distribution'].items():
        print(f"     {channel}: {count} 条")
    
    return notification_sender


async def demo_alert_processor():
    """演示预警处理器功能"""
    print("\n=== 预警处理器演示 ===")
    
    # 创建依赖组件（使用之前演示的结果）
    alert_engine, alerts = await demo_alert_engine()
    rule_manager = await demo_rule_manager()
    notification_sender = await demo_notification_system()
    
    # 创建预警处理器
    alert_processor = AlertProcessor(alert_engine, rule_manager, notification_sender)
    
    print(f"\n1. 预警处理器配置:")
    print(f"   处理规则数: {len(alert_processor.processing_rules)}")
    print(f"   最大并发数: {alert_processor.automation_config.max_concurrent_processing}")
    print(f"   处理超时: {alert_processor.automation_config.processing_timeout} 秒")
    print(f"   自动化启用: {alert_processor.automation_config.enabled}")
    
    print(f"\n   用户分配配置:")
    for level, users in alert_processor.user_assignments.items():
        print(f"     {level}: {', '.join(users)}")
    
    # 处理预警
    if alerts:
        print(f"\n2. 预警自动化处理:")
        print(f"   待处理预警数: {len(alerts)}")
        
        processing_results = await alert_processor.process_alerts(alerts)
        
        print(f"   处理结果数: {len(processing_results)}")
        
        if processing_results:
            print(f"\n   处理详情:")
            for result in processing_results:
                print(f"     处理ID: {result.processing_id}")
                print(f"     预警ID: {result.alert_id}")
                print(f"     状态: {result.status.value}")
                print(f"     执行动作: {', '.join([action.value for action in result.actions_taken])}")
                print(f"     发送通知: {result.notifications_sent} 条")
                print(f"     处理时间: {result.processing_time:.3f} 秒")
                if result.error_message:
                    print(f"     错误信息: {result.error_message}")
    
    # 处理统计
    print(f"\n3. 处理器统计信息:")
    processing_stats = alert_processor.get_processing_statistics()
    
    print(f"   总处理次数: {processing_stats['total_processing']}")
    print(f"   处理规则数: {processing_stats['total_rules']}")
    print(f"   启用规则数: {processing_stats['enabled_rules']}")
    print(f"   成功率: {processing_stats['success_rate']:.1f}%")
    print(f"   平均处理时间: {processing_stats['avg_processing_time']:.3f} 秒")
    print(f"   总通知数: {processing_stats['total_notifications']}")
    
    print(f"\n   处理状态分布:")
    for status, count in processing_stats['status_distribution'].items():
        print(f"     {status}: {count} 次")
    
    print(f"\n   执行动作分布:")
    for action, count in processing_stats['action_distribution'].items():
        print(f"     {action}: {count} 次")
    
    return alert_processor


async def main():
    """主演示函数"""
    print("🚀 智能预警系统演示")
    print("=" * 60)
    
    # 1. 预警引擎演示
    alert_engine, alerts = await demo_alert_engine()
    
    # 2. 规则管理器演示
    rule_manager = await demo_rule_manager()
    
    # 3. 通知系统演示
    notification_sender = await demo_notification_system()
    
    # 4. 预警处理器演示
    alert_processor = await demo_alert_processor()
    
    print("\n" + "=" * 60)
    print("✅ 智能预警系统演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 智能预警引擎：多维度预警检测，智能过滤，预警分级")
    print(f"- 规则管理系统：模板化规则，自定义规则，规则验证")
    print(f"- 通知推送系统：多渠道通知，个性化配置，模板化消息")
    print(f"- 预警处理器：自动化处理，流程管理，统计分析")
    
    print(f"\n📊 演示统计:")
    print(f"- 预警引擎：{len(alert_engine.alert_rules)} 个规则，{len(alerts)} 个预警")
    print(f"- 规则管理：{len(rule_manager.get_templates())} 个模板，{len(rule_manager.get_all_rules())} 个自定义规则")
    print(f"- 通知系统：{len(notification_sender.notification_templates)} 个模板，{len(notification_sender.user_configs)} 个用户配置")
    print(f"- 处理器：{len(alert_processor.processing_rules)} 个处理规则，{len(alert_processor.processing_results)} 个处理结果")
    
    print(f"\n🔧 技术特性:")
    print(f"- 多维度预警：价格异常、销量变化、库存不足、利润下降、供应商问题、市场机会")
    print(f"- 智能过滤：预警去重、疲劳过滤、优先级调整、智能分级")
    print(f"- 多渠道通知：邮件、微信、钉钉、短信、Webhook、应用内通知")
    print(f"- 自动化处理：规则驱动、并发处理、状态跟踪、统计分析")
    print(f"- 个性化配置：用户偏好、免打扰时间、频率限制、渠道选择")


if __name__ == "__main__":
    asyncio.run(main())
