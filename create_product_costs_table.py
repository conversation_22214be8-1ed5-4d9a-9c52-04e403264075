#!/usr/bin/env python3
"""
创建product_costs表的迁移脚本

适配现有的数据库结构
"""

import asyncio
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import os


async def create_product_costs_table():
    """创建product_costs表"""
    print("🔄 开始创建product_costs表...")
    
    # 数据库连接配置
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://moniit:moniit123@localhost:5432/moniit")
    
    # 创建异步引擎
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 检查product_costs表是否存在
            check_table_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'product_costs'
            );
            """
            
            result = await conn.execute(text(check_table_query))
            table_exists = result.scalar()
            
            if table_exists:
                print("  ✅ product_costs表已存在")
                return
            
            print("  📋 product_costs表不存在，创建新表...")
            
            # 创建product_costs表，适配现有的数据库结构
            create_table_query = """
            CREATE TABLE product_costs (
                id SERIAL PRIMARY KEY,
                product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
                supplier_id INTEGER REFERENCES suppliers(id) ON DELETE CASCADE,
                unit_cost DECIMAL(12, 4) NOT NULL,
                currency VARCHAR(10) NOT NULL DEFAULT 'USD',
                shipping_cost DECIMAL(12, 4),
                other_costs DECIMAL(12, 4),
                total_cost DECIMAL(12, 4) NOT NULL,
                min_quantity INTEGER,
                max_quantity INTEGER,
                valid_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                valid_until TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN NOT NULL DEFAULT true,
                is_preferred BOOLEAN NOT NULL DEFAULT false,
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """

            await conn.execute(text(create_table_query))

            # 添加注释
            comment_queries = [
                "COMMENT ON TABLE product_costs IS '商品成本表';",
                "COMMENT ON COLUMN product_costs.product_id IS '商品ID';",
                "COMMENT ON COLUMN product_costs.supplier_id IS '供货商ID';",
                "COMMENT ON COLUMN product_costs.unit_cost IS '单位成本';",
                "COMMENT ON COLUMN product_costs.currency IS '货币类型';",
                "COMMENT ON COLUMN product_costs.shipping_cost IS '运费';",
                "COMMENT ON COLUMN product_costs.other_costs IS '其他费用';",
                "COMMENT ON COLUMN product_costs.total_cost IS '总成本';",
                "COMMENT ON COLUMN product_costs.min_quantity IS '最小数量';",
                "COMMENT ON COLUMN product_costs.max_quantity IS '最大数量';",
                "COMMENT ON COLUMN product_costs.valid_from IS '有效开始时间';",
                "COMMENT ON COLUMN product_costs.valid_until IS '有效结束时间';",
                "COMMENT ON COLUMN product_costs.is_active IS '是否激活';",
                "COMMENT ON COLUMN product_costs.is_preferred IS '是否首选';",
                "COMMENT ON COLUMN product_costs.notes IS '备注';"
            ]

            for comment_query in comment_queries:
                await conn.execute(text(comment_query))

            # 创建索引
            index_queries = [
                "CREATE INDEX idx_product_costs_product_id ON product_costs(product_id);",
                "CREATE INDEX idx_product_costs_supplier_id ON product_costs(supplier_id);",
                "CREATE INDEX idx_product_costs_is_active ON product_costs(is_active);",
                "CREATE INDEX idx_product_costs_is_preferred ON product_costs(is_preferred);",
                "CREATE INDEX idx_product_costs_valid_from ON product_costs(valid_from);"
            ]

            for index_query in index_queries:
                await conn.execute(text(index_query))
            print("  ✅ product_costs表创建成功")
            
            # 插入一些测试数据
            print("  📊 插入测试数据...")
            
            # 先查询一些现有的商品和供货商
            products_query = "SELECT id FROM products LIMIT 3;"
            products_result = await conn.execute(text(products_query))
            products = products_result.fetchall()
            
            suppliers_query = "SELECT id FROM suppliers LIMIT 3;"
            suppliers_result = await conn.execute(text(suppliers_query))
            suppliers = suppliers_result.fetchall()
            
            if products and suppliers:
                # 插入测试成本数据
                test_data = []
                for i, product in enumerate(products):
                    for j, supplier in enumerate(suppliers):
                        if i <= j:  # 避免重复组合
                            test_data.append({
                                'product_id': product.id,
                                'supplier_id': supplier.id,
                                'unit_cost': 10.50 + i * 5 + j * 2,
                                'currency': 'USD',
                                'shipping_cost': 2.50 + i * 0.5,
                                'other_costs': 1.00 + j * 0.3,
                                'total_cost': 14.00 + i * 5.5 + j * 2.3,
                                'min_quantity': 10 + i * 5,
                                'is_preferred': j == 0  # 第一个供货商设为首选
                            })
                
                for data in test_data:
                    insert_query = """
                    INSERT INTO product_costs 
                    (product_id, supplier_id, unit_cost, currency, shipping_cost, other_costs, total_cost, min_quantity, is_preferred)
                    VALUES (:product_id, :supplier_id, :unit_cost, :currency, :shipping_cost, :other_costs, :total_cost, :min_quantity, :is_preferred);
                    """
                    await conn.execute(text(insert_query), data)
                
                print(f"  ✅ 插入了{len(test_data)}条测试数据")
            else:
                print("  ⚠️ 没有找到商品或供货商数据，跳过测试数据插入")
            
        print("🎉 product_costs表创建完成！")
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        raise
    finally:
        await engine.dispose()


async def verify_table_structure():
    """验证表结构"""
    print("\n🔍 验证product_costs表结构...")
    
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://moniit:moniit123@localhost:5432/moniit")
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 查询表结构
            structure_query = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'product_costs'
            ORDER BY ordinal_position;
            """
            
            result = await conn.execute(text(structure_query))
            columns = result.fetchall()
            
            print("  📋 product_costs表结构:")
            for column in columns:
                nullable = "NULL" if column.is_nullable == "YES" else "NOT NULL"
                default = f" DEFAULT {column.column_default}" if column.column_default else ""
                print(f"    - {column.column_name}: {column.data_type} {nullable}{default}")
            
            # 查询数据数量
            count_query = "SELECT COUNT(*) FROM product_costs;"
            count_result = await conn.execute(text(count_query))
            count = count_result.scalar()
            
            print(f"  ✅ 表结构验证完成，共{len(columns)}个字段，{count}条数据")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
    finally:
        await engine.dispose()


async def main():
    """主函数"""
    print("🚀 开始product_costs表创建...")
    print("🐳 目标: Docker环境中的PostgreSQL数据库")
    print("=" * 50)
    
    await create_product_costs_table()
    await verify_table_structure()
    
    print("\n" + "=" * 50)
    print("✅ 创建完成！现在可以使用完整的供货商关联功能了。")


if __name__ == "__main__":
    asyncio.run(main())
