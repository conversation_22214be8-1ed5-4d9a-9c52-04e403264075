# Moniit 商品监控系统

一个现代化的商品价格监控和分析系统，提供实时价格追踪、趋势分析和智能告警功能。

## 🎯 核心功能

- **实时价格监控**：自动抓取商品价格变化，支持多平台多商品监控
- **时序数据分析**：基于TimescaleDB的高性能时序数据存储和分析
- **智能告警系统**：价格波动、异常检测、多渠道通知（邮件、Slack、钉钉等）
- **数据可视化**：丰富的图表展示，价格趋势、对比分析、统计报表
- **供货商管理**：多供货商价格对比，成本分析，利润计算
- **现代化界面**：React + TypeScript + Ant Design，响应式设计

## 🛠 技术栈

- **后端**: Python 3.11, FastAPI, SQLAlchemy, Celery
- **前端**: React 18, TypeScript, Ant Design 5, Redux Toolkit
- **数据库**: TimescaleDB (PostgreSQL + 时序扩展)
- **缓存**: Redis 7
- **容器化**: Docker + Docker Compose
- **运维工具**: 完整的备份、监控、清理、优化脚本

## 快速开始

### 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- Python 3.11+ (本地开发)

### 1. 克隆项目

```bash
git clone <repository-url>
cd ecommerce-monitoring
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置值
```

### 3. 启动开发环境

```bash
# 一键启动开发环境（包含TimescaleDB、Redis、前端、后端）
docker-compose up -d

# 或使用部署脚本
./scripts/deploy.sh -e dev

```

### 4. 访问应用

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **数据库管理**: http://localhost:8080 (Adminer)
- **Redis管理**: http://localhost:8081 (Redis Commander)
- **邮件测试**: http://localhost:8025 (MailHog)
- **Celery监控**: http://localhost:5555 (Flower)

### 5. 生产环境部署

```bash
# 使用生产环境配置部署
./scripts/deploy.sh -e prod

# 或直接使用生产配置
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
./scripts/manage.sh status
```

## 项目结构

```
ecommerce-monitoring/
├── app/                    # 应用代码
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── database/              # 数据库脚本
├── logs/                  # 日志文件
├── nginx/                 # Nginx配置
├── redis/                 # Redis配置
├── tests/                 # 测试文件
├── uploads/               # 上传文件
├── docker-compose.yml     # 生产环境
├── docker-compose.dev.yml # 开发环境
├── Dockerfile            # Docker镜像
└── requirements.txt      # Python依赖
```

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 监控界面

- Flower (Celery监控): http://localhost:5555
- 应用健康检查: http://localhost:8000/health

## 开发指南

### 本地开发

1. 启动开发环境依赖服务：
```bash
docker-compose -f docker-compose.dev.yml up -d
```

2. 安装开发依赖：
```bash
pip install -r requirements.txt
```

3. 运行测试：
```bash
pytest
```

4. 代码格式化：
```bash
black app/
isort app/
```

### 数据库操作

```bash
# 创建新的迁移
alembic revision --autogenerate -m "描述"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

### 添加新的业务模块

1. 在 `app/models/` 中定义数据模型
2. 在 `app/services/` 中实现业务逻辑
3. 在 `app/api/` 中定义API路由
4. 在 `tests/` 中添加测试用例

## 配置说明

主要配置文件：
- `config/app.yaml`: 应用主配置
- `.env`: 环境变量配置
- `docker-compose.yml`: 生产环境配置

关键配置项：
- `DATABASE_URL`: 数据库连接字符串
- `REDIS_URL`: Redis连接字符串
- `SECRET_KEY`: JWT密钥
- `TASK_MIDDLEWARE_URL`: TaskMiddleware API地址

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接字符串是否正确

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis URL配置

3. **Celery任务不执行**
   - 检查Celery Worker是否启动
   - 查看Celery日志

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f db

# 查看所有服务日志
docker-compose logs -f
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
