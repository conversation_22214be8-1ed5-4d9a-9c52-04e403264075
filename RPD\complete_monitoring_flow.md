# 电商商品完整监控流程设计

## 业务流程概述

基于用户的完整业务需求，系统的核心监控流程如下：

```
商品URL → 监控任务 → TaskMiddleware API → 完整商品信息 → 时序数据库 → 多维分析 → 利差计算
```

## 完整监控流程

### 1. 商品URL管理阶段
```
用户输入商品URL → 系统识别平台 → 创建商品记录 → 设置监控频率 → 启动监控任务
```

**涉及的核心信息**：
- 商品URL和基本信息
- 平台类型识别
- 监控频率设置
- 分类和标签管理

### 2. 数据采集阶段
```
监控任务触发 → TaskMiddleware API调用 → 爬取商品页面 → 解析商品信息 → 数据验证和清洗
```

**采集的完整商品信息**：
- **价格信息**：当前价格、货币类型、价格变化
- **销量信息**：销售数量、销量变化、销售趋势
- **库存信息**：库存数量、库存状态、缺货风险
- **质量信息**：商品评分、评论数量、好评率
- **基础信息**：商品标题、描述、图片、卖家信息
- **元数据**：采集时间、数据质量、变化类型

### 3. 时序数据存储阶段
```
商品信息 → 数据标准化 → TimescaleDB存储 → 索引优化 → 历史数据积累
```

**存储结构设计**：
```sql
-- 商品历史数据表（时序数据）
CREATE TABLE product_history (
    time TIMESTAMPTZ NOT NULL,              -- 采集时间
    product_id UUID NOT NULL,               -- 商品ID
    platform TEXT NOT NULL,                 -- 平台名称
    
    -- 基础信息
    title TEXT,                             -- 商品标题
    title_translated TEXT,                  -- 翻译后标题
    
    -- 价格信息（核心）
    price DECIMAL(12,4),                    -- 当前价格
    currency TEXT,                          -- 货币类型
    
    -- 销量信息（核心）
    sales_count INTEGER,                    -- 销售数量
    
    -- 库存信息（核心）
    stock_quantity INTEGER,                 -- 库存数量
    
    -- 质量信息（核心）
    rating DECIMAL(3,2),                    -- 商品评分
    review_count INTEGER,                   -- 评论数量
    
    -- 变化追踪
    change_type TEXT,                       -- 变化类型
    change_value DECIMAL(12,4),             -- 变化数值
    
    -- 数据质量
    data_quality_score DECIMAL(3,2),        -- 数据质量评分
    raw_data JSONB,                         -- 原始数据
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 4. 多维分析阶段
```
历史数据 → 趋势计算 → 统计分析 → 预测模型 → 综合评分
```

#### 4.1 价格趋势分析
- **价格变化趋势**：日/周/月价格变化图表
- **价格波动分析**：价格波动率、稳定性评估
- **价格预测**：基于历史数据的价格预测
- **价格异常检测**：异常价格波动识别

#### 4.2 销量趋势分析
- **销量变化趋势**：销量增长/下降趋势
- **销量增长率**：环比、同比增长率计算
- **销量预测**：未来销量预测模型
- **销量异常检测**：销量突增/突降识别

#### 4.3 库存变化分析
- **库存水平监控**：库存数量变化趋势
- **缺货风险评估**：基于库存和销量的缺货预警
- **库存周转分析**：库存周转率和效率分析
- **补货建议**：基于趋势的补货时机建议

#### 4.4 好评率分析
- **评分趋势**：商品评分变化趋势
- **评论数量分析**：评论数量增长趋势
- **质量预警**：评分下降或差评增加预警
- **竞争力分析**：与同类商品的质量对比

### 5. 利差计算阶段
```
商品价格数据 + 供货商成本 → 利润计算 → 供货商对比 → 利润机会识别
```

#### 5.1 成本管理
- **供货商信息**：联系方式、付款条件、交货期
- **成本录入**：单位成本、运费、其他费用
- **成本历史**：成本变化趋势和历史记录
- **成本预警**：成本异常变化提醒

#### 5.2 利润计算
- **实时利润率**：基于当前价格和成本的利润率
- **利润趋势**：利润变化趋势分析
- **最优供货商**：成本最低的供货商推荐
- **利润预测**：基于价格和成本趋势的利润预测

### 6. 智能预警阶段
```
多维数据监控 → 异常检测 → 预警规则匹配 → 通知发送 → 处理跟踪
```

#### 6.1 预警类型
- **价格预警**：价格异常波动、价格机会
- **销量预警**：销量突增/突降、销量趋势变化
- **库存预警**：库存不足、库存积压
- **质量预警**：评分下降、差评增加
- **利润预警**：利润空间变化、高利润机会
- **成本预警**：供货商成本变化

## 核心API接口设计

### 商品监控API
```
POST /api/v1/products                     # 添加商品
GET  /api/v1/products                     # 获取商品列表
POST /api/v1/products/import              # 批量导入商品
POST /api/v1/products/monitor/batch       # 启动批量监控
GET  /api/v1/products/{id}/status         # 获取监控状态
```

### 数据分析API
```
GET /api/v1/analytics/price/trends/{product_id}      # 价格趋势
GET /api/v1/analytics/sales/trends/{product_id}      # 销量趋势
GET /api/v1/analytics/inventory/trends/{product_id}  # 库存趋势
GET /api/v1/analytics/rating/trends/{product_id}     # 好评率趋势
GET /api/v1/analytics/comprehensive/{product_id}     # 综合分析
POST /api/v1/analytics/forecast                      # 预测分析
GET /api/v1/analytics/compare                        # 商品对比
```

### 利差计算API
```
POST /api/v1/profit/suppliers            # 添加供货商
POST /api/v1/profit/costs                # 添加成本信息
GET  /api/v1/profit/analysis/{product_id} # 利润分析
GET  /api/v1/profit/opportunities        # 利润机会
GET  /api/v1/profit/suppliers/compare    # 供货商对比
```

### 预警管理API
```
GET  /api/v1/alerts/rules                # 预警规则
POST /api/v1/alerts/rules                # 创建预警规则
GET  /api/v1/alerts/history              # 预警历史
POST /api/v1/alerts/acknowledge          # 确认预警
```

## 数据流转示例

### 完整监控周期示例
```
1. 用户添加商品URL: "https://www.amazon.com/product/B08N5WRWNW"
   ↓
2. 系统识别平台: Amazon
   ↓
3. 创建监控任务: 每6小时监控一次
   ↓
4. TaskMiddleware API调用:
   {
     "url": "https://www.amazon.com/product/B08N5WRWNW",
     "fields": ["title", "price", "stock", "rating", "reviews", "sales"]
   }
   ↓
5. 获取商品信息:
   {
     "title": "Wireless Bluetooth Headphones",
     "price": 29.99,
     "currency": "USD",
     "stock_quantity": 156,
     "sales_count": 1250,
     "rating": 4.3,
     "review_count": 892
   }
   ↓
6. 存储到时序数据库:
   INSERT INTO product_history (time, product_id, platform, title, price, ...)
   ↓
7. 触发实时分析:
   - 价格变化检测: 价格从$32.99降到$29.99 (-9.1%)
   - 销量变化检测: 销量从1180增加到1250 (****%)
   - 库存状态检测: 库存充足，无缺货风险
   - 好评率检测: 评分稳定在4.3
   ↓
8. 利差计算:
   - 供货商A成本: $18.50 → 利润率: 38.1%
   - 供货商B成本: $20.00 → 利润率: 33.3%
   - 推荐供货商A
   ↓
9. 预警检查:
   - 价格下降超过5% → 触发价格预警
   - 销量增长 + 价格下降 → 触发利润机会预警
   ↓
10. 发送通知:
    "商品 Wireless Bluetooth Headphones 价格下降9.1%，销量增长5.9%，
     建议关注利润机会，当前利润率38.1%"
```

## 关键技术实现

### 1. 数据采集优化
```python
async def collect_product_data(self, product_urls: List[str]) -> List[ProductData]:
    """优化的商品数据采集"""
    # 按平台分组，批量处理
    platform_groups = self._group_by_platform(product_urls)
    
    tasks = []
    for platform, urls in platform_groups.items():
        config = await self._get_platform_config(platform)
        task = self._batch_crawl_platform(urls, config)
        tasks.append(task)
    
    # 并行采集所有平台数据
    results = await asyncio.gather(*tasks)
    
    # 合并和标准化数据
    return self._merge_and_standardize(results)
```

### 2. 时序数据优化
```python
async def store_product_history(self, product_data_list: List[ProductData]):
    """优化的时序数据存储"""
    # 批量插入优化
    batch_size = 1000
    for i in range(0, len(product_data_list), batch_size):
        batch = product_data_list[i:i + batch_size]
        
        # 使用COPY命令批量插入
        await self._batch_insert_history(batch)
        
        # 触发实时分析
        await self._trigger_batch_analysis(batch)
```

### 3. 多维分析优化
```python
async def comprehensive_analysis(self, product_id: str, days: int) -> ComprehensiveAnalysis:
    """优化的综合分析"""
    # 并行查询所有维度数据
    price_task = self.get_price_trend(product_id, days)
    sales_task = self.get_sales_trend(product_id, days)
    inventory_task = self.get_inventory_trend(product_id, days)
    rating_task = self.get_rating_trend(product_id, days)
    
    # 等待所有分析完成
    price_trend, sales_trend, inventory_trend, rating_trend = await asyncio.gather(
        price_task, sales_task, inventory_task, rating_task
    )
    
    # 综合评分和建议
    return self._generate_comprehensive_analysis(
        price_trend, sales_trend, inventory_trend, rating_trend
    )
```

## 预期业务价值

### 直接价值
1. **完整数据基础**：建立价格、销量、库存、好评率的完整历史数据
2. **精准趋势分析**：基于真实数据的多维度趋势分析
3. **智能利润计算**：实时利润空间计算和供货商优化
4. **及时风险预警**：多维度异常检测和业务机会识别

### 间接价值
1. **数据驱动决策**：基于完整历史数据制定采购和销售策略
2. **竞争优势**：更全面的市场分析和成本控制能力
3. **业务扩展**：为业务规模化提供数据支撑和决策依据
4. **风险控制**：提前识别价格、销量、库存、质量等各类风险

这个完整的监控流程确保了从商品URL到最终业务决策的全链路数据流转，为电商运营提供了强有力的数据支撑和分析能力。
