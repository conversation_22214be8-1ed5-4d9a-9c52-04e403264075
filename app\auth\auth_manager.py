"""
认证管理器

统一管理用户认证、会话管理、密码安全等功能
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .models import User, UserRole, Permission, Session
from .password_manager import PasswordManager
from .jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, TokenType
from .session_manager import SessionManager
from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AuthConfig:
    """认证配置"""
    # JWT配置
    jwt_secret_key: str = ""
    jwt_access_token_expire_hours: int = 1
    jwt_refresh_token_expire_days: int = 7
    
    # 会话配置
    max_concurrent_sessions: int = 5
    session_timeout_minutes: int = 480
    remember_me_days: int = 30
    
    # 安全配置
    max_login_attempts: int = 5
    account_lockout_minutes: int = 30
    password_expiry_days: int = 90
    require_password_change_on_first_login: bool = True
    
    # 双因子认证
    enable_two_factor: bool = False
    two_factor_issuer: str = "Moniit System"


class AuthManager:
    """认证管理器"""
    
    def __init__(self, config: AuthConfig = None, data_dir: str = "auth_data"):
        self.config = config or AuthConfig()
        self.data_dir = data_dir
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "users"), exist_ok=True)
        
        # 初始化组件
        self.password_manager = PasswordManager()
        self.jwt_handler = JWTHandler(self.config.jwt_secret_key)
        self.session_manager = SessionManager(self.jwt_handler, os.path.join(self.data_dir, "sessions"))
        
        # 用户存储
        self.users: Dict[str, User] = {}
        self.username_to_user_id: Dict[str, str] = {}
        self.email_to_user_id: Dict[str, str] = {}
        
        # 认证统计
        self.auth_stats = {
            "total_users": 0,
            "active_users": 0,
            "login_attempts": 0,
            "successful_logins": 0,
            "failed_logins": 0,
            "locked_accounts": 0
        }
        
        # 加载用户数据
        self._load_users()
        
        # 创建默认用户
        self._create_default_admin()
        self._create_default_operator()
    
    def register_user(self, username: str, email: str, password: str, 
                     full_name: str = "", role: UserRole = UserRole.VIEWER) -> Tuple[bool, str, Optional[User]]:
        """
        注册新用户
        
        Args:
            username: 用户名
            email: 邮箱
            password: 密码
            full_name: 全名
            role: 用户角色
        
        Returns:
            Tuple[bool, str, Optional[User]]: (是否成功, 消息, 用户对象)
        """
        try:
            # 检查用户名是否已存在
            if username in self.username_to_user_id:
                return False, "用户名已存在", None
            
            # 检查邮箱是否已存在
            if email in self.email_to_user_id:
                return False, "邮箱已存在", None
            
            # 检查密码强度
            password_check = self.password_manager.check_password_strength(password)
            if not password_check["is_valid"]:
                return False, f"密码不符合安全要求: {', '.join(password_check['issues'])}", None
            
            # 哈希密码
            password_hash, salt = self.password_manager.hash_password(password)
            
            # 创建用户
            user = User(
                user_id="",  # 将在__post_init__中生成
                username=username,
                email=email,
                password_hash=password_hash,
                salt=salt,
                role=role,
                full_name=full_name,
                require_password_change=self.config.require_password_change_on_first_login
            )
            
            # 存储用户
            self.users[user.user_id] = user
            self.username_to_user_id[username] = user.user_id
            self.email_to_user_id[email] = user.user_id
            
            # 保存用户到磁盘
            self._save_user(user)
            
            # 更新统计
            self.auth_stats["total_users"] += 1
            if user.is_active:
                self.auth_stats["active_users"] += 1
            
            logger.info(f"注册用户成功: {username}")
            
            return True, "用户注册成功", user
            
        except Exception as e:
            logger.error(f"注册用户失败: {e}")
            return False, f"注册失败: {str(e)}", None
    
    def authenticate_user(self, username: str, password: str, ip_address: str = "",
                         user_agent: str = "", remember_me: bool = False) -> Tuple[bool, str, Optional[Session]]:
        """
        用户认证登录
        
        Args:
            username: 用户名或邮箱
            password: 密码
            ip_address: IP地址
            user_agent: 用户代理
            remember_me: 是否记住登录
        
        Returns:
            Tuple[bool, str, Optional[Session]]: (是否成功, 消息, 会话对象)
        """
        try:
            self.auth_stats["login_attempts"] += 1
            
            # 查找用户
            user = self._find_user_by_username_or_email(username)
            if not user:
                self.auth_stats["failed_logins"] += 1
                logger.warning(f"登录失败: 用户不存在 - {username}")
                return False, "用户名或密码错误", None
            
            # 检查账户状态
            if not user.is_active:
                self.auth_stats["failed_logins"] += 1
                logger.warning(f"登录失败: 账户未激活 - {username}")
                return False, "账户未激活", None
            
            if user.is_locked:
                self.auth_stats["failed_logins"] += 1
                logger.warning(f"登录失败: 账户已锁定 - {username}")
                return False, "账户已锁定，请联系管理员", None
            
            # 验证密码
            if not self.password_manager.verify_password(password, user.password_hash, user.salt):
                user.increment_failed_login()
                self._save_user(user)
                self.auth_stats["failed_logins"] += 1
                
                if user.is_locked:
                    self.auth_stats["locked_accounts"] += 1
                    logger.warning(f"账户因多次登录失败被锁定: {username}")
                    return False, "登录失败次数过多，账户已锁定", None
                
                logger.warning(f"登录失败: 密码错误 - {username}")
                return False, "用户名或密码错误", None
            
            # 检查密码是否过期
            if user.is_password_expired(self.config.password_expiry_days):
                logger.info(f"用户密码已过期: {username}")
                return False, "密码已过期，请更改密码", None
            
            # 更新用户登录信息
            user.update_last_login()
            self._save_user(user)
            
            # 创建会话
            session = self.session_manager.create_session(
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                remember_me=remember_me
            )
            
            # 更新统计
            self.auth_stats["successful_logins"] += 1
            
            logger.info(f"用户登录成功: {username}")
            
            return True, "登录成功", session
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return False, f"登录失败: {str(e)}", None
    
    def logout_user(self, session_id: str) -> bool:
        """
        用户登出
        
        Args:
            session_id: 会话ID
        
        Returns:
            bool: 是否成功登出
        """
        try:
            success = self.session_manager.revoke_session(session_id)
            if success:
                logger.info(f"用户登出成功: 会话ID{session_id}")
            return success
            
        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return False
    
    def change_password(self, user_id: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        更改密码
        
        Args:
            user_id: 用户ID
            old_password: 旧密码
            new_password: 新密码
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            user = self.users.get(user_id)
            if not user:
                return False, "用户不存在"
            
            # 验证旧密码
            if not self.password_manager.verify_password(old_password, user.password_hash, user.salt):
                logger.warning(f"更改密码失败: 旧密码错误 - {user.username}")
                return False, "旧密码错误"
            
            # 检查新密码强度
            password_check = self.password_manager.check_password_strength(new_password)
            if not password_check["is_valid"]:
                return False, f"新密码不符合安全要求: {', '.join(password_check['issues'])}"
            
            # 检查密码历史（简化实现，实际应该存储密码历史）
            if old_password == new_password:
                return False, "新密码不能与旧密码相同"
            
            # 哈希新密码
            new_password_hash, new_salt = self.password_manager.hash_password(new_password)
            
            # 更新用户密码
            user.password_hash = new_password_hash
            user.salt = new_salt
            user.password_changed_at = datetime.now()
            user.require_password_change = False
            user.updated_at = datetime.now()
            
            # 保存用户
            self._save_user(user)
            
            # 如果配置要求，撤销所有会话
            if self.config.require_password_change_on_first_login:
                self.session_manager.revoke_user_sessions(user_id)
            
            logger.info(f"用户更改密码成功: {user.username}")
            
            return True, "密码更改成功"
            
        except Exception as e:
            logger.error(f"更改密码失败: {e}")
            return False, f"更改密码失败: {str(e)}"
    
    def reset_password(self, username_or_email: str) -> Tuple[bool, str, Optional[str]]:
        """
        重置密码
        
        Args:
            username_or_email: 用户名或邮箱
        
        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 重置令牌)
        """
        try:
            user = self._find_user_by_username_or_email(username_or_email)
            if not user:
                # 为了安全，不透露用户是否存在
                return True, "如果用户存在，重置链接已发送到邮箱", None
            
            # 生成重置令牌
            reset_payload = {
                "user_id": user.user_id,
                "username": user.username,
                "action": "password_reset"
            }
            
            reset_token = self.jwt_handler.generate_token(reset_payload, TokenType.RESET)
            
            logger.info(f"生成密码重置令牌: {user.username}")
            
            return True, "重置链接已发送到邮箱", reset_token
            
        except Exception as e:
            logger.error(f"重置密码失败: {e}")
            return False, f"重置密码失败: {str(e)}", None
    
    def confirm_password_reset(self, reset_token: str, new_password: str) -> Tuple[bool, str]:
        """
        确认密码重置
        
        Args:
            reset_token: 重置令牌
            new_password: 新密码
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 验证重置令牌
            payload = self.jwt_handler.verify_token(reset_token, TokenType.RESET)
            if not payload:
                return False, "重置令牌无效或已过期"
            
            user_id = payload.get("user_id")
            user = self.users.get(user_id)
            if not user:
                return False, "用户不存在"
            
            # 检查新密码强度
            password_check = self.password_manager.check_password_strength(new_password)
            if not password_check["is_valid"]:
                return False, f"新密码不符合安全要求: {', '.join(password_check['issues'])}"
            
            # 哈希新密码
            new_password_hash, new_salt = self.password_manager.hash_password(new_password)
            
            # 更新用户密码
            user.password_hash = new_password_hash
            user.salt = new_salt
            user.password_changed_at = datetime.now()
            user.require_password_change = False
            user.unlock_account()  # 解锁账户
            user.updated_at = datetime.now()
            
            # 保存用户
            self._save_user(user)
            
            # 撤销所有会话
            self.session_manager.revoke_user_sessions(user_id)
            
            # 撤销重置令牌
            self.jwt_handler.revoke_token(reset_token)
            
            logger.info(f"用户密码重置成功: {user.username}")
            
            return True, "密码重置成功"
            
        except Exception as e:
            logger.error(f"确认密码重置失败: {e}")
            return False, f"密码重置失败: {str(e)}"
    
    def get_user(self, user_id: str) -> Optional[User]:
        """获取用户"""
        return self.users.get(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        user_id = self.username_to_user_id.get(username)
        return self.users.get(user_id) if user_id else None
    
    def verify_session(self, session_id: str) -> Optional[Session]:
        """验证会话"""
        return self.session_manager.get_session(session_id)
    
    def verify_token(self, access_token: str) -> Optional[Session]:
        """验证访问令牌"""
        return self.session_manager.get_session_by_token(access_token)
    
    def _find_user_by_username_or_email(self, username_or_email: str) -> Optional[User]:
        """通过用户名或邮箱查找用户"""
        # 先尝试用户名
        user_id = self.username_to_user_id.get(username_or_email)
        if user_id:
            return self.users.get(user_id)
        
        # 再尝试邮箱
        user_id = self.email_to_user_id.get(username_or_email)
        if user_id:
            return self.users.get(user_id)
        
        return None
    
    def _save_user(self, user: User):
        """保存用户到磁盘"""
        try:
            user_file = os.path.join(self.data_dir, "users", f"{user.user_id}.json")
            with open(user_file, 'w', encoding='utf-8') as f:
                json.dump(user.to_dict(), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存用户失败: {e}")
    
    def _load_users(self):
        """从磁盘加载用户"""
        try:
            users_dir = os.path.join(self.data_dir, "users")
            if not os.path.exists(users_dir):
                return
            
            for filename in os.listdir(users_dir):
                if filename.endswith('.json'):
                    user_file = os.path.join(users_dir, filename)
                    try:
                        with open(user_file, 'r', encoding='utf-8') as f:
                            user_data = json.load(f)
                        
                        # 重建用户对象
                        user = User(
                            user_id=user_data["user_id"],
                            username=user_data["username"],
                            email=user_data["email"],
                            password_hash=user_data["password_hash"],
                            salt=user_data["salt"],
                            role=UserRole(user_data["role"]),
                            permissions={Permission(p) for p in user_data.get("permissions", [])},
                            full_name=user_data.get("full_name", ""),
                            phone=user_data.get("phone", ""),
                            department=user_data.get("department", ""),
                            is_active=user_data.get("is_active", True),
                            is_verified=user_data.get("is_verified", False),
                            is_locked=user_data.get("is_locked", False),
                            failed_login_attempts=user_data.get("failed_login_attempts", 0),
                            created_at=datetime.fromisoformat(user_data["created_at"]),
                            updated_at=datetime.fromisoformat(user_data["updated_at"]),
                            last_login_at=datetime.fromisoformat(user_data["last_login_at"]) if user_data.get("last_login_at") else None,
                            password_changed_at=datetime.fromisoformat(user_data["password_changed_at"]),
                            require_password_change=user_data.get("require_password_change", False),
                            two_factor_enabled=user_data.get("two_factor_enabled", False),
                            two_factor_secret=user_data.get("two_factor_secret", ""),
                            max_concurrent_sessions=user_data.get("max_concurrent_sessions", 3),
                            session_timeout_minutes=user_data.get("session_timeout_minutes", 480)
                        )
                        
                        self.users[user.user_id] = user
                        self.username_to_user_id[user.username] = user.user_id
                        self.email_to_user_id[user.email] = user.user_id
                        
                    except Exception as e:
                        logger.error(f"加载用户文件{filename}失败: {e}")
            
            # 更新统计
            self.auth_stats["total_users"] = len(self.users)
            self.auth_stats["active_users"] = len([u for u in self.users.values() if u.is_active])
            self.auth_stats["locked_accounts"] = len([u for u in self.users.values() if u.is_locked])
            
            logger.info(f"加载了{len(self.users)}个用户")
            
        except Exception as e:
            logger.error(f"加载用户失败: {e}")
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        try:
            admin_username = "admin"
            if admin_username not in self.username_to_user_id:
                # 使用符合密码策略的固定密码
                admin_password = "Admin123!"

                success, message, user = self.register_user(
                    username=admin_username,
                    email="<EMAIL>",
                    password=admin_password,
                    full_name="系统管理员",
                    role=UserRole.ADMIN
                )

                if success:
                    # 激活用户
                    user.is_active = True
                    user.is_verified = True
                    user.require_password_change = False  # 演示环境不强制修改密码
                    self._save_user(user)

                    logger.info(f"创建默认管理员用户成功: {admin_username}")
                    logger.info(f"默认管理员密码: {admin_password}")
                    print(f"🔐 默认管理员账户已创建:")
                    print(f"   用户名: {admin_username}")
                    print(f"   密码: {admin_password}")
                    print(f"   演示环境 - 密码固定为便于测试")

        except Exception as e:
            logger.error(f"创建默认管理员用户失败: {e}")

    def _create_default_operator(self):
        """创建默认操作员用户"""
        try:
            operator_username = "operator"
            if operator_username not in self.username_to_user_id:
                # 使用符合密码策略的固定密码
                operator_password = "Operator123!"

                success, message, user = self.register_user(
                    username=operator_username,
                    email="<EMAIL>",
                    password=operator_password,
                    full_name="系统操作员",
                    role=UserRole.OPERATOR
                )

                if success:
                    # 激活用户
                    user.is_active = True
                    user.is_verified = True
                    user.require_password_change = False  # 演示环境不强制修改密码
                    self._save_user(user)

                    logger.info(f"创建默认操作员用户成功: {operator_username}")
                    logger.info(f"默认操作员密码: {operator_password}")
                    print(f"👤 默认操作员账户已创建:")
                    print(f"   用户名: {operator_username}")
                    print(f"   密码: {operator_password}")
                    print(f"   演示环境 - 密码固定为便于测试")

        except Exception as e:
            logger.error(f"创建默认操作员用户失败: {e}")
    
    def get_auth_statistics(self) -> Dict:
        """获取认证统计信息"""
        return {
            "stats": self.auth_stats.copy(),
            "config": {
                "max_login_attempts": self.config.max_login_attempts,
                "account_lockout_minutes": self.config.account_lockout_minutes,
                "password_expiry_days": self.config.password_expiry_days,
                "max_concurrent_sessions": self.config.max_concurrent_sessions
            },
            "session_stats": self.session_manager.get_session_statistics(),
            "jwt_stats": self.jwt_handler.get_token_statistics()
        }
