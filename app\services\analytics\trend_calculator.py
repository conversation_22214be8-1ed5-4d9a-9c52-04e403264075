"""
趋势计算器

提供各种趋势计算算法和统计分析方法
"""

import math
import statistics
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger

logger = get_logger(__name__)


class TrendDirection(Enum):
    """趋势方向"""
    UP = "up"
    DOWN = "down"
    FLAT = "flat"
    VOLATILE = "volatile"


class TrendStrength(Enum):
    """趋势强度"""
    VERY_STRONG = "very_strong"    # 0.8-1.0
    STRONG = "strong"              # 0.6-0.8
    MODERATE = "moderate"          # 0.4-0.6
    WEAK = "weak"                  # 0.2-0.4
    VERY_WEAK = "very_weak"        # 0.0-0.2


@dataclass
class TrendResult:
    """趋势计算结果"""
    direction: TrendDirection
    strength: TrendStrength
    slope: float
    correlation: float
    confidence: float
    start_value: float
    end_value: float
    change_percent: float
    volatility: float


@dataclass
class MovingAverageResult:
    """移动平均结果"""
    values: List[float]
    period: int
    smoothing_factor: float


@dataclass
class SeasonalityResult:
    """季节性分析结果"""
    has_seasonality: bool
    seasonal_period: Optional[int]
    seasonal_strength: float
    seasonal_pattern: List[float]
    detrended_data: List[float]


class TrendCalculator:
    """趋势计算器"""
    
    def __init__(self):
        self.calculation_cache: Dict[str, Any] = {}
    
    def calculate_linear_trend(self, values: List[float], 
                             timestamps: Optional[List[datetime]] = None) -> TrendResult:
        """
        计算线性趋势
        
        Args:
            values: 数值列表
            timestamps: 时间戳列表（可选）
        
        Returns:
            TrendResult: 趋势计算结果
        """
        if len(values) < 2:
            return self._create_empty_trend_result(values)
        
        try:
            # 使用索引作为x值，如果没有提供时间戳
            x_values = list(range(len(values))) if timestamps is None else self._convert_timestamps_to_numeric(timestamps)
            
            # 计算线性回归
            slope, intercept, correlation = self._linear_regression(x_values, values)
            
            # 计算趋势方向
            direction = self._determine_trend_direction(slope, values)
            
            # 计算趋势强度
            strength = self._determine_trend_strength(abs(correlation))
            
            # 计算置信度
            confidence = self._calculate_confidence(values, slope, intercept, x_values)
            
            # 计算变化百分比
            start_value = values[0]
            end_value = values[-1]
            change_percent = ((end_value - start_value) / start_value * 100) if start_value != 0 else 0
            
            # 计算波动率
            volatility = self._calculate_volatility(values)
            
            return TrendResult(
                direction=direction,
                strength=strength,
                slope=slope,
                correlation=correlation,
                confidence=confidence,
                start_value=start_value,
                end_value=end_value,
                change_percent=change_percent,
                volatility=volatility
            )
            
        except Exception as e:
            logger.error(f"线性趋势计算失败: {e}")
            return self._create_empty_trend_result(values)
    
    def _convert_timestamps_to_numeric(self, timestamps: List[datetime]) -> List[float]:
        """将时间戳转换为数值"""
        if not timestamps:
            return []
        
        base_time = timestamps[0]
        return [(ts - base_time).total_seconds() for ts in timestamps]
    
    def _linear_regression(self, x_values: List[float], y_values: List[float]) -> Tuple[float, float, float]:
        """计算线性回归"""
        n = len(x_values)
        
        if n == 0:
            return 0.0, 0.0, 0.0
        
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(y_values)
        
        # 计算斜率和截距
        numerator = sum((x_values[i] - x_mean) * (y_values[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0, y_mean, 0.0
        
        slope = numerator / denominator
        intercept = y_mean - slope * x_mean
        
        # 计算相关系数
        y_variance = sum((y_values[i] - y_mean) ** 2 for i in range(n))
        if y_variance == 0:
            correlation = 0.0
        else:
            correlation = numerator / math.sqrt(denominator * y_variance)
        
        return slope, intercept, correlation
    
    def _determine_trend_direction(self, slope: float, values: List[float]) -> TrendDirection:
        """确定趋势方向"""
        if not values:
            return TrendDirection.FLAT
        
        # 计算相对斜率阈值
        avg_value = statistics.mean(values)
        relative_threshold = avg_value * 0.001  # 0.1%的变化阈值
        
        # 检查波动性
        if self._is_highly_volatile(values):
            return TrendDirection.VOLATILE
        
        if slope > relative_threshold:
            return TrendDirection.UP
        elif slope < -relative_threshold:
            return TrendDirection.DOWN
        else:
            return TrendDirection.FLAT
    
    def _is_highly_volatile(self, values: List[float]) -> bool:
        """检查是否高度波动"""
        if len(values) < 3:
            return False
        
        # 计算相邻值的变化率
        changes = []
        for i in range(1, len(values)):
            if values[i-1] != 0:
                change = abs(values[i] - values[i-1]) / values[i-1]
                changes.append(change)
        
        if not changes:
            return False
        
        # 如果变化率的标准差超过阈值，认为是高度波动
        change_std = statistics.stdev(changes) if len(changes) > 1 else 0
        return change_std > 0.1  # 10%的标准差阈值
    
    def _determine_trend_strength(self, correlation: float) -> TrendStrength:
        """确定趋势强度"""
        abs_correlation = abs(correlation)
        
        if abs_correlation >= 0.8:
            return TrendStrength.VERY_STRONG
        elif abs_correlation >= 0.6:
            return TrendStrength.STRONG
        elif abs_correlation >= 0.4:
            return TrendStrength.MODERATE
        elif abs_correlation >= 0.2:
            return TrendStrength.WEAK
        else:
            return TrendStrength.VERY_WEAK
    
    def _calculate_confidence(self, values: List[float], slope: float, 
                            intercept: float, x_values: List[float]) -> float:
        """计算置信度"""
        if len(values) < 3:
            return 0.0
        
        try:
            # 计算预测值
            predicted = [slope * x + intercept for x in x_values]
            
            # 计算残差平方和
            residual_sum_squares = sum((values[i] - predicted[i]) ** 2 for i in range(len(values)))
            
            # 计算总平方和
            mean_value = statistics.mean(values)
            total_sum_squares = sum((value - mean_value) ** 2 for value in values)
            
            # 计算R²
            if total_sum_squares == 0:
                return 0.0
            
            r_squared = 1 - (residual_sum_squares / total_sum_squares)
            return max(0.0, min(1.0, r_squared))
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def _calculate_volatility(self, values: List[float]) -> float:
        """计算波动率"""
        if len(values) < 2:
            return 0.0
        
        # 计算收益率
        returns = []
        for i in range(1, len(values)):
            if values[i-1] != 0:
                return_rate = (values[i] - values[i-1]) / values[i-1]
                returns.append(return_rate)
        
        if not returns:
            return 0.0
        
        # 返回标准差作为波动率
        return statistics.stdev(returns) if len(returns) > 1 else 0.0
    
    def _create_empty_trend_result(self, values: List[float]) -> TrendResult:
        """创建空的趋势结果"""
        start_value = values[0] if values else 0.0
        end_value = values[-1] if values else 0.0
        
        return TrendResult(
            direction=TrendDirection.FLAT,
            strength=TrendStrength.VERY_WEAK,
            slope=0.0,
            correlation=0.0,
            confidence=0.0,
            start_value=start_value,
            end_value=end_value,
            change_percent=0.0,
            volatility=0.0
        )
    
    def calculate_moving_average(self, values: List[float], 
                               period: int = 7,
                               method: str = "simple") -> MovingAverageResult:
        """
        计算移动平均
        
        Args:
            values: 数值列表
            period: 移动平均周期
            method: 方法类型 ("simple", "exponential", "weighted")
        
        Returns:
            MovingAverageResult: 移动平均结果
        """
        if len(values) < period:
            return MovingAverageResult(
                values=values.copy(),
                period=period,
                smoothing_factor=0.0
            )
        
        try:
            if method == "simple":
                ma_values = self._simple_moving_average(values, period)
                smoothing_factor = 1.0 / period
            elif method == "exponential":
                ma_values = self._exponential_moving_average(values, period)
                smoothing_factor = 2.0 / (period + 1)
            elif method == "weighted":
                ma_values = self._weighted_moving_average(values, period)
                smoothing_factor = 0.5  # 简化的权重因子
            else:
                ma_values = self._simple_moving_average(values, period)
                smoothing_factor = 1.0 / period
            
            return MovingAverageResult(
                values=ma_values,
                period=period,
                smoothing_factor=smoothing_factor
            )
            
        except Exception as e:
            logger.error(f"移动平均计算失败: {e}")
            return MovingAverageResult(
                values=values.copy(),
                period=period,
                smoothing_factor=0.0
            )
    
    def _simple_moving_average(self, values: List[float], period: int) -> List[float]:
        """简单移动平均"""
        result = []
        
        for i in range(len(values)):
            if i < period - 1:
                # 前面不足周期的数据，使用可用数据计算平均
                result.append(statistics.mean(values[:i+1]))
            else:
                # 使用完整周期计算移动平均
                result.append(statistics.mean(values[i-period+1:i+1]))
        
        return result
    
    def _exponential_moving_average(self, values: List[float], period: int) -> List[float]:
        """指数移动平均"""
        if not values:
            return []
        
        alpha = 2.0 / (period + 1)
        result = [values[0]]  # 第一个值作为初始值
        
        for i in range(1, len(values)):
            ema = alpha * values[i] + (1 - alpha) * result[-1]
            result.append(ema)
        
        return result
    
    def _weighted_moving_average(self, values: List[float], period: int) -> List[float]:
        """加权移动平均"""
        result = []
        
        # 生成权重（线性递增）
        weights = list(range(1, period + 1))
        weight_sum = sum(weights)
        
        for i in range(len(values)):
            if i < period - 1:
                # 前面不足周期的数据
                current_weights = weights[:i+1]
                current_weight_sum = sum(current_weights)
                weighted_sum = sum(values[j] * current_weights[j] for j in range(i+1))
                result.append(weighted_sum / current_weight_sum)
            else:
                # 使用完整周期计算加权平均
                weighted_sum = sum(values[i-period+1+j] * weights[j] for j in range(period))
                result.append(weighted_sum / weight_sum)
        
        return result
    
    def detect_seasonality(self, values: List[float], 
                         max_period: int = 30) -> SeasonalityResult:
        """
        检测季节性模式
        
        Args:
            values: 数值列表
            max_period: 最大检测周期
        
        Returns:
            SeasonalityResult: 季节性分析结果
        """
        if len(values) < max_period * 2:
            return SeasonalityResult(
                has_seasonality=False,
                seasonal_period=None,
                seasonal_strength=0.0,
                seasonal_pattern=[],
                detrended_data=values.copy()
            )
        
        try:
            # 寻找最佳季节性周期
            best_period = None
            best_strength = 0.0
            
            for period in range(2, min(max_period + 1, len(values) // 2)):
                strength = self._calculate_seasonal_strength(values, period)
                if strength > best_strength:
                    best_strength = strength
                    best_period = period
            
            # 判断是否有显著的季节性
            has_seasonality = best_strength > 0.3  # 30%的强度阈值
            
            if has_seasonality and best_period:
                # 计算季节性模式
                seasonal_pattern = self._extract_seasonal_pattern(values, best_period)
                
                # 去趋势化数据
                detrended_data = self._detrend_data(values, seasonal_pattern, best_period)
            else:
                seasonal_pattern = []
                detrended_data = values.copy()
            
            return SeasonalityResult(
                has_seasonality=has_seasonality,
                seasonal_period=best_period if has_seasonality else None,
                seasonal_strength=best_strength,
                seasonal_pattern=seasonal_pattern,
                detrended_data=detrended_data
            )
            
        except Exception as e:
            logger.error(f"季节性检测失败: {e}")
            return SeasonalityResult(
                has_seasonality=False,
                seasonal_period=None,
                seasonal_strength=0.0,
                seasonal_pattern=[],
                detrended_data=values.copy()
            )
    
    def _calculate_seasonal_strength(self, values: List[float], period: int) -> float:
        """计算季节性强度"""
        if len(values) < period * 2:
            return 0.0
        
        # 计算每个周期位置的平均值
        seasonal_averages = [[] for _ in range(period)]
        
        for i, value in enumerate(values):
            seasonal_position = i % period
            seasonal_averages[seasonal_position].append(value)
        
        # 计算每个位置的平均值
        position_means = []
        for position_values in seasonal_averages:
            if position_values:
                position_means.append(statistics.mean(position_values))
            else:
                position_means.append(0.0)
        
        # 计算季节性强度（变异系数）
        if not position_means or statistics.mean(position_means) == 0:
            return 0.0
        
        position_std = statistics.stdev(position_means) if len(position_means) > 1 else 0.0
        position_mean = statistics.mean(position_means)
        
        return position_std / position_mean if position_mean != 0 else 0.0
    
    def _extract_seasonal_pattern(self, values: List[float], period: int) -> List[float]:
        """提取季节性模式"""
        seasonal_sums = [0.0] * period
        seasonal_counts = [0] * period
        
        for i, value in enumerate(values):
            seasonal_position = i % period
            seasonal_sums[seasonal_position] += value
            seasonal_counts[seasonal_position] += 1
        
        # 计算每个位置的平均值
        seasonal_pattern = []
        for i in range(period):
            if seasonal_counts[i] > 0:
                seasonal_pattern.append(seasonal_sums[i] / seasonal_counts[i])
            else:
                seasonal_pattern.append(0.0)
        
        return seasonal_pattern
    
    def _detrend_data(self, values: List[float], seasonal_pattern: List[float], 
                     period: int) -> List[float]:
        """去趋势化数据"""
        if not seasonal_pattern:
            return values.copy()
        
        detrended = []
        overall_mean = statistics.mean(seasonal_pattern)
        
        for i, value in enumerate(values):
            seasonal_position = i % period
            seasonal_component = seasonal_pattern[seasonal_position] - overall_mean
            detrended_value = value - seasonal_component
            detrended.append(detrended_value)
        
        return detrended
    
    def calculate_correlation(self, x_values: List[float], 
                            y_values: List[float]) -> float:
        """计算相关系数"""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0
        
        try:
            x_mean = statistics.mean(x_values)
            y_mean = statistics.mean(y_values)
            
            numerator = sum((x_values[i] - x_mean) * (y_values[i] - y_mean) 
                          for i in range(len(x_values)))
            
            x_variance = sum((x - x_mean) ** 2 for x in x_values)
            y_variance = sum((y - y_mean) ** 2 for y in y_values)
            
            denominator = math.sqrt(x_variance * y_variance)
            
            if denominator == 0:
                return 0.0
            
            return numerator / denominator
            
        except Exception as e:
            logger.error(f"相关系数计算失败: {e}")
            return 0.0
    
    def calculate_statistics(self, values: List[float]) -> Dict[str, float]:
        """计算基础统计信息"""
        if not values:
            return {
                "count": 0,
                "mean": 0.0,
                "median": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0,
                "range": 0.0,
                "variance": 0.0
            }
        
        try:
            return {
                "count": len(values),
                "mean": statistics.mean(values),
                "median": statistics.median(values),
                "std": statistics.stdev(values) if len(values) > 1 else 0.0,
                "min": min(values),
                "max": max(values),
                "range": max(values) - min(values),
                "variance": statistics.variance(values) if len(values) > 1 else 0.0
            }
        except Exception as e:
            logger.error(f"统计信息计算失败: {e}")
            return {
                "count": len(values),
                "mean": 0.0,
                "median": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0,
                "range": 0.0,
                "variance": 0.0
            }
