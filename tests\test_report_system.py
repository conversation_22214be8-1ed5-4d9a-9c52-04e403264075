"""
报表生成系统测试
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from app.services.report_system.report_engine import (
    ReportEngine, ReportConfig, ReportType, ReportPeriod, ReportFormat
)
from app.services.report_system.chart_generator import (
    ChartGenerator, ChartData, ChartType
)
from app.services.report_system.report_scheduler import (
    ReportScheduler, ScheduleRule
)
from app.services.report_system.export_manager import (
    ExportManager, ExportFormat, ShareType
)


class TestReportEngine:
    """报表引擎测试"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """模拟依赖"""
        comprehensive_analyzer = Mock()
        cost_manager = Mock()
        profit_calculator = Mock()
        alert_engine = Mock()
        
        # 模拟预警汇总
        alert_summary = Mock()
        alert_summary.total_alerts = 10
        alert_summary.active_alerts = 3
        alert_summary.critical_alerts = 1
        alert_summary.high_alerts = 2
        alert_engine.get_alert_summary = AsyncMock(return_value=alert_summary)
        
        return comprehensive_analyzer, cost_manager, profit_calculator, alert_engine
    
    @pytest.fixture
    def report_engine(self, mock_dependencies):
        """报表引擎实例"""
        return ReportEngine(*mock_dependencies)
    
    def test_initialization(self, report_engine):
        """测试初始化"""
        assert len(report_engine.report_configs) == 3  # 3个默认配置
        assert "daily_competitor_analysis" in report_engine.report_configs
        assert "weekly_supplier_comparison" in report_engine.report_configs
        assert "monthly_profit_analysis" in report_engine.report_configs
    
    @pytest.mark.asyncio
    async def test_generate_competitor_analysis_report(self, report_engine):
        """测试生成竞品分析报表"""
        result = await report_engine.generate_report(
            "daily_competitor_analysis",
            format=ReportFormat.HTML
        )
        
        assert result.status == "completed"
        assert result.data is not None
        assert result.data.report_type == ReportType.COMPETITOR_ANALYSIS
        assert "竞品分析报表" in result.data.title
        assert len(result.data.sections) > 0
        assert "监控竞品数量" in result.data.summary
    
    @pytest.mark.asyncio
    async def test_generate_supplier_comparison_report(self, report_engine):
        """测试生成供应商对比报表"""
        result = await report_engine.generate_report(
            "weekly_supplier_comparison",
            format=ReportFormat.HTML
        )
        
        assert result.status == "completed"
        assert result.data is not None
        assert result.data.report_type == ReportType.SUPPLIER_COMPARISON
        assert "供应商对比报表" in result.data.title
        assert "供应商总数" in result.data.summary
    
    @pytest.mark.asyncio
    async def test_generate_profit_analysis_report(self, report_engine):
        """测试生成利润分析报表"""
        result = await report_engine.generate_report(
            "monthly_profit_analysis",
            format=ReportFormat.HTML
        )
        
        assert result.status == "completed"
        assert result.data is not None
        assert result.data.report_type == ReportType.PROFIT_ANALYSIS
        assert "利润分析报表" in result.data.title
        assert "总收入" in result.data.summary
        assert "总利润" in result.data.summary
    
    @pytest.mark.asyncio
    async def test_generate_json_report(self, report_engine):
        """测试生成JSON格式报表"""
        result = await report_engine.generate_report(
            "daily_competitor_analysis",
            format=ReportFormat.JSON
        )
        
        assert result.status == "completed"
        assert result.format == ReportFormat.JSON
        assert result.content.startswith("{")
        assert "report_id" in result.content
    
    @pytest.mark.asyncio
    async def test_generate_csv_report(self, report_engine):
        """测试生成CSV格式报表"""
        result = await report_engine.generate_report(
            "daily_competitor_analysis",
            format=ReportFormat.CSV
        )
        
        assert result.status == "completed"
        assert result.format == ReportFormat.CSV
        assert "," in result.content  # CSV应该包含逗号分隔符
    
    def test_add_report_config(self, report_engine):
        """测试添加报表配置"""
        config = ReportConfig(
            report_id="test_report",
            report_name="测试报表",
            report_type=ReportType.MARKET_OVERVIEW,
            report_period=ReportPeriod.DAILY
        )
        
        result = report_engine.add_report_config(config)
        assert result is True
        assert "test_report" in report_engine.report_configs
        
        # 测试重复添加
        result = report_engine.add_report_config(config)
        assert result is False
    
    def test_update_report_config(self, report_engine):
        """测试更新报表配置"""
        updates = {
            "report_name": "更新的报表名称",
            "enabled": False
        }
        
        result = report_engine.update_report_config("daily_competitor_analysis", updates)
        assert result is True
        
        config = report_engine.report_configs["daily_competitor_analysis"]
        assert config.report_name == "更新的报表名称"
        assert config.enabled is False
    
    def test_delete_report_config(self, report_engine):
        """测试删除报表配置"""
        result = report_engine.delete_report_config("daily_competitor_analysis")
        assert result is True
        assert "daily_competitor_analysis" not in report_engine.report_configs
        
        # 测试删除不存在的配置
        result = report_engine.delete_report_config("nonexistent")
        assert result is False
    
    def test_get_report_statistics(self, report_engine):
        """测试获取报表统计"""
        stats = report_engine.get_report_statistics()
        
        assert "total_reports" in stats
        assert "total_configs" in stats
        assert "type_distribution" in stats
        assert "available_types" in stats
        assert stats["total_configs"] == 3


class TestChartGenerator:
    """图表生成器测试"""
    
    @pytest.fixture
    def chart_generator(self):
        """图表生成器实例"""
        return ChartGenerator()
    
    @pytest.mark.asyncio
    async def test_generate_line_chart(self, chart_generator):
        """测试生成折线图"""
        chart_data = ChartData(
            chart_id="test_line_chart",
            title="测试折线图",
            chart_type=ChartType.LINE,
            data={
                "labels": ["1月", "2月", "3月", "4月"],
                "datasets": [{
                    "label": "销量",
                    "data": [100, 150, 120, 180]
                }]
            },
            options={}
        )
        
        result = await chart_generator.generate_chart(chart_data)
        
        assert result.status == "completed"
        assert result.html_content is not None
        assert "chart.js" in result.html_content
        assert "test_line_chart" in result.html_content
    
    @pytest.mark.asyncio
    async def test_generate_bar_chart(self, chart_generator):
        """测试生成柱状图"""
        chart_data = ChartData(
            chart_id="test_bar_chart",
            title="测试柱状图",
            chart_type=ChartType.BAR,
            data={
                "labels": ["产品A", "产品B", "产品C"],
                "datasets": [{
                    "label": "销量",
                    "data": [300, 250, 400]
                }]
            },
            options={}
        )
        
        result = await chart_generator.generate_chart(chart_data)
        
        assert result.status == "completed"
        assert result.html_content is not None
        assert "bar" in result.html_content
    
    @pytest.mark.asyncio
    async def test_generate_pie_chart(self, chart_generator):
        """测试生成饼图"""
        chart_data = ChartData(
            chart_id="test_pie_chart",
            title="测试饼图",
            chart_type=ChartType.PIE,
            data={
                "labels": ["iPhone", "Samsung", "华为"],
                "datasets": [{
                    "data": [40, 30, 30]
                }]
            },
            options={}
        )
        
        result = await chart_generator.generate_chart(chart_data)
        
        assert result.status == "completed"
        assert result.html_content is not None
        assert "pie" in result.html_content
    
    def test_get_chart_statistics(self, chart_generator):
        """测试获取图表统计"""
        stats = chart_generator.get_chart_statistics()
        
        assert "total_charts" in stats
        assert "successful_charts" in stats
        assert "type_distribution" in stats
        assert "available_types" in stats


class TestReportScheduler:
    """报表调度器测试"""
    
    @pytest.fixture
    def mock_report_engine(self):
        """模拟报表引擎"""
        engine = Mock()
        
        # 模拟报表配置
        config = Mock()
        config.report_id = "test_report"
        config.auto_generate = True
        config.report_period = ReportPeriod.DAILY
        
        engine.get_report_configs.return_value = [config]
        
        # 模拟报表生成结果
        result = Mock()
        result.report_id = "test_result"
        result.status = "completed"
        
        engine.generate_report = AsyncMock(return_value=result)
        
        return engine
    
    @pytest.fixture
    def report_scheduler(self, mock_report_engine):
        """报表调度器实例"""
        return ReportScheduler(mock_report_engine)
    
    def test_initialization(self, report_scheduler):
        """测试初始化"""
        assert len(report_scheduler.schedule_rules) == 1  # 1个自动生成规则
        assert "auto_test_report" in report_scheduler.schedule_rules
    
    def test_add_schedule_rule(self, report_scheduler):
        """测试添加调度规则"""
        rule = ScheduleRule(
            rule_id="test_rule",
            report_id="test_report",
            period=ReportPeriod.WEEKLY
        )
        
        result = report_scheduler.add_schedule_rule(rule)
        assert result is True
        assert "test_rule" in report_scheduler.schedule_rules
        
        # 测试重复添加
        result = report_scheduler.add_schedule_rule(rule)
        assert result is False
    
    def test_update_schedule_rule(self, report_scheduler):
        """测试更新调度规则"""
        updates = {
            "enabled": False,
            "period": "weekly"
        }
        
        result = report_scheduler.update_schedule_rule("auto_test_report", updates)
        assert result is True
        
        rule = report_scheduler.schedule_rules["auto_test_report"]
        assert rule.enabled is False
        assert rule.period == ReportPeriod.WEEKLY
    
    def test_delete_schedule_rule(self, report_scheduler):
        """测试删除调度规则"""
        result = report_scheduler.delete_schedule_rule("auto_test_report")
        assert result is True
        assert "auto_test_report" not in report_scheduler.schedule_rules
        
        # 测试删除不存在的规则
        result = report_scheduler.delete_schedule_rule("nonexistent")
        assert result is False
    
    def test_get_scheduler_statistics(self, report_scheduler):
        """测试获取调度器统计"""
        stats = report_scheduler.get_scheduler_statistics()
        
        assert "total_tasks" in stats
        assert "total_rules" in stats
        assert "enabled_rules" in stats
        assert "is_running" in stats
        assert stats["is_running"] is False


class TestExportManager:
    """导出管理器测试"""
    
    @pytest.fixture
    def export_manager(self):
        """导出管理器实例"""
        return ExportManager()
    
    @pytest.fixture
    def mock_report_result(self):
        """模拟报表结果"""
        result = Mock()
        result.report_id = "test_report_123"
        result.content = "<html><body><h1>测试报表</h1></body></html>"
        result.format = ReportFormat.HTML
        result.data = Mock()
        result.data.title = "测试报表"
        result.data.summary = {"总数": 100}
        result.data.sections = []
        result.data.generated_at = datetime.now()
        result.config = Mock()
        result.config.report_name = "测试报表"
        result.config.report_type = ReportType.COMPETITOR_ANALYSIS
        result.generation_time = 1.5
        result.status = "completed"
        
        return result
    
    @pytest.mark.asyncio
    async def test_export_to_html(self, export_manager, mock_report_result):
        """测试导出为HTML"""
        task = await export_manager.export_report(mock_report_result, ExportFormat.HTML)
        
        assert task.status == "completed"
        assert task.file_path is not None
        assert task.file_size > 0
        assert task.export_format == ExportFormat.HTML
    
    @pytest.mark.asyncio
    async def test_export_to_json(self, export_manager, mock_report_result):
        """测试导出为JSON"""
        task = await export_manager.export_report(mock_report_result, ExportFormat.JSON)
        
        assert task.status == "completed"
        assert task.file_path is not None
        assert task.file_size > 0
        assert task.export_format == ExportFormat.JSON
    
    @pytest.mark.asyncio
    async def test_export_to_csv(self, export_manager, mock_report_result):
        """测试导出为CSV"""
        task = await export_manager.export_report(mock_report_result, ExportFormat.CSV)
        
        assert task.status == "completed"
        assert task.file_path is not None
        assert task.file_size > 0
        assert task.export_format == ExportFormat.CSV
    
    def test_create_share_link(self, export_manager):
        """测试创建分享链接"""
        share_link = export_manager.create_share_link(
            "test_report",
            ShareType.PUBLIC
        )
        
        assert share_link.report_id == "test_report"
        assert share_link.share_type == ShareType.PUBLIC
        assert share_link.url.startswith("https://reports.company.com/share/")
        assert share_link.link_id in export_manager.share_links
    
    def test_create_password_share_link(self, export_manager):
        """测试创建密码保护分享链接"""
        share_link = export_manager.create_share_link(
            "test_report",
            ShareType.PASSWORD,
            password="test123"
        )
        
        assert share_link.share_type == ShareType.PASSWORD
        assert share_link.password == "test123"
    
    def test_access_share_link(self, export_manager):
        """测试访问分享链接"""
        # 创建公开分享链接
        share_link = export_manager.create_share_link("test_report", ShareType.PUBLIC)
        
        # 访问链接
        success, message, link = export_manager.access_share_link(share_link.link_id)
        
        assert success is True
        assert message == "访问成功"
        assert link is not None
        assert link.access_count == 1
    
    def test_access_password_share_link(self, export_manager):
        """测试访问密码保护分享链接"""
        # 创建密码保护分享链接
        share_link = export_manager.create_share_link(
            "test_report", 
            ShareType.PASSWORD, 
            password="test123"
        )
        
        # 错误密码访问
        success, message, link = export_manager.access_share_link(
            share_link.link_id, 
            "wrong_password"
        )
        assert success is False
        assert "密码错误" in message
        
        # 正确密码访问
        success, message, link = export_manager.access_share_link(
            share_link.link_id, 
            "test123"
        )
        assert success is True
        assert message == "访问成功"
    
    def test_get_export_statistics(self, export_manager):
        """测试获取导出统计"""
        stats = export_manager.get_export_statistics()
        
        assert "total_exports" in stats
        assert "total_shares" in stats
        assert "format_distribution" in stats
        assert "share_type_distribution" in stats
        assert "available_export_formats" in stats
