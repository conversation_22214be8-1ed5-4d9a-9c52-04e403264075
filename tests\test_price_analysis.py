"""
价格趋势分析引擎测试

测试价格分析、趋势计算、预测等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
from app.services.analytics.price_analyzer import (
    PriceAnalyzer, PriceTrend, PriceAlert, PricePoint
)
from app.services.analytics.sales_analyzer import (
    SalesAnalyzer, SalesTrend, SalesPerformance, SalesPoint
)
from app.services.analytics.trend_calculator import (
    TrendCalculator, TrendDirection, TrendStrength
)
from app.services.analytics.prediction_engine import (
    PredictionEngine, PredictionMethod
)


class TestPriceAnalyzer:
    """价格分析器测试"""
    
    @pytest.fixture
    def price_analyzer(self):
        """创建价格分析器实例"""
        return PriceAnalyzer()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 256GB",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=8999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=15000)
        )
    
    @pytest.mark.asyncio
    async def test_add_price_data(self, price_analyzer, sample_product):
        """测试添加价格数据"""
        product_id = sample_product.id
        
        # 添加价格数据
        await price_analyzer.add_price_data(product_id, 8999.00)
        await price_analyzer.add_price_data(product_id, 8899.00)
        await price_analyzer.add_price_data(product_id, 8799.00)
        
        # 检查数据是否添加成功
        assert product_id in price_analyzer.price_history
        assert len(price_analyzer.price_history[product_id]) == 3
        
        # 检查数据排序
        prices = [point.price for point in price_analyzer.price_history[product_id]]
        assert prices == [8999.00, 8899.00, 8799.00]
    
    @pytest.mark.asyncio
    async def test_analyze_price_trend_no_data(self, price_analyzer, sample_product):
        """测试无历史数据的价格趋势分析"""
        result = await price_analyzer.analyze_price_trend(sample_product)
        
        assert result.product_id == sample_product.id
        assert result.price_trend == PriceTrend.UNKNOWN
        assert result.current_price == 8999.00
        assert result.trend_strength == 0.0
    
    @pytest.mark.asyncio
    async def test_analyze_price_trend_with_data(self, price_analyzer, sample_product):
        """测试有历史数据的价格趋势分析"""
        product_id = sample_product.id
        
        # 添加下降趋势的价格数据
        base_time = datetime.now() - timedelta(days=10)
        prices = [9000, 8900, 8800, 8700, 8600]
        
        for i, price in enumerate(prices):
            timestamp = base_time + timedelta(days=i*2)
            await price_analyzer.add_price_data(product_id, price, timestamp)
        
        # 分析趋势
        result = await price_analyzer.analyze_price_trend(sample_product, days=10)
        
        assert result.product_id == product_id
        assert result.price_trend == PriceTrend.FALLING
        assert result.current_price == 8600
        assert result.price_change_percent < 0  # 价格下降
        assert result.trend_strength > 0.5  # 趋势明显
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_prices(self, price_analyzer, sample_product):
        """测试竞品价格分析"""
        # 创建竞品商品
        competitor1 = Product(
            url="https://item.taobao.com/item.htm?id=111111",
            title="Samsung Galaxy S24 Ultra",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=9999.00)
        )
        
        competitor2 = Product(
            url="https://item.taobao.com/item.htm?id=222222",
            title="Huawei Mate 60 Pro",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=7999.00)
        )
        
        competitors = [competitor1, competitor2]
        
        # 分析竞品价格
        result = await price_analyzer.analyze_competitor_prices(sample_product, competitors)
        
        assert result.product_id == sample_product.id
        assert len(result.competitor_products) == 2
        assert result.market_position in ["lowest", "middle", "highest"]
        assert isinstance(result.price_advantage, float)
        assert len(result.recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_supplier_prices(self, price_analyzer):
        """测试供应商价格分析"""
        # 创建供应商商品
        supplier_product = Product(
            url="https://detail.1688.com/offer/123456.html",
            title="手机壳批发 透明硅胶保护套",
            product_type=ProductType.SUPPLIER,
            price=ProductPrice(current_price=3.80)
        )
        
        # 添加价格历史数据
        product_id = supplier_product.id
        base_time = datetime.now() - timedelta(days=30)
        
        # 模拟成本上涨趋势
        for i in range(10):
            price = 3.50 + i * 0.05  # 逐渐上涨
            timestamp = base_time + timedelta(days=i*3)
            await price_analyzer.add_price_data(product_id, price, timestamp)
        
        # 分析供应商价格
        result = await price_analyzer.analyze_supplier_prices(supplier_product, "supplier_001")
        
        assert result.product_id == product_id
        assert result.supplier_id == "supplier_001"
        assert result.cost_trend.value in [trend.value for trend in PriceTrend]
        assert isinstance(result.cost_change_percent, float)
        assert result.supply_risk_level in ["low", "medium", "high"]
        assert len(result.recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_batch_analyze_prices(self, price_analyzer):
        """测试批量价格分析"""
        # 创建多个商品
        products = []
        for i in range(3):
            product = Product(
                url=f"https://example.com/{i}",
                title=f"测试商品 {i}",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=100.0 + i * 10)
            )
            products.append(product)
        
        # 批量分析
        results = await price_analyzer.batch_analyze_prices(products)
        
        assert len(results) == 3
        for result in results:
            assert result.product_id in [p.id for p in products]
            assert isinstance(result.current_price, float)
    
    def test_get_analysis_statistics(self, price_analyzer):
        """测试获取分析统计信息"""
        stats = price_analyzer.get_analysis_statistics()
        
        assert "total_products" in stats
        assert "total_price_points" in stats
        assert "cached_analyses" in stats
        assert "trend_distribution" in stats
        assert "alert_thresholds" in stats


class TestSalesAnalyzer:
    """销量分析器测试"""
    
    @pytest.fixture
    def sales_analyzer(self):
        """创建销量分析器实例"""
        return SalesAnalyzer()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="热销商品",
            product_type=ProductType.COMPETITOR,
            metrics=ProductMetrics(sales_count=5000)
        )
    
    @pytest.mark.asyncio
    async def test_add_sales_data(self, sales_analyzer, sample_product):
        """测试添加销量数据"""
        product_id = sample_product.id
        
        # 添加销量数据
        await sales_analyzer.add_sales_data(product_id, 1000)
        await sales_analyzer.add_sales_data(product_id, 1200)
        await sales_analyzer.add_sales_data(product_id, 1500)
        
        # 检查数据是否添加成功
        assert product_id in sales_analyzer.sales_history
        assert len(sales_analyzer.sales_history[product_id]) == 3
        
        # 检查数据内容
        sales_counts = [point.sales_count for point in sales_analyzer.sales_history[product_id]]
        assert sales_counts == [1000, 1200, 1500]
    
    @pytest.mark.asyncio
    async def test_analyze_sales_trend_growing(self, sales_analyzer, sample_product):
        """测试增长趋势的销量分析"""
        product_id = sample_product.id
        
        # 添加增长趋势的销量数据
        base_time = datetime.now() - timedelta(days=14)
        sales_data = [1000, 1100, 1200, 1300, 1400, 1500, 1600]
        
        for i, sales in enumerate(sales_data):
            timestamp = base_time + timedelta(days=i*2)
            await sales_analyzer.add_sales_data(product_id, sales, timestamp)
        
        # 分析趋势
        result = await sales_analyzer.analyze_sales_trend(sample_product, days=14)
        
        assert result.product_id == product_id
        assert result.sales_trend == SalesTrend.GROWING
        assert result.current_sales == 1600
        assert result.sales_growth_rate > 0
        assert result.total_sales == sum(sales_data)
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_sales(self, sales_analyzer, sample_product):
        """测试竞品销量分析"""
        # 创建竞品商品
        competitor1 = Product(
            url="https://item.taobao.com/item.htm?id=111111",
            title="竞品1",
            product_type=ProductType.COMPETITOR,
            metrics=ProductMetrics(sales_count=8000)
        )
        
        competitor2 = Product(
            url="https://item.taobao.com/item.htm?id=222222",
            title="竞品2",
            product_type=ProductType.COMPETITOR,
            metrics=ProductMetrics(sales_count=3000)
        )
        
        competitors = [competitor1, competitor2]
        
        # 分析竞品销量
        result = await sales_analyzer.analyze_competitor_sales(sample_product, competitors)
        
        assert result.product_id == sample_product.id
        assert len(result.competitor_products) == 2
        assert result.market_ranking > 0
        assert 0 <= result.market_share <= 100
        assert result.competitive_advantage in ["leading", "strong", "competitive", "weak"]
    
    def test_evaluate_sales_performance(self, sales_analyzer):
        """测试销售表现评估"""
        # 创建不同销量的商品
        excellent_product = Product(
            url="https://example.com/excellent",
            title="优秀商品",
            product_type=ProductType.COMPETITOR
        )
        
        poor_product = Product(
            url="https://example.com/poor",
            title="较差商品",
            product_type=ProductType.COMPETITOR
        )
        
        # 测试评估
        excellent_performance = sales_analyzer._evaluate_sales_performance(excellent_product, 15000)
        poor_performance = sales_analyzer._evaluate_sales_performance(poor_product, 50)
        
        assert excellent_performance == SalesPerformance.EXCELLENT
        assert poor_performance == SalesPerformance.POOR


class TestTrendCalculator:
    """趋势计算器测试"""
    
    @pytest.fixture
    def trend_calculator(self):
        """创建趋势计算器实例"""
        return TrendCalculator()
    
    def test_calculate_linear_trend_rising(self, trend_calculator):
        """测试上升趋势计算"""
        values = [100, 110, 120, 130, 140, 150]
        
        result = trend_calculator.calculate_linear_trend(values)
        
        assert result.direction == TrendDirection.UP
        assert result.slope > 0
        assert result.correlation > 0.9  # 强相关
        assert result.change_percent > 0
    
    def test_calculate_linear_trend_falling(self, trend_calculator):
        """测试下降趋势计算"""
        values = [150, 140, 130, 120, 110, 100]
        
        result = trend_calculator.calculate_linear_trend(values)
        
        assert result.direction == TrendDirection.DOWN
        assert result.slope < 0
        assert abs(result.correlation) > 0.9  # 强相关（绝对值）
        assert result.change_percent < 0
    
    def test_calculate_linear_trend_flat(self, trend_calculator):
        """测试平稳趋势计算"""
        values = [100, 100, 100, 100, 100, 100, 100]  # 完全平稳的数据

        result = trend_calculator.calculate_linear_trend(values)

        assert result.direction == TrendDirection.FLAT
        assert abs(result.slope) < 0.1  # 斜率接近0
        assert abs(result.change_percent) < 1  # 变化小于1%
    
    def test_calculate_moving_average(self, trend_calculator):
        """测试移动平均计算"""
        values = [100, 110, 120, 130, 140, 150, 160]
        
        # 简单移动平均
        result = trend_calculator.calculate_moving_average(values, period=3, method="simple")
        
        assert len(result.values) == len(values)
        assert result.period == 3
        assert result.smoothing_factor > 0
        
        # 指数移动平均
        exp_result = trend_calculator.calculate_moving_average(values, period=3, method="exponential")
        
        assert len(exp_result.values) == len(values)
        assert exp_result.values[0] == values[0]  # 第一个值相同
    
    def test_detect_seasonality(self, trend_calculator):
        """测试季节性检测"""
        # 创建有明显周期性的数据
        seasonal_values = []
        for i in range(28):  # 4周数据
            day_of_week = i % 7
            base_value = 100
            if day_of_week in [5, 6]:  # 周末销量高
                seasonal_value = base_value + 50
            else:
                seasonal_value = base_value
            seasonal_values.append(seasonal_value)
        
        result = trend_calculator.detect_seasonality(seasonal_values, max_period=14)
        
        assert isinstance(result.has_seasonality, bool)
        if result.has_seasonality:
            assert result.seasonal_period is not None
            assert result.seasonal_strength > 0
            assert len(result.seasonal_pattern) > 0
    
    def test_calculate_correlation(self, trend_calculator):
        """测试相关系数计算"""
        x_values = [1, 2, 3, 4, 5]
        y_values = [2, 4, 6, 8, 10]  # 完全正相关
        
        correlation = trend_calculator.calculate_correlation(x_values, y_values)
        
        assert abs(correlation - 1.0) < 0.01  # 接近1
    
    def test_calculate_statistics(self, trend_calculator):
        """测试统计信息计算"""
        values = [100, 110, 120, 130, 140]
        
        stats = trend_calculator.calculate_statistics(values)
        
        assert stats["count"] == 5
        assert stats["mean"] == 120
        assert stats["median"] == 120
        assert stats["min"] == 100
        assert stats["max"] == 140
        assert stats["range"] == 40


class TestPredictionEngine:
    """预测引擎测试"""
    
    @pytest.fixture
    def prediction_engine(self):
        """创建预测引擎实例"""
        return PredictionEngine()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="预测测试商品",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=1000.00),
            metrics=ProductMetrics(sales_count=5000)
        )
    
    @pytest.mark.asyncio
    async def test_predict_price_trend_linear(self, prediction_engine, sample_product):
        """测试线性回归价格预测"""
        # 创建上升趋势的历史价格
        historical_prices = [900.0, 920.0, 940.0, 960.0, 980.0, 1000.0]
        historical_timestamps = [
            datetime.now() - timedelta(days=i*5) 
            for i in range(len(historical_prices)-1, -1, -1)
        ]
        
        # 进行预测
        result = await prediction_engine.predict_price_trend(
            sample_product, historical_prices, historical_timestamps, 
            prediction_days=7, method=PredictionMethod.LINEAR_REGRESSION
        )
        
        assert result.current_price == 1000.0
        assert len(result.predicted_prices) == 7
        assert result.trend_direction == "up"
        assert result.price_change_percent > 0
        assert 0 <= result.confidence_score <= 1
    
    @pytest.mark.asyncio
    async def test_predict_sales_trend(self, prediction_engine, sample_product):
        """测试销量趋势预测"""
        # 创建增长趋势的历史销量
        historical_sales = [3000, 3500, 4000, 4500, 5000]
        historical_timestamps = [
            datetime.now() - timedelta(days=i*7) 
            for i in range(len(historical_sales)-1, -1, -1)
        ]
        
        # 进行预测
        result = await prediction_engine.predict_sales_trend(
            sample_product, historical_sales, historical_timestamps, 
            prediction_days=14
        )
        
        assert result.current_sales == 5000
        assert len(result.predicted_sales) == 14
        assert result.sales_growth_rate > 0
        assert len(result.market_factors) > 0
    
    @pytest.mark.asyncio
    async def test_batch_predict_prices(self, prediction_engine):
        """测试批量价格预测"""
        # 创建多个商品
        products = []
        historical_data = {}
        
        for i in range(3):
            product = Product(
                url=f"https://example.com/{i}",
                title=f"预测商品 {i}",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=100.0 + i * 10)
            )
            products.append(product)
            
            # 创建历史数据
            prices = [90.0 + i * 10 + j * 2 for j in range(5)]
            timestamps = [datetime.now() - timedelta(days=j) for j in range(4, -1, -1)]
            historical_data[product.id] = (prices, timestamps)
        
        # 批量预测
        results = await prediction_engine.batch_predict_prices(products, historical_data)
        
        assert len(results) == 3
        for product in products:
            assert product.id in results
            prediction = results[product.id]
            assert isinstance(prediction.current_price, float)
    
    def test_get_prediction_statistics(self, prediction_engine):
        """测试获取预测统计信息"""
        stats = prediction_engine.get_prediction_statistics()
        
        assert "cached_predictions" in stats
        assert "model_weights" in stats
        assert "supported_methods" in stats
        assert "confidence_levels" in stats


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
