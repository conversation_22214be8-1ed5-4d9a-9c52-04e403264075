# 电商商品监控系统优化设计文档

## 概述

电商商品监控系统是一个基于现有task-middleware和webui基础设施的专业化监控解决方案。本优化版本在保持核心功能的同时，简化了架构复杂度，优化了数据存储，增强了监控能力，并支持动态配置管理。

### 核心特性

- **简化架构**：合并相关服务，减少服务间通信开销
- **智能缓存**：多层缓存策略，提升系统性能
- **统一监控**：完整的指标体系和告警机制
- **动态配置**：支持配置热更新，无需重启服务
- **多平台支持**：支持主要国际电商平台的商品监控
- **智能翻译**：集成LLM API实现多语言商品信息翻译

## 优化后的架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "电商监控系统 (优化架构)"
        subgraph "前端层"
            A[WebUI - React/TypeScript]
            B[移动端响应式界面]
        end
        
        subgraph "API网关层"
            C[FastAPI Gateway + 认证 + 限流]
        end
        
        subgraph "核心服务层 (合并优化)"
            D[商品监控服务<br/>- 商品管理<br/>- 监控任务<br/>- 平台配置]
            E[数据分析服务<br/>- 趋势分析<br/>- 预警处理<br/>- 报表生成]
            F[翻译服务<br/>- 多LLM支持<br/>- 批量处理<br/>- 质量评估]
        end
        
        subgraph "配置中心"
            G[配置管理服务<br/>- 热更新<br/>- 版本控制<br/>- 分发机制]
        end
        
        subgraph "监控中心"
            H[监控服务<br/>- 指标收集<br/>- 告警处理<br/>- 健康检查]
        end
        
        subgraph "存储层 (优化)"
            I[TimescaleDB<br/>- 智能分区<br/>- 自动归档]
            J[PostgreSQL<br/>- 配置数据<br/>- 用户数据]
            K[Redis集群<br/>- 多层缓存<br/>- 会话存储]
        end
    end
    
    subgraph "外部服务"
        L[Task Middleware API]
        M[LLM API Services]
        N[电商平台]
        O[通知服务]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    
    D -.->|HTTP API| L
    F -.->|HTTP API| M
    E -.->|HTTP API| O
    
    D --> I
    D --> J
    E --> I
    F --> K
    
    G --> D
    G --> E
    G --> F
    
    H --> D
    H --> E
    H --> F
    H --> I
    H --> J
    H --> K
    
    L --> N
```

### 架构优化说明

#### 1. 服务合并优化
- **商品监控服务**：合并原来的商品管理、监控任务、平台配置三个独立服务
- **数据分析服务**：整合数据分析、预警、报表功能到单一服务
- **配置中心**：新增统一配置管理，支持热更新
- **监控中心**：统一的系统和业务监控

#### 2. 通信优化
- 减少服务间HTTP调用，改为内部方法调用
- 使用事件驱动架构处理异步操作
- 统一的配置分发机制

## 优化后的技术栈

- **前端**: React 18, TypeScript, Redux Toolkit, Ant Design, ECharts
- **后端**: Python 3.11, FastAPI, Pydantic, SQLAlchemy
- **任务处理**: Celery, Redis Cluster
- **数据库**: TimescaleDB (优化分区), PostgreSQL, Redis Cluster
- **配置管理**: Consul/etcd (配置中心)
- **监控**: Prometheus, Grafana, AlertManager
- **缓存**: Redis Cluster (多层缓存)
- **部署**: Docker, Kubernetes

## 核心服务设计

### 1. 商品监控服务 (合并优化)

```python
class ProductMonitoringService:
    """商品监控核心服务 - 合并商品管理、监控任务、平台配置"""
    
    def __init__(self, 
                 config_client: ConfigClient,
                 cache_manager: CacheManager,
                 crawler_client: CrawlerApiClient,
                 translation_client: TranslationClient):
        self.config = config_client
        self.cache = cache_manager
        self.crawler = crawler_client
        self.translator = translation_client
        self.metrics = MetricsCollector()
    
    # 商品管理功能
    async def import_products_from_excel(self, file_data: bytes) -> ImportResult
    async def add_product_url(self, url: str, metadata: ProductMetadata) -> Product
    async def get_products(self, filters: ProductFilter) -> List[Product]
    
    # 监控任务功能
    async def create_monitoring_task(self, product: Product) -> MonitoringTask
    async def schedule_batch_monitoring(self, products: List[Product]) -> BatchResult
    async def process_monitoring_result(self, result: CrawlResult) -> ProcessedData
    
    # 平台配置功能
    async def get_platform_config(self, platform: str) -> PlatformConfig
    async def update_platform_config(self, platform: str, config: PlatformConfig) -> bool
    async def test_platform_config(self, config: PlatformConfig) -> TestResult
    
    # 内部优化方法
    async def _get_cached_config(self, platform: str) -> PlatformConfig:
        """从缓存获取平台配置，支持热更新"""
        cache_key = f"platform_config:{platform}"
        config = await self.cache.get(cache_key)
        if not config:
            config = await self._load_config_from_db(platform)
            await self.cache.set(cache_key, config, ttl=300)
        return config
    
    async def _batch_process_with_cache(self, products: List[Product]) -> List[ProcessedData]:
        """批量处理商品数据，使用缓存优化"""
        results = []
        cached_results = await self.cache.mget([f"product_data:{p.id}" for p in products])
        
        for i, product in enumerate(products):
            if cached_results[i]:
                results.append(cached_results[i])
            else:
                result = await self._process_single_product(product)
                await self.cache.set(f"product_data:{product.id}", result, ttl=1800)
                results.append(result)
        
        return results
```

### 2. 数据分析服务 (整合优化)

```python
class DataAnalyticsService:
    """数据分析服务 - 整合分析、预警、报表功能"""
    
    def __init__(self, 
                 timeseries_repo: TimeSeriesRepository,
                 cache_manager: CacheManager,
                 alert_manager: AlertManager,
                 config_client: ConfigClient):
        self.timeseries = timeseries_repo
        self.cache = cache_manager
        self.alerts = alert_manager
        self.config = config_client
        self.metrics = MetricsCollector()
    
    # 趋势分析功能
    async def get_price_trend(self, product_id: str, time_range: TimeRange) -> PriceTrend
    async def analyze_market_opportunity(self, category: str) -> MarketAnalysis
    async def generate_recommendations(self, criteria: PurchaseCriteria) -> List[Recommendation]
    
    # 预警功能
    async def evaluate_alert_conditions(self, product_data: ProductData) -> List[Alert]
    async def create_alert_rule(self, rule: AlertRule) -> str
    async def process_alerts(self, alerts: List[Alert]) -> AlertProcessResult
    
    # 报表功能
    async def generate_trend_report(self, params: ReportParams) -> TrendReport
    async def export_data(self, export_request: ExportRequest) -> ExportResult
    
    # 内部优化方法
    async def _get_cached_analysis(self, cache_key: str, analysis_func, *args) -> Any:
        """通用缓存分析结果"""
        result = await self.cache.get(cache_key)
        if not result:
            result = await analysis_func(*args)
            ttl = self.config.get("analysis_cache_ttl", 3600)
            await self.cache.set(cache_key, result, ttl=ttl)
        return result
    
    async def _batch_alert_processing(self, product_data_list: List[ProductData]) -> List[Alert]:
        """批量处理预警，优化性能"""
        alert_rules = await self.cache.get("active_alert_rules")
        if not alert_rules:
            alert_rules = await self._load_alert_rules()
            await self.cache.set("active_alert_rules", alert_rules, ttl=600)
        
        alerts = []
        for data in product_data_list:
            for rule in alert_rules:
                if await self._evaluate_rule(rule, data):
                    alert = Alert(rule_id=rule.id, product_id=data.product_id, data=data)
                    alerts.append(alert)
        
        return alerts
```

### 3. 配置中心服务 (新增)

```python
class ConfigurationService:
    """配置中心服务 - 支持热更新和版本控制"""
    
    def __init__(self, 
                 config_store: ConfigStore,
                 event_bus: EventBus,
                 version_manager: VersionManager):
        self.store = config_store
        self.events = event_bus
        self.versions = version_manager
        self.subscribers = {}
        self.metrics = MetricsCollector()
    
    async def get_config(self, key: str, version: str = "latest") -> ConfigValue
    async def set_config(self, key: str, value: ConfigValue, description: str = "") -> ConfigVersion
    async def delete_config(self, key: str) -> bool
    
    async def subscribe_config_changes(self, pattern: str, callback: Callable) -> str:
        """订阅配置变更通知"""
        subscription_id = f"sub_{uuid.uuid4()}"
        self.subscribers[subscription_id] = {
            "pattern": pattern,
            "callback": callback
        }
        return subscription_id
    
    async def hot_reload_config(self, service_name: str, config_keys: List[str]) -> ReloadResult:
        """热更新指定服务的配置"""
        try:
            new_configs = {}
            for key in config_keys:
                new_configs[key] = await self.get_config(key)
            
            # 通知服务更新配置
            await self.events.publish(f"config.reload.{service_name}", {
                "configs": new_configs,
                "timestamp": datetime.utcnow()
            })
            
            self.metrics.increment("config_hot_reload_success")
            return ReloadResult(success=True, updated_keys=config_keys)
            
        except Exception as e:
            self.metrics.increment("config_hot_reload_error")
            return ReloadResult(success=False, error=str(e))
    
    async def rollback_config(self, key: str, target_version: str) -> RollbackResult:
        """配置回滚"""
        try:
            old_value = await self.store.get_version(key, target_version)
            await self.set_config(key, old_value, f"Rollback to version {target_version}")
            
            # 通知相关服务配置已回滚
            await self._notify_config_change(key, old_value)
            
            return RollbackResult(success=True, version=target_version)
        except Exception as e:
            return RollbackResult(success=False, error=str(e))
```

### 4. 监控中心服务 (新增)

```python
class MonitoringService:
    """监控中心服务 - 统一的系统和业务监控"""
    
    def __init__(self, 
                 metrics_collector: MetricsCollector,
                 alert_manager: AlertManager,
                 health_checker: HealthChecker):
        self.metrics = metrics_collector
        self.alerts = alert_manager
        self.health = health_checker
        self.thresholds = {}
    
    # 指标收集
    async def collect_system_metrics(self) -> SystemMetrics
    async def collect_business_metrics(self) -> BusinessMetrics
    async def collect_custom_metrics(self, metric_name: str, value: float, labels: Dict[str, str])
    
    # 健康检查
    async def check_service_health(self, service_name: str) -> HealthStatus
    async def check_database_health(self) -> HealthStatus
    async def check_external_services_health(self) -> Dict[str, HealthStatus]
    
    # 告警管理
    async def evaluate_alert_rules(self) -> List[Alert]
    async def send_alert(self, alert: Alert) -> AlertResult
    async def acknowledge_alert(self, alert_id: str, user_id: str) -> bool
    
    # 性能监控
    async def track_api_performance(self, endpoint: str, duration: float, status_code: int)
    async def track_database_performance(self, query_type: str, duration: float)
    async def track_cache_performance(self, operation: str, hit: bool, duration: float)
```

## 数据存储优化

### 1. TimescaleDB 优化分区策略

```sql
-- 优化的商品历史数据表
CREATE TABLE product_history (
    time TIMESTAMPTZ NOT NULL,
    product_id TEXT NOT NULL,
    platform TEXT NOT NULL,
    title TEXT,
    title_translated TEXT,
    price DECIMAL(12,4),
    currency TEXT,
    stock_quantity INTEGER,
    sales_count INTEGER,
    rating DECIMAL(3,2),
    review_count INTEGER,
    change_type TEXT,
    change_value DECIMAL(12,4),
    data_quality_score DECIMAL(3,2), -- 新增数据质量评分
    raw_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建智能分区超表 (按天分区，自动管理)
SELECT create_hypertable('product_history', 'time', 
    chunk_time_interval => INTERVAL '1 day',
    create_default_indexes => FALSE
);

-- 优化索引策略
CREATE INDEX CONCURRENTLY idx_product_history_product_time 
    ON product_history (product_id, time DESC);
CREATE INDEX CONCURRENTLY idx_product_history_platform_time 
    ON product_history (platform, time DESC) 
    WHERE time > NOW() - INTERVAL '30 days'; -- 部分索引
CREATE INDEX CONCURRENTLY idx_product_history_change_type 
    ON product_history (change_type, time DESC) 
    WHERE change_type IN ('price_change', 'stock_change');

-- 自动数据保留策略
SELECT add_retention_policy('product_history', INTERVAL '2 years');

-- 自动数据压缩策略
ALTER TABLE product_history SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'product_id, platform',
    timescaledb.compress_orderby = 'time DESC'
);

SELECT add_compression_policy('product_history', INTERVAL '7 days');
```

### 2. Redis 集群优化结构

```python
class OptimizedCacheManager:
    """优化的多层缓存管理器"""
    
    def __init__(self, redis_cluster: RedisCluster):
        self.redis = redis_cluster
        self.local_cache = TTLCache(maxsize=1000, ttl=300)  # 本地缓存
        self.cache_stats = CacheStats()
    
    async def get(self, key: str) -> Any:
        """多层缓存获取"""
        # L1: 本地缓存
        if key in self.local_cache:
            self.cache_stats.record_hit("local")
            return self.local_cache[key]
        
        # L2: Redis缓存
        value = await self.redis.get(key)
        if value:
            self.cache_stats.record_hit("redis")
            self.local_cache[key] = value
            return value
        
        self.cache_stats.record_miss()
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """多层缓存设置"""
        # 设置本地缓存
        self.local_cache[key] = value
        
        # 设置Redis缓存
        return await self.redis.setex(key, ttl, value)
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """批量失效缓存"""
        # 清理本地缓存
        keys_to_remove = [k for k in self.local_cache.keys() if fnmatch(k, pattern)]
        for key in keys_to_remove:
            del self.local_cache[key]
        
        # 清理Redis缓存
        keys = await self.redis.keys(pattern)
        if keys:
            return await self.redis.delete(*keys)
        return 0

# 缓存键命名规范
class CacheKeys:
    """统一的缓存键管理"""
    
    # 商品相关
    PRODUCT_DATA = "product:data:{product_id}"
    PRODUCT_CONFIG = "product:config:{platform}"
    PRODUCT_TREND = "product:trend:{product_id}:{timerange}"
    
    # 分析相关
    MARKET_ANALYSIS = "analysis:market:{category}:{date}"
    PRICE_COMPARISON = "analysis:price:{product_ids_hash}"
    
    # 配置相关
    PLATFORM_CONFIG = "config:platform:{platform}"
    ALERT_RULES = "config:alerts:active"
    
    # 会话相关
    USER_SESSION = "session:user:{user_id}"
    API_RATE_LIMIT = "ratelimit:api:{user_id}:{endpoint}"
    
    @staticmethod
    def get_ttl(key_type: str) -> int:
        """根据键类型返回合适的TTL"""
        ttl_map = {
            "product:data": 1800,      # 30分钟
            "product:config": 3600,    # 1小时
            "analysis": 7200,          # 2小时
            "config": 600,             # 10分钟
            "session": 86400,          # 24小时
            "ratelimit": 3600          # 1小时
        }
        
        for pattern, ttl in ttl_map.items():
            if key_type.startswith(pattern):
                return ttl
        
        return 3600  # 默认1小时
```

### 3. 数据库连接池优化

```python
class OptimizedDatabaseManager:
    """优化的数据库连接管理"""
    
    def __init__(self):
        # TimescaleDB连接池 (读写分离)
        self.timescale_write_pool = create_async_engine(
            TIMESCALE_WRITE_URL,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        self.timescale_read_pool = create_async_engine(
            TIMESCALE_READ_URL,
            pool_size=30,
            max_overflow=50,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # PostgreSQL连接池
        self.postgres_pool = create_async_engine(
            POSTGRES_URL,
            pool_size=15,
            max_overflow=25,
            pool_pre_ping=True,
            pool_recycle=3600
        )
    
    async def get_timescale_session(self, read_only: bool = False):
        """获取TimescaleDB会话"""
        engine = self.timescale_read_pool if read_only else self.timescale_write_pool
        async with AsyncSession(engine) as session:
            yield session
    
    async def get_postgres_session(self):
        """获取PostgreSQL会话"""
        async with AsyncSession(self.postgres_pool) as session:
            yield session
```

## 监控指标体系

### 1. 系统指标

```python
class SystemMetrics:
    """系统级监控指标"""
    
    # 基础资源指标
    CPU_USAGE = "system_cpu_usage_percent"
    MEMORY_USAGE = "system_memory_usage_percent"
    DISK_USAGE = "system_disk_usage_percent"
    NETWORK_IO = "system_network_io_bytes"
    
    # 应用指标
    API_REQUEST_COUNT = "api_request_total"
    API_REQUEST_DURATION = "api_request_duration_seconds"
    API_ERROR_RATE = "api_error_rate"
    
    # 数据库指标
    DB_CONNECTION_COUNT = "database_connections_active"
    DB_QUERY_DURATION = "database_query_duration_seconds"
    DB_SLOW_QUERY_COUNT = "database_slow_queries_total"
    
    # 缓存指标
    CACHE_HIT_RATE = "cache_hit_rate"
    CACHE_MEMORY_USAGE = "cache_memory_usage_bytes"
    CACHE_OPERATION_DURATION = "cache_operation_duration_seconds"

class BusinessMetrics:
    """业务级监控指标"""
    
    # 商品监控指标
    PRODUCTS_MONITORED = "products_monitored_total"
    MONITORING_TASKS_ACTIVE = "monitoring_tasks_active"
    MONITORING_SUCCESS_RATE = "monitoring_success_rate"
    
    # 数据质量指标
    DATA_QUALITY_SCORE = "data_quality_score"
    DATA_COMPLETENESS_RATE = "data_completeness_rate"
    TRANSLATION_SUCCESS_RATE = "translation_success_rate"
    
    # 预警指标
    ALERTS_GENERATED = "alerts_generated_total"
    ALERTS_ACKNOWLEDGED = "alerts_acknowledged_total"
    ALERT_RESPONSE_TIME = "alert_response_time_seconds"
    
    # 用户行为指标
    USER_ACTIVE_SESSIONS = "user_active_sessions"
    USER_API_CALLS = "user_api_calls_total"
    USER_FEATURE_USAGE = "user_feature_usage_total"
```

### 2. 告警规则配置

```yaml
# 告警规则配置文件
alert_rules:
  # 系统告警
  system:
    - name: "high_cpu_usage"
      condition: "system_cpu_usage_percent > 80"
      duration: "5m"
      severity: "warning"
      message: "CPU使用率超过80%"
      
    - name: "high_memory_usage"
      condition: "system_memory_usage_percent > 85"
      duration: "3m"
      severity: "critical"
      message: "内存使用率超过85%"
      
    - name: "api_error_rate_high"
      condition: "api_error_rate > 0.05"
      duration: "2m"
      severity: "warning"
      message: "API错误率超过5%"
  
  # 业务告警
  business:
    - name: "monitoring_success_rate_low"
      condition: "monitoring_success_rate < 0.9"
      duration: "10m"
      severity: "warning"
      message: "监控成功率低于90%"
      
    - name: "data_quality_degraded"
      condition: "data_quality_score < 0.8"
      duration: "15m"
      severity: "warning"
      message: "数据质量评分低于0.8"
      
    - name: "translation_service_down"
      condition: "translation_success_rate < 0.5"
      duration: "5m"
      severity: "critical"
      message: "翻译服务异常"

# 通知渠道配置
notification_channels:
  email:
    enabled: true
    smtp_server: "smtp.example.com"
    recipients: ["<EMAIL>", "<EMAIL>"]
    
  webhook:
    enabled: true
    url: "https://hooks.slack.com/services/xxx"
    
  sms:
    enabled: false
    provider: "aliyun"
```

## 动态配置管理

### 1. 配置热更新机制

```python
class HotReloadManager:
    """配置热更新管理器"""
    
    def __init__(self, config_service: ConfigurationService):
        self.config_service = config_service
        self.reload_handlers = {}
        self.config_cache = {}
    
    def register_reload_handler(self, config_key: str, handler: Callable):
        """注册配置重载处理器"""
        if config_key not in self.reload_handlers:
            self.reload_handlers[config_key] = []
        self.reload_handlers[config_key].append(handler)
    
    async def handle_config_change(self, config_key: str, new_value: Any):
        """处理配置变更"""
        old_value = self.config_cache.get(config_key)
        self.config_cache[config_key] = new_value
        
        # 调用注册的处理器
        if config_key in self.reload_handlers:
            for handler in self.reload_handlers[config_key]:
                try:
                    await handler(old_value, new_value)
                except Exception as e:
                    logger.error(f"Config reload handler failed: {e}")
    
    async def reload_service_config(self, service_name: str):
        """重载指定服务的配置"""
        service_configs = await self.config_service.get_service_configs(service_name)
        
        for config_key, config_value in service_configs.items():
            await self.handle_config_change(config_key, config_value)

# 使用示例
class ProductMonitoringService:
    def __init__(self, hot_reload_manager: HotReloadManager):
        self.hot_reload = hot_reload_manager
        self.monitoring_interval = 3600  # 默认值
        
        # 注册配置热更新处理器
        self.hot_reload.register_reload_handler(
            "monitoring.interval", 
            self._update_monitoring_interval
        )
    
    async def _update_monitoring_interval(self, old_value: int, new_value: int):
        """更新监控间隔配置"""
        self.monitoring_interval = new_value
        logger.info(f"Monitoring interval updated: {old_value} -> {new_value}")
        
        # 重新调度监控任务
        await self._reschedule_monitoring_tasks()
```

### 2. 配置版本管理

```python
class ConfigVersionManager:
    """配置版本管理器"""
    
    def __init__(self, storage: ConfigStorage):
        self.storage = storage
    
    async def create_version(self, config_key: str, value: Any, description: str = "") -> ConfigVersion:
        """创建配置版本"""
        version = ConfigVersion(
            id=str(uuid.uuid4()),
            config_key=config_key,
            value=value,
            description=description,
            created_at=datetime.utcnow(),
            created_by="system"
        )
        
        await self.storage.save_version(version)
        return version
    
    async def get_version_history(self, config_key: str, limit: int = 10) -> List[ConfigVersion]:
        """获取配置版本历史"""
        return await self.storage.get_versions(config_key, limit)
    
    async def rollback_to_version(self, config_key: str, version_id: str) -> bool:
        """回滚到指定版本"""
        version = await self.storage.get_version(version_id)
        if not version:
            return False
        
        # 创建回滚版本记录
        await self.create_version(
            config_key, 
            version.value, 
            f"Rollback to version {version_id}"
        )
        
        return True
    
    async def compare_versions(self, version1_id: str, version2_id: str) -> ConfigDiff:
        """比较两个版本的差异"""
        v1 = await self.storage.get_version(version1_id)
        v2 = await self.storage.get_version(version2_id)
        
        return ConfigDiff(
            version1=v1,
            version2=v2,
            differences=self._calculate_diff(v1.value, v2.value)
        )
```

## API接口优化

### 1. 统一API网关设计

```python
class OptimizedAPIGateway:
    """优化的API网关 - 集成认证、限流、监控"""

    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.auth_manager = AuthManager()
        self.metrics_collector = MetricsCollector()
        self.circuit_breaker = CircuitBreaker()

    async def process_request(self, request: Request) -> Response:
        """统一请求处理流程"""
        start_time = time.time()

        try:
            # 1. 认证检查
            user = await self.auth_manager.authenticate(request)

            # 2. 限流检查
            await self.rate_limiter.check_limit(user.id, request.url.path)

            # 3. 熔断器检查
            if self.circuit_breaker.is_open(request.url.path):
                raise CircuitBreakerOpenError()

            # 4. 路由到具体服务
            response = await self._route_request(request, user)

            # 5. 记录成功指标
            self.metrics_collector.record_request(
                endpoint=request.url.path,
                method=request.method,
                status_code=response.status_code,
                duration=time.time() - start_time,
                user_id=user.id
            )

            return response

        except Exception as e:
            # 记录错误指标
            self.metrics_collector.record_error(
                endpoint=request.url.path,
                error_type=type(e).__name__,
                duration=time.time() - start_time
            )
            raise

# 优化的路由配置
class APIRoutes:
    """API路由配置"""

    # 商品监控相关API
    PRODUCTS = "/api/v1/products"
    PRODUCT_DETAIL = "/api/v1/products/{product_id}"
    PRODUCT_IMPORT = "/api/v1/products/import"
    PRODUCT_BATCH = "/api/v1/products/batch"

    # 监控任务相关API
    MONITORING_TASKS = "/api/v1/monitoring/tasks"
    MONITORING_BATCH = "/api/v1/monitoring/batch"
    MONITORING_STATUS = "/api/v1/monitoring/status"

    # 数据分析相关API
    ANALYTICS_TRENDS = "/api/v1/analytics/trends"
    ANALYTICS_REPORTS = "/api/v1/analytics/reports"
    ANALYTICS_ALERTS = "/api/v1/analytics/alerts"

    # 配置管理相关API
    CONFIG_PLATFORMS = "/api/v1/config/platforms"
    CONFIG_ALERTS = "/api/v1/config/alerts"
    CONFIG_SYSTEM = "/api/v1/config/system"

    # 监控相关API
    MONITORING_METRICS = "/api/v1/monitoring/metrics"
    MONITORING_HEALTH = "/api/v1/monitoring/health"
```

### 2. 批量操作优化

```python
class BatchOperationManager:
    """批量操作管理器"""

    def __init__(self, max_batch_size: int = 1000):
        self.max_batch_size = max_batch_size
        self.operation_queue = asyncio.Queue()
        self.result_cache = {}

    async def batch_create_products(self, products: List[ProductCreate]) -> BatchResult:
        """批量创建商品"""
        if len(products) > self.max_batch_size:
            return await self._process_large_batch(products, self._create_products_chunk)

        return await self._create_products_chunk(products)

    async def batch_update_products(self, updates: List[ProductUpdate]) -> BatchResult:
        """批量更新商品"""
        if len(updates) > self.max_batch_size:
            return await self._process_large_batch(updates, self._update_products_chunk)

        return await self._update_products_chunk(updates)

    async def _process_large_batch(self, items: List[Any], processor: Callable) -> BatchResult:
        """处理大批量操作"""
        chunks = [items[i:i + self.max_batch_size]
                 for i in range(0, len(items), self.max_batch_size)]

        results = []
        for chunk in chunks:
            chunk_result = await processor(chunk)
            results.append(chunk_result)

        return self._merge_batch_results(results)

    async def _create_products_chunk(self, products: List[ProductCreate]) -> BatchResult:
        """创建商品块"""
        success_count = 0
        failed_items = []
        created_items = []

        async with database.transaction():
            for product in products:
                try:
                    created_product = await self._create_single_product(product)
                    created_items.append(created_product)
                    success_count += 1
                except Exception as e:
                    failed_items.append({
                        "item": product,
                        "error": str(e)
                    })

        return BatchResult(
            total_count=len(products),
            success_count=success_count,
            failed_count=len(failed_items),
            created_items=created_items,
            failed_items=failed_items
        )
```

## 性能优化策略

### 1. 查询优化

```python
class OptimizedQueryManager:
    """优化的查询管理器"""

    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.query_stats = QueryStats()

    async def get_product_trends(self,
                               product_ids: List[str],
                               time_range: TimeRange,
                               use_cache: bool = True) -> Dict[str, PriceTrend]:
        """优化的商品趋势查询"""

        if use_cache:
            cache_key = f"trends:{hash(tuple(product_ids))}:{time_range.hash()}"
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                self.query_stats.record_cache_hit("product_trends")
                return cached_result

        # 使用优化的SQL查询
        query = """
        WITH trend_data AS (
            SELECT
                product_id,
                time_bucket('1 hour', time) as bucket,
                avg(price) as avg_price,
                min(price) as min_price,
                max(price) as max_price,
                count(*) as data_points
            FROM product_history
            WHERE product_id = ANY($1)
                AND time >= $2
                AND time <= $3
                AND price IS NOT NULL
            GROUP BY product_id, bucket
            ORDER BY product_id, bucket
        )
        SELECT
            product_id,
            array_agg(bucket ORDER BY bucket) as time_points,
            array_agg(avg_price ORDER BY bucket) as avg_prices,
            array_agg(min_price ORDER BY bucket) as min_prices,
            array_agg(max_price ORDER BY bucket) as max_prices
        FROM trend_data
        GROUP BY product_id
        """

        start_time = time.time()
        result = await database.fetch_all(query, product_ids, time_range.start, time_range.end)
        query_duration = time.time() - start_time

        self.query_stats.record_query("product_trends", query_duration, len(result))

        # 转换为业务对象
        trends = {}
        for row in result:
            trends[row['product_id']] = PriceTrend(
                product_id=row['product_id'],
                time_points=row['time_points'],
                avg_prices=row['avg_prices'],
                min_prices=row['min_prices'],
                max_prices=row['max_prices']
            )

        # 缓存结果
        if use_cache:
            await self.cache.set(cache_key, trends, ttl=1800)

        return trends

    async def get_market_analysis(self, category: str, date_range: DateRange) -> MarketAnalysis:
        """优化的市场分析查询"""

        # 使用物化视图加速查询
        query = """
        SELECT
            platform,
            count(DISTINCT product_id) as product_count,
            avg(price) as avg_price,
            percentile_cont(0.5) WITHIN GROUP (ORDER BY price) as median_price,
            min(price) as min_price,
            max(price) as max_price,
            sum(sales_count) as total_sales
        FROM market_analysis_mv
        WHERE category = $1
            AND analysis_date >= $2
            AND analysis_date <= $3
        GROUP BY platform
        ORDER BY total_sales DESC
        """

        result = await database.fetch_all(query, category, date_range.start, date_range.end)

        return MarketAnalysis(
            category=category,
            date_range=date_range,
            platform_stats=[PlatformStats(**row) for row in result]
        )
```

### 2. 连接池优化

```python
class ConnectionPoolManager:
    """连接池管理器"""

    def __init__(self):
        self.pools = {}
        self.pool_stats = {}

    async def create_optimized_pools(self):
        """创建优化的连接池"""

        # TimescaleDB 读写分离连接池
        self.pools['timescale_write'] = await asyncpg.create_pool(
            TIMESCALE_WRITE_URL,
            min_size=5,
            max_size=20,
            max_queries=50000,
            max_inactive_connection_lifetime=300,
            command_timeout=60
        )

        self.pools['timescale_read'] = await asyncpg.create_pool(
            TIMESCALE_READ_URL,
            min_size=10,
            max_size=30,
            max_queries=50000,
            max_inactive_connection_lifetime=300,
            command_timeout=60
        )

        # PostgreSQL 连接池
        self.pools['postgres'] = await asyncpg.create_pool(
            POSTGRES_URL,
            min_size=5,
            max_size=15,
            max_queries=50000,
            max_inactive_connection_lifetime=300,
            command_timeout=30
        )

    async def get_connection(self, pool_name: str):
        """获取数据库连接"""
        pool = self.pools.get(pool_name)
        if not pool:
            raise ValueError(f"Pool {pool_name} not found")

        # 记录连接池使用统计
        self._record_pool_usage(pool_name)

        return pool.acquire()

    def _record_pool_usage(self, pool_name: str):
        """记录连接池使用统计"""
        if pool_name not in self.pool_stats:
            self.pool_stats[pool_name] = {
                'total_requests': 0,
                'active_connections': 0
            }

        self.pool_stats[pool_name]['total_requests'] += 1

        pool = self.pools[pool_name]
        self.pool_stats[pool_name]['active_connections'] = pool.get_size()
```

## 错误处理和恢复机制

### 1. 统一错误处理

```python
class ErrorHandler:
    """统一错误处理器"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.error_patterns = {}

    async def handle_error(self, error: Exception, context: Dict[str, Any]) -> ErrorResponse:
        """统一错误处理"""

        error_type = type(error).__name__
        error_id = str(uuid.uuid4())

        # 记录错误指标
        self.metrics.increment(f"error.{error_type}")

        # 根据错误类型决定处理策略
        if isinstance(error, ValidationError):
            return self._handle_validation_error(error, error_id)
        elif isinstance(error, DatabaseError):
            return await self._handle_database_error(error, error_id, context)
        elif isinstance(error, ExternalServiceError):
            return await self._handle_external_service_error(error, error_id, context)
        else:
            return self._handle_unknown_error(error, error_id)

    async def _handle_database_error(self, error: DatabaseError, error_id: str, context: Dict[str, Any]) -> ErrorResponse:
        """处理数据库错误"""

        # 检查是否是连接问题
        if "connection" in str(error).lower():
            # 尝试重新建立连接
            await self._reconnect_database()

            return ErrorResponse(
                error_id=error_id,
                error_type="database_connection_error",
                message="数据库连接异常，正在尝试重连",
                retry_after=30,
                recoverable=True
            )

        # 检查是否是锁超时
        if "lock timeout" in str(error).lower():
            return ErrorResponse(
                error_id=error_id,
                error_type="database_lock_timeout",
                message="数据库操作超时，请稍后重试",
                retry_after=10,
                recoverable=True
            )

        return ErrorResponse(
            error_id=error_id,
            error_type="database_error",
            message="数据库操作失败",
            recoverable=False
        )

    async def _handle_external_service_error(self, error: ExternalServiceError, error_id: str, context: Dict[str, Any]) -> ErrorResponse:
        """处理外部服务错误"""

        service_name = context.get('service_name', 'unknown')

        # 记录服务特定的错误指标
        self.metrics.increment(f"external_service_error.{service_name}")

        # 检查是否需要熔断
        error_rate = await self._get_service_error_rate(service_name)
        if error_rate > 0.5:  # 错误率超过50%
            await self._trigger_circuit_breaker(service_name)

        return ErrorResponse(
            error_id=error_id,
            error_type="external_service_error",
            message=f"外部服务 {service_name} 暂时不可用",
            retry_after=60,
            recoverable=True
        )
```

### 2. 自动恢复机制

```python
class AutoRecoveryManager:
    """自动恢复管理器"""

    def __init__(self):
        self.recovery_strategies = {}
        self.recovery_history = []

    def register_recovery_strategy(self, error_type: str, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[error_type] = strategy

    async def attempt_recovery(self, error: Exception, context: Dict[str, Any]) -> RecoveryResult:
        """尝试自动恢复"""

        error_type = type(error).__name__

        if error_type not in self.recovery_strategies:
            return RecoveryResult(success=False, message="无可用恢复策略")

        strategy = self.recovery_strategies[error_type]

        try:
            recovery_result = await strategy(error, context)

            # 记录恢复历史
            self.recovery_history.append({
                'timestamp': datetime.utcnow(),
                'error_type': error_type,
                'success': recovery_result.success,
                'context': context
            })

            return recovery_result

        except Exception as recovery_error:
            return RecoveryResult(
                success=False,
                message=f"恢复策略执行失败: {str(recovery_error)}"
            )

# 具体恢复策略实现
async def database_connection_recovery(error: Exception, context: Dict[str, Any]) -> RecoveryResult:
    """数据库连接恢复策略"""
    try:
        # 重新初始化连接池
        pool_manager = context.get('pool_manager')
        await pool_manager.recreate_pools()

        # 测试连接
        async with pool_manager.get_connection('postgres') as conn:
            await conn.fetchval('SELECT 1')

        return RecoveryResult(success=True, message="数据库连接已恢复")

    except Exception as e:
        return RecoveryResult(success=False, message=f"数据库连接恢复失败: {str(e)}")

async def external_service_recovery(error: Exception, context: Dict[str, Any]) -> RecoveryResult:
    """外部服务恢复策略"""
    service_name = context.get('service_name')

    try:
        # 检查服务健康状态
        health_checker = context.get('health_checker')
        health_status = await health_checker.check_service(service_name)

        if health_status.is_healthy:
            # 重置熔断器
            circuit_breaker = context.get('circuit_breaker')
            circuit_breaker.reset(service_name)

            return RecoveryResult(success=True, message=f"服务 {service_name} 已恢复")
        else:
            return RecoveryResult(success=False, message=f"服务 {service_name} 仍不可用")

    except Exception as e:
        return RecoveryResult(success=False, message=f"服务恢复检查失败: {str(e)}")
```

## 部署和运维优化

### 1. Docker容器优化

```dockerfile
# 优化的Dockerfile
FROM python:3.11-slim as builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 生产镜像
FROM python:3.11-slim

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY --chown=appuser:appuser . .

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "main:app"]
```

### 2. Kubernetes部署配置

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ecommerce-monitoring
  labels:
    app: ecommerce-monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ecommerce-monitoring
  template:
    metadata:
      labels:
        app: ecommerce-monitoring
    spec:
      containers:
      - name: api
        image: ecommerce-monitoring:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: redis-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ecommerce-monitoring-service
spec:
  selector:
    app: ecommerce-monitoring
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP

---
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ecommerce-monitoring-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ecommerce-monitoring
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

这样我们就完成了电商监控系统设计文档的全面优化，涵盖了架构简化、数据存储优化、监控增强和动态配置支持等四个关键方面。
