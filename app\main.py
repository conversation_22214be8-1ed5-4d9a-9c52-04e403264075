"""
电商商品监控系统主应用
"""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi

from app.core.config import get_settings
from app.core.cache import init_cache_manager, close_cache_manager
from app.core.middleware import (
    CacheMiddleware,
    RateLimitMiddleware, 
    PerformanceMiddleware,
    CacheCleanupMiddleware
)
from app.core.database import init_database, close_database
from app.core.logging import setup_logging
from app.api.v1 import api_router

# 设置日志
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    settings = get_settings()
    
    # 设置日志
    setup_logging(settings.logging)
    logger.info("应用启动中...")
    
    try:
        # 初始化数据库
        await init_database(settings.database.url)
        logger.info("数据库连接成功")
        
        # 初始化缓存
        await init_cache_manager(settings.redis.url)
        logger.info("缓存系统初始化成功")
        
        logger.info("应用启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    
    finally:
        # 关闭时执行
        logger.info("应用关闭中...")
        
        try:
            await close_cache_manager()
            logger.info("缓存连接已关闭")
            
            await close_database()
            logger.info("数据库连接已关闭")
            
        except Exception as e:
            logger.error(f"应用关闭时出错: {e}")
        
        logger.info("应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    # 创建应用实例
    app = FastAPI(
        title=settings.name,
        version=settings.version,
        description="专业的电商商品监控和利润分析系统",
        docs_url=None,  # 禁用默认文档
        redoc_url=None,  # 禁用默认ReDoc
        openapi_url="/api/v1/openapi.json",
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 信任的主机
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境应该限制具体主机
    )
    
    # 性能监控中间件
    app.add_middleware(PerformanceMiddleware)
    
    # 限流中间件
    app.add_middleware(RateLimitMiddleware, default_limit=1000, window=3600)
    
    # 缓存中间件
    app.add_middleware(CacheMiddleware)
    
    # 缓存清理中间件
    app.add_middleware(CacheCleanupMiddleware)
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 自定义异常处理
    setup_exception_handlers(app)
    
    # 自定义文档路由
    setup_docs_routes(app)
    
    return app


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "status_code": exc.status_code,
                "path": request.url.path
            }
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """值错误处理"""
        logger.error(f"值错误: {exc}")
        return JSONResponse(
            status_code=400,
            content={
                "error": True,
                "message": str(exc),
                "status_code": 400,
                "path": request.url.path
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": "内部服务器错误",
                "status_code": 500,
                "path": request.url.path
            }
        )


def setup_docs_routes(app: FastAPI):
    """设置文档路由"""
    
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """自定义Swagger UI"""
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - Swagger UI",
            oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        )
    
    @app.get("/redoc", include_in_schema=False)
    async def redoc_html():
        """自定义ReDoc"""
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - ReDoc",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@2.1.0/bundles/redoc.standalone.js",
        )
    
    def custom_openapi():
        """自定义OpenAPI规范"""
        if app.openapi_schema:
            return app.openapi_schema
        
        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )
        
        # 添加安全定义
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
            }
        }
        
        app.openapi_schema = openapi_schema
        return app.openapi_schema
    
    app.openapi = custom_openapi


# 创建应用实例
app = create_app()


# 根路由
@app.get("/", tags=["Root"])
async def root():
    """根路径"""
    settings = get_settings()
    return {
        "message": f"欢迎使用{settings.name}",
        "version": settings.version,
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/health"
    }


# 健康检查
@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查"""
    from app.core.cache import get_cache_manager
    from app.core.database import get_database
    
    try:
        # 检查数据库
        db = await get_database()
        await db.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        db_status = "unhealthy"
    
    try:
        # 检查缓存
        cache = await get_cache_manager()
        cache_info = await cache.get_info()
        cache_status = "healthy" if cache_info["redis"]["connected"] else "unhealthy"
    except Exception as e:
        logger.error(f"缓存健康检查失败: {e}")
        cache_status = "unhealthy"
    
    # 检查磁盘空间
    import shutil
    try:
        total, used, free = shutil.disk_usage("/")
        disk_free_percent = (free / total) * 100
        disk_status = "healthy" if disk_free_percent > 10 else "warning"
    except Exception:
        disk_status = "unknown"
    
    # 检查内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_status = "healthy" if memory.percent < 90 else "warning"
    except Exception:
        memory_status = "unknown"
    
    # 整体状态
    overall_status = "healthy"
    if db_status == "unhealthy" or cache_status == "unhealthy":
        overall_status = "unhealthy"
    elif disk_status == "warning" or memory_status == "warning":
        overall_status = "warning"
    
    return {
        "status": overall_status,
        "timestamp": "2024-01-01T00:00:00Z",  # 实际应该使用当前时间
        "services": {
            "database": db_status,
            "cache": cache_status,
            "disk": disk_status,
            "memory": memory_status
        },
        "version": get_settings().version
    }


if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.logging.level.lower()
    )
