"""
监控调度器

负责商品监控任务的调度和执行
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductStatus
from app.services.product_management.lifecycle_manager import (
    ProductLifecycleManager, MonitoringConfig, MonitoringPriority, LifecycleEvent
)

logger = get_logger(__name__)


class SchedulerStatus(Enum):
    """调度器状态"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


@dataclass
class MonitoringTask:
    """监控任务"""
    id: str
    product_id: str
    priority: MonitoringPriority
    scheduled_time: datetime
    attempts: int = 0
    max_attempts: int = 3
    status: str = "pending"
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class SchedulerMetrics:
    """调度器指标"""
    total_tasks_scheduled: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_tasks_retried: int = 0
    average_execution_time: float = 0.0
    last_execution_time: Optional[datetime] = None
    uptime_start: datetime = field(default_factory=datetime.now)


class MonitoringScheduler:
    """监控调度器"""
    
    def __init__(self, lifecycle_manager: ProductLifecycleManager):
        self.lifecycle_manager = lifecycle_manager
        self.status = SchedulerStatus.STOPPED
        self.metrics = SchedulerMetrics()
        self.pending_tasks: List[MonitoringTask] = []
        self.running_tasks: Dict[str, MonitoringTask] = {}
        self.completed_tasks: List[MonitoringTask] = []
        self.max_concurrent_tasks = 10
        self.task_timeout_minutes = 30
        self.scheduler_task: Optional[asyncio.Task] = None
        self.monitoring_callbacks: List[Callable] = []
        
    async def start(self):
        """启动调度器"""
        if self.status == SchedulerStatus.RUNNING:
            logger.warning("调度器已在运行中")
            return
        
        logger.info("启动监控调度器")
        self.status = SchedulerStatus.RUNNING
        self.metrics.uptime_start = datetime.now()
        
        # 启动调度循环
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
    
    async def stop(self):
        """停止调度器"""
        logger.info("停止监控调度器")
        self.status = SchedulerStatus.STOPPED
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 等待正在运行的任务完成
        if self.running_tasks:
            logger.info(f"等待 {len(self.running_tasks)} 个任务完成")
            await asyncio.sleep(5)  # 给任务一些时间完成
    
    async def pause(self):
        """暂停调度器"""
        logger.info("暂停监控调度器")
        self.status = SchedulerStatus.PAUSED
    
    async def resume(self):
        """恢复调度器"""
        logger.info("恢复监控调度器")
        self.status = SchedulerStatus.RUNNING
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        try:
            while self.status != SchedulerStatus.STOPPED:
                if self.status == SchedulerStatus.RUNNING:
                    await self._schedule_monitoring_tasks()
                    await self._execute_pending_tasks()
                    await self._cleanup_completed_tasks()
                
                # 每30秒检查一次
                await asyncio.sleep(30)
                
        except asyncio.CancelledError:
            logger.info("调度器循环被取消")
        except Exception as e:
            logger.error(f"调度器循环异常: {e}")
            self.status = SchedulerStatus.ERROR
    
    async def _schedule_monitoring_tasks(self):
        """调度监控任务"""
        try:
            # 获取需要监控的商品
            products_to_monitor = await self.lifecycle_manager.get_products_to_monitor()
            
            for product_id, config in products_to_monitor:
                # 检查是否已有待处理或正在运行的任务
                if self._has_pending_or_running_task(product_id):
                    continue
                
                # 创建监控任务
                task = MonitoringTask(
                    id=f"monitor_{product_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    product_id=product_id,
                    priority=config.priority,
                    scheduled_time=config.next_monitor_time or datetime.now()
                )
                
                self.pending_tasks.append(task)
                self.metrics.total_tasks_scheduled += 1
                
                logger.debug(f"调度监控任务: {task.id}")
            
        except Exception as e:
            logger.error(f"调度监控任务失败: {e}")
    
    def _has_pending_or_running_task(self, product_id: str) -> bool:
        """检查商品是否已有待处理或正在运行的任务"""
        # 检查待处理任务
        for task in self.pending_tasks:
            if task.product_id == product_id:
                return True
        
        # 检查正在运行的任务
        for task in self.running_tasks.values():
            if task.product_id == product_id:
                return True
        
        return False
    
    async def _execute_pending_tasks(self):
        """执行待处理任务"""
        if not self.pending_tasks:
            return
        
        # 限制并发任务数量
        available_slots = self.max_concurrent_tasks - len(self.running_tasks)
        if available_slots <= 0:
            return
        
        # 按优先级和调度时间排序
        self.pending_tasks.sort(key=lambda x: (
            self._get_priority_order(x.priority),
            x.scheduled_time
        ))
        
        # 执行任务
        tasks_to_execute = self.pending_tasks[:available_slots]
        
        for task in tasks_to_execute:
            if task.scheduled_time <= datetime.now():
                await self._execute_task(task)
                self.pending_tasks.remove(task)
    
    def _get_priority_order(self, priority: MonitoringPriority) -> int:
        """获取优先级排序值"""
        priority_order = {
            MonitoringPriority.CRITICAL: 0,
            MonitoringPriority.HIGH: 1,
            MonitoringPriority.NORMAL: 2,
            MonitoringPriority.LOW: 3,
            MonitoringPriority.MINIMAL: 4,
        }
        return priority_order.get(priority, 5)
    
    async def _execute_task(self, task: MonitoringTask):
        """执行监控任务"""
        try:
            logger.info(f"开始执行监控任务: {task.id}")
            
            task.status = "running"
            task.started_at = datetime.now()
            task.attempts += 1
            
            self.running_tasks[task.id] = task
            
            # 创建任务协程
            task_coroutine = self._run_monitoring_task(task)
            
            # 设置超时
            timeout_seconds = self.task_timeout_minutes * 60
            
            # 执行任务
            asyncio.create_task(
                self._execute_task_with_timeout(task, task_coroutine, timeout_seconds)
            )
            
        except Exception as e:
            logger.error(f"执行监控任务失败: {task.id} - {e}")
            await self._handle_task_failure(task, str(e))
    
    async def _execute_task_with_timeout(self, task: MonitoringTask, 
                                       task_coroutine, timeout_seconds: int):
        """带超时的任务执行"""
        try:
            await asyncio.wait_for(task_coroutine, timeout=timeout_seconds)
            await self._handle_task_success(task)
            
        except asyncio.TimeoutError:
            logger.error(f"监控任务超时: {task.id}")
            await self._handle_task_failure(task, "任务执行超时")
            
        except Exception as e:
            logger.error(f"监控任务异常: {task.id} - {e}")
            await self._handle_task_failure(task, str(e))
    
    async def _run_monitoring_task(self, task: MonitoringTask):
        """运行监控任务的具体逻辑"""
        try:
            # 这里是实际的监控逻辑
            # 在实际应用中，这里会调用爬取服务或其他监控逻辑
            
            # 模拟监控过程
            await asyncio.sleep(2)  # 模拟网络请求时间
            
            # 模拟监控结果
            success_rate = 0.9  # 90%的成功率
            import random
            
            if random.random() < success_rate:
                # 监控成功
                logger.info(f"监控任务成功: {task.product_id}")
                
                # 记录成功
                await self.lifecycle_manager.record_monitoring_success(task.product_id)
                
                # 触发更新事件
                # 这里可以创建一个模拟的Product对象
                # 实际应用中应该从数据库获取完整的Product对象
                
            else:
                # 监控失败
                raise Exception("模拟监控失败")
            
        except Exception as e:
            # 记录失败
            await self.lifecycle_manager.record_monitoring_failure(task.product_id)
            raise e
    
    async def _handle_task_success(self, task: MonitoringTask):
        """处理任务成功"""
        task.status = "completed"
        task.completed_at = datetime.now()
        
        # 从运行任务中移除
        if task.id in self.running_tasks:
            del self.running_tasks[task.id]
        
        # 添加到完成任务列表
        self.completed_tasks.append(task)
        
        # 更新指标
        self.metrics.total_tasks_completed += 1
        self.metrics.last_execution_time = datetime.now()
        
        # 计算平均执行时间
        if task.started_at:
            execution_time = (task.completed_at - task.started_at).total_seconds()
            self._update_average_execution_time(execution_time)
        
        # 执行回调
        await self._execute_monitoring_callbacks(task.product_id, True, None)
        
        logger.info(f"监控任务完成: {task.id}")
    
    async def _handle_task_failure(self, task: MonitoringTask, error_message: str):
        """处理任务失败"""
        task.status = "failed"
        task.error_message = error_message
        task.completed_at = datetime.now()
        
        # 从运行任务中移除
        if task.id in self.running_tasks:
            del self.running_tasks[task.id]
        
        # 检查是否需要重试
        if task.attempts < task.max_attempts:
            # 重新调度任务
            retry_delay = min(300, task.attempts * 60)  # 最多延迟5分钟
            task.scheduled_time = datetime.now() + timedelta(seconds=retry_delay)
            task.status = "pending"
            task.started_at = None
            task.completed_at = None
            task.error_message = None
            
            self.pending_tasks.append(task)
            self.metrics.total_tasks_retried += 1
            
            logger.info(f"监控任务重试: {task.id} (第{task.attempts}次尝试)")
        else:
            # 任务最终失败
            self.completed_tasks.append(task)
            self.metrics.total_tasks_failed += 1
            
            logger.error(f"监控任务最终失败: {task.id} - {error_message}")
        
        # 执行回调
        await self._execute_monitoring_callbacks(task.product_id, False, error_message)
    
    def _update_average_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        if self.metrics.average_execution_time == 0:
            self.metrics.average_execution_time = execution_time
        else:
            # 使用指数移动平均
            alpha = 0.1
            self.metrics.average_execution_time = (
                alpha * execution_time + 
                (1 - alpha) * self.metrics.average_execution_time
            )
    
    async def _execute_monitoring_callbacks(self, product_id: str, success: bool, 
                                          error_message: Optional[str]):
        """执行监控回调"""
        for callback in self.monitoring_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(product_id, success, error_message)
                else:
                    callback(product_id, success, error_message)
            except Exception as e:
                logger.error(f"执行监控回调失败: {e}")
    
    async def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        # 保留最近24小时的任务记录
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        original_count = len(self.completed_tasks)
        self.completed_tasks = [
            task for task in self.completed_tasks
            if task.completed_at and task.completed_at > cutoff_time
        ]
        
        cleaned_count = original_count - len(self.completed_tasks)
        if cleaned_count > 0:
            logger.debug(f"清理了 {cleaned_count} 个已完成的任务记录")
    
    def add_monitoring_callback(self, callback: Callable):
        """添加监控回调"""
        self.monitoring_callbacks.append(callback)
        logger.info("添加监控回调")
    
    def remove_monitoring_callback(self, callback: Callable):
        """移除监控回调"""
        if callback in self.monitoring_callbacks:
            self.monitoring_callbacks.remove(callback)
            logger.info("移除监控回调")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        uptime = datetime.now() - self.metrics.uptime_start
        
        return {
            "status": self.status.value,
            "uptime_seconds": uptime.total_seconds(),
            "pending_tasks": len(self.pending_tasks),
            "running_tasks": len(self.running_tasks),
            "completed_tasks_24h": len(self.completed_tasks),
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "task_timeout_minutes": self.task_timeout_minutes,
            "metrics": {
                "total_scheduled": self.metrics.total_tasks_scheduled,
                "total_completed": self.metrics.total_tasks_completed,
                "total_failed": self.metrics.total_tasks_failed,
                "total_retried": self.metrics.total_tasks_retried,
                "average_execution_time": round(self.metrics.average_execution_time, 2),
                "last_execution": (
                    self.metrics.last_execution_time.isoformat() 
                    if self.metrics.last_execution_time else None
                ),
                "success_rate": (
                    self.metrics.total_tasks_completed / 
                    max(1, self.metrics.total_tasks_completed + self.metrics.total_tasks_failed)
                ) * 100
            }
        }
    
    def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务历史"""
        # 合并待处理、运行中和已完成的任务
        all_tasks = (
            self.pending_tasks + 
            list(self.running_tasks.values()) + 
            self.completed_tasks
        )
        
        # 按创建时间排序
        all_tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        # 限制数量
        tasks = all_tasks[:limit]
        
        return [
            {
                "id": task.id,
                "product_id": task.product_id,
                "priority": task.priority.value,
                "status": task.status,
                "attempts": task.attempts,
                "scheduled_time": task.scheduled_time.isoformat(),
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
                "execution_time": (
                    (task.completed_at - task.started_at).total_seconds()
                    if task.started_at and task.completed_at else None
                )
            }
            for task in tasks
        ]
    
    async def force_monitor_product(self, product_id: str, priority: MonitoringPriority = MonitoringPriority.HIGH):
        """强制监控指定商品"""
        # 创建高优先级任务
        task = MonitoringTask(
            id=f"force_monitor_{product_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            product_id=product_id,
            priority=priority,
            scheduled_time=datetime.now()
        )
        
        # 插入到待处理任务队列的前面
        self.pending_tasks.insert(0, task)
        self.metrics.total_tasks_scheduled += 1
        
        logger.info(f"强制监控商品: {product_id}")
        
        return task.id
