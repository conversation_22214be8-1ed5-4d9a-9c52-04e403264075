/**
 * 全局样式
 */

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 卡片间距优化 */
.ant-card + .ant-card {
  margin-top: 16px;
}

/* 表单优化 */
.ant-form-item {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .ant-form-item {
    margin-bottom: 12px;
  }
  
  .ant-form-item-label {
    padding-bottom: 4px;
  }
}

/* 按钮组优化 */
.ant-btn-group .ant-btn {
  margin-right: 0;
}

.ant-space-item:not(:last-child) {
  margin-right: 8px;
}

/* 统计卡片样式 */
.stats-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.stats-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stats-card .ant-statistic-content {
  color: white;
}

/* 加载状态优化 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 空状态优化 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  flex-direction: column;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h2 {
  margin: 0;
  color: #262626;
  font-weight: 600;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .page-header .ant-space {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 搜索栏样式 */
.search-bar {
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.search-bar .ant-row {
  align-items: flex-end;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .action-buttons {
    width: 100%;
  }
  
  .action-buttons .ant-btn {
    flex: 1;
    min-width: 0;
  }
}

/* 标签样式优化 */
.status-tag {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
}

/* 数据展示优化 */
.data-display {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

/* 图表容器 */
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 悬浮按钮组 */
.float-button-group {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 999;
}

.float-button-group .ant-float-btn {
  margin-bottom: 8px;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 高亮效果 */
.highlight {
  background: linear-gradient(90deg, transparent, #fff3cd, transparent);
  animation: highlight 2s ease-in-out;
}

@keyframes highlight {
  0%, 100% {
    background: transparent;
  }
  50% {
    background: linear-gradient(90deg, transparent, #fff3cd, transparent);
  }
}

/* 工具提示样式 */
.custom-tooltip {
  max-width: 300px;
}

/* 批量操作栏 */
.batch-operation-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-card {
    box-shadow: none;
    border: 1px solid #d9d9d9;
  }
  
  .page-break {
    page-break-before: always;
  }
}
