"""
提示词和规则管理器

建立翻译提示词和规则管理，可配置的翻译提示词，翻译规则配置
"""

import json
import uuid
from typing import Dict, Any, List, Optional, Set, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
import os
import re

from app.core.logging import get_logger

logger = get_logger(__name__)


class PromptType(Enum):
    """提示词类型"""
    SYSTEM = "system"           # 系统提示词
    USER = "user"              # 用户提示词
    ASSISTANT = "assistant"     # 助手提示词
    EXAMPLE = "example"         # 示例提示词


class RuleType(Enum):
    """规则类型"""
    PREPROCESSING = "preprocessing"     # 预处理规则
    POSTPROCESSING = "postprocessing"   # 后处理规则
    VALIDATION = "validation"           # 验证规则
    FORMATTING = "formatting"           # 格式化规则
    TERMINOLOGY = "terminology"         # 术语规则


class RuleAction(Enum):
    """规则动作"""
    REPLACE = "replace"         # 替换
    REMOVE = "remove"          # 移除
    INSERT = "insert"          # 插入
    VALIDATE = "validate"       # 验证
    TRANSFORM = "transform"     # 转换


@dataclass
class PromptTemplate:
    """提示词模板"""
    prompt_id: str
    name: str
    prompt_type: PromptType
    content: str
    variables: List[str] = field(default_factory=list)  # 模板变量
    description: str = ""
    tags: List[str] = field(default_factory=list)
    version: str = "1.0"
    enabled: bool = True
    usage_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"


@dataclass
class TranslationRule:
    """翻译规则"""
    rule_id: str
    name: str
    rule_type: RuleType
    action: RuleAction
    pattern: str = ""                    # 匹配模式
    replacement: str = ""                # 替换内容
    condition: str = ""                  # 应用条件
    priority: int = 5                    # 优先级（1-10）
    enabled: bool = True                 # 是否启用
    case_sensitive: bool = False         # 是否区分大小写
    use_regex: bool = False             # 是否使用正则表达式
    apply_to_source: bool = False       # 应用于源文本
    apply_to_target: bool = True        # 应用于目标文本
    languages: List[str] = field(default_factory=list)  # 适用语言
    platforms: List[str] = field(default_factory=list)  # 适用平台
    text_types: List[str] = field(default_factory=list)  # 适用文本类型
    description: str = ""
    tags: List[str] = field(default_factory=list)
    usage_count: int = 0
    success_rate: float = 100.0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"


@dataclass
class TerminologyEntry:
    """术语条目"""
    term_id: str
    source_term: str
    target_term: str
    source_lang: str
    target_lang: str
    domain: str = "general"             # 领域
    context: str = ""                   # 上下文
    notes: str = ""                     # 备注
    confidence: float = 1.0             # 置信度
    verified: bool = False              # 是否已验证
    usage_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"


class PromptManager:
    """提示词和规则管理器"""
    
    def __init__(self, data_dir: str = "prompt_data"):
        self.data_dir = data_dir
        
        # 数据存储
        self.prompts: Dict[str, PromptTemplate] = {}
        self.rules: Dict[str, TranslationRule] = {}
        self.terminology: Dict[str, TerminologyEntry] = {}
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "prompts"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "rules"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "terminology"), exist_ok=True)
        
        # 配置
        self.config = {
            "auto_save": True,              # 自动保存
            "backup_enabled": True,         # 启用备份
            "max_prompts": 1000,           # 最大提示词数
            "max_rules": 5000,             # 最大规则数
            "max_terminology": 10000,      # 最大术语数
            "validation_enabled": True,     # 启用验证
            "version_control": True         # 版本控制
        }
        
        # 统计信息
        self.stats = {
            "total_prompts": 0,
            "active_prompts": 0,
            "total_rules": 0,
            "active_rules": 0,
            "total_terminology": 0,
            "verified_terminology": 0,
            "total_usage": 0
        }
        
        # 加载默认数据
        self._load_default_data()
        
        # 加载已保存的数据
        self._load_data_from_disk()
    
    # ==================== 提示词管理 ====================
    
    def create_prompt(self, name: str, prompt_type: PromptType, content: str,
                     variables: Optional[List[str]] = None, **kwargs) -> str:
        """
        创建提示词模板
        
        Args:
            name: 提示词名称
            prompt_type: 提示词类型
            content: 提示词内容
            variables: 模板变量
            **kwargs: 其他参数
        
        Returns:
            str: 提示词ID
        """
        try:
            prompt_id = str(uuid.uuid4())
            
            # 检查数量限制
            if len(self.prompts) >= self.config["max_prompts"]:
                raise ValueError(f"提示词数量已达上限: {self.config['max_prompts']}")
            
            # 提取模板变量
            if variables is None:
                variables = self._extract_variables(content)
            
            # 创建提示词模板
            prompt = PromptTemplate(
                prompt_id=prompt_id,
                name=name,
                prompt_type=prompt_type,
                content=content,
                variables=variables,
                **kwargs
            )
            
            # 验证提示词
            if self.config["validation_enabled"]:
                self._validate_prompt(prompt)
            
            # 存储提示词
            self.prompts[prompt_id] = prompt
            
            # 更新统计
            self._update_stats()
            
            # 自动保存
            if self.config["auto_save"]:
                self._save_prompt_to_disk(prompt)
            
            logger.info(f"创建提示词模板: {prompt_id}, 名称: {name}")
            return prompt_id
            
        except Exception as e:
            logger.error(f"创建提示词模板失败: {e}")
            raise
    
    def render_prompt(self, prompt_id: str, variables: Dict[str, Any]) -> str:
        """
        渲染提示词模板
        
        Args:
            prompt_id: 提示词ID
            variables: 变量值
        
        Returns:
            str: 渲染后的提示词
        """
        try:
            if prompt_id not in self.prompts:
                raise ValueError(f"提示词不存在: {prompt_id}")
            
            prompt = self.prompts[prompt_id]
            
            if not prompt.enabled:
                raise ValueError(f"提示词已禁用: {prompt_id}")
            
            # 渲染模板
            rendered_content = prompt.content
            for var_name, var_value in variables.items():
                placeholder = "{" + var_name + "}"
                rendered_content = rendered_content.replace(placeholder, str(var_value))
            
            # 更新使用统计
            prompt.usage_count += 1
            self.stats["total_usage"] += 1
            
            return rendered_content
            
        except Exception as e:
            logger.error(f"渲染提示词模板失败: {prompt_id}, {e}")
            raise
    
    def find_prompts(self, prompt_type: Optional[PromptType] = None,
                    tags: Optional[List[str]] = None,
                    enabled_only: bool = True) -> List[PromptTemplate]:
        """查找提示词模板"""
        try:
            prompts = list(self.prompts.values())
            
            # 过滤条件
            if prompt_type:
                prompts = [p for p in prompts if p.prompt_type == prompt_type]
            
            if tags:
                prompts = [p for p in prompts if any(tag in p.tags for tag in tags)]
            
            if enabled_only:
                prompts = [p for p in prompts if p.enabled]
            
            # 按使用次数排序
            prompts.sort(key=lambda p: p.usage_count, reverse=True)
            
            return prompts
            
        except Exception as e:
            logger.error(f"查找提示词模板失败: {e}")
            return []
    
    # ==================== 规则管理 ====================
    
    def create_rule(self, name: str, rule_type: RuleType, action: RuleAction,
                   pattern: str = "", replacement: str = "", **kwargs) -> str:
        """
        创建翻译规则
        
        Args:
            name: 规则名称
            rule_type: 规则类型
            action: 规则动作
            pattern: 匹配模式
            replacement: 替换内容
            **kwargs: 其他参数
        
        Returns:
            str: 规则ID
        """
        try:
            rule_id = str(uuid.uuid4())
            
            # 检查数量限制
            if len(self.rules) >= self.config["max_rules"]:
                raise ValueError(f"规则数量已达上限: {self.config['max_rules']}")
            
            # 创建规则
            rule = TranslationRule(
                rule_id=rule_id,
                name=name,
                rule_type=rule_type,
                action=action,
                pattern=pattern,
                replacement=replacement,
                **kwargs
            )
            
            # 验证规则
            if self.config["validation_enabled"]:
                self._validate_rule(rule)
            
            # 存储规则
            self.rules[rule_id] = rule
            
            # 更新统计
            self._update_stats()
            
            # 自动保存
            if self.config["auto_save"]:
                self._save_rule_to_disk(rule)
            
            logger.info(f"创建翻译规则: {rule_id}, 名称: {name}")
            return rule_id
            
        except Exception as e:
            logger.error(f"创建翻译规则失败: {e}")
            raise
    
    def apply_rules(self, text: str, rule_type: RuleType,
                   language: Optional[str] = None,
                   platform: Optional[str] = None,
                   text_type: Optional[str] = None) -> tuple[str, List[str]]:
        """
        应用翻译规则
        
        Args:
            text: 待处理文本
            rule_type: 规则类型
            language: 语言
            platform: 平台
            text_type: 文本类型
        
        Returns:
            tuple[str, List[str]]: 处理后的文本和应用的规则列表
        """
        try:
            processed_text = text
            applied_rules = []
            
            # 获取匹配的规则
            matching_rules = self._get_matching_rules(
                rule_type, language, platform, text_type
            )
            
            # 按优先级排序
            matching_rules.sort(key=lambda r: r.priority)
            
            # 应用规则
            for rule in matching_rules:
                if not rule.enabled:
                    continue
                
                try:
                    new_text, applied = self._apply_single_rule(rule, processed_text)
                    if applied:
                        processed_text = new_text
                        applied_rules.append(rule.rule_id)
                        
                        # 更新使用统计
                        rule.usage_count += 1
                        
                except Exception as e:
                    logger.error(f"应用规则失败: {rule.rule_id}, {e}")
                    # 更新成功率
                    rule.success_rate = max(0, rule.success_rate - 1)
            
            return processed_text, applied_rules
            
        except Exception as e:
            logger.error(f"应用翻译规则失败: {e}")
            return text, []
    
    def _apply_single_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用单个规则"""
        try:
            if rule.action == RuleAction.REPLACE:
                return self._apply_replace_rule(rule, text)
            elif rule.action == RuleAction.REMOVE:
                return self._apply_remove_rule(rule, text)
            elif rule.action == RuleAction.INSERT:
                return self._apply_insert_rule(rule, text)
            elif rule.action == RuleAction.VALIDATE:
                return self._apply_validate_rule(rule, text)
            elif rule.action == RuleAction.TRANSFORM:
                return self._apply_transform_rule(rule, text)
            else:
                return text, False
                
        except Exception as e:
            logger.error(f"应用单个规则失败: {rule.rule_id}, {e}")
            return text, False
    
    def _apply_replace_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用替换规则"""
        if not rule.pattern:
            return text, False
        
        flags = 0 if rule.case_sensitive else re.IGNORECASE
        
        if rule.use_regex:
            # 使用正则表达式
            if re.search(rule.pattern, text, flags):
                new_text = re.sub(rule.pattern, rule.replacement, text, flags=flags)
                return new_text, new_text != text
        else:
            # 简单字符串替换
            if rule.case_sensitive:
                if rule.pattern in text:
                    return text.replace(rule.pattern, rule.replacement), True
            else:
                if rule.pattern.lower() in text.lower():
                    # 保持大小写的替换
                    pattern = re.compile(re.escape(rule.pattern), re.IGNORECASE)
                    new_text = pattern.sub(rule.replacement, text)
                    return new_text, new_text != text
        
        return text, False
    
    def _apply_remove_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用移除规则"""
        return self._apply_replace_rule(
            TranslationRule(
                rule_id=rule.rule_id,
                name=rule.name,
                rule_type=rule.rule_type,
                action=RuleAction.REPLACE,
                pattern=rule.pattern,
                replacement="",
                case_sensitive=rule.case_sensitive,
                use_regex=rule.use_regex
            ),
            text
        )
    
    def _apply_insert_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用插入规则"""
        # 简单实现：在文本末尾插入
        if rule.replacement:
            return text + rule.replacement, True
        return text, False
    
    def _apply_validate_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用验证规则"""
        # 验证规则不修改文本，只检查是否符合条件
        if not rule.pattern:
            return text, True
        
        flags = 0 if rule.case_sensitive else re.IGNORECASE
        
        if rule.use_regex:
            return text, bool(re.search(rule.pattern, text, flags))
        else:
            if rule.case_sensitive:
                return text, rule.pattern in text
            else:
                return text, rule.pattern.lower() in text.lower()
    
    def _apply_transform_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用转换规则"""
        # 简单的转换实现
        if "uppercase" in rule.replacement.lower():
            return text.upper(), True
        elif "lowercase" in rule.replacement.lower():
            return text.lower(), True
        elif "title" in rule.replacement.lower():
            return text.title(), True
        
        return text, False
    
    def _get_matching_rules(self, rule_type: RuleType,
                          language: Optional[str] = None,
                          platform: Optional[str] = None,
                          text_type: Optional[str] = None) -> List[TranslationRule]:
        """获取匹配的规则"""
        matching_rules = []
        
        for rule in self.rules.values():
            if rule.rule_type != rule_type:
                continue
            
            # 检查语言匹配
            if language and rule.languages and language not in rule.languages:
                continue
            
            # 检查平台匹配
            if platform and rule.platforms and platform not in rule.platforms:
                continue
            
            # 检查文本类型匹配
            if text_type and rule.text_types and text_type not in rule.text_types:
                continue
            
            matching_rules.append(rule)
        
        return matching_rules
    
    # ==================== 术语管理 ====================
    
    def add_terminology(self, source_term: str, target_term: str,
                       source_lang: str, target_lang: str,
                       domain: str = "general", **kwargs) -> str:
        """
        添加术语条目
        
        Args:
            source_term: 源术语
            target_term: 目标术语
            source_lang: 源语言
            target_lang: 目标语言
            domain: 领域
            **kwargs: 其他参数
        
        Returns:
            str: 术语ID
        """
        try:
            term_id = str(uuid.uuid4())
            
            # 检查数量限制
            if len(self.terminology) >= self.config["max_terminology"]:
                raise ValueError(f"术语数量已达上限: {self.config['max_terminology']}")
            
            # 创建术语条目
            term_entry = TerminologyEntry(
                term_id=term_id,
                source_term=source_term,
                target_term=target_term,
                source_lang=source_lang,
                target_lang=target_lang,
                domain=domain,
                **kwargs
            )
            
            # 存储术语
            self.terminology[term_id] = term_entry
            
            # 更新统计
            self._update_stats()
            
            # 自动保存
            if self.config["auto_save"]:
                self._save_terminology_to_disk(term_entry)
            
            logger.info(f"添加术语条目: {term_id}, {source_term} -> {target_term}")
            return term_id
            
        except Exception as e:
            logger.error(f"添加术语条目失败: {e}")
            raise
    
    def find_terminology(self, source_term: str, source_lang: str, target_lang: str,
                        domain: Optional[str] = None) -> List[TerminologyEntry]:
        """查找术语条目"""
        try:
            matching_terms = []
            
            for term_entry in self.terminology.values():
                # 精确匹配
                if (term_entry.source_term.lower() == source_term.lower() and
                    term_entry.source_lang == source_lang and
                    term_entry.target_lang == target_lang):
                    
                    if domain is None or term_entry.domain == domain:
                        matching_terms.append(term_entry)
            
            # 按置信度和使用次数排序
            matching_terms.sort(key=lambda t: (t.confidence, t.usage_count), reverse=True)
            
            return matching_terms
            
        except Exception as e:
            logger.error(f"查找术语条目失败: {e}")
            return []
    
    def apply_terminology(self, text: str, source_lang: str, target_lang: str,
                         domain: Optional[str] = None) -> tuple[str, List[str]]:
        """应用术语翻译"""
        try:
            processed_text = text
            applied_terms = []
            
            # 获取所有相关术语
            relevant_terms = []
            for term_entry in self.terminology.values():
                if (term_entry.source_lang == source_lang and
                    term_entry.target_lang == target_lang):
                    
                    if domain is None or term_entry.domain == domain:
                        relevant_terms.append(term_entry)
            
            # 按术语长度排序（长的先处理，避免部分匹配）
            relevant_terms.sort(key=lambda t: len(t.source_term), reverse=True)
            
            # 应用术语替换
            for term_entry in relevant_terms:
                if term_entry.source_term.lower() in processed_text.lower():
                    # 使用正则表达式进行词边界匹配
                    pattern = r'\b' + re.escape(term_entry.source_term) + r'\b'
                    if re.search(pattern, processed_text, re.IGNORECASE):
                        processed_text = re.sub(
                            pattern, term_entry.target_term, processed_text, flags=re.IGNORECASE
                        )
                        applied_terms.append(term_entry.term_id)
                        
                        # 更新使用统计
                        term_entry.usage_count += 1
            
            return processed_text, applied_terms
            
        except Exception as e:
            logger.error(f"应用术语翻译失败: {e}")
            return text, []
    
    # ==================== 辅助方法 ====================
    
    def _extract_variables(self, content: str) -> List[str]:
        """从内容中提取模板变量"""
        try:
            # 查找 {variable_name} 格式的变量
            variables = re.findall(r'\{([^}]+)\}', content)
            return list(set(variables))  # 去重
        except Exception as e:
            logger.error(f"提取模板变量失败: {e}")
            return []
    
    def _validate_prompt(self, prompt: PromptTemplate):
        """验证提示词"""
        if not prompt.name:
            raise ValueError("提示词名称不能为空")
        
        if not prompt.content:
            raise ValueError("提示词内容不能为空")
    
    def _validate_rule(self, rule: TranslationRule):
        """验证规则"""
        if not rule.name:
            raise ValueError("规则名称不能为空")
        
        if rule.action in [RuleAction.REPLACE, RuleAction.REMOVE] and not rule.pattern:
            raise ValueError("替换和移除规则必须指定匹配模式")
        
        if rule.priority < 1 or rule.priority > 10:
            raise ValueError("规则优先级必须在1-10之间")
    
    def _load_default_data(self):
        """加载默认数据"""
        try:
            # 默认系统提示词
            self.create_prompt(
                name="通用翻译系统提示词",
                prompt_type=PromptType.SYSTEM,
                content="你是一个专业的翻译专家。请准确、流畅地翻译用户提供的文本，保持原文的意思和风格。",
                description="通用的翻译系统提示词"
            )
            
            # 默认用户提示词模板
            self.create_prompt(
                name="商品标题翻译提示词",
                prompt_type=PromptType.USER,
                content="请将以下{source_lang}商品标题翻译成{target_lang}：\n\n{text}\n\n要求：\n1. 保持关键词准确性\n2. 符合{target_lang}表达习惯\n3. 突出商品特色",
                variables=["source_lang", "target_lang", "text"],
                description="商品标题翻译专用提示词模板"
            )
            
            # 默认预处理规则
            self.create_rule(
                name="移除多余空格",
                rule_type=RuleType.PREPROCESSING,
                action=RuleAction.REPLACE,
                pattern=r'\s+',
                replacement=" ",
                use_regex=True,
                description="移除文本中的多余空格"
            )
            
            # 默认术语
            self.add_terminology(
                source_term="smartphone",
                target_term="智能手机",
                source_lang="en",
                target_lang="zh",
                domain="electronics"
            )
            
            logger.info("加载默认数据完成")
            
        except Exception as e:
            logger.error(f"加载默认数据失败: {e}")
    
    def _load_data_from_disk(self):
        """从磁盘加载数据"""
        try:
            # 加载提示词
            prompts_dir = os.path.join(self.data_dir, "prompts")
            if os.path.exists(prompts_dir):
                for filename in os.listdir(prompts_dir):
                    if filename.endswith('.json'):
                        file_path = os.path.join(prompts_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            prompt = self._dict_to_prompt(data)
                            self.prompts[prompt.prompt_id] = prompt
                            
                        except Exception as e:
                            logger.error(f"加载提示词文件失败: {file_path}, {e}")
            
            # 加载规则
            rules_dir = os.path.join(self.data_dir, "rules")
            if os.path.exists(rules_dir):
                for filename in os.listdir(rules_dir):
                    if filename.endswith('.json'):
                        file_path = os.path.join(rules_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            rule = self._dict_to_rule(data)
                            self.rules[rule.rule_id] = rule
                            
                        except Exception as e:
                            logger.error(f"加载规则文件失败: {file_path}, {e}")
            
            # 加载术语
            terminology_dir = os.path.join(self.data_dir, "terminology")
            if os.path.exists(terminology_dir):
                for filename in os.listdir(terminology_dir):
                    if filename.endswith('.json'):
                        file_path = os.path.join(terminology_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            term = self._dict_to_terminology(data)
                            self.terminology[term.term_id] = term
                            
                        except Exception as e:
                            logger.error(f"加载术语文件失败: {file_path}, {e}")
            
            self._update_stats()
            logger.info(f"从磁盘加载数据完成: 提示词{len(self.prompts)}个, 规则{len(self.rules)}个, 术语{len(self.terminology)}个")
            
        except Exception as e:
            logger.error(f"从磁盘加载数据失败: {e}")
    
    def _save_prompt_to_disk(self, prompt: PromptTemplate):
        """保存提示词到磁盘"""
        try:
            file_path = os.path.join(self.data_dir, "prompts", f"{prompt.prompt_id}.json")
            data = self._prompt_to_dict(prompt)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存提示词到磁盘失败: {prompt.prompt_id}, {e}")
    
    def _save_rule_to_disk(self, rule: TranslationRule):
        """保存规则到磁盘"""
        try:
            file_path = os.path.join(self.data_dir, "rules", f"{rule.rule_id}.json")
            data = self._rule_to_dict(rule)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存规则到磁盘失败: {rule.rule_id}, {e}")
    
    def _save_terminology_to_disk(self, term: TerminologyEntry):
        """保存术语到磁盘"""
        try:
            file_path = os.path.join(self.data_dir, "terminology", f"{term.term_id}.json")
            data = self._terminology_to_dict(term)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存术语到磁盘失败: {term.term_id}, {e}")
    
    def _prompt_to_dict(self, prompt: PromptTemplate) -> Dict[str, Any]:
        """提示词对象转字典"""
        data = asdict(prompt)
        data['prompt_type'] = prompt.prompt_type.value
        return data
    
    def _dict_to_prompt(self, data: Dict[str, Any]) -> PromptTemplate:
        """字典转提示词对象"""
        data['prompt_type'] = PromptType(data['prompt_type'])
        
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return PromptTemplate(**data)
    
    def _rule_to_dict(self, rule: TranslationRule) -> Dict[str, Any]:
        """规则对象转字典"""
        data = asdict(rule)
        data['rule_type'] = rule.rule_type.value
        data['action'] = rule.action.value
        return data
    
    def _dict_to_rule(self, data: Dict[str, Any]) -> TranslationRule:
        """字典转规则对象"""
        data['rule_type'] = RuleType(data['rule_type'])
        data['action'] = RuleAction(data['action'])
        
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return TranslationRule(**data)
    
    def _terminology_to_dict(self, term: TerminologyEntry) -> Dict[str, Any]:
        """术语对象转字典"""
        return asdict(term)
    
    def _dict_to_terminology(self, data: Dict[str, Any]) -> TerminologyEntry:
        """字典转术语对象"""
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return TerminologyEntry(**data)
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            self.stats["total_prompts"] = len(self.prompts)
            self.stats["active_prompts"] = len([p for p in self.prompts.values() if p.enabled])
            self.stats["total_rules"] = len(self.rules)
            self.stats["active_rules"] = len([r for r in self.rules.values() if r.enabled])
            self.stats["total_terminology"] = len(self.terminology)
            self.stats["verified_terminology"] = len([t for t in self.terminology.values() if t.verified])
            self.stats["total_usage"] = (
                sum(p.usage_count for p in self.prompts.values()) +
                sum(r.usage_count for r in self.rules.values()) +
                sum(t.usage_count for t in self.terminology.values())
            )
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            self._update_stats()
            
            return {
                "basic_stats": self.stats.copy(),
                "prompt_types": {
                    ptype.value: len([p for p in self.prompts.values() if p.prompt_type == ptype])
                    for ptype in PromptType
                },
                "rule_types": {
                    rtype.value: len([r for r in self.rules.values() if r.rule_type == rtype])
                    for rtype in RuleType
                },
                "terminology_domains": {
                    domain: len([t for t in self.terminology.values() if t.domain == domain])
                    for domain in set(t.domain for t in self.terminology.values())
                },
                "config": self.config.copy()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"basic_stats": {}, "prompt_types": {}, "rule_types": {}}
