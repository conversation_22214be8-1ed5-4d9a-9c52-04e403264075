"""
价格趋势分析引擎演示

展示价格分析、销量分析、趋势计算、预测等功能
"""

import asyncio
import sys
import random
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.analytics.price_analyzer import PriceAnalyzer, PriceTrend
from app.services.analytics.sales_analyzer import SalesAnalyzer, SalesTrend
from app.services.analytics.trend_calculator import TrendCalculator, TrendDirection
from app.services.analytics.prediction_engine import PredictionEngine, PredictionMethod


async def demo_price_analysis():
    """演示价格分析功能"""
    print("=== 价格趋势分析演示 ===")
    
    price_analyzer = PriceAnalyzer()
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=8999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=25000, rating=4.9)
        ),
        Product(
            url="https://detail.1688.com/offer/789012.html",
            title="手机壳批发 透明硅胶保护套 工厂直销",
            platform="1688",
            product_type=ProductType.SUPPLIER,
            price=ProductPrice(current_price=3.80, min_order_quantity=100),
            metrics=ProductMetrics(sales_count=150000, stock_quantity=9999)
        ),
        Product(
            url="https://item.jd.com/345678.html",
            title="Samsung Galaxy S24 Ultra 512GB 钛金灰",
            platform="jd",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=9999.00),
            specs=ProductSpecs(brand="Samsung"),
            metrics=ProductMetrics(sales_count=18000, rating=4.8)
        )
    ]
    
    print(f"\n1. 添加历史价格数据:")
    
    # 为每个商品添加历史价格数据
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        
        # 生成模拟的历史价格数据
        base_price = product.price.current_price
        base_time = datetime.now() - timedelta(days=30)
        
        # 不同的价格趋势模式
        if i == 0:  # iPhone - 价格下降趋势
            price_trend = "下降"
            for j in range(15):
                price = base_price * (1.1 - j * 0.006)  # 逐渐下降
                timestamp = base_time + timedelta(days=j*2)
                await price_analyzer.add_price_data(product.id, price, timestamp)
        elif i == 1:  # 手机壳 - 价格上涨趋势
            price_trend = "上涨"
            for j in range(15):
                price = base_price * (0.9 + j * 0.008)  # 逐渐上涨
                timestamp = base_time + timedelta(days=j*2)
                await price_analyzer.add_price_data(product.id, price, timestamp)
        else:  # Samsung - 价格波动
            price_trend = "波动"
            for j in range(15):
                # 添加随机波动
                fluctuation = random.uniform(-0.05, 0.05)
                price = base_price * (1 + fluctuation)
                timestamp = base_time + timedelta(days=j*2)
                await price_analyzer.add_price_data(product.id, price, timestamp)
        
        print(f"     模拟趋势: {price_trend}")
        print(f"     数据点数: 15个")
    
    print(f"\n2. 价格趋势分析:")
    
    # 分析每个商品的价格趋势
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1} 分析结果:")
        
        analysis = await price_analyzer.analyze_price_trend(product, days=30)
        
        print(f"     当前价格: ¥{analysis.current_price:.2f}")
        print(f"     价格趋势: {analysis.price_trend.value}")
        print(f"     趋势强度: {analysis.trend_strength:.2f}")
        print(f"     价格变化: {analysis.price_change_percent:+.1f}%")
        print(f"     波动率: {analysis.volatility:.1%}")
        print(f"     平均价格: ¥{analysis.average_price:.2f}")
        print(f"     价格区间: ¥{analysis.min_price:.2f} - ¥{analysis.max_price:.2f}")
        
        if analysis.price_alerts:
            print(f"     价格预警:")
            for alert in analysis.price_alerts:
                print(f"       - {alert['level']}: {alert['message']}")
    
    print(f"\n3. 竞品价格分析:")
    
    # 竞品价格分析
    iphone = test_products[0]
    samsung = test_products[2]
    competitors = [samsung]
    
    comp_analysis = await price_analyzer.analyze_competitor_prices(iphone, competitors)
    
    print(f"   目标商品: {iphone.title[:30]}...")
    print(f"   当前价格: ¥{iphone.price.current_price:.2f}")
    print(f"   竞品数量: {len(comp_analysis.competitor_products)}")
    print(f"   市场位置: {comp_analysis.market_position}")
    print(f"   价格优势: {comp_analysis.price_advantage:+.1f}%")
    print(f"   价格对比:")
    for comp_id, price in comp_analysis.price_comparison.items():
        print(f"     - {comp_id[:20]}...: ¥{price:.2f}")
    print(f"   建议:")
    for rec in comp_analysis.recommendations:
        print(f"     - {rec}")
    
    print(f"\n4. 供应商价格分析:")
    
    # 供应商价格分析
    supplier_product = test_products[1]
    supplier_analysis = await price_analyzer.analyze_supplier_prices(supplier_product, "supplier_001")
    
    print(f"   供应商商品: {supplier_product.title[:30]}...")
    print(f"   成本趋势: {supplier_analysis.cost_trend.value}")
    print(f"   成本变化: {supplier_analysis.cost_change_percent:+.1f}%")
    print(f"   利润率影响: {supplier_analysis.profit_margin_impact:+.1f}%")
    print(f"   供应风险: {supplier_analysis.supply_risk_level}")
    print(f"   建议:")
    for rec in supplier_analysis.recommendations:
        print(f"     - {rec}")
    
    return test_products, price_analyzer


async def demo_sales_analysis(products):
    """演示销量分析功能"""
    print("\n=== 销量趋势分析演示 ===")
    
    sales_analyzer = SalesAnalyzer()
    
    print(f"\n1. 添加历史销量数据:")
    
    # 为每个商品添加历史销量数据
    for i, product in enumerate(products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        
        base_sales = product.metrics.sales_count if product.metrics else 1000
        base_time = datetime.now() - timedelta(days=21)
        
        # 不同的销量趋势模式
        if i == 0:  # iPhone - 销量增长
            trend_type = "增长"
            for j in range(10):
                sales = int(base_sales * (0.8 + j * 0.025))  # 逐渐增长
                timestamp = base_time + timedelta(days=j*2)
                await sales_analyzer.add_sales_data(product.id, sales, timestamp)
        elif i == 1:  # 手机壳 - 销量稳定
            trend_type = "稳定"
            for j in range(10):
                sales = int(base_sales * (0.95 + random.uniform(-0.05, 0.05)))
                timestamp = base_time + timedelta(days=j*2)
                await sales_analyzer.add_sales_data(product.id, sales, timestamp)
        else:  # Samsung - 销量下降
            trend_type = "下降"
            for j in range(10):
                sales = int(base_sales * (1.1 - j * 0.015))  # 逐渐下降
                timestamp = base_time + timedelta(days=j*2)
                await sales_analyzer.add_sales_data(product.id, sales, timestamp)
        
        print(f"     模拟趋势: {trend_type}")
        print(f"     数据点数: 10个")
    
    print(f"\n2. 销量趋势分析:")
    
    # 分析每个商品的销量趋势
    for i, product in enumerate(products):
        print(f"\n   商品 {i+1} 分析结果:")
        
        analysis = await sales_analyzer.analyze_sales_trend(product, days=21)
        
        print(f"     当前销量: {analysis.current_sales:,} 件")
        print(f"     销量趋势: {analysis.sales_trend.value}")
        print(f"     趋势强度: {analysis.trend_strength:.2f}")
        print(f"     增长率: {analysis.sales_growth_rate:+.1f}%")
        print(f"     日均销量: {analysis.average_daily_sales:.0f} 件")
        print(f"     总销量: {analysis.total_sales:,} 件")
        print(f"     峰值销量: {analysis.peak_sales:,} 件")
        print(f"     销售表现: {analysis.sales_performance.value}")
        print(f"     市场份额: {analysis.market_share_estimate:.2f}%")
        
        if analysis.insights:
            print(f"     洞察:")
            for insight in analysis.insights:
                print(f"       - {insight}")
    
    print(f"\n3. 竞品销量分析:")
    
    # 竞品销量分析
    iphone = products[0]
    samsung = products[2]
    competitors = [samsung]
    
    comp_sales_analysis = await sales_analyzer.analyze_competitor_sales(iphone, competitors)
    
    print(f"   目标商品: {iphone.title[:30]}...")
    print(f"   当前销量: {iphone.metrics.sales_count:,} 件")
    print(f"   竞品数量: {len(comp_sales_analysis.competitor_products)}")
    print(f"   市场排名: 第 {comp_sales_analysis.market_ranking} 名")
    print(f"   市场份额: {comp_sales_analysis.market_share:.1f}%")
    print(f"   竞争优势: {comp_sales_analysis.competitive_advantage}")
    print(f"   销量对比:")
    for comp_id, sales in comp_sales_analysis.sales_comparison.items():
        print(f"     - {comp_id[:20]}...: {sales:,} 件")
    print(f"   建议:")
    for rec in comp_sales_analysis.recommendations:
        print(f"     - {rec}")
    
    return sales_analyzer


async def demo_trend_calculation():
    """演示趋势计算功能"""
    print("\n=== 趋势计算演示 ===")
    
    trend_calculator = TrendCalculator()
    
    print(f"\n1. 线性趋势计算:")
    
    # 测试不同的趋势数据
    trend_data = {
        "上升趋势": [100, 110, 120, 130, 140, 150, 160],
        "下降趋势": [160, 150, 140, 130, 120, 110, 100],
        "平稳趋势": [100, 102, 98, 101, 99, 103, 97],
        "波动趋势": [100, 120, 90, 130, 80, 140, 85]
    }
    
    for trend_name, values in trend_data.items():
        print(f"\n   {trend_name}:")
        result = trend_calculator.calculate_linear_trend(values)
        
        print(f"     方向: {result.direction.value}")
        print(f"     强度: {result.strength.value}")
        print(f"     斜率: {result.slope:.2f}")
        print(f"     相关系数: {result.correlation:.3f}")
        print(f"     置信度: {result.confidence:.3f}")
        print(f"     变化百分比: {result.change_percent:+.1f}%")
        print(f"     波动率: {result.volatility:.3f}")
    
    print(f"\n2. 移动平均计算:")
    
    test_values = [100, 105, 110, 108, 112, 115, 118, 120, 125, 122]
    
    # 简单移动平均
    sma_result = trend_calculator.calculate_moving_average(test_values, period=3, method="simple")
    print(f"   简单移动平均 (周期=3):")
    print(f"     原始数据: {test_values}")
    print(f"     移动平均: {[round(v, 1) for v in sma_result.values]}")
    
    # 指数移动平均
    ema_result = trend_calculator.calculate_moving_average(test_values, period=3, method="exponential")
    print(f"   指数移动平均 (周期=3):")
    print(f"     移动平均: {[round(v, 1) for v in ema_result.values]}")
    
    print(f"\n3. 季节性检测:")
    
    # 创建有季节性的数据（7天周期）
    seasonal_data = []
    for week in range(4):  # 4周数据
        for day in range(7):
            base_value = 100
            if day in [5, 6]:  # 周末销量高
                seasonal_value = base_value + 30
            else:
                seasonal_value = base_value + random.randint(-10, 10)
            seasonal_data.append(seasonal_value)
    
    seasonality_result = trend_calculator.detect_seasonality(seasonal_data, max_period=14)
    
    print(f"   测试数据长度: {len(seasonal_data)}")
    print(f"   有季节性: {seasonality_result.has_seasonality}")
    if seasonality_result.has_seasonality:
        print(f"   季节周期: {seasonality_result.seasonal_period} 天")
        print(f"   季节强度: {seasonality_result.seasonal_strength:.3f}")
        print(f"   季节模式: {[round(v, 1) for v in seasonality_result.seasonal_pattern[:7]]}")
    
    print(f"\n4. 统计信息计算:")
    
    stats = trend_calculator.calculate_statistics(test_values)
    print(f"   数据: {test_values}")
    print(f"   统计信息:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"     {key}: {value:.2f}")
        else:
            print(f"     {key}: {value}")


async def demo_prediction_engine(products, price_analyzer, sales_analyzer):
    """演示预测引擎功能"""
    print("\n=== 预测引擎演示 ===")
    
    prediction_engine = PredictionEngine()
    
    print(f"\n1. 价格趋势预测:")
    
    # 为前两个商品进行价格预测
    for i, product in enumerate(products[:2]):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        
        # 获取历史价格数据
        if product.id in price_analyzer.price_history:
            price_points = price_analyzer.price_history[product.id]
            historical_prices = [point.price for point in price_points]
            historical_timestamps = [point.timestamp for point in price_points]
            
            # 进行价格预测
            price_prediction = await prediction_engine.predict_price_trend(
                product, historical_prices, historical_timestamps, 
                prediction_days=7, method=PredictionMethod.ENSEMBLE
            )
            
            print(f"     当前价格: ¥{price_prediction.current_price:.2f}")
            print(f"     预测价格 (7天后): ¥{price_prediction.predicted_prices[-1]:.2f}")
            print(f"     价格变化: {price_prediction.price_change_percent:+.1f}%")
            print(f"     趋势方向: {price_prediction.trend_direction}")
            print(f"     置信度: {price_prediction.confidence_score:.2f}")
            
            if price_prediction.risk_factors:
                print(f"     风险因素:")
                for risk in price_prediction.risk_factors:
                    print(f"       - {risk}")
        else:
            print(f"     无历史价格数据")
    
    print(f"\n2. 销量趋势预测:")
    
    # 为前两个商品进行销量预测
    for i, product in enumerate(products[:2]):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        
        # 获取历史销量数据
        if product.id in sales_analyzer.sales_history:
            sales_points = sales_analyzer.sales_history[product.id]
            historical_sales = [point.sales_count for point in sales_points]
            historical_timestamps = [point.timestamp for point in sales_points]
            
            # 进行销量预测
            sales_prediction = await prediction_engine.predict_sales_trend(
                product, historical_sales, historical_timestamps, 
                prediction_days=14
            )
            
            print(f"     当前销量: {sales_prediction.current_sales:,} 件")
            print(f"     预测销量 (14天后): {sales_prediction.predicted_sales[-1]:,} 件")
            print(f"     销量增长: {sales_prediction.sales_growth_rate:+.1f}%")
            
            if sales_prediction.seasonal_factors:
                print(f"     季节因子: {[round(f, 1) for f in sales_prediction.seasonal_factors[:7]]}")
            
            if sales_prediction.market_factors:
                print(f"     市场因素:")
                for factor in sales_prediction.market_factors:
                    print(f"       - {factor}")
        else:
            print(f"     无历史销量数据")
    
    print(f"\n3. 预测方法对比:")
    
    # 使用不同方法预测第一个商品的价格
    product = products[0]
    if product.id in price_analyzer.price_history:
        price_points = price_analyzer.price_history[product.id]
        historical_prices = [point.price for point in price_points]
        historical_timestamps = [point.timestamp for point in price_points]
        
        methods = [
            PredictionMethod.LINEAR_REGRESSION,
            PredictionMethod.MOVING_AVERAGE,
            PredictionMethod.EXPONENTIAL_SMOOTHING,
            PredictionMethod.ENSEMBLE
        ]
        
        print(f"   商品: {product.title[:30]}...")
        print(f"   当前价格: ¥{historical_prices[-1]:.2f}")
        
        for method in methods:
            prediction = await prediction_engine.predict_price_trend(
                product, historical_prices, historical_timestamps, 
                prediction_days=7, method=method
            )
            
            predicted_price = prediction.predicted_prices[-1] if prediction.predicted_prices else 0
            print(f"     {method.value}: ¥{predicted_price:.2f} ({prediction.price_change_percent:+.1f}%)")


async def demo_analytics_statistics():
    """演示分析统计信息"""
    print("\n=== 分析统计信息演示 ===")
    
    # 创建分析器实例
    price_analyzer = PriceAnalyzer()
    sales_analyzer = SalesAnalyzer()
    prediction_engine = PredictionEngine()
    
    # 添加一些模拟数据
    for i in range(5):
        product_id = f"product_{i}"
        
        # 添加价格数据
        for j in range(10):
            price = 100 + i * 10 + j * 2
            await price_analyzer.add_price_data(product_id, price)
        
        # 添加销量数据
        for j in range(10):
            sales = 1000 + i * 100 + j * 50
            await sales_analyzer.add_sales_data(product_id, sales)
    
    print(f"\n1. 价格分析统计:")
    price_stats = price_analyzer.get_analysis_statistics()
    for key, value in price_stats.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"     {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n2. 销量分析统计:")
    sales_stats = sales_analyzer.get_sales_statistics()
    for key, value in sales_stats.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"     {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n3. 预测引擎统计:")
    prediction_stats = prediction_engine.get_prediction_statistics()
    for key, value in prediction_stats.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"     {sub_key}: {sub_value}")
        elif isinstance(value, list):
            print(f"   {key}: {', '.join(value)}")
        else:
            print(f"   {key}: {value}")


async def main():
    """主演示函数"""
    print("🚀 价格趋势分析引擎演示")
    print("=" * 60)
    
    # 1. 价格分析演示
    products, price_analyzer = await demo_price_analysis()
    
    # 2. 销量分析演示
    sales_analyzer = await demo_sales_analysis(products)
    
    # 3. 趋势计算演示
    await demo_trend_calculation()
    
    # 4. 预测引擎演示
    await demo_prediction_engine(products, price_analyzer, sales_analyzer)
    
    # 5. 统计信息演示
    await demo_analytics_statistics()
    
    print("\n" + "=" * 60)
    print("✅ 价格趋势分析引擎演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 价格分析：趋势识别、波动检测、竞品对比、供应商分析")
    print(f"- 销量分析：增长趋势、季节性检测、市场份额、竞争分析")
    print(f"- 趋势计算：线性回归、移动平均、季节性分解、统计分析")
    print(f"- 预测引擎：多种预测方法、集成预测、置信度评估")
    print(f"- 异常检测：价格预警、波动监控、风险识别")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: 3个")
    print(f"- 价格数据点: 45个")
    print(f"- 销量数据点: 30个")
    print(f"- 趋势计算: 4种模式")
    print(f"- 预测方法: 4种算法")
    print(f"- 分析维度: 15个指标")
    
    print(f"\n🔧 技术特性:")
    print(f"- 多维度分析：价格、销量、趋势、预测四大维度")
    print(f"- 智能算法：线性回归、移动平均、指数平滑、季节分解")
    print(f"- 异步处理：支持大批量数据的并行分析")
    print(f"- 缓存优化：分析结果缓存，提高查询效率")
    print(f"- 置信度评估：基于数据质量和模型准确性的置信度计算")
    print(f"- 风险识别：自动识别价格风险和市场风险因素")
    
    print(f"\n🏗️ 架构优势:")
    print(f"- 模块化设计：价格分析器 + 销量分析器 + 趋势计算器 + 预测引擎")
    print(f"- 算法丰富：集成多种统计和机器学习算法")
    print(f"- 扩展性强：易于添加新的分析方法和预测模型")
    print(f"- 实时分析：支持实时数据更新和增量分析")
    print(f"- 商业智能：面向业务的洞察生成和建议推荐")


if __name__ == "__main__":
    asyncio.run(main())
