<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>令牌管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button.danger {
            background-color: #ff4d4f;
        }
        button.danger:hover {
            background-color: #ff7875;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 令牌管理测试</h1>
        <p>测试前端应用的令牌管理和401错误处理</p>
        
        <div class="test-section">
            <h3>1. 检查当前localStorage状态</h3>
            <button onclick="checkLocalStorage()">检查localStorage</button>
            <div id="localStorageResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 登录获取新令牌</h3>
            <button onclick="testLogin()">登录获取令牌</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试有效令牌的API调用</h3>
            <button onclick="testValidToken()">测试 /api/v1/auth/me</button>
            <div id="validTokenResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 模拟无效令牌测试</h3>
            <button onclick="setInvalidToken()" class="danger">设置无效令牌</button>
            <button onclick="testInvalidToken()">测试无效令牌</button>
            <div id="invalidTokenResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 清除所有令牌</h3>
            <button onclick="clearAllTokens()" class="danger">清除localStorage</button>
            <div id="clearResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const credentials = {
            username: 'admin',
            password: ',d7@b]FDs]9s'
        };

        function checkLocalStorage() {
            const resultDiv = document.getElementById('localStorageResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const user = localStorage.getItem('user');
            
            let tokenInfo = '';
            if (accessToken) {
                try {
                    const payload = JSON.parse(atob(accessToken.split('.')[1]));
                    const currentTime = Math.floor(Date.now() / 1000);
                    const isExpired = payload.exp < currentTime;
                    
                    tokenInfo = `
访问令牌: ${accessToken.substring(0, 30)}...
令牌用户: ${payload.username}
令牌角色: ${payload.role}
过期时间: ${new Date(payload.exp * 1000).toLocaleString()}
当前时间: ${new Date().toLocaleString()}
是否过期: ${isExpired ? '是' : '否'}`;
                } catch (e) {
                    tokenInfo = `访问令牌: 格式无效 - ${accessToken.substring(0, 30)}...`;
                }
            } else {
                tokenInfo = '访问令牌: 不存在';
            }
            
            resultDiv.textContent = `📋 localStorage状态检查:

${tokenInfo}
刷新令牌: ${refreshToken ? refreshToken.substring(0, 30) + '...' : '不存在'}
用户信息: ${user ? '存在' : '不存在'}

🔍 这些信息帮助诊断令牌管理问题`;
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在登录...';
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    // 手动保存到localStorage（模拟Redux行为）
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录成功！令牌已保存到localStorage
用户: ${data.user.username}
角色: ${data.user.role}
令牌: ${data.access_token.substring(0, 30)}...
过期时间: ${data.expires_in}秒

🎉 新令牌已保存，可以进行API测试`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testValidToken() {
            const resultDiv = document.getElementById('validTokenResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试有效令牌...';
            
            const token = localStorage.getItem('access_token');
            if (!token) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ 没有找到访问令牌，请先登录';
                return;
            }
            
            try {
                const response = await fetch('/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 有效令牌测试成功！
用户ID: ${data.user_id}
用户名: ${data.username}
邮箱: ${data.email}
角色: ${data.role}
状态: ${data.is_active ? '活跃' : '非活跃'}

🎉 令牌有效，API调用正常`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API调用失败:
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}

这可能表示令牌已过期或无效`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        function setInvalidToken() {
            const invalidToken = 'invalid.token.here';
            localStorage.setItem('access_token', invalidToken);
            
            const resultDiv = document.getElementById('invalidTokenResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result warning';
            resultDiv.textContent = `⚠️ 已设置无效令牌: ${invalidToken}

现在可以测试前端应用如何处理无效令牌`;
        }

        async function testInvalidToken() {
            const resultDiv = document.getElementById('invalidTokenResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试无效令牌处理...';
            
            const token = localStorage.getItem('access_token');
            
            try {
                const response = await fetch('/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.status === 401) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 无效令牌处理正确！
状态码: ${response.status}
错误消息: ${data.message || data.detail}

🎉 系统正确识别并拒绝了无效令牌
前端应该会自动清除localStorage并跳转到登录页`;
                } else {
                    resultDiv.className = 'result warning';
                    resultDiv.textContent = `⚠️ 意外的响应:
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        function clearAllTokens() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user');
            
            const resultDiv = document.getElementById('clearResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ 已清除所有令牌和用户信息

localStorage现在是干净的状态
刷新页面应该会跳转到登录页面`;
        }
    </script>
</body>
</html>
