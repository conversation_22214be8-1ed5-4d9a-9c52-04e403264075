# 电商商品监控系统配置文件

# 应用基础配置
app:
  name: "电商商品监控系统"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000
  reload: false

# 数据库配置
database:
  url: "postgresql+asyncpg://moniit:moniit123@localhost:5432/moniit"
  pool_size: 10
  max_overflow: 20
  echo: false
  pool_pre_ping: true
  pool_recycle: 3600

# Redis配置
redis:
  url: "redis://localhost:6380/0"
  max_connections: 10
  socket_timeout: 5
  socket_connect_timeout: 5

# 任务队列配置
celery:
  broker_url: "redis://localhost:6379/1"
  result_backend: "redis://localhost:6379/2"
  task_serializer: "json"
  accept_content: ["json"]
  result_serializer: "json"
  timezone: "UTC"
  enable_utc: true

# TaskMiddleware API配置
task_middleware:
  base_url: "http://localhost:3000"
  timeout: 30
  max_retries: 3
  retry_delay: 5

# 翻译服务配置
translation:
  default_provider: "openai"
  openai:
    api_key: ""
    base_url: "https://api.openai.com/v1"
    model: "gpt-3.5-turbo"
    max_tokens: 1000
    temperature: 0.3
  claude:
    api_key: ""
    base_url: "https://api.anthropic.com"
    model: "claude-3-haiku-20240307"
    max_tokens: 1000

# 监控配置
monitoring:
  default_frequency: 24  # 小时
  max_concurrent_tasks: 10
  retry_attempts: 3
  timeout: 30
  batch_size: 100

# 通知配置
notification:
  email:
    enabled: true
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    smtp_user: ""
    smtp_password: ""
    from_address: "<EMAIL>"
    use_tls: true

# 安全配置
security:
  secret_key: "your-secret-key-here-change-in-production"
  access_token_expire_minutes: 30
  algorithm: "HS256"
  password_min_length: 8

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
  console_output: true

# 缓存配置
cache:
  default_ttl: 3600  # 1小时
  price_trend_ttl: 1800  # 30分钟
  sales_trend_ttl: 1800  # 30分钟
  product_config_ttl: 3600  # 1小时
  supplier_info_ttl: 7200  # 2小时

# 数据处理配置
data_processing:
  batch_size: 1000
  max_file_size: "50MB"
  allowed_file_types: ["xlsx", "xls", "csv"]
  data_retention_days: 730  # 2年

# 平台配置
platforms:
  "1688":
    name: "1688.com"
    selectors:
      title: ".d-title"
      price: ".price-now"
      stock: ".amount-text"
      sales: ".sale-text"
      rating: ".star-text"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    rate_limit:
      requests_per_minute: 30
      delay_between_requests: 2
  
  amazon:
    name: "Amazon"
    selectors:
      title: "#productTitle"
      price: ".a-price-whole"
      stock: "#availability span"
      rating: ".a-icon-alt"
      sales: "#social-proofing-faceout-title-tk_bought"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    rate_limit:
      requests_per_minute: 20
      delay_between_requests: 3
