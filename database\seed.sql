-- 电商商品监控系统种子数据

-- 插入示例供货商数据
INSERT INTO suppliers (id, name, contact_person, phone, email, address, payment_terms, delivery_time, min_order_quantity, is_active, rating, notes) VALUES
(uuid_generate_v4(), '深圳电子科技有限公司', '张经理', '13800138001', '<EMAIL>', '深圳市南山区科技园', '30天付款', 7, 100, true, 4.5, '主要供应电子产品'),
(uuid_generate_v4(), '广州贸易公司', '李总', '13800138002', '<EMAIL>', '广州市天河区商务中心', '货到付款', 5, 50, true, 4.2, '快速发货，质量稳定'),
(uuid_generate_v4(), '义乌小商品批发', '王老板', '13800138003', '<EMAIL>', '义乌市国际商贸城', '预付50%', 3, 200, true, 4.0, '价格优势明显'),
(uuid_generate_v4(), '上海进出口贸易', '陈女士', '13800138004', '<EMAIL>', '上海市浦东新区外高桥', '信用证', 14, 500, true, 4.8, '国际品质，认证齐全'),
(uuid_generate_v4(), '北京科技供应链', '刘先生', '13800138005', '<EMAIL>', '北京市海淀区中关村', '月结', 10, 300, false, 3.8, '暂停合作');

-- 插入示例商品数据
INSERT INTO products (id, url, platform, title, title_translated, category, status, monitoring_frequency, is_active, tags, notes) VALUES
(uuid_generate_v4(), 'https://www.1688.com/product/123456.html', '1688', '无线蓝牙耳机 TWS真无线立体声', 'Wireless Bluetooth Earphones TWS True Wireless Stereo', '电子产品', 'active', 6, true, '{"brand": "自主品牌", "type": "耳机"}', '热销产品，需要密切监控'),
(uuid_generate_v4(), 'https://www.amazon.com/dp/B08N5WRWNW', 'amazon', 'Wireless Bluetooth Headphones', '无线蓝牙耳机', '电子产品', 'active', 12, true, '{"brand": "Sony", "type": "headphones"}', '国际市场热门产品'),
(uuid_generate_v4(), 'https://www.1688.com/product/789012.html', '1688', '手机充电器 快充PD20W', 'Phone Charger Fast Charging PD20W', '电子配件', 'active', 24, true, '{"brand": "通用", "type": "充电器"}', '标准监控频率'),
(uuid_generate_v4(), 'https://www.amazon.com/dp/B09JQKJQKJ', 'amazon', 'USB-C Fast Charger 20W', 'USB-C快速充电器20W', '电子配件', 'active', 24, true, '{"brand": "Anker", "type": "charger"}', '品牌产品，价格稳定'),
(uuid_generate_v4(), 'https://www.1688.com/product/345678.html', '1688', '智能手表 运动健康监测', 'Smart Watch Sports Health Monitoring', '智能穿戴', 'inactive', 48, false, '{"brand": "自主品牌", "type": "手表"}', '暂停监控的产品');

-- 获取插入的商品和供货商ID（用于后续插入成本数据）
DO $$
DECLARE
    product_ids uuid[];
    supplier_ids uuid[];
    p_id uuid;
    s_id uuid;
    i integer;
BEGIN
    -- 获取商品ID
    SELECT array_agg(id) INTO product_ids FROM products WHERE is_active = true LIMIT 4;
    -- 获取供货商ID
    SELECT array_agg(id) INTO supplier_ids FROM suppliers WHERE is_active = true LIMIT 4;
    
    -- 为每个商品添加成本信息
    FOR i IN 1..array_length(product_ids, 1) LOOP
        p_id := product_ids[i];
        
        -- 为每个商品添加2-3个供货商的成本信息
        FOR s_id IN SELECT unnest(supplier_ids[1:2]) LOOP
            INSERT INTO product_costs (
                id, product_id, supplier_id, unit_cost, currency, 
                shipping_cost, other_costs, total_cost, min_quantity, max_quantity,
                valid_from, valid_until, is_active, notes
            ) VALUES (
                uuid_generate_v4(), p_id, s_id, 
                (random() * 50 + 10)::numeric(12,4), 'USD',
                (random() * 5 + 1)::numeric(12,4), (random() * 2)::numeric(12,4), 
                0, -- total_cost will be calculated by trigger
                case when random() > 0.5 then 100 else 50 end,
                case when random() > 0.5 then 1000 else 500 end,
                CURRENT_TIMESTAMP - interval '30 days',
                CURRENT_TIMESTAMP + interval '90 days',
                true,
                '示例成本数据'
            );
        END LOOP;
    END LOOP;
END $$;

-- 插入示例商品历史数据（最近30天的数据）
DO $$
DECLARE
    product_ids uuid[];
    p_id uuid;
    day_offset integer;
    base_price numeric;
    base_sales integer;
    base_stock integer;
    base_rating numeric;
BEGIN
    -- 获取活跃商品ID
    SELECT array_agg(id) INTO product_ids FROM products WHERE is_active = true;
    
    -- 为每个商品生成历史数据
    FOREACH p_id IN ARRAY product_ids LOOP
        -- 设置基础值
        base_price := (random() * 100 + 20)::numeric(12,4);
        base_sales := (random() * 1000 + 100)::integer;
        base_stock := (random() * 500 + 50)::integer;
        base_rating := (random() * 1.5 + 3.5)::numeric(3,2);
        
        -- 生成最近30天的数据
        FOR day_offset IN 0..29 LOOP
            INSERT INTO product_history (
                time, product_id, platform, title, title_translated,
                price, currency, sales_count, stock_quantity, 
                rating, review_count, change_type, data_quality_score, raw_data
            ) VALUES (
                CURRENT_TIMESTAMP - (day_offset || ' days')::interval + (random() * interval '23 hours'),
                p_id,
                (SELECT platform FROM products WHERE id = p_id),
                (SELECT title FROM products WHERE id = p_id),
                (SELECT title_translated FROM products WHERE id = p_id),
                (base_price * (0.9 + random() * 0.2))::numeric(12,4), -- 价格波动±10%
                'USD',
                (base_sales + (random() * 100 - 50))::integer, -- 销量波动±50
                (base_stock - day_offset * 2 + (random() * 10 - 5))::integer, -- 库存逐渐减少
                (base_rating + (random() * 0.4 - 0.2))::numeric(3,2), -- 评分小幅波动
                (random() * 500 + 100)::integer, -- 评论数
                case when random() > 0.8 then 'price_change' else null end,
                (0.7 + random() * 0.3)::numeric(3,2), -- 数据质量评分
                json_build_object(
                    'crawl_time', CURRENT_TIMESTAMP,
                    'source', 'demo_data',
                    'quality', 'high'
                )
            );
        END LOOP;
    END LOOP;
END $$;

-- 插入示例监控任务
DO $$
DECLARE
    product_ids uuid[];
    p_id uuid;
BEGIN
    SELECT array_agg(id) INTO product_ids FROM products WHERE is_active = true;
    
    FOREACH p_id IN ARRAY product_ids LOOP
        -- 为每个商品创建一个待执行的监控任务
        INSERT INTO monitoring_tasks (
            id, product_id, task_type, status, priority,
            scheduled_at, max_retries
        ) VALUES (
            uuid_generate_v4(), p_id, 'price_monitor', 'pending', 
            (random() * 5 + 1)::integer,
            CURRENT_TIMESTAMP + interval '1 hour',
            3
        );
        
        -- 创建一个已完成的监控任务
        INSERT INTO monitoring_tasks (
            id, product_id, task_type, status, priority,
            scheduled_at, started_at, completed_at, max_retries,
            result_data
        ) VALUES (
            uuid_generate_v4(), p_id, 'full_monitor', 'completed',
            5,
            CURRENT_TIMESTAMP - interval '2 hours',
            CURRENT_TIMESTAMP - interval '2 hours',
            CURRENT_TIMESTAMP - interval '1 hour',
            3,
            json_build_object(
                'success', true,
                'data_points', 5,
                'execution_time', 45.2
            )
        );
    END LOOP;
END $$;

-- 插入示例预警数据
DO $$
DECLARE
    product_ids uuid[];
    p_id uuid;
BEGIN
    SELECT array_agg(id) INTO product_ids FROM products WHERE is_active = true LIMIT 2;
    
    -- 创建价格变化预警
    INSERT INTO alerts (
        id, product_id, alert_type, severity, title, message, data, is_read
    ) VALUES (
        uuid_generate_v4(), product_ids[1], 'price_change', 'medium',
        '价格异常波动', '商品价格在24小时内下降了15%，请关注市场动态',
        json_build_object('price_change', -15.5, 'previous_price', 29.99, 'current_price', 25.34),
        false
    );
    
    -- 创建库存不足预警
    INSERT INTO alerts (
        id, product_id, alert_type, severity, title, message, data, is_read
    ) VALUES (
        uuid_generate_v4(), product_ids[2], 'low_stock', 'high',
        '库存不足预警', '商品库存仅剩8件，建议及时补货',
        json_build_object('stock_quantity', 8, 'threshold', 10),
        false
    );
    
    -- 创建利润机会预警
    INSERT INTO alerts (
        id, alert_type, severity, title, message, data, is_read, is_resolved
    ) VALUES (
        uuid_generate_v4(), 'profit_opportunity', 'low',
        '利润机会提醒', '发现新的高利润商品机会，预计利润率可达45%',
        json_build_object('profit_rate', 45.2, 'product_count', 3),
        true, true
    );
END $$;

-- 创建TimescaleDB超表（如果表已存在）
SELECT create_hypertable('product_history', 'time', if_not_exists => TRUE);

-- 创建分区
SELECT auto_create_partitions();

-- 更新表统计信息
ANALYZE products;
ANALYZE suppliers;
ANALYZE product_costs;
ANALYZE product_history;
ANALYZE monitoring_tasks;
ANALYZE alerts;

-- 输出统计信息
DO $$
BEGIN
    RAISE NOTICE '数据初始化完成！';
    RAISE NOTICE '商品数量: %', (SELECT count(*) FROM products);
    RAISE NOTICE '供货商数量: %', (SELECT count(*) FROM suppliers);
    RAISE NOTICE '成本记录数量: %', (SELECT count(*) FROM product_costs);
    RAISE NOTICE '历史数据记录数量: %', (SELECT count(*) FROM product_history);
    RAISE NOTICE '监控任务数量: %', (SELECT count(*) FROM monitoring_tasks);
    RAISE NOTICE '预警数量: %', (SELECT count(*) FROM alerts);
END $$;
