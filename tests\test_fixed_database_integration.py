"""
修复后的数据库集成测试
使用简化的连接方式，避免复杂的异步fixture问题
"""

import pytest
import asyncio
import asyncpg
import os
from datetime import datetime, timedelta
from decimal import Decimal
import math


class TestFixedDatabaseIntegration:
    """修复后的数据库集成测试"""
    
    async def get_db_connection(self):
        """获取数据库连接"""
        db_url = "postgresql://moniit:moniit123@localhost:5432/moniit"
        
        try:
            conn = await asyncpg.connect(db_url)
            return conn
        except Exception as e:
            pytest.skip(f"数据库连接失败: {e}")
            return None
    
    @pytest.mark.asyncio
    async def test_database_connection_and_data_exists(self):
        """测试数据库连接和数据存在性"""
        conn = await self.get_db_connection()
        if not conn:
            return
        
        try:
            # 测试基本连接
            result = await conn.fetchval("SELECT 1")
            assert result == 1
            
            # 检查测试表是否存在
            tables_exist = await conn.fetchval("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name IN ('test_products', 'test_price_history')
            """)
            
            if tables_exist < 2:
                pytest.skip("测试表不存在，请先运行数据生成脚本")
                return
            
            # 检查是否有测试数据
            product_count = await conn.fetchval("SELECT COUNT(*) FROM test_products")
            price_count = await conn.fetchval("SELECT COUNT(*) FROM test_price_history")
            
            assert product_count > 0, "应该有测试商品数据"
            assert price_count > 0, "应该有测试价格数据"
            
            print(f"✅ 数据库连接和数据检查通过: {product_count} 个商品, {price_count} 条价格记录")
            
        finally:
            await conn.close()
    
    @pytest.mark.asyncio
    async def test_price_trend_analysis_with_real_data(self):
        """使用真实数据库数据测试价格趋势分析"""
        conn = await self.get_db_connection()
        if not conn:
            return
        
        try:
            # 获取测试商品
            products = await conn.fetch("SELECT id, name FROM test_products LIMIT 3")
            
            if not products:
                pytest.skip("没有找到测试数据，请先运行数据生成脚本")
                return
            
            for product in products:
                product_id = product["id"]
                product_name = product["name"]
                
                # 从数据库获取价格历史
                price_records = await conn.fetch("""
                    SELECT price, recorded_at 
                    FROM test_price_history 
                    WHERE product_id = $1 
                    ORDER BY recorded_at DESC 
                    LIMIT 50
                """, product_id)
                
                assert len(price_records) > 0, f"商品 {product_name} 应该有价格数据"
                
                # 提取价格数据进行趋势分析
                prices = [float(record['price']) for record in price_records]
                timestamps = [record['recorded_at'] for record in price_records]
                
                # 计算简单的线性趋势
                trend_result = self._calculate_linear_trend(prices)
                
                assert "slope" in trend_result
                assert "r_squared" in trend_result
                assert "trend_direction" in trend_result
                assert 0 <= trend_result["r_squared"] <= 1
                
                print(f"✅ {product_name} 趋势分析:")
                print(f"   斜率: {trend_result['slope']:.4f}")
                print(f"   R²: {trend_result['r_squared']:.4f}")
                print(f"   趋势: {trend_result['trend_direction']}")
        
        finally:
            await conn.close()
    
    @pytest.mark.asyncio
    async def test_price_volatility_analysis(self):
        """测试价格波动率分析"""
        conn = await self.get_db_connection()
        if not conn:
            return
        
        try:
            # 获取测试商品
            products = await conn.fetch("SELECT id, name FROM test_products LIMIT 2")
            
            if not products:
                pytest.skip("没有找到测试数据")
                return
            
            for product in products:
                product_id = product["id"]
                product_name = product["name"]
                
                # 获取价格数据
                price_records = await conn.fetch("""
                    SELECT price FROM test_price_history 
                    WHERE product_id = $1 
                    ORDER BY recorded_at
                """, product_id)
                
                if len(price_records) < 10:
                    continue
                
                prices = [float(record['price']) for record in price_records]
                
                # 计算波动率
                volatility_result = self._calculate_volatility(prices)
                
                assert "volatility" in volatility_result
                assert "volatility_percentage" in volatility_result
                assert volatility_result["volatility"] >= 0
                
                print(f"✅ {product_name} 波动率分析:")
                print(f"   波动率: {volatility_result['volatility']:.2f}")
                print(f"   波动率百分比: {volatility_result['volatility_percentage']:.2%}")
        
        finally:
            await conn.close()
    
    @pytest.mark.asyncio
    async def test_price_anomaly_detection(self):
        """测试价格异常检测"""
        conn = await self.get_db_connection()
        if not conn:
            return
        
        try:
            # 获取一个有足够数据的商品
            product = await conn.fetchrow("""
                SELECT p.id, p.name, COUNT(ph.id) as record_count
                FROM test_products p
                JOIN test_price_history ph ON p.id = ph.product_id
                GROUP BY p.id, p.name
                HAVING COUNT(ph.id) >= 20
                LIMIT 1
            """)
            
            if not product:
                pytest.skip("没有找到足够数据的测试商品")
                return
            
            product_id = product['id']
            product_name = product['name']
            
            # 获取价格数据
            price_records = await conn.fetch("""
                SELECT price FROM test_price_history 
                WHERE product_id = $1 
                ORDER BY recorded_at
            """, product_id)
            
            prices = [float(record['price']) for record in price_records]
            
            # 执行异常检测
            anomalies = self._detect_price_anomalies(prices)
            
            assert "anomaly_count" in anomalies
            assert "anomaly_indices" in anomalies
            assert "threshold_used" in anomalies
            assert anomalies["anomaly_count"] >= 0
            
            print(f"✅ {product_name} 异常检测:")
            print(f"   总记录数: {len(prices)}")
            print(f"   异常数量: {anomalies['anomaly_count']}")
            print(f"   异常比例: {anomalies['anomaly_count']/len(prices):.2%}")
            print(f"   检测阈值: ¥{anomalies['threshold_used']:.2f}")
        
        finally:
            await conn.close()
    
    @pytest.mark.asyncio
    async def test_multi_product_comparison(self):
        """测试多商品价格对比分析"""
        conn = await self.get_db_connection()
        if not conn:
            return
        
        try:
            # 获取所有商品的最新价格
            latest_prices = await conn.fetch("""
                SELECT DISTINCT ON (product_id)
                    p.name, ph.product_id, ph.price, ph.recorded_at
                FROM test_products p
                JOIN test_price_history ph ON p.id = ph.product_id
                ORDER BY ph.product_id, ph.recorded_at DESC
            """)
            
            if len(latest_prices) < 2:
                pytest.skip("需要至少2个商品进行比较")
                return
            
            # 计算价格统计
            prices = [float(record['price']) for record in latest_prices]
            avg_price = sum(prices) / len(prices)
            max_price = max(prices)
            min_price = min(prices)
            
            comparison_result = {
                "product_count": len(latest_prices),
                "average_price": avg_price,
                "max_price": max_price,
                "min_price": min_price,
                "price_range": max_price - min_price,
                "products": [
                    {
                        "name": record['name'],
                        "price": float(record['price']),
                        "price_vs_avg": float(record['price']) - avg_price
                    }
                    for record in latest_prices
                ]
            }
            
            assert comparison_result["product_count"] > 0
            assert comparison_result["average_price"] > 0
            assert comparison_result["price_range"] >= 0
            
            print(f"✅ 多商品价格对比分析:")
            print(f"   商品数量: {comparison_result['product_count']}")
            print(f"   平均价格: ¥{comparison_result['average_price']:.2f}")
            print(f"   价格范围: ¥{comparison_result['min_price']:.2f} - ¥{comparison_result['max_price']:.2f}")
            print(f"   价格差异: ¥{comparison_result['price_range']:.2f}")
        
        finally:
            await conn.close()
    
    def _calculate_linear_trend(self, prices):
        """计算线性趋势"""
        n = len(prices)
        if n < 2:
            return {"slope": 0, "r_squared": 0, "trend_direction": "stable"}
        
        x_values = list(range(n))
        sum_x = sum(x_values)
        sum_y = sum(prices)
        sum_xy = sum(x_values[i] * prices[i] for i in range(n))
        sum_x2 = sum(x * x for x in x_values)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # 计算R²
        y_mean = sum_y / n
        ss_tot = sum((prices[i] - y_mean) ** 2 for i in range(n))
        ss_res = sum((prices[i] - (slope * i + intercept)) ** 2 for i in range(n))
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        trend_direction = "increasing" if slope > 0.1 else "decreasing" if slope < -0.1 else "stable"
        
        return {
            "slope": slope,
            "intercept": intercept,
            "r_squared": max(0, min(1, r_squared)),
            "trend_direction": trend_direction
        }
    
    def _calculate_volatility(self, prices):
        """计算价格波动率"""
        if len(prices) < 2:
            return {"volatility": 0, "volatility_percentage": 0}
        
        mean_price = sum(prices) / len(prices)
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        volatility = variance ** 0.5
        volatility_percentage = volatility / mean_price if mean_price > 0 else 0
        
        return {
            "volatility": volatility,
            "volatility_percentage": volatility_percentage,
            "mean_price": mean_price
        }
    
    def _detect_price_anomalies(self, prices):
        """检测价格异常"""
        if len(prices) < 10:
            return {"anomaly_count": 0, "anomaly_indices": [], "threshold_used": 0}
        
        mean_price = sum(prices) / len(prices)
        std_price = (sum((p - mean_price)**2 for p in prices) / len(prices))**0.5
        threshold = 2 * std_price  # 2倍标准差作为阈值
        
        anomaly_indices = []
        for i, price in enumerate(prices):
            if abs(price - mean_price) > threshold:
                anomaly_indices.append(i)
        
        return {
            "anomaly_count": len(anomaly_indices),
            "anomaly_indices": anomaly_indices,
            "threshold_used": threshold
        }
