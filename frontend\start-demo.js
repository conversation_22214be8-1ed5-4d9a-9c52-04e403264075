#!/usr/bin/env node

/**
 * 前端应用演示启动脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🎉 Moniit 前端应用演示');
console.log('=' * 50);

// 检查Node.js版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ 需要 Node.js 16.0.0 或更高版本');
  console.error(`   当前版本: ${nodeVersion}`);
  process.exit(1);
}

console.log(`✅ Node.js 版本: ${nodeVersion}`);

// 检查package.json
const packagePath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packagePath)) {
  console.error('❌ 未找到 package.json 文件');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
console.log(`✅ 项目: ${packageJson.name} v${packageJson.version}`);

// 检查依赖
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 正在安装依赖...');
  console.log('   请运行: npm install');
  console.log('');
} else {
  console.log('✅ 依赖已安装');
}

// 显示功能特性
console.log('');
console.log('🎯 功能特性:');
console.log('  ✅ 用户认证和权限管理');
console.log('  ✅ 商品管理 (列表、搜索、编辑、导入导出)');
console.log('  ✅ 监控任务管理');
console.log('  ✅ 数据分析和图表展示');
console.log('  ✅ 供货商管理');
console.log('  ✅ 系统设置和用户管理');
console.log('  ✅ 响应式设计 (支持移动端)');

// 显示技术栈
console.log('');
console.log('🔧 技术栈:');
console.log('  • React 18 + TypeScript');
console.log('  • Ant Design 5 (UI组件库)');
console.log('  • Redux Toolkit (状态管理)');
console.log('  • React Router 6 (路由管理)');
console.log('  • Axios (HTTP客户端)');
console.log('  • ECharts (图表库)');

// 显示启动说明
console.log('');
console.log('🚀 启动说明:');
console.log('  1. 安装依赖: npm install');
console.log('  2. 启动开发服务器: npm start');
console.log('  3. 打开浏览器访问: http://localhost:3000');
console.log('');

// 显示演示账户
console.log('👤 演示账户:');
console.log('  管理员: admin / admin123');
console.log('  操作员: operator / operator123');
console.log('');

// 显示页面路由
console.log('📄 页面路由:');
console.log('  / - 仪表板');
console.log('  /products - 商品管理');
console.log('  /monitor - 监控管理');
console.log('  /analytics - 数据分析');
console.log('  /suppliers - 供货商管理');
console.log('  /system/settings - 系统设置');
console.log('  /system/users - 用户管理');
console.log('  /profile - 个人中心');
console.log('');

// 显示API配置说明
console.log('⚙️ API配置:');
console.log('  默认API地址: http://localhost:8000');
console.log('  可通过环境变量 REACT_APP_API_URL 修改');
console.log('  支持请求拦截、错误处理、令牌刷新');
console.log('');

// 显示开发命令
console.log('📝 开发命令:');
console.log('  npm start     - 启动开发服务器');
console.log('  npm run build - 构建生产版本');
console.log('  npm run lint  - 代码检查');
console.log('  npm test      - 运行测试');
console.log('');

console.log('🎊 前端应用已准备就绪！');
console.log('   请按照上述说明启动应用进行体验。');
console.log('=' * 50);
