"""
完整翻译服务系统演示

展示批量翻译优化系统和翻译配置监控的完整功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.translation import (
    TranslationEngine, ProviderManager, QualityAssessor, CacheManager,
    BatchProcessor, PriorityQueueManager, ProgressTracker,
    TemplateManager, PromptManager, MonitoringSystem
)
from app.services.translation.translation_engine import (
    TranslationRequest, LanguageCode, TextType
)
from app.services.translation.batch_processor import BatchItem, BatchPriority, BatchStatus
from app.services.translation.queue_manager import QueuePriority
from app.services.translation.template_manager import PlatformType, TemplateType
from app.services.translation.prompt_manager import PromptType, RuleType, RuleAction
from app.services.translation.monitoring_system import MetricType, AlertLevel, TimeRange
from app.services.translation.cache_manager import CacheType


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*80}")
    print(f"=== {title} ===")
    print()


async def demo_batch_processor():
    """演示批量处理器"""
    print_section("批量翻译处理器演示")
    
    # 创建翻译引擎
    provider_manager = ProviderManager()
    quality_assessor = QualityAssessor()
    cache_manager = CacheManager(CacheType.MEMORY)
    translation_engine = TranslationEngine(provider_manager, quality_assessor, cache_manager)
    
    # 创建批量处理器
    batch_processor = BatchProcessor(translation_engine)
    
    print("1. 批量处理器配置:")
    print(f"   最大并发作业数: {batch_processor.processor_config['max_concurrent_jobs']}")
    print(f"   每作业最大并发数: {batch_processor.processor_config['max_concurrent_per_job']}")
    print(f"   默认批量大小: {batch_processor.processor_config['default_batch_size']}")
    print(f"   自动重试: {batch_processor.processor_config['auto_retry_enabled']}")
    
    # 创建批量翻译项目
    print("\n2. 创建批量翻译项目:")
    batch_items = []
    
    # 商品标题翻译
    product_titles = [
        "iPhone 15 Pro Max 256GB Smartphone",
        "Samsung Galaxy S24 Ultra Mobile Phone",
        "MacBook Pro 16-inch Laptop Computer",
        "Dell XPS 13 Ultrabook Notebook",
        "Sony WH-1000XM5 Wireless Headphones"
    ]
    
    for i, title in enumerate(product_titles):
        item = BatchItem(
            item_id=f"product_{i+1}",
            text=title,
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.PRODUCT_TITLE,
            priority=3 if i < 2 else 2,  # 前两个高优先级
            metadata={"category": "electronics", "brand": title.split()[0]}
        )
        batch_items.append(item)
    
    print(f"   创建了 {len(batch_items)} 个翻译项目")
    for item in batch_items:
        print(f"     {item.item_id}: {item.text[:50]}... (优先级: {item.priority})")
    
    # 创建批量作业
    print("\n3. 创建批量翻译作业:")
    job_id = await batch_processor.create_batch_job(
        name="商品标题批量翻译",
        items=batch_items,
        priority=BatchPriority.HIGH,
        description="电子产品标题英文到中文翻译",
        max_concurrent=3,
        retry_attempts=2
    )
    
    print(f"   作业ID: {job_id}")
    print(f"   作业名称: 商品标题批量翻译")
    print(f"   项目数量: {len(batch_items)}")
    print(f"   作业优先级: {BatchPriority.HIGH.name}")
    
    # 启动批量作业
    print("\n4. 启动批量翻译作业:")
    success = await batch_processor.start_batch_job(job_id)
    print(f"   启动结果: {'成功' if success else '失败'}")
    
    if success:
        batch_job = batch_processor.get_batch_job(job_id)
        print(f"   作业状态: {batch_job.status.value}")
        print(f"   开始时间: {batch_job.started_at}")
        
        # 监控作业进度
        print("\n5. 监控作业进度:")
        for i in range(10):  # 最多等待10秒
            await asyncio.sleep(1)
            batch_job = batch_processor.get_batch_job(job_id)
            print(f"   进度: {batch_job.progress:.1f}% "
                  f"({batch_job.completed_items}/{batch_job.total_items})")
            
            if batch_job.status in [BatchStatus.COMPLETED, BatchStatus.FAILED]:
                break
        
        # 显示最终结果
        print("\n6. 批量翻译结果:")
        batch_job = batch_processor.get_batch_job(job_id)
        print(f"   最终状态: {batch_job.status.value}")
        print(f"   完成项目: {batch_job.completed_items}/{batch_job.total_items}")
        print(f"   失败项目: {batch_job.failed_items}")
        print(f"   总成本: ${batch_job.total_cost:.4f}")
        print(f"   总处理时间: {batch_job.total_processing_time:.2f}秒")
        print(f"   平均质量评分: {batch_job.average_quality_score:.2f}")
        
        # 显示翻译结果
        print("\n   翻译结果详情:")
        for item in batch_job.items:
            if item.translated_text:
                print(f"     {item.item_id}:")
                print(f"       原文: {item.text}")
                print(f"       译文: {item.translated_text}")
                print(f"       质量: {item.quality_score:.2f}")
                print(f"       耗时: {item.processing_time:.2f}秒")
    
    # 获取处理器统计
    print("\n7. 批量处理器统计:")
    stats = batch_processor.get_processor_statistics()
    print(f"   总作业数: {stats['total_jobs']}")
    print(f"   完成作业数: {stats['completed_jobs']}")
    print(f"   活跃作业数: {stats['active_jobs']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    print(f"   总处理项目数: {stats['total_items_processed']}")
    print(f"   总成本: ${stats['total_cost']:.4f}")
    print(f"   平均每作业项目数: {stats['average_items_per_job']:.1f}")
    
    return batch_processor


async def demo_queue_manager():
    """演示队列管理器"""
    print_section("优先级队列管理器演示")
    
    # 创建翻译引擎和队列管理器
    provider_manager = ProviderManager()
    quality_assessor = QualityAssessor()
    cache_manager = CacheManager(CacheType.MEMORY)
    translation_engine = TranslationEngine(provider_manager, quality_assessor, cache_manager)
    queue_manager = PriorityQueueManager(translation_engine)
    
    print("1. 队列管理器配置:")
    print(f"   默认队列: {queue_manager.global_config['default_queue']}")
    print(f"   自动创建队列: {queue_manager.global_config['auto_create_queues']}")
    print(f"   负载均衡: {queue_manager.global_config['load_balancing_enabled']}")
    print(f"   优先级老化: {queue_manager.global_config['priority_aging_enabled']}")
    
    # 创建多个队列
    print("\n2. 创建专用队列:")
    queue_manager.create_queue("urgent_queue")
    queue_manager.create_queue("normal_queue")
    queue_manager.create_queue("batch_queue")
    
    print("   创建的队列:")
    for queue_name in queue_manager.queues.keys():
        info = queue_manager.get_queue_info(queue_name)
        print(f"     {queue_name}: 状态={info['status']}, 工作器={info['workers']['active_workers']}")
    
    # 添加不同优先级的翻译请求
    print("\n3. 添加翻译请求到队列:")
    requests_data = [
        ("紧急订单翻译", QueuePriority.CRITICAL, "urgent_queue"),
        ("重要商品描述", QueuePriority.HIGH, "normal_queue"),
        ("普通标题翻译", QueuePriority.NORMAL, "normal_queue"),
        ("批量内容翻译", QueuePriority.LOW, "batch_queue"),
        ("系统消息翻译", QueuePriority.URGENT, "default")
    ]
    
    item_ids = []
    for text, priority, queue_name in requests_data:
        request = TranslationRequest(
            request_id=f"req_{len(item_ids)+1}",
            text=f"{text}: Sample text for translation",
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.GENERAL_TEXT,
            priority=priority.value
        )
        
        item_id = await queue_manager.enqueue(
            request=request,
            priority=priority,
            queue_name=queue_name
        )
        item_ids.append(item_id)
        print(f"   添加请求: {text} -> {queue_name} (优先级: {priority.name})")
    
    # 监控队列状态
    print("\n4. 队列状态监控:")
    await asyncio.sleep(2)  # 等待处理
    
    all_info = queue_manager.get_all_queues_info()
    print(f"   全局统计:")
    print(f"     总队列数: {all_info['global_stats']['total_queues']}")
    print(f"     活跃队列数: {all_info['global_stats']['active_queues']}")
    print(f"     待处理项目: {all_info['global_stats']['total_pending_items']}")
    print(f"     处理中项目: {all_info['global_stats']['total_processing_items']}")
    print(f"     已完成项目: {all_info['global_stats']['total_completed_items']}")
    print(f"     全局成功率: {all_info['global_stats']['global_success_rate']:.1f}%")
    
    print(f"\n   各队列详情:")
    for queue_name, queue_info in all_info['queues'].items():
        stats = queue_info['stats']
        print(f"     {queue_name}:")
        print(f"       状态: {queue_info['status']}")
        print(f"       总项目: {stats['total_items']}")
        print(f"       待处理: {stats['pending_items']}")
        print(f"       已完成: {stats['completed_items']}")
        print(f"       成功率: {stats['success_rate']:.1f}%")
        print(f"       平均处理时间: {stats['average_processing_time']:.2f}秒")
    
    return queue_manager


async def demo_template_manager():
    """演示模板管理器"""
    print_section("平台翻译模板管理器演示")
    
    template_manager = TemplateManager()
    
    print("1. 模板管理器配置:")
    print(f"   自动保存: {template_manager.config['auto_save']}")
    print(f"   备份启用: {template_manager.config['backup_enabled']}")
    print(f"   最大模板数: {template_manager.config['max_templates']}")
    print(f"   模板验证: {template_manager.config['template_validation']}")
    
    # 显示默认模板
    print("\n2. 默认模板:")
    for template_id, template in template_manager.templates.items():
        print(f"   {template.name}:")
        print(f"     ID: {template_id}")
        print(f"     平台: {template.platform.value}")
        print(f"     类型: {template.template_type.value}")
        print(f"     语言: {template.source_lang} -> {template.target_lang}")
        print(f"     使用次数: {template.usage_count}")
    
    # 创建自定义模板
    print("\n3. 创建自定义模板:")
    custom_template_id = template_manager.create_template(
        name="eBay商品描述模板",
        platform=PlatformType.EBAY,
        template_type=TemplateType.PRODUCT_DESCRIPTION,
        source_lang="en",
        target_lang="zh",
        system_prompt="你是eBay平台的专业商品描述翻译专家。",
        user_prompt_template="请将以下eBay商品描述翻译成中文：\n\n{text}\n\n要求：\n1. 保持HTML格式\n2. 突出卖点\n3. 符合eBay规范",
        description="专用于eBay平台的商品描述翻译模板",
        min_quality_score=7.5
    )
    
    print(f"   创建模板ID: {custom_template_id}")
    
    # 查找和应用模板
    print("\n4. 模板查找和应用:")
    
    # 查找Amazon商品标题模板
    amazon_templates = template_manager.find_templates(
        platform=PlatformType.AMAZON,
        template_type=TemplateType.PRODUCT_TITLE,
        source_lang="en",
        target_lang="zh"
    )
    
    print(f"   找到Amazon商品标题模板: {len(amazon_templates)}个")
    
    if amazon_templates:
        template = amazon_templates[0]
        test_text = "Apple iPhone 15 Pro Max 256GB - Natural Titanium"
        
        print(f"   测试文本: {test_text}")
        
        # 应用模板
        result = template_manager.apply_template(template, test_text)
        
        print(f"   应用结果:")
        print(f"     模板ID: {result['template_id']}")
        print(f"     原文: {result['original_text']}")
        print(f"     处理后: {result['processed_text']}")
        print(f"     系统提示词: {result['system_prompt']}")
        print(f"     用户提示词: {result['user_prompt'][:100]}...")
        print(f"     应用规则: {len(result.get('applied_rules', []))}个")
    
    # 获取统计信息
    print("\n5. 模板统计信息:")
    stats = template_manager.get_statistics()
    print(f"   总模板数: {stats['basic_stats']['total_templates']}")
    print(f"   活跃模板数: {stats['basic_stats']['active_templates']}")
    print(f"   总使用次数: {stats['basic_stats']['total_usage']}")
    print(f"   平台数量: {stats['basic_stats']['platforms_count']}")
    print(f"   模板类型数量: {stats['basic_stats']['template_types_count']}")
    
    print(f"\n   平台分布:")
    for platform, count in stats['platform_distribution'].items():
        print(f"     {platform}: {count}个")
    
    print(f"\n   类型分布:")
    for template_type, count in stats['type_distribution'].items():
        print(f"     {template_type}: {count}个")
    
    return template_manager


async def demo_prompt_manager():
    """演示提示词管理器"""
    print_section("提示词和规则管理器演示")
    
    prompt_manager = PromptManager()
    
    print("1. 提示词管理器配置:")
    print(f"   自动保存: {prompt_manager.config['auto_save']}")
    print(f"   最大提示词数: {prompt_manager.config['max_prompts']}")
    print(f"   最大规则数: {prompt_manager.config['max_rules']}")
    print(f"   最大术语数: {prompt_manager.config['max_terminology']}")
    
    # 显示默认数据
    print("\n2. 默认数据概览:")
    print(f"   提示词数量: {len(prompt_manager.prompts)}")
    print(f"   规则数量: {len(prompt_manager.rules)}")
    print(f"   术语数量: {len(prompt_manager.terminology)}")
    
    # 创建自定义提示词
    print("\n3. 创建自定义提示词:")
    prompt_id = prompt_manager.create_prompt(
        name="电商产品翻译提示词",
        prompt_type=PromptType.USER,
        content="请将以下{platform}平台的{text_type}从{source_lang}翻译成{target_lang}：\n\n{text}\n\n要求：\n1. 保持专业术语准确\n2. 符合{platform}平台规范\n3. 突出产品特色\n4. 长度控制在{max_length}字符以内",
        description="电商平台产品信息翻译专用提示词",
        tags=["电商", "产品", "翻译"]
    )
    
    print(f"   创建提示词ID: {prompt_id}")
    
    # 渲染提示词
    rendered = prompt_manager.render_prompt(prompt_id, {
        "platform": "Amazon",
        "text_type": "商品标题",
        "source_lang": "英文",
        "target_lang": "中文",
        "text": "Apple iPhone 15 Pro Max",
        "max_length": "200"
    })
    
    print(f"   渲染结果:")
    print(f"     {rendered}")
    
    # 创建翻译规则
    print("\n4. 创建翻译规则:")
    
    # 品牌名称保护规则
    brand_rule_id = prompt_manager.create_rule(
        name="品牌名称保护",
        rule_type=RuleType.PREPROCESSING,
        action=RuleAction.VALIDATE,
        pattern=r'\b(Apple|Samsung|Huawei|Xiaomi|OPPO|Vivo)\b',
        use_regex=True,
        description="保护知名品牌名称不被错误翻译",
        tags=["品牌", "保护"]
    )
    
    # 数字格式规则
    number_rule_id = prompt_manager.create_rule(
        name="数字格式统一",
        rule_type=RuleType.POSTPROCESSING,
        action=RuleAction.REPLACE,
        pattern=r'(\d+)\s*GB',
        replacement=r'\1GB',
        use_regex=True,
        description="统一存储容量格式",
        tags=["格式", "数字"]
    )
    
    print(f"   创建规则: 品牌名称保护 (ID: {brand_rule_id})")
    print(f"   创建规则: 数字格式统一 (ID: {number_rule_id})")
    
    # 应用规则
    print("\n5. 应用翻译规则:")
    test_text = "Apple iPhone 15 Pro with 256 GB storage"
    
    processed_text, applied_rules = prompt_manager.apply_rules(
        test_text, RuleType.PREPROCESSING
    )
    
    print(f"   原文: {test_text}")
    print(f"   处理后: {processed_text}")
    print(f"   应用规则: {len(applied_rules)}个")
    
    # 添加术语
    print("\n6. 术语管理:")
    
    # 添加电子产品术语
    tech_terms = [
        ("smartphone", "智能手机", "electronics"),
        ("processor", "处理器", "electronics"),
        ("battery", "电池", "electronics"),
        ("display", "显示屏", "electronics"),
        ("camera", "摄像头", "electronics")
    ]
    
    for source, target, domain in tech_terms:
        term_id = prompt_manager.add_terminology(
            source_term=source,
            target_term=target,
            source_lang="en",
            target_lang="zh",
            domain=domain
        )
        print(f"   添加术语: {source} -> {target} (领域: {domain})")
    
    # 应用术语翻译
    test_text = "This smartphone has a powerful processor and long-lasting battery"
    processed_text, applied_terms = prompt_manager.apply_terminology(
        test_text, "en", "zh", "electronics"
    )
    
    print(f"\n   术语翻译测试:")
    print(f"     原文: {test_text}")
    print(f"     处理后: {processed_text}")
    print(f"     应用术语: {len(applied_terms)}个")
    
    # 获取统计信息
    print("\n7. 提示词管理器统计:")
    stats = prompt_manager.get_statistics()
    print(f"   基本统计:")
    print(f"     总提示词: {stats['basic_stats']['total_prompts']}")
    print(f"     活跃提示词: {stats['basic_stats']['active_prompts']}")
    print(f"     总规则: {stats['basic_stats']['total_rules']}")
    print(f"     活跃规则: {stats['basic_stats']['active_rules']}")
    print(f"     总术语: {stats['basic_stats']['total_terminology']}")
    print(f"     已验证术语: {stats['basic_stats']['verified_terminology']}")
    print(f"     总使用次数: {stats['basic_stats']['total_usage']}")
    
    return prompt_manager


async def demo_monitoring_system():
    """演示监控系统"""
    print_section("翻译统计和监控系统演示")
    
    monitoring_system = MonitoringSystem()
    
    print("1. 监控系统配置:")
    print(f"   数据保留天数: {monitoring_system.config['data_retention_days']}")
    print(f"   指标收集间隔: {monitoring_system.config['metric_collection_interval']}秒")
    print(f"   告警检查间隔: {monitoring_system.config['alert_check_interval']}秒")
    print(f"   自动报告: {monitoring_system.config['auto_report_enabled']}")
    print(f"   实时告警: {monitoring_system.config['enable_real_time_alerts']}")
    
    # 模拟记录指标数据
    print("\n2. 记录翻译指标:")
    
    # 翻译量指标
    monitoring_system.record_metric(MetricType.VOLUME, "translations_count", 150, "count")
    monitoring_system.record_metric(MetricType.VOLUME, "characters_translated", 12500, "chars")
    
    # 成本指标
    monitoring_system.record_metric(MetricType.COST, "translation_cost", 25.75, "USD")
    monitoring_system.record_metric(MetricType.COST, "provider_cost_openai", 15.50, "USD")
    monitoring_system.record_metric(MetricType.COST, "provider_cost_claude", 10.25, "USD")
    
    # 质量指标
    for score in [8.5, 9.2, 7.8, 8.9, 9.1, 8.3, 7.9, 8.7]:
        monitoring_system.record_metric(MetricType.QUALITY, "quality_score", score, "score")
    
    # 性能指标
    for time_val in [2.3, 1.8, 3.1, 2.7, 2.1, 2.9, 2.4, 2.6]:
        monitoring_system.record_metric(MetricType.PERFORMANCE, "processing_time", time_val, "seconds")
    
    # 错误指标
    monitoring_system.record_metric(MetricType.ERROR, "error_count", 3, "count")
    
    print("   记录了以下指标:")
    print(f"     翻译量: 150次翻译, 12,500字符")
    print(f"     成本: $25.75 (OpenAI: $15.50, Claude: $10.25)")
    print(f"     质量: 8个质量评分")
    print(f"     性能: 8个处理时间记录")
    print(f"     错误: 3个错误")
    
    # 获取聚合指标
    print("\n3. 聚合指标分析:")
    
    volume_stats = monitoring_system.get_aggregated_metrics(
        MetricType.VOLUME, "translations_count", TimeRange.DAY, "sum"
    )
    cost_stats = monitoring_system.get_aggregated_metrics(
        MetricType.COST, "translation_cost", TimeRange.DAY, "sum"
    )
    quality_stats = monitoring_system.get_aggregated_metrics(
        MetricType.QUALITY, "quality_score", TimeRange.DAY, "avg"
    )
    performance_stats = monitoring_system.get_aggregated_metrics(
        MetricType.PERFORMANCE, "processing_time", TimeRange.DAY, "avg"
    )
    
    print(f"   今日翻译总量: {volume_stats['value']:.0f}次")
    print(f"   今日总成本: ${cost_stats['value']:.2f}")
    print(f"   平均质量评分: {quality_stats['value']:.2f}")
    print(f"   平均处理时间: {performance_stats['value']:.2f}秒")
    print(f"   每次翻译成本: ${cost_stats['value']/volume_stats['value']:.4f}")
    print(f"   每字符成本: ${cost_stats['value']/12500:.6f}")
    
    # 创建告警
    print("\n4. 告警管理:")
    
    # 成本告警
    cost_alert_id = monitoring_system.create_alert(
        level=AlertLevel.WARNING,
        title="翻译成本偏高",
        message=f"今日翻译成本 ${cost_stats['value']:.2f} 接近预算上限",
        metric_type=MetricType.COST,
        threshold_value=30.0,
        current_value=cost_stats['value'],
        tags={"category": "cost", "severity": "medium"}
    )
    
    # 质量告警
    if quality_stats['value'] < 8.0:
        quality_alert_id = monitoring_system.create_alert(
            level=AlertLevel.INFO,
            title="翻译质量提醒",
            message=f"平均质量评分 {quality_stats['value']:.2f} 可以进一步优化",
            metric_type=MetricType.QUALITY,
            threshold_value=8.0,
            current_value=quality_stats['value'],
            tags={"category": "quality", "severity": "low"}
        )
    
    active_alerts = monitoring_system.get_active_alerts()
    print(f"   创建告警: {len(active_alerts)}个")
    
    for alert in active_alerts:
        print(f"     {alert.level.value.upper()}: {alert.title}")
        print(f"       消息: {alert.message}")
        print(f"       阈值: {alert.threshold_value}, 当前值: {alert.current_value}")
        print(f"       时间: {alert.triggered_at.strftime('%H:%M:%S')}")
    
    # 生成报告
    print("\n5. 生成翻译报告:")
    
    report_id = monitoring_system.generate_report("daily", TimeRange.DAY, "翻译服务日报")
    
    if report_id:
        report = monitoring_system.reports[report_id]
        print(f"   报告ID: {report_id}")
        print(f"   报告标题: {report.title}")
        print(f"   时间范围: {report.time_range.value}")
        print(f"   生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示报告摘要
        summary = report.data.get("summary", {})
        print(f"\n   报告摘要:")
        print(f"     时间范围: {summary.get('time_range', 'N/A')}")
        print(f"     总翻译数: {summary.get('total_translations', 0):.0f}")
        print(f"     总成本: ${summary.get('total_cost', 0):.2f}")
        print(f"     平均质量: {summary.get('avg_quality_score', 0):.2f}")
        print(f"     平均处理时间: {summary.get('avg_processing_time', 0):.2f}秒")
        print(f"     错误率: {summary.get('error_rate', 0):.2f}%")
        
        # 显示建议
        recommendations = report.data.get("recommendations", [])
        if recommendations:
            print(f"\n   系统建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"     {i}. {rec}")
    
    # 获取仪表板数据
    print("\n6. 仪表板数据:")
    dashboard_data = monitoring_system.get_dashboard_data()
    
    if dashboard_data:
        overview = dashboard_data.get("overview", {})
        print(f"   概览:")
        print(f"     今日翻译数: {overview.get('total_translations_today', 0):.0f}")
        print(f"     今日成本: ${overview.get('total_cost_today', 0):.2f}")
        print(f"     今日平均质量: {overview.get('avg_quality_today', 0):.2f}")
        print(f"     活跃告警数: {overview.get('active_alerts', 0)}")
        
        system_health = dashboard_data.get("system_health", {})
        print(f"\n   系统健康:")
        print(f"     错误率: {system_health.get('error_rate', 0):.2f}")
        print(f"     平均处理时间: {system_health.get('avg_processing_time', 0):.2f}秒")
    
    return monitoring_system


async def main():
    """主演示函数"""
    print("🚀 完整翻译服务系统演示")
    print("="*80)
    print("展示批量翻译优化系统和翻译配置监控的完整功能")
    
    # 演示批量处理器
    batch_processor = await demo_batch_processor()
    
    # 演示队列管理器
    queue_manager = await demo_queue_manager()
    
    # 演示模板管理器
    template_manager = await demo_template_manager()
    
    # 演示提示词管理器
    prompt_manager = await demo_prompt_manager()
    
    # 演示监控系统
    monitoring_system = await demo_monitoring_system()
    
    # 系统总结
    print_section("完整翻译服务系统演示总结")
    
    print("🎯 核心功能展示:")
    print("✅ 批量翻译处理器：智能批量翻译，支持优先级管理和进度跟踪")
    print("✅ 优先级队列管理器：多队列管理，自动负载均衡和优先级老化")
    print("✅ 进度跟踪器：实时进度监控，任务状态管理和统计分析")
    print("✅ 平台模板管理器：多平台翻译模板，规则引擎和约束管理")
    print("✅ 提示词管理器：智能提示词生成，翻译规则和术语管理")
    print("✅ 监控系统：全面指标监控，智能告警和自动报告")
    
    print("\n📊 演示统计:")
    batch_stats = batch_processor.get_processor_statistics()
    queue_info = queue_manager.get_all_queues_info()
    template_stats = template_manager.get_statistics()
    prompt_stats = prompt_manager.get_statistics()
    
    print(f"- 批量处理器：{batch_stats['total_jobs']}个作业，{batch_stats['total_items_processed']}个项目")
    print(f"- 队列管理器：{queue_info['global_stats']['total_queues']}个队列，{queue_info['global_stats']['total_completed_items']}个完成项目")
    print(f"- 模板管理器：{template_stats['basic_stats']['total_templates']}个模板，{template_stats['basic_stats']['total_usage']}次使用")
    print(f"- 提示词管理器：{prompt_stats['basic_stats']['total_prompts']}个提示词，{prompt_stats['basic_stats']['total_rules']}个规则，{prompt_stats['basic_stats']['total_terminology']}个术语")
    print(f"- 监控系统：{len(monitoring_system.metrics)}个指标，{len(monitoring_system.alerts)}个告警，{len(monitoring_system.reports)}个报告")
    
    print("\n🔧 技术特性:")
    print("- 异步并发处理：支持高并发翻译请求，智能资源调度")
    print("- 智能优先级管理：多级优先级队列，自动优先级老化")
    print("- 实时进度跟踪：任务状态监控，ETA预测，失败重试")
    print("- 平台专用模板：针对不同平台的专业翻译模板")
    print("- 智能规则引擎：预处理和后处理规则，术语管理")
    print("- 全面监控告警：多维度指标监控，智能告警，自动报告")
    
    print("\n🎉 演示完成！")
    print("完整翻译服务系统已成功展示所有核心功能。")
    print("系统具备企业级的翻译处理能力，支持大规模批量翻译、")
    print("智能质量管理、成本控制和全面监控。")
    
    # 停止后台任务
    batch_processor.stop_progress_updater()
    queue_manager.stop_monitoring()
    monitoring_system.stop_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
