"""
平台配置管理服务测试

测试配置CRUD操作、模板管理和验证功能
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

from app.services.task_middleware.config_service import (
    PlatformConfigService, ConfigTemplate, ConfigValidationResult
)
from app.services.task_middleware.config_manager import Platform, ProductType


class TestPlatformConfigService:
    """平台配置管理服务测试"""
    
    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def config_service(self, temp_config_dir):
        """创建配置服务实例"""
        template_dir = Path(temp_config_dir) / "templates"
        return PlatformConfigService(temp_config_dir, str(template_dir))
    
    @pytest.fixture
    def sample_config_data(self):
        """示例配置数据"""
        return {
            "platform": "1688",
            "base_config": {
                "name": "测试1688平台",
                "base_url": "https://www.1688.com",
                "selectors": {
                    "title": ".test-title",
                    "price": ".test-price",
                    "stock": ".test-stock"
                },
                "rate_limit": {
                    "requests_per_minute": 30,
                    "concurrent_requests": 3,
                    "delay_between_requests": 2.0
                },
                "headers": {
                    "User-Agent": "Test Agent"
                },
                "javascript_required": True
            },
            "product_types": {
                "competitor": {
                    "additional_selectors": {
                        "brand": ".test-brand"
                    },
                    "custom_query_template": "测试竞品查询",
                    "priority_boost": 2,
                    "monitoring_frequency": 1800
                },
                "supplier": {
                    "additional_selectors": {
                        "min_order": ".test-min-order"
                    },
                    "custom_query_template": "测试供货商查询",
                    "priority_boost": 1,
                    "monitoring_frequency": 3600
                }
            },
            "enabled": True
        }
    
    @pytest.fixture
    def sample_template(self):
        """示例配置模板"""
        return ConfigTemplate(
            name="test_template",
            description="测试模板",
            platform=Platform.ALIBABA_1688,
            template_data={
                "platform": "1688",
                "base_config": {
                    "name": "模板平台",
                    "base_url": "https://template.com",
                    "selectors": {
                        "title": ".template-title",
                        "price": ".template-price"
                    },
                    "rate_limit": {
                        "requests_per_minute": 20
                    }
                },
                "product_types": {
                    "other": {
                        "monitoring_frequency": 7200
                    }
                }
            }
        )
    
    # CRUD操作测试
    @pytest.mark.asyncio
    async def test_create_platform_config(self, config_service, sample_config_data):
        """测试创建平台配置"""
        success = await config_service.create_platform_config(
            Platform.ALIBABA_1688, 
            sample_config_data
        )
        
        assert success is True
        
        # 验证配置已创建
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        assert config is not None
        assert config['base_config']['name'] == "测试1688平台"
    
    @pytest.mark.asyncio
    async def test_get_platform_config(self, config_service, sample_config_data):
        """测试获取平台配置"""
        # 先创建配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, sample_config_data)
        
        # 获取配置
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        
        assert config is not None
        assert config['platform'] == "1688"
        assert config['base_config']['name'] == "测试1688平台"
        assert 'competitor' in config['product_types']
        assert 'supplier' in config['product_types']
    
    @pytest.mark.asyncio
    async def test_update_platform_config(self, config_service, sample_config_data):
        """测试更新平台配置"""
        # 先创建配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, sample_config_data)
        
        # 更新配置
        updates = {
            "base_config": {
                "name": "更新后的1688平台",
                "rate_limit": {
                    "requests_per_minute": 50
                }
            }
        }
        
        success = await config_service.update_platform_config(Platform.ALIBABA_1688, updates)
        assert success is True
        
        # 验证更新
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        assert config['base_config']['name'] == "更新后的1688平台"
        assert config['base_config']['rate_limit']['requests_per_minute'] == 50
        # 验证其他字段未被覆盖
        assert config['base_config']['base_url'] == "https://www.1688.com"
    
    @pytest.mark.asyncio
    async def test_delete_platform_config(self, config_service, sample_config_data):
        """测试删除平台配置"""
        # 先创建配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, sample_config_data)
        
        # 验证配置存在
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        assert config is not None
        
        # 删除配置
        success = await config_service.delete_platform_config(Platform.ALIBABA_1688)
        assert success is True
        
        # 验证配置已删除
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        assert config is None
    
    @pytest.mark.asyncio
    async def test_list_platform_configs(self, config_service, sample_config_data):
        """测试列出平台配置"""
        # 创建多个配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, sample_config_data)
        
        taobao_config = sample_config_data.copy()
        taobao_config['platform'] = 'taobao'
        taobao_config['base_config']['name'] = '测试淘宝平台'
        await config_service.create_platform_config(Platform.TAOBAO, taobao_config)
        
        # 列出配置
        configs = await config_service.list_platform_configs()
        
        assert len(configs) >= 2
        platform_names = [config['platform'] for config in configs]
        assert '1688' in platform_names
        assert 'taobao' in platform_names
    
    # 模板管理测试
    @pytest.mark.asyncio
    async def test_create_template(self, config_service, sample_template):
        """测试创建配置模板"""
        success = await config_service.create_template(sample_template)
        assert success is True
        
        # 验证模板已创建
        template = config_service.get_template("test_template")
        assert template is not None
        assert template.name == "test_template"
        assert template.description == "测试模板"
    
    def test_get_template(self, config_service, sample_template):
        """测试获取配置模板"""
        # 手动添加模板
        config_service._templates[sample_template.name] = sample_template
        
        # 获取模板
        template = config_service.get_template("test_template")
        
        assert template is not None
        assert template.name == "test_template"
        assert template.platform == Platform.ALIBABA_1688
    
    def test_list_templates(self, config_service, sample_template):
        """测试列出配置模板"""
        # 添加模板
        config_service._templates[sample_template.name] = sample_template
        
        # 列出模板
        templates = config_service.list_templates()
        
        assert len(templates) >= 1
        template_names = [t['name'] for t in templates]
        assert "test_template" in template_names
    
    @pytest.mark.asyncio
    async def test_create_config_with_template(self, config_service, sample_template, sample_config_data):
        """测试使用模板创建配置"""
        # 先创建模板
        await config_service.create_template(sample_template)
        
        # 使用模板创建配置
        success = await config_service.create_platform_config(
            Platform.ALIBABA_1688,
            sample_config_data,
            template_name="test_template"
        )
        
        assert success is True
        
        # 验证配置合并了模板数据
        config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        assert config is not None
        # 用户数据应该覆盖模板数据
        assert config['base_config']['name'] == "测试1688平台"  # 用户数据
    
    # 配置验证测试
    @pytest.mark.asyncio
    async def test_validate_config_success(self, config_service, sample_config_data):
        """测试配置验证成功"""
        result = await config_service.validate_config(sample_config_data)
        
        assert isinstance(result, ConfigValidationResult)
        assert result.is_valid is True
        assert result.score > 0.8
        assert len(result.errors) == 0
    
    @pytest.mark.asyncio
    async def test_validate_config_missing_fields(self, config_service):
        """测试配置验证缺少字段"""
        invalid_config = {
            "platform": "1688"
            # 缺少 base_config 和 product_types
        }
        
        result = await config_service.validate_config(invalid_config)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert result.score < 0.5
        assert any("缺少必需字段" in error for error in result.errors)
    
    @pytest.mark.asyncio
    async def test_validate_config_invalid_platform(self, config_service, sample_config_data):
        """测试配置验证无效平台"""
        invalid_config = sample_config_data.copy()
        invalid_config['platform'] = 'invalid_platform'
        
        result = await config_service.validate_config(invalid_config)
        
        assert result.is_valid is False
        assert any("无效的平台类型" in error for error in result.errors)
    
    @pytest.mark.asyncio
    async def test_validate_config_warnings(self, config_service, sample_config_data):
        """测试配置验证警告"""
        config_with_warnings = sample_config_data.copy()
        config_with_warnings['base_config']['rate_limit']['requests_per_minute'] = 150  # 过高
        
        result = await config_service.validate_config(config_with_warnings)
        
        assert result.is_valid is True  # 仍然有效，但有警告
        assert len(result.warnings) > 0
        assert any("过高" in warning for warning in result.warnings)
    
    # 配置测试功能测试
    @pytest.mark.asyncio
    async def test_test_config(self, config_service, sample_config_data):
        """测试配置测试功能"""
        # 先创建配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, sample_config_data)
        
        # 测试配置
        test_result = await config_service.test_config(
            Platform.ALIBABA_1688,
            test_urls=["https://detail.1688.com/offer/123.html"]
        )
        
        assert test_result['success'] is True
        assert 'tests' in test_result
        assert len(test_result['tests']) > 0
        
        # 检查特定测试
        test_names = [test['test'] for test in test_result['tests']]
        assert 'config_validation' in test_names
        assert 'query_build_competitor' in test_names
        assert 'monitoring_frequency' in test_names
    
    @pytest.mark.asyncio
    async def test_test_nonexistent_config(self, config_service):
        """测试不存在的配置"""
        test_result = await config_service.test_config(Platform.JD)
        
        assert test_result['success'] is False
        assert 'error' in test_result
        assert '平台配置不存在' in test_result['error']


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
