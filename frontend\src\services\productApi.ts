/**
 * 商品相关API
 */

import { api, uploadFile, downloadFile } from './api';
import { Product, ProductForm, PaginatedResponse, SearchParams, ApiResponse } from '../types';

// 商品查询参数
interface ProductQueryParams {
  page?: number;
  page_size?: number;
  search?: SearchParams;
}

// 批量操作参数
interface BatchOperationParams {
  product_ids: string[];
  operation: 'delete' | 'activate' | 'deactivate';
}

// 导出参数
interface ExportParams {
  format: 'excel' | 'csv';
  filters?: SearchParams;
}

// 导入结果
interface ImportResult {
  success_count: number;
  error_count: number;
  errors: Array<{
    row: number;
    message: string;
  }>;
}

export const productApi = {
  // 获取商品列表
  getProducts: (params?: ProductQueryParams): Promise<ApiResponse<PaginatedResponse<Product>>> => {
    // 转换分页参数：page/page_size -> skip/limit
    const backendParams: any = {};
    if (params?.page && params?.page_size) {
      backendParams.skip = (params.page - 1) * params.page_size;
      backendParams.limit = params.page_size;
    }
    // 添加其他查询参数
    if (params?.search) {
      Object.assign(backendParams, params.search);
    }

    return api.get('/api/v1/products/', { params: backendParams });
  },

  // 获取商品详情
  getProductById: (productId: string): Promise<ApiResponse<Product>> => {
    return api.get(`/api/v1/products/${productId}`);
  },

  // 创建商品
  createProduct: (productData: ProductForm): Promise<ApiResponse<Product>> => {
    return api.post('/api/v1/products/', productData);
  },

  // 更新商品
  updateProduct: (productId: string, productData: Partial<ProductForm>): Promise<ApiResponse<Product>> => {
    return api.put(`/api/v1/products/${productId}`, productData);
  },

  // 删除商品
  deleteProduct: (productId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/api/v1/products/${productId}`);
  },

  // 批量删除商品
  batchDeleteProducts: (productIds: string[]): Promise<ApiResponse<null>> => {
    return api.post('/api/v1/products/batch-delete', { product_ids: productIds });
  },

  // 批量操作商品
  batchOperation: (params: BatchOperationParams): Promise<ApiResponse<null>> => {
    return api.post('/api/v1/products/batch-operation', params);
  },

  // 激活/停用商品
  toggleProductStatus: (productId: string, isActive: boolean): Promise<ApiResponse<Product>> => {
    return api.patch(`/api/v1/products/${productId}/status`, { is_active: isActive });
  },

  // 获取商品分类列表
  getCategories: (): Promise<ApiResponse<string[]>> => {
    return api.get('/api/v1/products/categories');
  },

  // 获取商品品牌列表
  getBrands: (): Promise<ApiResponse<string[]>> => {
    return api.get('/api/v1/products/brands');
  },

  // 搜索商品
  searchProducts: (keyword: string, filters?: SearchParams): Promise<ApiResponse<Product[]>> => {
    return api.get('/api/v1/products/search', {
      params: { keyword, ...filters }
    });
  },

  // 获取商品价格历史
  getProductPriceHistory: (productId: string, days?: number): Promise<ApiResponse<any[]>> => {
    return api.get(`/api/v1/products/${productId}/price-history`, {
      params: { days }
    });
  },

  // 获取商品统计信息
  getProductStats: (productId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/v1/products/${productId}/stats`);
  },

  // 导入商品
  importProducts: (file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<ImportResult>> => {
    return uploadFile('/api/v1/products/import', file, onProgress);
  },

  // 导出商品
  exportProducts: (params: ExportParams): Promise<void> => {
    const queryParams = new URLSearchParams();
    queryParams.append('format', params.format);
    
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });
    }

    const filename = `products_${new Date().toISOString().split('T')[0]}.${params.format}`;
    return downloadFile(`/api/v1/products/export?${queryParams.toString()}`, filename);
  },

  // 下载导入模板
  downloadImportTemplate: (format: 'excel' | 'csv' = 'excel'): Promise<void> => {
    const filename = `product_import_template.${format}`;
    return downloadFile(`/api/v1/products/import-template?format=${format}`, filename);
  },

  // 验证商品数据
  validateProduct: (productData: ProductForm): Promise<ApiResponse<{ valid: boolean; errors: string[] }>> => {
    return api.post('/api/v1/products/validate', productData);
  },

  // 检查商品名称是否重复
  checkProductName: (name: string, excludeId?: string): Promise<ApiResponse<{ exists: boolean }>> => {
    return api.get('/api/v1/products/check-name', {
      params: { name, exclude_id: excludeId }
    });
  },

  // 获取推荐商品
  getRecommendedProducts: (productId: string, limit?: number): Promise<ApiResponse<Product[]>> => {
    return api.get(`/api/v1/products/${productId}/recommendations`, {
      params: { limit }
    });
  },

  // 获取热门商品
  getPopularProducts: (limit?: number): Promise<ApiResponse<Product[]>> => {
    return api.get('/api/v1/products/popular', {
      params: { limit }
    });
  },

  // 获取最新商品
  getLatestProducts: (limit?: number): Promise<ApiResponse<Product[]>> => {
    return api.get('/api/v1/products/latest', {
      params: { limit }
    });
  },
};
