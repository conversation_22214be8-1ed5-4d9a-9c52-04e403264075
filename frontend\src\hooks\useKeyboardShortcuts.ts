/**
 * 键盘快捷键Hook
 */

import { useEffect, useCallback } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  action: () => void;
  description?: string;
  preventDefault?: boolean;
}

export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const matchingShortcut = shortcuts.find(shortcut => {
      const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatch = !!shortcut.ctrl === event.ctrlKey;
      const altMatch = !!shortcut.alt === event.altKey;
      const shiftMatch = !!shortcut.shift === event.shiftKey;
      const metaMatch = !!shortcut.meta === event.metaKey;

      return keyMatch && ctrlMatch && altMatch && shiftMatch && metaMatch;
    });

    if (matchingShortcut) {
      if (matchingShortcut.preventDefault !== false) {
        event.preventDefault();
      }
      matchingShortcut.action();
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};

// 常用快捷键配置
export const commonShortcuts = {
  // 搜索
  search: { key: 'f', ctrl: true, description: 'Ctrl+F 搜索' },
  // 刷新
  refresh: { key: 'r', ctrl: true, description: 'Ctrl+R 刷新' },
  // 新建
  create: { key: 'n', ctrl: true, description: 'Ctrl+N 新建' },
  // 保存
  save: { key: 's', ctrl: true, description: 'Ctrl+S 保存' },
  // 删除
  delete: { key: 'Delete', description: 'Delete 删除' },
  // 全选
  selectAll: { key: 'a', ctrl: true, description: 'Ctrl+A 全选' },
  // 复制
  copy: { key: 'c', ctrl: true, description: 'Ctrl+C 复制' },
  // 粘贴
  paste: { key: 'v', ctrl: true, description: 'Ctrl+V 粘贴' },
  // 撤销
  undo: { key: 'z', ctrl: true, description: 'Ctrl+Z 撤销' },
  // 重做
  redo: { key: 'y', ctrl: true, description: 'Ctrl+Y 重做' },
  // 帮助
  help: { key: 'F1', description: 'F1 帮助' },
  // ESC
  escape: { key: 'Escape', description: 'ESC 取消' },
  // 回车
  enter: { key: 'Enter', description: 'Enter 确认' },
};

// 快捷键帮助组件数据
export const getShortcutHelp = (shortcuts: KeyboardShortcut[]) => {
  return shortcuts
    .filter(shortcut => shortcut.description)
    .map(shortcut => {
      const keys = [];
      if (shortcut.ctrl) keys.push('Ctrl');
      if (shortcut.alt) keys.push('Alt');
      if (shortcut.shift) keys.push('Shift');
      if (shortcut.meta) keys.push('Cmd');
      keys.push(shortcut.key);
      
      return {
        keys: keys.join(' + '),
        description: shortcut.description,
      };
    });
};
