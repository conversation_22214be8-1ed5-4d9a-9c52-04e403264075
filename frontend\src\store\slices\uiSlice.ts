/**
 * UI状态管理
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 状态类型
interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  loading: {
    global: boolean;
    [key: string]: boolean;
  };
  modals: {
    [key: string]: {
      visible: boolean;
      data?: any;
    };
  };
  breadcrumbs: Array<{
    title: string;
    path?: string;
  }>;
  pageTitle: string;
  notifications: Array<{
    id: string;
    type: 'success' | 'info' | 'warning' | 'error';
    title: string;
    message: string;
    duration?: number;
  }>;
}

// 初始状态
const initialState: UIState = {
  sidebarCollapsed: false,
  theme: 'light',
  language: 'zh-CN',
  loading: {
    global: false,
  },
  modals: {},
  breadcrumbs: [],
  pageTitle: 'Moniit',
  notifications: [],
};

// Slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<'zh-CN' | 'en-US'>) => {
      state.language = action.payload;
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      const { key, loading } = action.payload;
      state.loading[key] = loading;
    },
    showModal: (state, action: PayloadAction<{ key: string; data?: any }>) => {
      const { key, data } = action.payload;
      state.modals[key] = {
        visible: true,
        data,
      };
    },
    hideModal: (state, action: PayloadAction<string>) => {
      const key = action.payload;
      if (state.modals[key]) {
        state.modals[key].visible = false;
        state.modals[key].data = undefined;
      }
    },
    setBreadcrumbs: (state, action: PayloadAction<Array<{ title: string; path?: string }>>) => {
      state.breadcrumbs = action.payload;
    },
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
      // 更新浏览器标题
      if (typeof document !== 'undefined') {
        document.title = `${action.payload} - Moniit`;
      }
    },
    addNotification: (state, action: PayloadAction<{
      type: 'success' | 'info' | 'warning' | 'error';
      title: string;
      message: string;
      duration?: number;
    }>) => {
      const notification = {
        id: Date.now().toString(),
        ...action.payload,
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
  },
});

// 导出actions
export const {
  toggleSidebar,
  setSidebarCollapsed,
  setTheme,
  setLanguage,
  setGlobalLoading,
  setLoading,
  showModal,
  hideModal,
  setBreadcrumbs,
  setPageTitle,
  addNotification,
  removeNotification,
  clearNotifications,
} = uiSlice.actions;

// 选择器
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectLanguage = (state: { ui: UIState }) => state.ui.language;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.loading.global;
export const selectLoading = (key: string) => (state: { ui: UIState }) => state.ui.loading[key] || false;
export const selectModal = (key: string) => (state: { ui: UIState }) => state.ui.modals[key] || { visible: false };
export const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs;
export const selectPageTitle = (state: { ui: UIState }) => state.ui.pageTitle;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;

export default uiSlice.reducer;
