<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .params {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 商品API测试</h1>
        <p>测试修复后的商品管理API功能</p>
        
        <div class="test-section">
            <h3>1. 直接API测试</h3>
            <p>直接测试后端API端点</p>
            <button onclick="testDirectAPI()">测试 GET /api/v1/products/</button>
            <div id="directResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 前端代理测试</h3>
            <p>通过前端代理测试API</p>
            <button onclick="testProxyAPI()">测试代理 /api/v1/products/</button>
            <div id="proxyResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 分页参数测试</h3>
            <p>测试分页参数转换 (page/page_size → skip/limit)</p>
            <div class="params">
                前端参数: page=2, page_size=10<br>
                后端参数: skip=10, limit=10
            </div>
            <button onclick="testPaginationParams()">测试分页参数</button>
            <div id="paginationResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 商品详情测试</h3>
            <p>测试商品详情API</p>
            <button onclick="testProductDetail()">测试 GET /api/v1/products/{id}</button>
            <div id="detailResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 综合测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="allTestsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testDirectAPI() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试直接API调用...';
            
            try {
                const response = await fetch('/api/v1/products/?skip=0&limit=20', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 直接API调用成功!
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}

🎉 后端API端点正常工作!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API调用失败:
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testProxyAPI() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试前端代理...';
            
            try {
                // 模拟前端API调用的方式
                const response = await fetch('/api/v1/products/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 前端代理测试成功!
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}

🎉 前端代理配置正常工作!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 代理测试失败:
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testPaginationParams() {
            const resultDiv = document.getElementById('paginationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试分页参数转换...';
            
            try {
                // 模拟前端发送 page=2, page_size=10 的请求
                // 应该转换为 skip=10, limit=10
                const response = await fetch('/api/v1/products/?skip=10&limit=10', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 分页参数测试成功!
前端参数: page=2, page_size=10
转换后参数: skip=10, limit=10

后端响应:
- skip: ${data.skip}
- limit: ${data.limit}
- total: ${data.total}
- items: ${data.items.length} 个

🎉 分页参数转换正常工作!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 分页参数测试失败:
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testProductDetail() {
            const resultDiv = document.getElementById('detailResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试商品详情API...';
            
            try {
                const testProductId = 'test-product-123';
                const response = await fetch(`/api/v1/products/${testProductId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 商品详情API测试成功!
状态码: ${response.status}
商品ID: ${testProductId}
响应数据:
${JSON.stringify(data, null, 2)}

🎉 商品详情API正常工作!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 商品详情API测试失败:
状态码: ${response.status}
错误: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('allTestsResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在运行所有商品API测试...\n';
            
            const tests = [
                { name: '直接API测试', func: testDirectAPI },
                { name: '前端代理测试', func: testProxyAPI },
                { name: '分页参数测试', func: testPaginationParams },
                { name: '商品详情测试', func: testProductDetail }
            ];
            
            let results = [];
            
            for (let test of tests) {
                try {
                    await test.func();
                    results.push(`✅ ${test.name}: 通过`);
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            const passedTests = results.filter(r => r.includes('✅')).length;
            const totalTests = results.length;
            
            if (passedTests === totalTests) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `🎉 所有商品API测试通过! (${passedTests}/${totalTests})

${results.join('\n')}

🚀 商品管理API修复完成!
- API路径修复: /products → /api/v1/products/
- 分页参数转换: page/page_size → skip/limit
- 前端代理配置正常
- 后端API端点正常`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `⚠️ 部分测试失败 (${passedTests}/${totalTests})

${results.join('\n')}

请检查失败的测试项目。`;
            }
        }
    </script>
</body>
</html>
