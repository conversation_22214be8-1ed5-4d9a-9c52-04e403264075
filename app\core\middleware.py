"""
中间件模块
"""

import time
import logging
from typing import Callable
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from fastapi import HTTPException

from app.core.cache import get_cache_manager
from app.utils.cache_utils import cache_helper

logger = logging.getLogger(__name__)


class CacheMiddleware(BaseHTTPMiddleware):
    """缓存中间件"""
    
    def __init__(self, app, cache_paths: list = None):
        super().__init__(app)
        self.cache_paths = cache_paths or [
            "/api/v1/analytics/",
            "/api/v1/products/",
            "/api/v1/profit/"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否需要缓存
        if not self._should_cache(request):
            return await call_next(request)
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        try:
            # 尝试从缓存获取响应
            cache_manager = await get_cache_manager()
            cached_response = await cache_manager.get(cache_key)
            
            if cached_response:
                logger.debug(f"缓存命中: {cache_key}")
                return Response(
                    content=cached_response["content"],
                    status_code=cached_response["status_code"],
                    headers=cached_response["headers"],
                    media_type=cached_response["media_type"]
                )
        
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
        
        # 执行请求
        response = await call_next(request)
        
        # 缓存响应
        if response.status_code == 200:
            try:
                await self._cache_response(cache_key, response)
            except Exception as e:
                logger.error(f"缓存设置失败: {e}")
        
        return response
    
    def _should_cache(self, request: Request) -> bool:
        """判断是否应该缓存"""
        # 只缓存GET请求
        if request.method != "GET":
            return False
        
        # 检查路径
        path = request.url.path
        return any(path.startswith(cache_path) for cache_path in self.cache_paths)
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        path = request.url.path
        query_params = str(request.query_params)
        user_id = getattr(request.state, "user_id", "anonymous")
        
        return f"http_cache:{user_id}:{path}:{hash(query_params)}"
    
    async def _cache_response(self, cache_key: str, response: Response):
        """缓存响应"""
        # 读取响应内容
        content = b""
        async for chunk in response.body_iterator:
            content += chunk
        
        # 构建缓存数据
        cache_data = {
            "content": content.decode("utf-8"),
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "media_type": response.media_type
        }
        
        # 设置缓存
        cache_manager = await get_cache_manager()
        await cache_manager.set(cache_key, cache_data, ttl=300)  # 5分钟缓存
        
        # 重新创建响应
        response.body_iterator = iter([content])


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app, default_limit: int = 100, window: int = 3600):
        super().__init__(app)
        self.default_limit = default_limit
        self.window = window
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端标识
        client_id = self._get_client_id(request)
        endpoint = request.url.path
        
        # 检查限流
        try:
            rate_limit_result = await cache_helper.check_rate_limit(
                client_id, endpoint, self.default_limit, self.window
            )
            
            if not rate_limit_result["allowed"]:
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "Rate limit exceeded",
                        "limit": rate_limit_result["limit"],
                        "current_count": rate_limit_result["current_count"],
                        "reset_time": rate_limit_result["reset_time"].isoformat()
                    }
                )
            
            # 添加限流头信息
            response = await call_next(request)
            response.headers["X-RateLimit-Limit"] = str(rate_limit_result["limit"])
            response.headers["X-RateLimit-Remaining"] = str(rate_limit_result.get("remaining", 0))
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"限流检查失败: {e}")
            return await call_next(request)
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用API密钥
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key}"
        
        # 使用IP地址
        client_ip = request.client.host
        return f"ip:{client_ip}"


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 执行请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加性能头信息
        response.headers["X-Process-Time"] = str(round(process_time, 4))
        
        # 记录慢请求
        if process_time > 1.0:  # 超过1秒的请求
            logger.warning(
                f"慢请求: {request.method} {request.url.path} - {process_time:.4f}s"
            )
        
        # 记录请求日志
        logger.info(
            f"{request.method} {request.url.path} - "
            f"{response.status_code} - {process_time:.4f}s"
        )
        
        return response


class CacheCleanupMiddleware(BaseHTTPMiddleware):
    """缓存清理中间件"""
    
    def __init__(self, app, cleanup_interval: int = 300):
        super().__init__(app)
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 定期清理本地缓存
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            try:
                cache_manager = await get_cache_manager()
                cache_manager.cleanup_local_cache()
                self.last_cleanup = current_time
                logger.debug("本地缓存清理完成")
            except Exception as e:
                logger.error(f"缓存清理失败: {e}")
        
        return await call_next(request)
