/**
 * 全局样式
 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 全局工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 8px !important; }
.ml-2 { margin-left: 16px !important; }
.ml-3 { margin-left: 24px !important; }
.ml-4 { margin-left: 32px !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 8px !important; }
.mr-2 { margin-right: 16px !important; }
.mr-3 { margin-right: 24px !important; }
.mr-4 { margin-right: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 8px !important; }
.p-2 { padding: 16px !important; }
.p-3 { padding: 24px !important; }
.p-4 { padding: 32px !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 8px !important; }
.pb-2 { padding-bottom: 16px !important; }
.pb-3 { padding-bottom: 24px !important; }
.pb-4 { padding-bottom: 32px !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 8px !important; }
.pt-2 { padding-top: 16px !important; }
.pt-3 { padding-top: 24px !important; }
.pt-4 { padding-top: 32px !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 8px !important; }
.pl-2 { padding-left: 16px !important; }
.pl-3 { padding-left: 24px !important; }
.pl-4 { padding-left: 32px !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 8px !important; }
.pr-2 { padding-right: 16px !important; }
.pr-3 { padding-right: 24px !important; }
.pr-4 { padding-right: 32px !important; }

/* 布局工具类 */
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* 响应式工具类 */
.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 状态颜色 */
.status-success {
  color: #52c41a;
}

.status-warning {
  color: #faad14;
}

.status-error {
  color: #ff4d4f;
}

.status-info {
  color: #1890ff;
}

/* 背景颜色 */
.bg-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.bg-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.bg-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.bg-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

/* 卡片阴影 */
.card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-shadow-hover {
  transition: box-shadow 0.3s ease;
}

.card-shadow-hover:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 表格样式增强 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* 表单样式增强 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 按钮组间距 */
.ant-btn + .ant-btn {
  margin-left: 8px;
}

/* 响应式隐藏 */
@media (max-width: 768px) {
  .d-none-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .d-none-desktop {
    display: none !important;
  }
}
