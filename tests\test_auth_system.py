"""
用户权限系统测试

测试用户认证、权限控制、审计日志等功能
"""

import pytest
import asyncio
import tempfile
import shutil
from datetime import datetime, timedelta

from app.auth import (
    AuthManager, AuthConfig, PasswordManager, JWTHandler, TokenType,
    PermissionManager, PermissionDeniedError, AuditLogger, AuditAction, AuditResult,
    User, UserRole, Permission
)


class TestPasswordManager:
    """密码管理器测试"""
    
    @pytest.fixture
    def password_manager(self):
        """密码管理器"""
        return PasswordManager()
    
    def test_hash_and_verify_password(self, password_manager):
        """测试密码哈希和验证"""
        password = "TestPassword123!"
        
        # 哈希密码
        password_hash, salt = password_manager.hash_password(password)
        
        assert password_hash is not None
        assert salt is not None
        assert len(password_hash) > 0
        assert len(salt) > 0
        
        # 验证正确密码
        assert password_manager.verify_password(password, password_hash, salt) is True
        
        # 验证错误密码
        assert password_manager.verify_password("WrongPassword", password_hash, salt) is False
    
    def test_password_strength_check(self, password_manager):
        """测试密码强度检查"""
        # 强密码
        strong_password = "StrongPass123!@#"
        result = password_manager.check_password_strength(strong_password)
        assert result["is_valid"] is True
        assert result["strength"] >= 4
        
        # 弱密码
        weak_password = "123"
        result = password_manager.check_password_strength(weak_password)
        assert result["is_valid"] is False
        assert len(result["issues"]) > 0
    
    def test_generate_secure_password(self, password_manager):
        """测试生成安全密码"""
        password = password_manager.generate_secure_password(12)
        
        assert len(password) == 12
        
        # 检查生成的密码强度
        result = password_manager.check_password_strength(password)
        assert result["is_valid"] is True


class TestJWTHandler:
    """JWT处理器测试"""
    
    @pytest.fixture
    def jwt_handler(self):
        """JWT处理器"""
        return JWTHandler("test-secret-key")
    
    def test_generate_and_verify_token(self, jwt_handler):
        """测试生成和验证令牌"""
        payload = {
            "user_id": "test_user",
            "username": "testuser",
            "role": "admin"
        }
        
        # 生成令牌
        token = jwt_handler.generate_token(payload, TokenType.ACCESS)
        assert token is not None
        
        # 验证令牌
        decoded_payload = jwt_handler.verify_token(token, TokenType.ACCESS)
        assert decoded_payload is not None
        assert decoded_payload["user_id"] == "test_user"
        assert decoded_payload["username"] == "testuser"
        assert decoded_payload["role"] == "admin"
    
    def test_token_expiration(self, jwt_handler):
        """测试令牌过期"""
        payload = {"user_id": "test_user"}
        
        # 生成短期令牌
        token = jwt_handler.generate_token(
            payload, 
            TokenType.ACCESS, 
            expires_delta=timedelta(seconds=1)
        )
        
        # 立即验证应该成功
        decoded = jwt_handler.verify_token(token, TokenType.ACCESS)
        assert decoded is not None
        
        # 等待过期后验证应该失败
        import time
        time.sleep(2)
        decoded = jwt_handler.verify_token(token, TokenType.ACCESS)
        assert decoded is None
    
    def test_refresh_token(self, jwt_handler):
        """测试刷新令牌"""
        payload = {
            "user_id": "test_user",
            "username": "testuser",
            "role": "admin",
            "permissions": ["read", "write"]
        }
        
        # 生成刷新令牌
        refresh_token = jwt_handler.generate_token(payload, TokenType.REFRESH)
        
        # 刷新令牌
        result = jwt_handler.refresh_token(refresh_token)
        assert result is not None
        
        new_access_token, new_refresh_token = result
        assert new_access_token is not None
        assert new_refresh_token is not None
        
        # 验证新令牌
        decoded = jwt_handler.verify_token(new_access_token, TokenType.ACCESS)
        assert decoded["user_id"] == "test_user"


class TestAuthManager:
    """认证管理器测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def auth_manager(self, temp_dir):
        """认证管理器"""
        config = AuthConfig()
        return AuthManager(config, temp_dir)
    
    def test_register_user(self, auth_manager):
        """测试用户注册"""
        success, message, user = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!",
            full_name="Test User",
            role=UserRole.OPERATOR
        )
        
        assert success is True
        assert user is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.role == UserRole.OPERATOR
    
    def test_duplicate_user_registration(self, auth_manager):
        """测试重复用户注册"""
        # 第一次注册
        success1, _, _ = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!"
        )
        assert success1 is True
        
        # 重复用户名
        success2, message2, _ = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!"
        )
        assert success2 is False
        assert "用户名已存在" in message2
        
        # 重复邮箱
        success3, message3, _ = auth_manager.register_user(
            username="testuser2",
            email="<EMAIL>",
            password="TestPassword123!"
        )
        assert success3 is False
        assert "邮箱已存在" in message3
    
    @pytest.mark.asyncio
    async def test_authenticate_user(self, auth_manager):
        """测试用户认证"""
        # 注册用户
        auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!"
        )
        
        # 正确认证
        success, message, session = auth_manager.authenticate_user(
            username="testuser",
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        
        assert success is True
        assert session is not None
        assert session.user_id is not None
        
        # 错误密码
        success2, message2, session2 = auth_manager.authenticate_user(
            username="testuser",
            password="WrongPassword"
        )
        
        assert success2 is False
        assert session2 is None
    
    def test_change_password(self, auth_manager):
        """测试更改密码"""
        # 注册用户
        _, _, user = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="OldPassword123!"
        )
        
        # 更改密码
        success, message = auth_manager.change_password(
            user_id=user.user_id,
            old_password="OldPassword123!",
            new_password="NewPassword123!"
        )
        
        assert success is True
        
        # 用新密码登录
        success2, _, _ = auth_manager.authenticate_user(
            username="testuser",
            password="NewPassword123!"
        )
        assert success2 is True


class TestPermissionManager:
    """权限管理器测试"""
    
    @pytest.fixture
    def permission_manager(self):
        """权限管理器"""
        return PermissionManager()
    
    @pytest.fixture
    def test_user(self):
        """测试用户"""
        user = User(
            user_id="test_user_id",
            username="testuser",
            email="<EMAIL>",
            password_hash="hash",
            salt="salt",
            role=UserRole.OPERATOR
        )
        return user
    
    def test_check_permission(self, permission_manager, test_user):
        """测试权限检查"""
        # 用户有的权限
        result = permission_manager.check_permission(test_user, Permission.PRODUCT_READ)
        assert result is True
        
        # 用户没有的权限
        result = permission_manager.check_permission(test_user, Permission.SYSTEM_ADMIN)
        assert result is False
    
    def test_check_resource_permission(self, permission_manager, test_user):
        """测试资源权限检查"""
        # 有权限的资源操作
        result = permission_manager.check_resource_permission(
            test_user, "products", "read"
        )
        assert result is True
        
        # 没有权限的资源操作
        result = permission_manager.check_resource_permission(
            test_user, "system", "admin"
        )
        assert result is False
    
    def test_get_user_permissions(self, permission_manager, test_user):
        """测试获取用户权限"""
        permissions = permission_manager.get_user_permissions(test_user)
        
        assert Permission.PRODUCT_READ in permissions
        assert Permission.PRODUCT_CREATE in permissions
        assert Permission.TRANSLATION_USE in permissions
        assert Permission.SYSTEM_ADMIN not in permissions
    
    def test_permission_decorator(self, permission_manager, test_user):
        """测试权限装饰器"""
        @permission_manager.require_permission(Permission.PRODUCT_READ)
        def read_product(user):
            return "success"
        
        # 有权限的用户
        result = read_product(test_user)
        assert result == "success"
        
        # 没有权限的用户
        admin_user = User(
            user_id="admin_id",
            username="admin",
            email="<EMAIL>",
            password_hash="hash",
            salt="salt",
            role=UserRole.GUEST  # 访客没有读权限
        )
        
        with pytest.raises(PermissionDeniedError):  # 应该抛出权限异常
            read_product(admin_user)


class TestAuditLogger:
    """审计日志测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def audit_logger(self, temp_dir):
        """审计日志器"""
        return AuditLogger(temp_dir)
    
    def test_log_action(self, audit_logger):
        """测试记录审计日志"""
        audit_logger.log_action(
            user_id="test_user",
            action=AuditAction.LOGIN,
            resource_type="auth",
            resource_id="login",
            username="testuser",
            result=AuditResult.SUCCESS,
            ip_address="127.0.0.1"
        )
        
        # 检查统计
        stats = audit_logger.get_audit_statistics()
        assert stats["stats"]["total_entries"] == 1
    
    def test_query_audit_logs(self, audit_logger):
        """测试查询审计日志"""
        # 记录几条日志
        for i in range(3):
            audit_logger.log_action(
                user_id=f"user_{i}",
                action=AuditAction.LOGIN,
                resource_type="auth",
                resource_id="login",
                username=f"user{i}",
                result=AuditResult.SUCCESS
            )
        
        # 查询日志
        logs = audit_logger.query_audit_logs(limit=10)
        assert len(logs) == 3
    
    def test_get_user_activity(self, audit_logger):
        """测试获取用户活动"""
        user_id = "test_user"
        
        # 记录用户活动
        audit_logger.log_action(
            user_id=user_id,
            action=AuditAction.LOGIN,
            resource_type="auth",
            resource_id="login",
            username="testuser",
            result=AuditResult.SUCCESS
        )
        
        audit_logger.log_action(
            user_id=user_id,
            action=AuditAction.DATA_READ,
            resource_type="product",
            resource_id="123",
            username="testuser",
            result=AuditResult.SUCCESS
        )
        
        # 获取用户活动
        activity = audit_logger.get_user_activity(user_id, days=1)
        
        assert activity["total_actions"] == 2
        assert "login" in activity["by_action"]
        assert "data_read" in activity["by_action"]
    
    def test_get_security_events(self, audit_logger):
        """测试获取安全事件"""
        # 记录安全事件
        audit_logger.log_action(
            user_id="test_user",
            action=AuditAction.LOGIN_FAILED,
            resource_type="auth",
            resource_id="login",
            username="testuser",
            result=AuditResult.FAILURE,
            error_message="密码错误"
        )
        
        # 获取安全事件
        events = audit_logger.get_security_events(days=1)
        
        assert len(events) == 1
        assert events[0]["action"] == "login_failed"
        assert events[0]["result"] == "failure"


@pytest.mark.asyncio
async def test_complete_auth_flow():
    """测试完整认证流程"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建认证管理器
        config = AuthConfig()
        auth_manager = AuthManager(config, temp_dir)
        
        # 注册用户
        success, message, user = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!",
            role=UserRole.OPERATOR
        )
        assert success is True
        
        # 用户登录
        success, message, session = auth_manager.authenticate_user(
            username="testuser",
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        assert success is True
        assert session is not None
        
        # 验证会话
        verified_session = auth_manager.verify_session(session.session_id)
        assert verified_session is not None
        assert verified_session.user_id == user.user_id
        
        # 权限检查
        permission_manager = PermissionManager()
        has_permission = permission_manager.check_permission(
            user, Permission.PRODUCT_READ
        )
        assert has_permission is True
        
        # 用户登出
        logout_success = auth_manager.logout_user(session.session_id)
        assert logout_success is True
        
        # 验证会话已失效
        verified_session = auth_manager.verify_session(session.session_id)
        assert verified_session is None
