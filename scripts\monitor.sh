# 检查Docker服务
check_docker() {
    log_info "检查Docker服务..."

    if ! docker info &>/dev/null; then
        log_error "Docker服务未运行"
        log_monitor "ERROR" "Docker服务未运行"
        return 1
    fi

    log_success "Docker服务正常"
    log_monitor "INFO" "Docker服务正常"
    return 0
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."

    local containers=(
        "moniit-backend"
        "moniit-frontend"
        "moniit-redis"
        "moniit-postgres"
    )

    local failed_containers=()

    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
            local status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "unknown")
            if [ "$status" = "healthy" ] || [ "$status" = "unknown" ]; then
                log_success "容器 $container 运行正常"
                log_monitor "INFO" "容器 $container 运行正常"
            else
                log_warning "容器 $container 健康检查失败: $status"
                log_monitor "WARNING" "容器 $container 健康检查失败: $status"
                failed_containers+=("$container")
            fi
        else
            log_error "容器 $container 未运行"
            log_monitor "ERROR" "容器 $container 未运行"
            failed_containers+=("$container")
        fi
    done

    if [ ${#failed_containers[@]} -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# 检查服务健康状态
check_services() {
    log_info "检查服务健康状态..."

    local services=(
        "http://localhost:8000/health:后端API"
        "http://localhost:3000:前端服务"
    )

    local failed_services=()

    for service in "${services[@]}"; do
        local url=$(echo "$service" | cut -d: -f1-2)
        local name=$(echo "$service" | cut -d: -f3)

        if curl -f -s --max-time 10 "$url" &>/dev/null; then
            log_success "$name 健康检查通过"
            log_monitor "INFO" "$name 健康检查通过"
        else
            log_error "$name 健康检查失败"
            log_monitor "ERROR" "$name 健康检查失败"
            failed_services+=("$name")
        fi
    done

    if [ ${#failed_services[@]} -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# 检查Docker服务状态
check_docker_services() {
    echo "=== Docker服务状态 ==="
    
    services=("app" "worker" "beat" "flower" "db" "redis" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            log_success "✅ $service: 运行中"
        else
            log_error "❌ $service: 停止"
        fi
    done
    echo ""
}

# 检查HTTP服务
check_http_services() {
    echo "=== HTTP服务检查 ==="
    
    # 检查应用健康状态
    if curl -f -s http://localhost:8000/health > /dev/null; then
        log_success "✅ 应用健康检查: 正常"
    else
        log_error "❌ 应用健康检查: 异常"
    fi
    
    # 检查前端页面
    if curl -f -s http://localhost/ > /dev/null; then
        log_success "✅ 前端页面: 正常"
    else
        log_warning "⚠️  前端页面: 异常"
    fi
    
    # 检查API文档
    if curl -f -s http://localhost:8000/docs > /dev/null; then
        log_success "✅ API文档: 正常"
    else
        log_warning "⚠️  API文档: 异常"
    fi
    
    # 检查Flower监控
    if curl -f -s http://localhost:5555/ > /dev/null; then
        log_success "✅ Flower监控: 正常"
    else
        log_warning "⚠️  Flower监控: 异常"
    fi
    
    echo ""
}

# 检查系统资源
check_system_resources() {
    echo "=== 系统资源使用情况 ==="
    
    # 磁盘使用情况
    echo "💾 磁盘使用情况:"
    df -h / | tail -1 | awk '{
        used_percent = $5
        gsub(/%/, "", used_percent)
        if (used_percent > 90) 
            printf "   \033[0;31m使用: %s/%s (%s) - 警告：磁盘空间不足！\033[0m\n", $3, $2, $5
        else if (used_percent > 80)
            printf "   \033[1;33m使用: %s/%s (%s) - 注意：磁盘使用率较高\033[0m\n", $3, $2, $5
        else
            printf "   \033[0;32m使用: %s/%s (%s)\033[0m\n", $3, $2, $5
    }'
    
    # 内存使用情况
    echo "🧠 内存使用情况:"
    free -h | grep "Mem:" | awk '{
        total = $2
        used = $3
        available = $7
        printf "   使用: %s/%s (可用: %s)\n", used, total, available
    }'
    
    # CPU负载
    echo "⚡ CPU负载:"
    uptime | awk -F'load average:' '{
        load = $2
        gsub(/^[ \t]+/, "", load)
        printf "   负载: %s\n", load
    }'
    
    echo ""
}

# 检查Docker容器资源使用
check_container_resources() {
    echo "=== 容器资源使用情况 ==="
    
    # 检查容器CPU和内存使用
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | head -10
    
    echo ""
}

# 检查日志错误
check_logs() {
    echo "=== 最近日志错误 ==="
    
    # 检查应用日志中的错误
    if docker-compose logs --tail=50 app 2>/dev/null | grep -i "error\|exception\|failed" | tail -5; then
        log_warning "发现应用错误日志"
    else
        log_success "应用日志正常"
    fi
    
    # 检查数据库日志中的错误
    if docker-compose logs --tail=50 db 2>/dev/null | grep -i "error\|fatal" | tail -3; then
        log_warning "发现数据库错误日志"
    else
        log_success "数据库日志正常"
    fi
    
    echo ""
}

# 检查数据库连接
check_database() {
    echo "=== 数据库状态 ==="
    
    if docker-compose exec -T db pg_isready -U postgres -d ecommerce_monitor 2>/dev/null; then
        log_success "✅ 数据库连接: 正常"
        
        # 检查数据库大小
        db_size=$(docker-compose exec -T db psql -U postgres -d ecommerce_monitor -t -c "SELECT pg_size_pretty(pg_database_size('ecommerce_monitor'));" 2>/dev/null | xargs)
        echo "   数据库大小: $db_size"
        
        # 检查活跃连接数
        active_connections=$(docker-compose exec -T db psql -U postgres -d ecommerce_monitor -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs)
        echo "   活跃连接数: $active_connections"
        
    else
        log_error "❌ 数据库连接: 异常"
    fi
    
    echo ""
}

# 检查Redis状态
check_redis() {
    echo "=== Redis状态 ==="
    
    if docker-compose exec -T redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log_success "✅ Redis连接: 正常"
        
        # 检查Redis内存使用
        redis_memory=$(docker-compose exec -T redis redis-cli info memory 2>/dev/null | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        echo "   内存使用: $redis_memory"
        
        # 检查连接数
        redis_connections=$(docker-compose exec -T redis redis-cli info clients 2>/dev/null | grep "connected_clients" | cut -d: -f2 | tr -d '\r')
        echo "   连接数: $redis_connections"
        
    else
        log_error "❌ Redis连接: 异常"
    fi
    
    echo ""
}

# 生成监控报告
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="logs/monitor_report_$(date '+%Y%m%d_%H%M%S').txt"
    
    {
        echo "电商监控系统状态报告"
        echo "生成时间: $timestamp"
        echo "=========================="
        echo ""
        
        check_docker_services
        check_http_services
        check_system_resources
        check_database
        check_redis
        check_logs
        
    } > "$report_file"
    
    log_info "监控报告已保存到: $report_file"
}

# 自动重启异常服务
auto_restart() {
    log_info "检查并重启异常服务..."
    
    services=("app" "worker" "beat" "flower" "db" "redis" "nginx")
    
    for service in "${services[@]}"; do
        if ! docker-compose ps $service | grep -q "Up"; then
            log_warning "重启服务: $service"
            docker-compose restart $service
            sleep 10
        fi
    done
}

# 清理日志文件
cleanup_logs() {
    log_info "清理旧日志文件..."
    
    # 清理7天前的日志文件
    find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find logs/ -name "monitor_report_*.txt" -mtime +7 -delete 2>/dev/null || true
    
    log_success "日志清理完成"
}

# 显示帮助信息
show_help() {
    echo "电商监控系统监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  check     - 检查系统状态（默认）"
    echo "  report    - 生成监控报告"
    echo "  restart   - 自动重启异常服务"
    echo "  cleanup   - 清理旧日志文件"
    echo "  help      - 显示帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-check}" in
        "check")
            echo "电商监控系统状态检查 - $(date)"
            echo "================================"
            check_docker_services
            check_http_services
            check_system_resources
            check_database
            check_redis
            check_logs
            echo "监控检查完成"
            ;;
        "report")
            generate_report
            ;;
        "restart")
            auto_restart
            ;;
        "cleanup")
            cleanup_logs
            ;;
        "help")
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
