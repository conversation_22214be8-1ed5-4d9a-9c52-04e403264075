# Moniit 性能优化和故障排除手册

## 📋 目录

1. [性能监控](#性能监控)
2. [性能优化](#性能优化)
3. [故障诊断](#故障诊断)
4. [常见问题解决](#常见问题解决)
5. [预防性维护](#预防性维护)
6. [应急响应](#应急响应)

## 📊 性能监控

### 关键性能指标 (KPI)

**系统性能指标**:
- **响应时间**: 页面加载 <2s, API响应 <500ms
- **吞吐量**: 支持1000+ 并发用户
- **可用性**: 系统可用性 ≥99.9%
- **错误率**: 错误率 <0.1%

**业务性能指标**:
- **监控成功率**: ≥98%
- **数据准确率**: ≥99%
- **任务执行延迟**: <5分钟
- **数据处理速度**: 10000+ 记录/分钟

### 监控工具配置

**Prometheus监控配置**:
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'moniit-web'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'moniit-celery'
    static_configs:
      - targets: ['localhost:8001']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
```

**Grafana仪表板**:
```json
{
  "dashboard": {
    "title": "Moniit System Monitoring",
    "panels": [
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      }
    ]
  }
}
```

### 性能基准测试

**负载测试脚本**:
```python
# load_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

async def test_api_performance():
    """API性能测试"""
    url = "http://localhost:8000/api/v1/products"
    headers = {"Authorization": "Bearer your_token"}
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        tasks = []
        
        for i in range(1000):  # 1000个并发请求
            task = session.get(url, headers=headers)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        end_time = time.time()
        
        success_count = sum(1 for r in responses if r.status == 200)
        total_time = end_time - start_time
        
        print(f"总请求数: {len(responses)}")
        print(f"成功请求数: {success_count}")
        print(f"成功率: {success_count/len(responses)*100:.2f}%")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"QPS: {len(responses)/total_time:.2f}")

if __name__ == "__main__":
    asyncio.run(test_api_performance())
```

## ⚡ 性能优化

### 数据库优化

**查询优化**:
```sql
-- 1. 创建复合索引
CREATE INDEX CONCURRENTLY idx_price_records_product_time 
ON price_records (product_id, recorded_at DESC);

-- 2. 分区表优化
CREATE TABLE price_records_2025_08 PARTITION OF price_records
FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');

-- 3. 查询重写
-- 优化前
SELECT * FROM price_records 
WHERE product_id = '123' 
ORDER BY recorded_at DESC 
LIMIT 100;

-- 优化后
SELECT pr.* FROM price_records pr
WHERE pr.product_id = '123' 
  AND pr.recorded_at >= NOW() - INTERVAL '30 days'
ORDER BY pr.recorded_at DESC 
LIMIT 100;
```

**连接池优化**:
```python
# settings/production.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'moniit',
        'USER': 'moniit',
        'PASSWORD': 'password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        },
        'CONN_MAX_AGE': 600,  # 连接复用10分钟
    }
}
```

**PostgreSQL配置优化**:
```bash
# postgresql.conf
shared_buffers = 2GB                    # 25% of RAM
effective_cache_size = 6GB              # 75% of RAM
maintenance_work_mem = 512MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1                  # SSD优化
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
```

### 应用层优化

**缓存策略优化**:
```python
# cache_optimization.py
from django.core.cache import cache
from functools import wraps
import hashlib

def smart_cache(timeout=300, key_prefix=''):
    """智能缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result
        return wrapper
    return decorator

@smart_cache(timeout=600, key_prefix='product_analysis')
def get_product_analysis(product_id, period):
    """获取商品分析（带缓存）"""
    # 复杂的分析逻辑
    return analysis_result
```

**异步任务优化**:
```python
# celery_optimization.py
from celery import Celery
from celery.signals import worker_ready
from kombu import Queue

app = Celery('moniit')

# 队列配置
app.conf.task_routes = {
    'moniit.tasks.price_monitoring': {'queue': 'monitoring'},
    'moniit.tasks.data_analysis': {'queue': 'analysis'},
    'moniit.tasks.notification': {'queue': 'notification'},
}

# 并发优化
app.conf.worker_concurrency = 4
app.conf.worker_prefetch_multiplier = 1
app.conf.task_acks_late = True
app.conf.worker_max_tasks_per_child = 1000

# 任务优先级
app.conf.task_default_priority = 5
app.conf.worker_disable_rate_limits = True
```

### 前端优化

**资源优化**:
```javascript
// webpack.config.js
const path = require('path');

module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    },
  },
  plugins: [
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
    }),
  ],
};
```

**API请求优化**:
```javascript
// api_optimization.js
class APIClient {
  constructor() {
    this.cache = new Map();
    this.pendingRequests = new Map();
  }

  async request(url, options = {}) {
    const cacheKey = `${url}:${JSON.stringify(options)}`;
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
        return cached.data;
      }
    }
    
    // 防止重复请求
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }
    
    // 发起请求
    const promise = fetch(url, options)
      .then(response => response.json())
      .then(data => {
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now()
        });
        this.pendingRequests.delete(cacheKey);
        return data;
      });
    
    this.pendingRequests.set(cacheKey, promise);
    return promise;
  }
}
```

## 🔍 故障诊断

### 诊断工具

**系统资源监控**:
```bash
#!/bin/bash
# system_check.sh

echo "=== 系统资源检查 ==="
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo "内存使用率:"
free -m | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'

echo "磁盘使用率:"
df -h | awk '$NF=="/"{printf "%s\n", $5}'

echo "网络连接数:"
netstat -an | grep ESTABLISHED | wc -l

echo "=== 应用状态检查 ==="
echo "Web服务状态:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health/

echo "数据库连接:"
pg_isready -h localhost -p 5432

echo "Redis连接:"
redis-cli ping

echo "=== 日志错误检查 ==="
echo "最近的错误日志:"
tail -n 20 /var/log/moniit/error.log | grep ERROR
```

**数据库性能诊断**:
```sql
-- 慢查询分析
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 锁等待分析
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity 
    ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity 
    ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;

-- 表大小分析
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 日志分析

**日志聚合分析**:
```python
# log_analyzer.py
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta

class LogAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.error_patterns = {
            'database_error': r'DatabaseError|OperationalError',
            'timeout_error': r'TimeoutError|timeout',
            'memory_error': r'MemoryError|OutOfMemory',
            'connection_error': r'ConnectionError|Connection refused'
        }
    
    def analyze_errors(self, hours=24):
        """分析最近N小时的错误"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        error_counts = Counter()
        error_details = defaultdict(list)
        
        with open(self.log_file, 'r') as f:
            for line in f:
                # 解析时间戳
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if not timestamp_match:
                    continue
                
                timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                if timestamp < cutoff_time:
                    continue
                
                # 检查错误模式
                for error_type, pattern in self.error_patterns.items():
                    if re.search(pattern, line, re.IGNORECASE):
                        error_counts[error_type] += 1
                        error_details[error_type].append({
                            'timestamp': timestamp,
                            'message': line.strip()
                        })
        
        return {
            'counts': dict(error_counts),
            'details': dict(error_details)
        }
    
    def generate_report(self):
        """生成错误报告"""
        analysis = self.analyze_errors()
        
        print("=== 错误分析报告 ===")
        print(f"分析时间范围: 最近24小时")
        print(f"总错误数: {sum(analysis['counts'].values())}")
        print()
        
        for error_type, count in analysis['counts'].items():
            print(f"{error_type}: {count}次")
            if count > 0:
                latest_error = max(analysis['details'][error_type], 
                                 key=lambda x: x['timestamp'])
                print(f"  最新错误: {latest_error['timestamp']}")
                print(f"  错误信息: {latest_error['message'][:100]}...")
            print()

if __name__ == "__main__":
    analyzer = LogAnalyzer('/var/log/moniit/error.log')
    analyzer.generate_report()
```

## 🚨 常见问题解决

### 性能问题

**问题1: 页面加载缓慢**
```
症状: 页面加载时间超过5秒
诊断步骤:
1. 检查网络延迟: ping 服务器IP
2. 检查服务器负载: top, htop
3. 检查数据库性能: 慢查询日志
4. 检查缓存命中率: Redis info

解决方案:
1. 启用页面缓存
2. 优化数据库查询
3. 使用CDN加速静态资源
4. 增加服务器资源
```

**问题2: API响应超时**
```
症状: API请求超时或响应时间过长
诊断步骤:
1. 检查API日志: tail -f /var/log/moniit/api.log
2. 监控数据库连接: SELECT * FROM pg_stat_activity;
3. 检查Redis连接: redis-cli info clients
4. 分析慢查询: pg_stat_statements

解决方案:
1. 增加API超时时间
2. 优化数据库查询
3. 使用异步处理
4. 实现请求限流
```

### 数据问题

**问题3: 价格数据不准确**
```
症状: 采集的价格与实际价格不符
诊断步骤:
1. 人工验证实际价格
2. 检查网站页面结构变化
3. 查看爬虫日志
4. 测试URL可访问性

解决方案:
1. 更新爬虫规则
2. 调整CSS选择器
3. 处理反爬虫机制
4. 使用备用数据源
```

**问题4: 监控任务失败**
```
症状: 大量监控任务执行失败
诊断步骤:
1. 查看Celery日志: celery events
2. 检查任务队列状态: celery inspect active
3. 监控网络连接
4. 检查目标网站状态

解决方案:
1. 重启Celery worker
2. 调整任务重试策略
3. 增加任务超时时间
4. 实现智能重试机制
```

### 系统问题

**问题5: 内存使用过高**
```
症状: 系统内存使用率超过90%
诊断步骤:
1. 查看进程内存使用: ps aux --sort=-%mem
2. 检查内存泄漏: valgrind, memory_profiler
3. 分析缓存使用: redis-cli info memory
4. 检查日志文件大小

解决方案:
1. 重启相关服务
2. 调整缓存配置
3. 清理日志文件
4. 增加系统内存
```

## 🛡️ 预防性维护

### 定期维护任务

**每日维护**:
```bash
#!/bin/bash
# daily_maintenance.sh

echo "开始每日维护任务..."

# 1. 清理过期日志
find /var/log/moniit -name "*.log" -mtime +7 -delete

# 2. 清理临时文件
find /tmp -name "moniit_*" -mtime +1 -delete

# 3. 检查磁盘空间
df -h | awk '$5 > 85 {print "警告: " $1 " 磁盘使用率过高: " $5}'

# 4. 备份数据库
pg_dump moniit | gzip > /backup/moniit_$(date +%Y%m%d).sql.gz

# 5. 检查服务状态
systemctl is-active postgresql redis-server nginx

echo "每日维护任务完成"
```

**每周维护**:
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "开始每周维护任务..."

# 1. 数据库维护
psql -d moniit -c "VACUUM ANALYZE;"
psql -d moniit -c "REINDEX DATABASE moniit;"

# 2. 清理Redis缓存
redis-cli FLUSHDB

# 3. 更新系统包
apt update && apt upgrade -y

# 4. 检查SSL证书
certbot certificates

# 5. 性能报告
python /opt/moniit/scripts/performance_report.py

echo "每周维护任务完成"
```

### 监控告警

**告警规则配置**:
```yaml
# alert_rules.yml
groups:
  - name: moniit_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率告警"
          description: "错误率超过10%，持续5分钟"

      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接数超过80个"

      - alert: DiskSpaceHigh
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率超过85%"

      - alert: MemoryUsageHigh
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过90%，持续5分钟"
```

## 🚑 应急响应

### 应急响应流程

**1. 故障发现**
- 监控告警触发
- 用户反馈问题
- 定期健康检查

**2. 初步评估**
- 确定故障影响范围
- 评估故障严重程度
- 启动应急响应流程

**3. 故障处理**
- 执行快速修复措施
- 记录处理过程
- 持续监控系统状态

**4. 故障恢复**
- 验证系统功能正常
- 通知相关人员
- 更新故障状态

**5. 事后分析**
- 分析故障根本原因
- 制定预防措施
- 更新应急预案

### 应急处理脚本

**服务重启脚本**:
```bash
#!/bin/bash
# emergency_restart.sh

echo "开始应急重启..."

# 1. 停止服务
docker-compose -f docker-compose.prod.yml stop

# 2. 清理资源
docker system prune -f

# 3. 重启服务
docker-compose -f docker-compose.prod.yml up -d

# 4. 等待服务启动
sleep 30

# 5. 健康检查
curl -f http://localhost:8000/health/ || exit 1

echo "应急重启完成"
```

**数据库恢复脚本**:
```bash
#!/bin/bash
# db_recovery.sh

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

echo "开始数据库恢复..."

# 1. 停止应用
docker-compose stop web celery

# 2. 恢复数据库
gunzip -c $BACKUP_FILE | psql -d moniit

# 3. 重启应用
docker-compose start web celery

echo "数据库恢复完成"
```

---

## 📞 紧急联系方式

- **技术支持热线**: +86-400-xxx-xxxx (7×24小时)
- **紧急邮箱**: <EMAIL>
- **值班工程师**: 微信群 "Moniit应急响应"
- **远程支持**: TeamViewer ID: 123456789

---

*本手册最后更新时间: 2025-08-24*
