"""
预警处理器

提供预警处理流程、自动化响应、预警分析等功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product
from .alert_engine import AlertEngine, Alert, AlertLevel, AlertType, AlertCategory
from .rule_manager import RuleManager
from .notification_sender import NotificationSender

logger = get_logger(__name__)


class ProcessingAction(Enum):
    """处理动作"""
    NOTIFY_USERS = "notify_users"           # 通知用户
    AUTO_ACKNOWLEDGE = "auto_acknowledge"   # 自动确认
    AUTO_RESOLVE = "auto_resolve"          # 自动解决
    ESCALATE = "escalate"                  # 升级处理
    SUPPRESS = "suppress"                  # 抑制预警
    LOG_ONLY = "log_only"                  # 仅记录日志


class ProcessingStatus(Enum):
    """处理状态"""
    PENDING = "pending"         # 待处理
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 处理失败
    SKIPPED = "skipped"        # 已跳过


@dataclass
class ProcessingRule:
    """处理规则"""
    rule_id: str
    rule_name: str
    alert_types: List[AlertType]
    alert_levels: List[AlertLevel]
    conditions: Dict[str, Any]
    actions: List[ProcessingAction]
    enabled: bool = True
    priority: int = 100  # 数值越小优先级越高
    description: str = ""


@dataclass
class ProcessingResult:
    """处理结果"""
    alert_id: str
    processing_id: str
    status: ProcessingStatus
    actions_taken: List[ProcessingAction]
    notifications_sent: int
    processing_time: float  # 处理耗时（秒）
    error_message: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class AutomationConfig:
    """自动化配置"""
    enabled: bool = True
    max_concurrent_processing: int = 10
    processing_timeout: int = 300  # 处理超时时间（秒）
    retry_attempts: int = 3
    retry_delay: int = 60  # 重试延迟（秒）
    escalation_delay: int = 3600  # 升级延迟（秒）


class AlertProcessor:
    """预警处理器"""
    
    def __init__(self, alert_engine: AlertEngine, rule_manager: RuleManager,
                 notification_sender: NotificationSender):
        self.alert_engine = alert_engine
        self.rule_manager = rule_manager
        self.notification_sender = notification_sender
        
        # 处理存储
        self.processing_results: List[ProcessingResult] = []
        self.processing_rules: Dict[str, ProcessingRule] = {}
        
        # 自动化配置
        self.automation_config = AutomationConfig()
        
        # 用户配置
        self.user_assignments = {
            "default": ["admin", "manager"],  # 默认通知用户
            "critical": ["admin", "manager", "on_call"],  # 紧急预警通知用户
            "high": ["admin", "manager"],
            "medium": ["manager"],
            "low": ["manager"]
        }
        
        # 处理队列
        self.processing_queue: List[Alert] = []
        self.processing_semaphore = asyncio.Semaphore(self.automation_config.max_concurrent_processing)
        
        # 初始化默认处理规则
        self._initialize_default_processing_rules()
    
    def _initialize_default_processing_rules(self):
        """初始化默认处理规则"""
        # 紧急预警处理规则
        critical_rule = ProcessingRule(
            rule_id="critical_alert_processing",
            rule_name="紧急预警处理",
            alert_types=list(AlertType),
            alert_levels=[AlertLevel.CRITICAL],
            conditions={},
            actions=[
                ProcessingAction.NOTIFY_USERS,
                ProcessingAction.ESCALATE
            ],
            priority=10,
            description="紧急预警立即通知并升级处理"
        )
        
        # 高级预警处理规则
        high_rule = ProcessingRule(
            rule_id="high_alert_processing",
            rule_name="高级预警处理",
            alert_types=list(AlertType),
            alert_levels=[AlertLevel.HIGH],
            conditions={},
            actions=[
                ProcessingAction.NOTIFY_USERS
            ],
            priority=20,
            description="高级预警通知相关用户"
        )
        
        # 中级预警处理规则
        medium_rule = ProcessingRule(
            rule_id="medium_alert_processing",
            rule_name="中级预警处理",
            alert_types=list(AlertType),
            alert_levels=[AlertLevel.MEDIUM],
            conditions={},
            actions=[
                ProcessingAction.NOTIFY_USERS
            ],
            priority=30,
            description="中级预警通知管理员"
        )
        
        # 低级预警处理规则
        low_rule = ProcessingRule(
            rule_id="low_alert_processing",
            rule_name="低级预警处理",
            alert_types=list(AlertType),
            alert_levels=[AlertLevel.LOW],
            conditions={},
            actions=[
                ProcessingAction.LOG_ONLY
            ],
            priority=40,
            description="低级预警仅记录日志"
        )
        
        # 信息预警处理规则
        info_rule = ProcessingRule(
            rule_id="info_alert_processing",
            rule_name="信息预警处理",
            alert_types=list(AlertType),
            alert_levels=[AlertLevel.INFO],
            conditions={},
            actions=[
                ProcessingAction.AUTO_ACKNOWLEDGE,
                ProcessingAction.LOG_ONLY
            ],
            priority=50,
            description="信息预警自动确认并记录"
        )
        
        # 库存不足特殊处理
        inventory_rule = ProcessingRule(
            rule_id="inventory_shortage_processing",
            rule_name="库存不足特殊处理",
            alert_types=[AlertType.INVENTORY_SHORTAGE],
            alert_levels=[AlertLevel.CRITICAL, AlertLevel.HIGH],
            conditions={},
            actions=[
                ProcessingAction.NOTIFY_USERS,
                ProcessingAction.ESCALATE
            ],
            priority=5,
            description="库存不足预警特殊处理流程"
        )
        
        # 利润预警特殊处理
        profit_rule = ProcessingRule(
            rule_id="profit_alert_processing",
            rule_name="利润预警特殊处理",
            alert_types=[AlertType.PROFIT_MARGIN_DROP],
            alert_levels=[AlertLevel.CRITICAL],
            conditions={},
            actions=[
                ProcessingAction.NOTIFY_USERS,
                ProcessingAction.AUTO_ACKNOWLEDGE
            ],
            priority=15,
            description="利润预警需要立即关注"
        )
        
        # 注册所有规则
        rules = [critical_rule, high_rule, medium_rule, low_rule, info_rule, inventory_rule, profit_rule]
        for rule in rules:
            self.processing_rules[rule.rule_id] = rule
    
    async def process_alerts(self, alerts: List[Alert]) -> List[ProcessingResult]:
        """
        处理预警列表
        
        Args:
            alerts: 预警列表
        
        Returns:
            List[ProcessingResult]: 处理结果列表
        """
        try:
            logger.info(f"开始处理预警: {len(alerts)} 个")
            
            if not self.automation_config.enabled:
                logger.info("自动化处理已禁用")
                return []
            
            # 添加到处理队列
            self.processing_queue.extend(alerts)
            
            # 并发处理预警
            tasks = []
            for alert in alerts:
                task = self._process_single_alert(alert)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            processing_results = []
            for result in results:
                if isinstance(result, ProcessingResult):
                    processing_results.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"预警处理异常: {result}")
            
            # 保存处理结果
            self.processing_results.extend(processing_results)
            
            logger.info(f"预警处理完成: {len(processing_results)} 个结果")
            return processing_results
            
        except Exception as e:
            logger.error(f"批量处理预警失败: {e}")
            return []
    
    async def _process_single_alert(self, alert: Alert) -> ProcessingResult:
        """处理单个预警"""
        processing_id = f"proc_{alert.alert_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        
        async with self.processing_semaphore:
            try:
                logger.info(f"开始处理预警: {alert.alert_id}")
                
                # 查找适用的处理规则
                applicable_rules = self._find_applicable_rules(alert)
                if not applicable_rules:
                    logger.warning(f"未找到适用的处理规则: {alert.alert_id}")
                    return ProcessingResult(
                        alert_id=alert.alert_id,
                        processing_id=processing_id,
                        status=ProcessingStatus.SKIPPED,
                        actions_taken=[],
                        notifications_sent=0,
                        processing_time=0,
                        error_message="未找到适用的处理规则"
                    )
                
                # 按优先级排序规则
                applicable_rules.sort(key=lambda x: x.priority)
                
                # 执行处理动作
                actions_taken = []
                notifications_sent = 0
                
                for rule in applicable_rules:
                    for action in rule.actions:
                        try:
                            success, notification_count = await self._execute_action(alert, action)
                            if success:
                                actions_taken.append(action)
                                notifications_sent += notification_count
                        except Exception as e:
                            logger.error(f"执行处理动作失败: {action.value}, {e}")
                
                # 计算处理时间
                processing_time = (datetime.now() - start_time).total_seconds()
                
                result = ProcessingResult(
                    alert_id=alert.alert_id,
                    processing_id=processing_id,
                    status=ProcessingStatus.COMPLETED,
                    actions_taken=actions_taken,
                    notifications_sent=notifications_sent,
                    processing_time=processing_time
                )
                
                logger.info(f"预警处理完成: {alert.alert_id}, 动作: {len(actions_taken)}, 通知: {notifications_sent}")
                return result
                
            except Exception as e:
                processing_time = (datetime.now() - start_time).total_seconds()
                logger.error(f"处理预警失败: {alert.alert_id}, {e}")
                
                return ProcessingResult(
                    alert_id=alert.alert_id,
                    processing_id=processing_id,
                    status=ProcessingStatus.FAILED,
                    actions_taken=[],
                    notifications_sent=0,
                    processing_time=processing_time,
                    error_message=str(e)
                )
    
    def _find_applicable_rules(self, alert: Alert) -> List[ProcessingRule]:
        """查找适用的处理规则"""
        applicable_rules = []
        
        for rule in self.processing_rules.values():
            if not rule.enabled:
                continue
            
            # 检查预警类型
            if alert.alert_type not in rule.alert_types:
                continue
            
            # 检查预警级别
            if alert.alert_level not in rule.alert_levels:
                continue
            
            # 检查其他条件
            if self._check_rule_conditions(alert, rule.conditions):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    def _check_rule_conditions(self, alert: Alert, conditions: Dict[str, Any]) -> bool:
        """检查规则条件"""
        # 这里可以实现更复杂的条件检查逻辑
        # 目前简化处理，返回True
        return True

    async def _execute_action(self, alert: Alert, action: ProcessingAction) -> Tuple[bool, int]:
        """
        执行处理动作

        Args:
            alert: 预警信息
            action: 处理动作

        Returns:
            Tuple[bool, int]: (是否成功, 通知数量)
        """
        try:
            if action == ProcessingAction.NOTIFY_USERS:
                return await self._notify_users(alert)
            elif action == ProcessingAction.AUTO_ACKNOWLEDGE:
                return await self._auto_acknowledge(alert)
            elif action == ProcessingAction.AUTO_RESOLVE:
                return await self._auto_resolve(alert)
            elif action == ProcessingAction.ESCALATE:
                return await self._escalate_alert(alert)
            elif action == ProcessingAction.SUPPRESS:
                return await self._suppress_alert(alert)
            elif action == ProcessingAction.LOG_ONLY:
                return await self._log_only(alert)
            else:
                logger.warning(f"未知的处理动作: {action.value}")
                return False, 0

        except Exception as e:
            logger.error(f"执行动作失败: {action.value}, {alert.alert_id}, {e}")
            return False, 0

    async def _notify_users(self, alert: Alert) -> Tuple[bool, int]:
        """通知用户"""
        try:
            # 根据预警级别确定通知用户
            level_key = alert.alert_level.value
            user_ids = self.user_assignments.get(level_key, self.user_assignments["default"])

            # 发送通知
            notification_records = await self.notification_sender.send_alert_notification(alert, user_ids)

            logger.info(f"用户通知完成: {alert.alert_id}, 通知数: {len(notification_records)}")
            return True, len(notification_records)

        except Exception as e:
            logger.error(f"通知用户失败: {alert.alert_id}, {e}")
            return False, 0

    async def _auto_acknowledge(self, alert: Alert) -> Tuple[bool, int]:
        """自动确认预警"""
        try:
            success = await self.alert_engine.acknowledge_alert(alert.alert_id)
            if success:
                logger.info(f"预警自动确认: {alert.alert_id}")
                return True, 0
            else:
                logger.warning(f"预警自动确认失败: {alert.alert_id}")
                return False, 0

        except Exception as e:
            logger.error(f"自动确认失败: {alert.alert_id}, {e}")
            return False, 0

    async def _auto_resolve(self, alert: Alert) -> Tuple[bool, int]:
        """自动解决预警"""
        try:
            success = await self.alert_engine.resolve_alert(alert.alert_id)
            if success:
                logger.info(f"预警自动解决: {alert.alert_id}")
                return True, 0
            else:
                logger.warning(f"预警自动解决失败: {alert.alert_id}")
                return False, 0

        except Exception as e:
            logger.error(f"自动解决失败: {alert.alert_id}, {e}")
            return False, 0

    async def _escalate_alert(self, alert: Alert) -> Tuple[bool, int]:
        """升级预警"""
        try:
            # 升级到更高级别的用户
            escalation_users = self.user_assignments.get("critical", ["admin"])

            # 发送升级通知
            notification_records = await self.notification_sender.send_alert_notification(alert, escalation_users)

            logger.info(f"预警升级完成: {alert.alert_id}, 升级通知数: {len(notification_records)}")
            return True, len(notification_records)

        except Exception as e:
            logger.error(f"预警升级失败: {alert.alert_id}, {e}")
            return False, 0

    async def _suppress_alert(self, alert: Alert) -> Tuple[bool, int]:
        """抑制预警"""
        try:
            # 将预警标记为已忽略
            success = await self.alert_engine.dismiss_alert(alert.alert_id)
            if success:
                logger.info(f"预警已抑制: {alert.alert_id}")
                return True, 0
            else:
                logger.warning(f"预警抑制失败: {alert.alert_id}")
                return False, 0

        except Exception as e:
            logger.error(f"抑制预警失败: {alert.alert_id}, {e}")
            return False, 0

    async def _log_only(self, alert: Alert) -> Tuple[bool, int]:
        """仅记录日志"""
        try:
            logger.info(f"预警记录: {alert.alert_id} - {alert.title} - {alert.description}")
            return True, 0
        except Exception as e:
            logger.error(f"记录预警失败: {alert.alert_id}, {e}")
            return False, 0

    def add_processing_rule(self, rule: ProcessingRule) -> bool:
        """
        添加处理规则

        Args:
            rule: 处理规则

        Returns:
            bool: 是否成功添加
        """
        try:
            if rule.rule_id in self.processing_rules:
                logger.warning(f"处理规则已存在: {rule.rule_id}")
                return False

            self.processing_rules[rule.rule_id] = rule
            logger.info(f"处理规则已添加: {rule.rule_id}")
            return True

        except Exception as e:
            logger.error(f"添加处理规则失败: {rule.rule_id}, {e}")
            return False

    def update_processing_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新处理规则

        Args:
            rule_id: 规则ID
            updates: 更新内容

        Returns:
            bool: 是否成功更新
        """
        try:
            if rule_id not in self.processing_rules:
                logger.error(f"处理规则不存在: {rule_id}")
                return False

            rule = self.processing_rules[rule_id]

            # 更新规则属性
            for key, value in updates.items():
                if hasattr(rule, key):
                    # 处理枚举类型
                    if key == "alert_types":
                        rule.alert_types = [AlertType(at) for at in value]
                    elif key == "alert_levels":
                        rule.alert_levels = [AlertLevel(al) for al in value]
                    elif key == "actions":
                        rule.actions = [ProcessingAction(pa) for pa in value]
                    else:
                        setattr(rule, key, value)

            logger.info(f"处理规则已更新: {rule_id}")
            return True

        except Exception as e:
            logger.error(f"更新处理规则失败: {rule_id}, {e}")
            return False

    def delete_processing_rule(self, rule_id: str) -> bool:
        """
        删除处理规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 是否成功删除
        """
        try:
            if rule_id in self.processing_rules:
                del self.processing_rules[rule_id]
                logger.info(f"处理规则已删除: {rule_id}")
                return True
            else:
                logger.error(f"处理规则不存在: {rule_id}")
                return False

        except Exception as e:
            logger.error(f"删除处理规则失败: {rule_id}, {e}")
            return False

    def update_user_assignments(self, assignments: Dict[str, List[str]]) -> bool:
        """
        更新用户分配

        Args:
            assignments: 用户分配配置

        Returns:
            bool: 是否成功更新
        """
        try:
            self.user_assignments.update(assignments)
            logger.info("用户分配已更新")
            return True
        except Exception as e:
            logger.error(f"更新用户分配失败: {e}")
            return False

    def get_processing_results(self, alert_id: Optional[str] = None,
                             status: Optional[ProcessingStatus] = None,
                             limit: Optional[int] = None) -> List[ProcessingResult]:
        """
        获取处理结果

        Args:
            alert_id: 预警ID过滤
            status: 状态过滤
            limit: 数量限制

        Returns:
            List[ProcessingResult]: 处理结果列表
        """
        filtered_results = self.processing_results.copy()

        if alert_id:
            filtered_results = [r for r in filtered_results if r.alert_id == alert_id]

        if status:
            filtered_results = [r for r in filtered_results if r.status == status]

        # 按创建时间倒序排序
        filtered_results.sort(key=lambda x: x.created_at, reverse=True)

        if limit:
            filtered_results = filtered_results[:limit]

        return filtered_results

    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            total_processing = len(self.processing_results)

            # 按状态统计
            status_distribution = {}
            for result in self.processing_results:
                status = result.status.value
                status_distribution[status] = status_distribution.get(status, 0) + 1

            # 按动作统计
            action_distribution = {}
            for result in self.processing_results:
                for action in result.actions_taken:
                    action_name = action.value
                    action_distribution[action_name] = action_distribution.get(action_name, 0) + 1

            # 成功率统计
            completed_count = status_distribution.get("completed", 0)
            success_rate = (completed_count / total_processing * 100) if total_processing > 0 else 0

            # 平均处理时间
            processing_times = [r.processing_time for r in self.processing_results if r.processing_time > 0]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0

            # 通知统计
            total_notifications = sum(r.notifications_sent for r in self.processing_results)

            # 最近24小时统计
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_processing = [r for r in self.processing_results if r.created_at > recent_cutoff]

            return {
                "total_processing": total_processing,
                "total_rules": len(self.processing_rules),
                "enabled_rules": len([r for r in self.processing_rules.values() if r.enabled]),
                "success_rate": success_rate,
                "avg_processing_time": avg_processing_time,
                "total_notifications": total_notifications,
                "recent_24h_count": len(recent_processing),
                "status_distribution": status_distribution,
                "action_distribution": action_distribution,
                "automation_enabled": self.automation_config.enabled,
                "available_actions": [pa.value for pa in ProcessingAction],
                "available_statuses": [ps.value for ps in ProcessingStatus]
            }

        except Exception as e:
            logger.error(f"获取处理统计失败: {e}")
            return {
                "total_processing": 0,
                "total_rules": 0,
                "enabled_rules": 0,
                "success_rate": 0,
                "avg_processing_time": 0,
                "total_notifications": 0,
                "recent_24h_count": 0,
                "status_distribution": {},
                "action_distribution": {},
                "automation_enabled": False,
                "available_actions": [pa.value for pa in ProcessingAction],
                "available_statuses": [ps.value for ps in ProcessingStatus]
            }
