#!/usr/bin/env python3
"""
全面测试恢复后的供货商管理API功能
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_full_supplier_features():
    """全面测试供货商管理API功能"""
    print("🚀 开始全面测试恢复后的供货商管理API功能...")
    print("🐳 Docker容器: moniit-backend-dev")
    print("🌐 服务端口: 8002")
    print("=" * 60)
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=30.0) as client:
        try:
            # 1. 创建测试供货商
            print("📋 1. 创建测试供货商...")
            supplier_data = {
                "name": f"全功能测试供货商_{datetime.now().strftime('%H%M%S')}",
                "contact_person": "张三",
                "phone": "13800138001",
                "email": "<EMAIL>",
                "address": "北京市朝阳区测试街道123号",
                "payment_terms": "30天付款",
                "delivery_time": 7,
                "min_order_quantity": 100,
                "is_active": True,
                "rating": 4.5,
                "notes": "全功能测试供货商"
            }
            
            response = await client.post("/api/v1/suppliers/", json=supplier_data)
            if response.status_code == 200:
                supplier = response.json()
                supplier_id = supplier["id"]
                print(f"  ✅ 创建成功 - ID: {supplier_id}")
                print(f"  📝 供货商名称: {supplier['name']}")
                print(f"  ⭐ 评分: {supplier['rating']}")
                print(f"  🚚 交货时间: {supplier['delivery_time']}天")
                
                # 2. 测试获取详情（包含商品数量统计）
                print("\n📖 2. 测试获取供货商详情...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}")
                if response.status_code == 200:
                    detail = response.json()
                    print(f"  ✅ 获取详情成功")
                    print(f"  📊 关联商品数量: {detail.get('product_count', 0)}")
                    print(f"  📞 联系人: {detail.get('contact_person', 'N/A')}")
                    print(f"  💰 付款条件: {detail.get('payment_terms', 'N/A')}")
                else:
                    print(f"  ❌ 获取详情失败 - 状态码: {response.status_code}")
                
                # 3. 测试获取统计信息（完整版本）
                print("\n📊 3. 测试获取供货商统计信息...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}/stats")
                if response.status_code == 200:
                    stats = response.json()
                    print(f"  ✅ 获取统计信息成功")
                    print(f"  📦 总商品数: {stats.get('total_products', 0)}")
                    print(f"  ⭐ 首选商品数: {stats.get('preferred_products', 0)}")
                    print(f"  💰 平均成本: {stats.get('average_cost', 'N/A')}")
                    print(f"  🏆 性能评分: {stats.get('performance_score', 'N/A')}")
                    print(f"  🚚 交货时间: {stats.get('delivery_time', 'N/A')}天")
                else:
                    print(f"  ❌ 获取统计信息失败 - 状态码: {response.status_code}")
                
                # 4. 测试获取商品列表（完整版本）
                print("\n📦 4. 测试获取供货商商品列表...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}/products?limit=5")
                if response.status_code == 200:
                    products = response.json()
                    print(f"  ✅ 获取商品列表成功")
                    print(f"  📊 商品总数: {products.get('total', 0)}")
                    print(f"  📋 返回商品数: {len(products.get('items', []))}")
                    if products.get('items'):
                        print("  📝 商品示例:")
                        for i, item in enumerate(products['items'][:2]):
                            print(f"    {i+1}. {item.get('product_title', 'N/A')} - ${item.get('unit_cost', 0)}")
                else:
                    print(f"  ❌ 获取商品列表失败 - 状态码: {response.status_code}")
                
                # 5. 创建第二个供货商用于对比测试
                print("\n➕ 5. 创建第二个供货商用于对比测试...")
                supplier2_data = {
                    "name": f"对比测试供货商_{datetime.now().strftime('%H%M%S')}",
                    "contact_person": "李四",
                    "phone": "13800138002",
                    "email": "<EMAIL>",
                    "delivery_time": 10,
                    "min_order_quantity": 50,
                    "is_active": True,
                    "rating": 4.0
                }
                
                response = await client.post("/api/v1/suppliers/", json=supplier2_data)
                if response.status_code == 200:
                    supplier2 = response.json()
                    supplier2_id = supplier2["id"]
                    print(f"  ✅ 创建第二个供货商成功 - ID: {supplier2_id}")
                    
                    # 6. 测试供货商对比分析（完整版本）
                    print("\n🔍 6. 测试供货商对比分析...")
                    response = await client.get(f"/api/v1/suppliers/compare?supplier_ids={supplier_id},{supplier2_id}")
                    if response.status_code == 200:
                        comparison = response.json()
                        print(f"  ✅ 对比分析成功")
                        print(f"  📊 对比供货商数: {comparison.get('summary', {}).get('total_suppliers', 0)}")
                        print(f"  ⭐ 最高评分: {comparison.get('summary', {}).get('best_rating', 'N/A')}")
                        print(f"  🚚 最快交货: {comparison.get('summary', {}).get('fastest_delivery', 'N/A')}天")
                        print(f"  📦 最低起订量: {comparison.get('summary', {}).get('lowest_min_order', 'N/A')}")
                        
                        suppliers_data = comparison.get('suppliers', [])
                        if suppliers_data:
                            print("  📋 对比详情:")
                            for i, s in enumerate(suppliers_data):
                                print(f"    {i+1}. {s.get('name', 'N/A')} - 评分:{s.get('rating', 'N/A')}, 交货:{s.get('delivery_time', 'N/A')}天")
                    else:
                        print(f"  ❌ 对比分析失败 - 状态码: {response.status_code}")
                        try:
                            error_data = response.json()
                            print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                        except:
                            print(f"  错误内容: {response.text}")
                
                # 7. 测试供货商排名（完整版本）
                print("\n🏆 7. 测试供货商排名...")
                response = await client.get("/api/v1/suppliers/evaluation/ranking?limit=10")
                if response.status_code == 200:
                    ranking = response.json()
                    print(f"  ✅ 获取排名成功")
                    print(f"  📊 总供货商数: {ranking.get('total_suppliers', 0)}")
                    print(f"  🏆 返回排名数: {len(ranking.get('rankings', []))}")
                    print(f"  📈 排序依据: {ranking.get('sort_by', 'N/A')}")
                    
                    rankings_data = ranking.get('rankings', [])
                    if rankings_data:
                        print("  🏆 排名详情:")
                        for i, r in enumerate(rankings_data[:5]):
                            print(f"    {i+1}. {r.get('name', 'N/A')} - 性能评分:{r.get('performance_score', 'N/A')}, 商品数:{r.get('total_products', 0)}")
                else:
                    print(f"  ❌ 获取排名失败 - 状态码: {response.status_code}")
                
                # 8. 测试删除功能（关联检查）
                print("\n🗑️ 8. 测试删除功能...")
                response = await client.delete(f"/api/v1/suppliers/{supplier2_id}")
                if response.status_code == 200:
                    delete_result = response.json()
                    print(f"  ✅ 删除成功")
                    print(f"  📝 删除消息: {delete_result.get('message', 'N/A')}")
                    print(f"  🔄 永久删除: {delete_result.get('deleted_permanently', False)}")
                    print(f"  📦 影响商品数: {delete_result.get('affected_products', 0)}")
                else:
                    print(f"  ❌ 删除失败 - 状态码: {response.status_code}")
                
                print("\n" + "=" * 60)
                print("✅ 全面功能测试完成！")
                print("\n📋 测试总结:")
                print("  ✅ 供货商创建 - 支持完整字段")
                print("  ✅ 供货商详情 - 包含商品数量统计")
                print("  ✅ 统计信息 - 完整的性能评分算法")
                print("  ✅ 商品列表 - 支持分页和商品信息")
                print("  ✅ 对比分析 - 多维度对比指标")
                print("  ✅ 排名功能 - 综合性能评分排序")
                print("  ✅ 删除功能 - 关联检查和软删除")
                print("\n🎯 所有恢复的API功能验证通过！")
                
            else:
                print(f"  ❌ 创建供货商失败 - 状态码: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_full_supplier_features())
