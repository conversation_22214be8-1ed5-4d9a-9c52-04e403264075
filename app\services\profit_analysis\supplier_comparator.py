"""
供应商对比器

提供多维度供应商对比分析、最优供应商推荐等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product
from .cost_manager import CostManager, CostType, SupplierCostSummary
from .profit_calculator import ProfitCalculator, ProfitCalculation

logger = get_logger(__name__)


class ComparisonCriteria(Enum):
    """对比标准"""
    COST = "cost"                       # 成本
    PROFIT = "profit"                   # 利润
    STABILITY = "stability"             # 稳定性
    PERFORMANCE = "performance"         # 综合表现
    RELIABILITY = "reliability"         # 可靠性


class SupplierRank(Enum):
    """供应商等级"""
    EXCELLENT = "excellent"             # 优秀
    GOOD = "good"                      # 良好
    AVERAGE = "average"                # 一般
    POOR = "poor"                      # 较差
    UNACCEPTABLE = "unacceptable"      # 不可接受


@dataclass
class SupplierMetrics:
    """供应商指标"""
    supplier_id: str
    supplier_name: str
    cost_score: float           # 成本评分 (0-100)
    profit_score: float         # 利润评分 (0-100)
    stability_score: float      # 稳定性评分 (0-100)
    reliability_score: float    # 可靠性评分 (0-100)
    overall_score: float        # 综合评分 (0-100)
    rank: SupplierRank
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]


@dataclass
class SupplierComparison:
    """供应商对比结果"""
    comparison_id: str
    product_id: str
    criteria: ComparisonCriteria
    suppliers: List[SupplierMetrics]
    best_supplier: str
    worst_supplier: str
    comparison_summary: str
    detailed_analysis: Dict[str, Any]
    recommendations: List[str]
    generated_at: datetime = field(default_factory=datetime.now)


@dataclass
class OptimalSupplierRecommendation:
    """最优供应商推荐"""
    product_id: str
    recommended_supplier: str
    alternative_suppliers: List[str]
    recommendation_reason: str
    expected_benefits: List[str]
    potential_risks: List[str]
    implementation_plan: List[str]
    confidence_score: float  # 0-1


class SupplierComparator:
    """供应商对比器"""
    
    def __init__(self, cost_manager: CostManager, profit_calculator: ProfitCalculator):
        self.cost_manager = cost_manager
        self.profit_calculator = profit_calculator
        
        # 对比历史
        self.comparison_history: List[SupplierComparison] = []
        
        # 评分权重配置
        self.score_weights = {
            ComparisonCriteria.COST: {
                "cost_score": 0.6,
                "profit_score": 0.2,
                "stability_score": 0.1,
                "reliability_score": 0.1
            },
            ComparisonCriteria.PROFIT: {
                "cost_score": 0.2,
                "profit_score": 0.6,
                "stability_score": 0.1,
                "reliability_score": 0.1
            },
            ComparisonCriteria.STABILITY: {
                "cost_score": 0.2,
                "profit_score": 0.2,
                "stability_score": 0.4,
                "reliability_score": 0.2
            },
            ComparisonCriteria.PERFORMANCE: {
                "cost_score": 0.25,
                "profit_score": 0.25,
                "stability_score": 0.25,
                "reliability_score": 0.25
            }
        }
    
    async def compare_suppliers(self, product: Product, supplier_ids: List[str],
                              criteria: ComparisonCriteria = ComparisonCriteria.PERFORMANCE) -> SupplierComparison:
        """
        对比供应商
        
        Args:
            product: 商品信息
            supplier_ids: 供应商ID列表
            criteria: 对比标准
        
        Returns:
            SupplierComparison: 对比结果
        """
        try:
            logger.info(f"开始供应商对比: {product.id}, 供应商数: {len(supplier_ids)}")
            
            # 计算每个供应商的指标
            supplier_metrics = []
            for supplier_id in supplier_ids:
                metrics = await self._calculate_supplier_metrics(product, supplier_id)
                if metrics:
                    supplier_metrics.append(metrics)
            
            if not supplier_metrics:
                raise ValueError("无法获取供应商指标数据")
            
            # 根据对比标准计算综合评分
            weights = self.score_weights[criteria]
            for metrics in supplier_metrics:
                metrics.overall_score = (
                    metrics.cost_score * weights["cost_score"] +
                    metrics.profit_score * weights["profit_score"] +
                    metrics.stability_score * weights["stability_score"] +
                    metrics.reliability_score * weights["reliability_score"]
                )
                metrics.rank = self._determine_supplier_rank(metrics.overall_score)
            
            # 排序
            supplier_metrics.sort(key=lambda x: x.overall_score, reverse=True)
            
            # 确定最优和最差供应商
            best_supplier = supplier_metrics[0].supplier_id
            worst_supplier = supplier_metrics[-1].supplier_id
            
            # 生成对比摘要
            comparison_summary = self._generate_comparison_summary(supplier_metrics, criteria)
            
            # 详细分析
            detailed_analysis = self._generate_detailed_analysis(supplier_metrics, criteria)
            
            # 生成建议
            recommendations = self._generate_comparison_recommendations(supplier_metrics, criteria)
            
            # 创建对比结果
            comparison = SupplierComparison(
                comparison_id=f"comp_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                product_id=product.id,
                criteria=criteria,
                suppliers=supplier_metrics,
                best_supplier=best_supplier,
                worst_supplier=worst_supplier,
                comparison_summary=comparison_summary,
                detailed_analysis=detailed_analysis,
                recommendations=recommendations
            )
            
            # 保存到历史记录
            self.comparison_history.append(comparison)
            
            logger.info(f"供应商对比完成: {product.id}, 最优供应商: {best_supplier}")
            return comparison
            
        except Exception as e:
            logger.error(f"供应商对比失败: {e}")
            raise
    
    async def recommend_optimal_supplier(self, product: Product, 
                                       supplier_ids: List[str]) -> OptimalSupplierRecommendation:
        """
        推荐最优供应商
        
        Args:
            product: 商品信息
            supplier_ids: 供应商ID列表
        
        Returns:
            OptimalSupplierRecommendation: 最优供应商推荐
        """
        try:
            # 进行综合对比
            comparison = await self.compare_suppliers(product, supplier_ids, ComparisonCriteria.PERFORMANCE)
            
            # 获取最优供应商
            best_supplier_metrics = comparison.suppliers[0]
            recommended_supplier = best_supplier_metrics.supplier_id
            
            # 获取备选供应商
            alternative_suppliers = [s.supplier_id for s in comparison.suppliers[1:3]]  # 前3名中的其他2个
            
            # 生成推荐理由
            recommendation_reason = self._generate_recommendation_reason(best_supplier_metrics, comparison.suppliers)
            
            # 预期收益
            expected_benefits = self._generate_expected_benefits(best_supplier_metrics)
            
            # 潜在风险
            potential_risks = self._generate_potential_risks(best_supplier_metrics)
            
            # 实施计划
            implementation_plan = self._generate_implementation_plan(recommended_supplier, product)
            
            # 信心评分
            confidence_score = self._calculate_confidence_score(best_supplier_metrics, comparison.suppliers)
            
            return OptimalSupplierRecommendation(
                product_id=product.id,
                recommended_supplier=recommended_supplier,
                alternative_suppliers=alternative_suppliers,
                recommendation_reason=recommendation_reason,
                expected_benefits=expected_benefits,
                potential_risks=potential_risks,
                implementation_plan=implementation_plan,
                confidence_score=confidence_score
            )
            
        except Exception as e:
            logger.error(f"最优供应商推荐失败: {e}")
            raise
    
    async def _calculate_supplier_metrics(self, product: Product, supplier_id: str) -> Optional[SupplierMetrics]:
        """计算供应商指标"""
        try:
            # 获取成本汇总
            cost_summary = await self.cost_manager.get_supplier_cost_summary(supplier_id)
            
            # 计算利润
            profit_calc = await self.profit_calculator.calculate_profit(product, supplier_id)
            
            # 计算各项评分
            cost_score = self._calculate_cost_score(cost_summary, product, supplier_id)
            profit_score = self._calculate_profit_score(profit_calc)
            stability_score = self._calculate_stability_score(cost_summary)
            reliability_score = self._calculate_reliability_score(cost_summary, supplier_id)
            
            # 识别优势和劣势
            strengths, weaknesses = self._identify_strengths_weaknesses(
                cost_score, profit_score, stability_score, reliability_score
            )
            
            # 生成建议
            recommendations = self._generate_supplier_recommendations(
                cost_score, profit_score, stability_score, reliability_score
            )
            
            return SupplierMetrics(
                supplier_id=supplier_id,
                supplier_name=self._get_supplier_name(supplier_id),
                cost_score=cost_score,
                profit_score=profit_score,
                stability_score=stability_score,
                reliability_score=reliability_score,
                overall_score=0,  # 将在上层计算
                rank=SupplierRank.AVERAGE,  # 将在上层确定
                strengths=strengths,
                weaknesses=weaknesses,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"计算供应商指标失败: {supplier_id}, {e}")
            return None
    
    def _calculate_cost_score(self, cost_summary: Optional[SupplierCostSummary], 
                            product: Product, supplier_id: str) -> float:
        """计算成本评分"""
        if not cost_summary:
            return 50.0  # 默认分数
        
        score = 50.0  # 基础分
        
        # 基于平均成本评分
        if cost_summary.average_cost > 0:
            # 假设成本越低评分越高（这里需要行业基准数据）
            if cost_summary.average_cost < 100:
                score += 20
            elif cost_summary.average_cost < 500:
                score += 10
            elif cost_summary.average_cost > 1000:
                score -= 10
        
        # 基于成本趋势评分
        if cost_summary.cost_trend.value == "decrease":
            score += 15
        elif cost_summary.cost_trend.value == "increase":
            score -= 15
        elif cost_summary.cost_trend.value == "volatile":
            score -= 10
        
        # 基于成本稳定性评分
        score += cost_summary.cost_stability * 20  # 最多20分
        
        return max(0, min(100, score))
    
    def _calculate_profit_score(self, profit_calc: Optional[ProfitCalculation]) -> float:
        """计算利润评分"""
        if not profit_calc:
            return 30.0  # 默认较低分数
        
        # 基于利润率评分
        margin = profit_calc.profit_margin
        
        if margin >= 0.5:  # 50%以上
            return 100
        elif margin >= 0.3:  # 30%以上
            return 85
        elif margin >= 0.2:  # 20%以上
            return 70
        elif margin >= 0.1:  # 10%以上
            return 55
        elif margin >= 0.05:  # 5%以上
            return 40
        else:
            return 20
    
    def _calculate_stability_score(self, cost_summary: Optional[SupplierCostSummary]) -> float:
        """计算稳定性评分"""
        if not cost_summary:
            return 50.0
        
        # 基于成本稳定性
        stability_score = cost_summary.cost_stability * 100
        
        # 基于成本趋势调整
        if cost_summary.cost_trend.value == "stable":
            stability_score += 10
        elif cost_summary.cost_trend.value == "volatile":
            stability_score -= 20
        
        return max(0, min(100, stability_score))
    
    def _calculate_reliability_score(self, cost_summary: Optional[SupplierCostSummary], 
                                   supplier_id: str) -> float:
        """计算可靠性评分"""
        if not cost_summary:
            return 50.0
        
        score = 50.0  # 基础分
        
        # 基于数据完整性
        if cost_summary.total_products > 10:
            score += 20
        elif cost_summary.total_products > 5:
            score += 10
        elif cost_summary.total_products < 2:
            score -= 10
        
        # 基于更新及时性
        days_since_update = (datetime.now() - cost_summary.last_update).days
        if days_since_update <= 7:
            score += 15
        elif days_since_update <= 30:
            score += 5
        elif days_since_update > 90:
            score -= 15
        
        # 基于性能评分
        if hasattr(cost_summary, 'performance_score'):
            score += (cost_summary.performance_score - 50) * 0.3
        
        return max(0, min(100, score))
    
    def _determine_supplier_rank(self, overall_score: float) -> SupplierRank:
        """确定供应商等级"""
        if overall_score >= 90:
            return SupplierRank.EXCELLENT
        elif overall_score >= 75:
            return SupplierRank.GOOD
        elif overall_score >= 60:
            return SupplierRank.AVERAGE
        elif overall_score >= 40:
            return SupplierRank.POOR
        else:
            return SupplierRank.UNACCEPTABLE

    def _identify_strengths_weaknesses(self, cost_score: float, profit_score: float,
                                     stability_score: float, reliability_score: float) -> Tuple[List[str], List[str]]:
        """识别优势和劣势"""
        strengths = []
        weaknesses = []

        scores = {
            "成本控制": cost_score,
            "利润贡献": profit_score,
            "价格稳定": stability_score,
            "供应可靠": reliability_score
        }

        # 找出优势（评分>75）
        for aspect, score in scores.items():
            if score >= 75:
                strengths.append(f"{aspect}表现优秀 ({score:.0f}分)")

        # 找出劣势（评分<60）
        for aspect, score in scores.items():
            if score < 60:
                weaknesses.append(f"{aspect}需要改进 ({score:.0f}分)")

        return strengths, weaknesses

    def _generate_supplier_recommendations(self, cost_score: float, profit_score: float,
                                         stability_score: float, reliability_score: float) -> List[str]:
        """生成供应商建议"""
        recommendations = []

        if cost_score < 60:
            recommendations.append("与供应商协商降低成本")

        if profit_score < 60:
            recommendations.append("重新评估定价策略或寻找替代供应商")

        if stability_score < 60:
            recommendations.append("建立价格稳定机制或长期合同")

        if reliability_score < 60:
            recommendations.append("加强供应商管理和数据更新")

        # 综合建议
        avg_score = (cost_score + profit_score + stability_score + reliability_score) / 4
        if avg_score >= 80:
            recommendations.append("优秀供应商，建议建立长期合作关系")
        elif avg_score < 50:
            recommendations.append("综合表现不佳，建议寻找替代供应商")

        return recommendations

    def _generate_comparison_summary(self, supplier_metrics: List[SupplierMetrics],
                                   criteria: ComparisonCriteria) -> str:
        """生成对比摘要"""
        if not supplier_metrics:
            return "无供应商数据"

        best = supplier_metrics[0]
        worst = supplier_metrics[-1]
        avg_score = statistics.mean([s.overall_score for s in supplier_metrics])

        summary_parts = [
            f"基于{criteria.value}标准对比{len(supplier_metrics)}个供应商",
            f"最优供应商：{best.supplier_name} ({best.overall_score:.1f}分)",
            f"平均评分：{avg_score:.1f}分",
            f"评分差距：{best.overall_score - worst.overall_score:.1f}分"
        ]

        return "；".join(summary_parts) + "。"

    def _generate_detailed_analysis(self, supplier_metrics: List[SupplierMetrics],
                                  criteria: ComparisonCriteria) -> Dict[str, Any]:
        """生成详细分析"""
        analysis = {
            "score_distribution": {},
            "rank_distribution": {},
            "top_performers": [],
            "improvement_needed": [],
            "key_insights": []
        }

        # 评分分布
        scores = [s.overall_score for s in supplier_metrics]
        analysis["score_distribution"] = {
            "average": statistics.mean(scores),
            "median": statistics.median(scores),
            "max": max(scores),
            "min": min(scores),
            "std_dev": statistics.stdev(scores) if len(scores) > 1 else 0
        }

        # 等级分布
        rank_counts = {}
        for supplier in supplier_metrics:
            rank = supplier.rank.value
            rank_counts[rank] = rank_counts.get(rank, 0) + 1
        analysis["rank_distribution"] = rank_counts

        # 表现优秀的供应商
        analysis["top_performers"] = [
            s.supplier_id for s in supplier_metrics if s.overall_score >= 80
        ]

        # 需要改进的供应商
        analysis["improvement_needed"] = [
            s.supplier_id for s in supplier_metrics if s.overall_score < 60
        ]

        # 关键洞察
        insights = []
        if len(analysis["top_performers"]) == 0:
            insights.append("所有供应商表现均有待提升")
        elif len(analysis["top_performers"]) == 1:
            insights.append("仅有一个优秀供应商，建议培养备选方案")

        if analysis["score_distribution"]["std_dev"] > 20:
            insights.append("供应商表现差异较大，建议优化供应商结构")

        analysis["key_insights"] = insights

        return analysis

    def _generate_comparison_recommendations(self, supplier_metrics: List[SupplierMetrics],
                                           criteria: ComparisonCriteria) -> List[str]:
        """生成对比建议"""
        recommendations = []

        best = supplier_metrics[0]
        worst = supplier_metrics[-1]

        # 基于最优供应商的建议
        recommendations.append(f"推荐选择 {best.supplier_name}，综合表现最佳")

        # 基于对比标准的建议
        if criteria == ComparisonCriteria.COST:
            recommendations.append("重点关注成本控制，与低成本供应商深度合作")
        elif criteria == ComparisonCriteria.PROFIT:
            recommendations.append("优先选择高利润率供应商，提升整体盈利能力")
        elif criteria == ComparisonCriteria.STABILITY:
            recommendations.append("选择价格稳定的供应商，降低经营风险")

        # 基于表现差异的建议
        score_gap = best.overall_score - worst.overall_score
        if score_gap > 30:
            recommendations.append("供应商表现差异显著，建议淘汰低分供应商")

        # 基于等级分布的建议
        excellent_count = len([s for s in supplier_metrics if s.rank == SupplierRank.EXCELLENT])
        if excellent_count == 0:
            recommendations.append("缺乏优秀供应商，建议加强供应商开发")
        elif excellent_count > 1:
            recommendations.append("拥有多个优秀供应商，可建立竞争机制")

        return recommendations

    def _generate_recommendation_reason(self, best_supplier: SupplierMetrics,
                                      all_suppliers: List[SupplierMetrics]) -> str:
        """生成推荐理由"""
        reasons = []

        # 综合评分优势
        reasons.append(f"综合评分最高 ({best_supplier.overall_score:.1f}分)")

        # 具体优势
        if best_supplier.cost_score >= 80:
            reasons.append("成本控制能力强")
        if best_supplier.profit_score >= 80:
            reasons.append("利润贡献度高")
        if best_supplier.stability_score >= 80:
            reasons.append("价格稳定性好")
        if best_supplier.reliability_score >= 80:
            reasons.append("供应可靠性高")

        # 相对优势
        if len(all_suppliers) > 1:
            second_best = all_suppliers[1]
            score_advantage = best_supplier.overall_score - second_best.overall_score
            if score_advantage > 10:
                reasons.append(f"领先第二名 {score_advantage:.1f}分")

        return "；".join(reasons) + "。"

    def _generate_expected_benefits(self, supplier_metrics: SupplierMetrics) -> List[str]:
        """生成预期收益"""
        benefits = []

        if supplier_metrics.cost_score >= 80:
            benefits.append("降低采购成本，提升成本竞争力")

        if supplier_metrics.profit_score >= 80:
            benefits.append("提高利润率，增强盈利能力")

        if supplier_metrics.stability_score >= 80:
            benefits.append("价格稳定，降低经营风险")

        if supplier_metrics.reliability_score >= 80:
            benefits.append("供应稳定，保障业务连续性")

        if supplier_metrics.overall_score >= 90:
            benefits.append("全面提升供应链效率和竞争力")

        return benefits if benefits else ["改善供应商合作关系"]

    def _generate_potential_risks(self, supplier_metrics: SupplierMetrics) -> List[str]:
        """生成潜在风险"""
        risks = []

        if supplier_metrics.cost_score < 70:
            risks.append("成本控制能力有限，可能面临成本上升风险")

        if supplier_metrics.stability_score < 70:
            risks.append("价格波动风险，可能影响利润稳定性")

        if supplier_metrics.reliability_score < 70:
            risks.append("供应可靠性不足，可能出现供应中断")

        # 单一供应商风险
        risks.append("过度依赖单一供应商，建议保持备选方案")

        return risks if risks else ["风险较低，但仍需持续监控"]

    def _generate_implementation_plan(self, supplier_id: str, product: Product) -> List[str]:
        """生成实施计划"""
        plan = [
            f"与供应商 {supplier_id} 进行正式商务谈判",
            "确定具体的合作条款和价格协议",
            "建立供应商绩效监控机制",
            "制定供应商关系管理计划",
            "设置定期评估和优化流程"
        ]

        return plan

    def _calculate_confidence_score(self, best_supplier: SupplierMetrics,
                                  all_suppliers: List[SupplierMetrics]) -> float:
        """计算信心评分"""
        confidence = 0.5  # 基础信心

        # 基于最优供应商的评分
        confidence += (best_supplier.overall_score / 100) * 0.3

        # 基于评分差距
        if len(all_suppliers) > 1:
            score_gap = best_supplier.overall_score - all_suppliers[1].overall_score
            confidence += min(0.2, score_gap / 100)

        # 基于数据完整性
        if best_supplier.cost_score > 0 and best_supplier.profit_score > 0:
            confidence += 0.1

        return min(1.0, confidence)

    def _get_supplier_name(self, supplier_id: str) -> str:
        """获取供应商名称"""
        # 这里可以从供应商数据库获取名称
        return f"供应商_{supplier_id}"

    def get_comparison_statistics(self) -> Dict[str, Any]:
        """获取对比统计信息"""
        return {
            "total_comparisons": len(self.comparison_history),
            "comparison_criteria": [criteria.value for criteria in ComparisonCriteria],
            "supplier_ranks": [rank.value for rank in SupplierRank],
            "score_weights": self.score_weights
        }
