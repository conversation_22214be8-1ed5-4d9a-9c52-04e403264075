"""
成本管理系统测试

测试成本管理、利润计算、供应商对比、机会发现等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
from app.services.profit_analysis.cost_manager import (
    CostManager, CostRecord, CostType, AlertLevel, CostChangeType
)
from app.services.profit_analysis.profit_calculator import (
    ProfitCalculator, ProfitMarginLevel, PricingStrategy
)
from app.services.profit_analysis.supplier_comparator import (
    SupplierComparator, ComparisonCriteria, SupplierRank
)
from app.services.profit_analysis.opportunity_finder import (
    OpportunityFinder, OpportunityType, RiskLevel, InvestmentPriority
)


class TestCostManager:
    """成本管理器测试"""
    
    @pytest.fixture
    def cost_manager(self):
        """创建成本管理器实例"""
        return CostManager()
    
    @pytest.fixture
    def sample_cost_record(self):
        """示例成本记录"""
        return CostRecord(
            supplier_id="supplier_001",
            product_id="product_001",
            cost_type=CostType.PURCHASE_COST,
            cost_value=100.0,
            currency="CNY",
            unit="piece",
            effective_date=datetime.now(),
            notes="测试成本记录"
        )
    
    @pytest.mark.asyncio
    async def test_add_cost_record(self, cost_manager, sample_cost_record):
        """测试添加成本记录"""
        result = await cost_manager.add_cost_record(sample_cost_record)
        
        assert result is True
        
        key = f"{sample_cost_record.supplier_id}_{sample_cost_record.product_id}"
        assert key in cost_manager.cost_records
        assert len(cost_manager.cost_records[key]) == 1
        assert cost_manager.cost_records[key][0] == sample_cost_record
    
    @pytest.mark.asyncio
    async def test_batch_add_cost_records(self, cost_manager):
        """测试批量添加成本记录"""
        records = [
            CostRecord(
                supplier_id=f"supplier_{i:03d}",
                product_id=f"product_{i:03d}",
                cost_type=CostType.PURCHASE_COST,
                cost_value=100.0 + i * 10,
                effective_date=datetime.now() - timedelta(days=i)
            )
            for i in range(5)
        ]
        
        result = await cost_manager.batch_add_cost_records(records)
        
        assert result["total"] == 5
        assert result["success"] == 5
        assert result["failed"] == 0
        assert len(result["errors"]) == 0
    
    @pytest.mark.asyncio
    async def test_get_cost_history(self, cost_manager):
        """测试获取成本历史"""
        # 添加测试数据
        supplier_id = "supplier_001"
        product_id = "product_001"
        
        records = [
            CostRecord(
                supplier_id=supplier_id,
                product_id=product_id,
                cost_type=CostType.PURCHASE_COST,
                cost_value=100.0 + i * 5,
                effective_date=datetime.now() - timedelta(days=i * 10)
            )
            for i in range(5)
        ]
        
        for record in records:
            await cost_manager.add_cost_record(record)
        
        # 获取成本历史
        history = await cost_manager.get_cost_history(
            supplier_id, product_id, CostType.PURCHASE_COST
        )
        
        assert history is not None
        assert history.supplier_id == supplier_id
        assert history.product_id == product_id
        assert history.cost_type == CostType.PURCHASE_COST
        assert len(history.cost_records) == 5
        assert history.average_cost > 0
        assert history.min_cost <= history.max_cost
        assert history.cost_trend in [ct for ct in CostChangeType]
    
    @pytest.mark.asyncio
    async def test_compare_supplier_costs(self, cost_manager):
        """测试供应商成本对比"""
        product_id = "product_001"
        supplier_ids = ["supplier_001", "supplier_002", "supplier_003"]
        
        # 为每个供应商添加成本记录
        for i, supplier_id in enumerate(supplier_ids):
            record = CostRecord(
                supplier_id=supplier_id,
                product_id=product_id,
                cost_type=CostType.PURCHASE_COST,
                cost_value=100.0 + i * 20,  # 不同的成本
                effective_date=datetime.now()
            )
            await cost_manager.add_cost_record(record)
        
        # 对比供应商成本
        comparison = await cost_manager.compare_supplier_costs(
            product_id, supplier_ids, CostType.PURCHASE_COST
        )
        
        assert comparison["product_id"] == product_id
        assert comparison["cost_type"] == CostType.PURCHASE_COST.value
        assert len(comparison["suppliers"]) == 3
        assert comparison["best_supplier"] in supplier_ids
        assert comparison["worst_supplier"] in supplier_ids
        assert comparison["cost_range"]["min"] <= comparison["cost_range"]["max"]
        assert comparison["average_cost"] > 0
        assert isinstance(comparison["recommendations"], list)
    
    @pytest.mark.asyncio
    async def test_cost_alerts(self, cost_manager):
        """测试成本预警"""
        supplier_id = "supplier_001"
        product_id = "product_001"
        
        # 添加初始成本记录
        initial_record = CostRecord(
            supplier_id=supplier_id,
            product_id=product_id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=100.0,
            effective_date=datetime.now() - timedelta(days=1)
        )
        await cost_manager.add_cost_record(initial_record)
        
        # 添加大幅上涨的成本记录（应该触发预警）
        high_cost_record = CostRecord(
            supplier_id=supplier_id,
            product_id=product_id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=130.0,  # 30%上涨
            effective_date=datetime.now()
        )
        await cost_manager.add_cost_record(high_cost_record)
        
        # 获取预警
        alerts = await cost_manager.get_cost_alerts()
        
        assert len(alerts) > 0
        alert = alerts[0]
        assert alert.supplier_id == supplier_id
        assert alert.product_id == product_id
        assert alert.cost_type == CostType.PURCHASE_COST
        assert alert.alert_level in [level for level in AlertLevel]
        assert alert.current_cost == 130.0
        assert alert.previous_cost == 100.0
        assert not alert.acknowledged
        assert not alert.resolved
    
    @pytest.mark.asyncio
    async def test_acknowledge_and_resolve_alert(self, cost_manager):
        """测试确认和解决预警"""
        # 先创建一个预警
        supplier_id = "supplier_001"
        product_id = "product_001"
        
        initial_record = CostRecord(
            supplier_id=supplier_id,
            product_id=product_id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=100.0,
            effective_date=datetime.now() - timedelta(days=1)
        )
        await cost_manager.add_cost_record(initial_record)
        
        high_cost_record = CostRecord(
            supplier_id=supplier_id,
            product_id=product_id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=130.0,
            effective_date=datetime.now()
        )
        await cost_manager.add_cost_record(high_cost_record)
        
        alerts = await cost_manager.get_cost_alerts()
        assert len(alerts) > 0
        
        alert_id = alerts[0].alert_id
        
        # 确认预警
        result = await cost_manager.acknowledge_alert(alert_id)
        assert result is True
        
        # 解决预警
        result = await cost_manager.resolve_alert(alert_id)
        assert result is True
        
        # 验证状态
        updated_alerts = await cost_manager.get_cost_alerts(unresolved_only=False)
        resolved_alert = next(a for a in updated_alerts if a.alert_id == alert_id)
        assert resolved_alert.acknowledged is True
        assert resolved_alert.resolved is True
    
    @pytest.mark.asyncio
    async def test_get_supplier_cost_summary(self, cost_manager):
        """测试获取供应商成本汇总"""
        supplier_id = "supplier_001"
        
        # 为供应商添加多个商品的成本记录
        products = ["product_001", "product_002", "product_003"]
        cost_types = [CostType.PURCHASE_COST, CostType.SHIPPING_COST]
        
        for product_id in products:
            for cost_type in cost_types:
                record = CostRecord(
                    supplier_id=supplier_id,
                    product_id=product_id,
                    cost_type=cost_type,
                    cost_value=100.0 + hash(product_id + cost_type.value) % 50,
                    effective_date=datetime.now()
                )
                await cost_manager.add_cost_record(record)
        
        # 获取供应商成本汇总
        summary = await cost_manager.get_supplier_cost_summary(supplier_id)
        
        assert summary is not None
        assert summary.supplier_id == supplier_id
        assert summary.total_products == len(products)
        assert summary.average_cost > 0
        assert summary.total_cost_value > 0
        assert summary.cost_trend in [ct for ct in CostChangeType]
        assert 0 <= summary.cost_stability <= 1
        assert isinstance(summary.cost_breakdown, dict)
        assert 0 <= summary.performance_score <= 100
    
    def test_get_cost_statistics(self, cost_manager):
        """测试获取成本统计信息"""
        stats = cost_manager.get_cost_statistics()
        
        assert "total_cost_records" in stats
        assert "total_suppliers" in stats
        assert "total_products" in stats
        assert "total_alerts" in stats
        assert "unresolved_alerts" in stats
        assert "cost_types" in stats
        assert "alert_levels" in stats
        
        # 验证枚举值
        assert len(stats["cost_types"]) == len(CostType)
        assert len(stats["alert_levels"]) == len(AlertLevel)


class TestProfitCalculator:
    """利润计算器测试"""
    
    @pytest.fixture
    def cost_manager(self):
        return CostManager()
    
    @pytest.fixture
    def profit_calculator(self, cost_manager):
        return ProfitCalculator(cost_manager)
    
    @pytest.fixture
    def sample_product(self):
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="利润计算测试商品",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=200.0),
            specs=ProductSpecs(brand="TestBrand"),
            metrics=ProductMetrics(sales_count=1000, rating=4.5)
        )
    
    @pytest.mark.asyncio
    async def test_calculate_profit(self, profit_calculator, cost_manager, sample_product):
        """测试利润计算"""
        supplier_id = "supplier_001"
        
        # 添加成本记录
        cost_record = CostRecord(
            supplier_id=supplier_id,
            product_id=sample_product.id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=120.0,
            effective_date=datetime.now()
        )
        await cost_manager.add_cost_record(cost_record)
        
        # 计算利润
        profit_calc = await profit_calculator.calculate_profit(sample_product, supplier_id)
        
        assert profit_calc is not None
        assert profit_calc.product_id == sample_product.id
        assert profit_calc.supplier_id == supplier_id
        assert profit_calc.selling_price == 200.0
        assert profit_calc.total_cost == 120.0
        assert profit_calc.gross_profit == 80.0
        assert profit_calc.profit_margin == 0.4  # 40%
        assert profit_calc.profit_margin_level == ProfitMarginLevel.HIGH
        assert isinstance(profit_calc.cost_breakdown, dict)
    
    @pytest.mark.asyncio
    async def test_analyze_profit_trend(self, profit_calculator, cost_manager, sample_product):
        """测试利润趋势分析"""
        supplier_id = "supplier_001"
        
        # 添加多个时间点的成本记录
        for i in range(5):
            cost_record = CostRecord(
                supplier_id=supplier_id,
                product_id=sample_product.id,
                cost_type=CostType.PURCHASE_COST,
                cost_value=100.0 + i * 5,  # 成本逐渐上升
                effective_date=datetime.now() - timedelta(days=(4-i) * 10)
            )
            await cost_manager.add_cost_record(cost_record)
            
            # 计算利润（会自动保存到历史）
            await profit_calculator.calculate_profit(sample_product, supplier_id)
        
        # 分析利润趋势
        trend = await profit_calculator.analyze_profit_trend(sample_product.id, supplier_id)
        
        assert trend is not None
        assert trend.product_id == sample_product.id
        assert trend.supplier_id == supplier_id
        assert len(trend.profit_history) == 5
        assert trend.trend_direction in ["increasing", "decreasing", "stable", "volatile"]
        assert trend.average_margin > 0
        assert trend.margin_volatility >= 0
        assert trend.best_margin >= trend.worst_margin
        assert isinstance(trend.trend_analysis, str)
    
    @pytest.mark.asyncio
    async def test_generate_pricing_recommendation(self, profit_calculator, cost_manager, sample_product):
        """测试生成定价建议"""
        supplier_id = "supplier_001"
        
        # 添加成本记录
        cost_record = CostRecord(
            supplier_id=supplier_id,
            product_id=sample_product.id,
            cost_type=CostType.PURCHASE_COST,
            cost_value=120.0,
            effective_date=datetime.now()
        )
        await cost_manager.add_cost_record(cost_record)
        
        # 生成定价建议
        recommendation = await profit_calculator.generate_pricing_recommendation(
            sample_product, supplier_id, target_margin=0.3
        )
        
        assert recommendation is not None
        assert recommendation.product_id == sample_product.id
        assert recommendation.supplier_id == supplier_id
        assert recommendation.current_price == 200.0
        assert recommendation.recommended_price > 0
        assert recommendation.strategy in [strategy for strategy in PricingStrategy]
        assert recommendation.expected_margin == 0.3
        assert isinstance(recommendation.reasoning, str)
        assert isinstance(recommendation.risk_assessment, str)
        assert isinstance(recommendation.implementation_notes, list)
    
    def test_get_profit_statistics(self, profit_calculator):
        """测试获取利润统计信息"""
        stats = profit_calculator.get_profit_statistics()
        
        assert "total_calculations" in stats
        assert "total_products" in stats
        assert "average_margin" in stats
        assert "margin_distribution" in stats
        assert "pricing_strategies" in stats
        assert "margin_levels" in stats
        
        # 验证枚举值
        assert len(stats["pricing_strategies"]) == len(PricingStrategy)
        assert len(stats["margin_levels"]) == len(ProfitMarginLevel)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
