"""
商品状态跟踪系统演示

展示生命周期管理、监控调度、状态跟踪等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductStatus, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.product_management.lifecycle_manager import (
    ProductLifecycleManager, LifecycleEvent, MonitoringPriority, LifecycleRule
)
from app.services.product_management.monitoring_scheduler import MonitoringScheduler


async def demo_lifecycle_management():
    """演示生命周期管理"""
    print("=== 商品生命周期管理演示 ===")
    
    lifecycle_manager = ProductLifecycleManager()
    
    print(f"\n1. 默认生命周期规则:")
    print(f"   规则数量: {len(lifecycle_manager.lifecycle_rules)}")
    
    for rule in lifecycle_manager.lifecycle_rules:
        print(f"   - {rule.name}: {rule.trigger_event.value} -> {rule.actions}")
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            status=ProductStatus.NEW,
            data_quality_score=0.95,
            specs=ProductSpecs(brand="Apple"),
            price=ProductPrice(current_price=8999.00),
            metrics=ProductMetrics(sales_count=25000, rating=4.9)
        ),
        Product(
            url="https://detail.1688.com/offer/789012.html",
            title="手机壳批发 透明硅胶保护套 工厂直销",
            platform="1688",
            product_type=ProductType.SUPPLIER,
            status=ProductStatus.NEW,
            data_quality_score=0.7,
            price=ProductPrice(current_price=3.80, min_order_quantity=100),
            metrics=ProductMetrics(sales_count=150000, stock_quantity=9999)
        ),
        Product(
            url="https://example.com/poor-quality",
            title="低质量商品测试",
            platform="other",
            product_type=ProductType.OTHER,
            status=ProductStatus.NEW,
            data_quality_score=0.2
        )
    ]
    
    print(f"\n2. 商品生命周期事件跟踪:")
    
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        print(f"   类型: {product.product_type.value}")
        print(f"   质量分数: {product.data_quality_score:.2f}")
        
        # 跟踪创建事件
        success = await lifecycle_manager.track_lifecycle_event(
            product=product,
            event=LifecycleEvent.CREATED,
            metadata={"source": "demo", "batch": "test_batch_1"}
        )
        
        print(f"   创建事件跟踪: {'成功' if success else '失败'}")
        print(f"   变更记录数: {len(product.change_history)}")
        print(f"   当前状态: {product.status.value}")
        
        # 检查监控配置
        if product.id in lifecycle_manager.monitoring_configs:
            config = lifecycle_manager.monitoring_configs[product.id]
            print(f"   监控配置:")
            print(f"     - 优先级: {config.priority.value}")
            print(f"     - 频率: {config.frequency_minutes} 分钟")
            print(f"     - 启用: {config.enabled}")
        
        # 模拟质量变更事件
        if product.data_quality_score < 0.5:
            await lifecycle_manager.track_lifecycle_event(
                product=product,
                event=LifecycleEvent.QUALITY_CHANGED,
                metadata={"old_score": product.data_quality_score, "new_score": 0.2}
            )
            print(f"   质量变更事件已触发")
    
    return test_products, lifecycle_manager


async def demo_monitoring_scheduler(products, lifecycle_manager):
    """演示监控调度器"""
    print("\n=== 监控调度器演示 ===")
    
    scheduler = MonitoringScheduler(lifecycle_manager)
    
    print(f"\n1. 调度器初始状态:")
    status = scheduler.get_scheduler_status()
    print(f"   状态: {status['status']}")
    print(f"   最大并发任务: {status['max_concurrent_tasks']}")
    print(f"   任务超时: {status['task_timeout_minutes']} 分钟")
    
    print(f"\n2. 启动监控调度器:")
    await scheduler.start()
    
    # 等待一小段时间让调度器运行
    await asyncio.sleep(1)
    
    status = scheduler.get_scheduler_status()
    print(f"   状态: {status['status']}")
    print(f"   运行时间: {status['uptime_seconds']:.1f} 秒")
    
    print(f"\n3. 强制监控商品:")
    
    for i, product in enumerate(products[:2]):  # 只测试前两个商品
        task_id = await scheduler.force_monitor_product(
            product.id, 
            MonitoringPriority.HIGH
        )
        print(f"   商品 {i+1} 强制监控任务: {task_id}")
    
    # 等待任务执行
    print(f"\n4. 等待任务执行...")
    await asyncio.sleep(3)
    
    # 检查任务历史
    task_history = scheduler.get_task_history(limit=10)
    print(f"   任务历史 ({len(task_history)} 个):")
    
    for task in task_history:
        print(f"     - {task['id'][:20]}...")
        print(f"       商品: {task['product_id'][:20]}...")
        print(f"       状态: {task['status']}")
        print(f"       优先级: {task['priority']}")
        if task['execution_time']:
            print(f"       执行时间: {task['execution_time']:.2f} 秒")
    
    print(f"\n5. 调度器统计:")
    status = scheduler.get_scheduler_status()
    metrics = status['metrics']
    
    print(f"   总调度任务: {metrics['total_scheduled']}")
    print(f"   完成任务: {metrics['total_completed']}")
    print(f"   失败任务: {metrics['total_failed']}")
    print(f"   重试任务: {metrics['total_retried']}")
    print(f"   成功率: {metrics['success_rate']:.1f}%")
    print(f"   平均执行时间: {metrics['average_execution_time']:.2f} 秒")
    
    # 暂停和恢复演示
    print(f"\n6. 调度器控制演示:")
    
    await scheduler.pause()
    print(f"   调度器已暂停")
    
    await asyncio.sleep(1)
    
    await scheduler.resume()
    print(f"   调度器已恢复")
    
    # 停止调度器
    await scheduler.stop()
    print(f"   调度器已停止")
    
    return scheduler


async def demo_advanced_lifecycle_features(products, lifecycle_manager):
    """演示高级生命周期功能"""
    print("\n=== 高级生命周期功能演示 ===")
    
    print(f"\n1. 自定义生命周期规则:")
    
    # 添加自定义规则
    custom_rule = LifecycleRule(
        id="rule_high_value_product",
        name="高价值商品特殊处理",
        trigger_event=LifecycleEvent.CREATED,
        conditions={
            "product_type": "competitor",
            "quality_score_min": 0.9
        },
        actions=["raise_priority", "increase_frequency", "notify_admin"],
        priority=15
    )
    
    lifecycle_manager.add_lifecycle_rule(custom_rule)
    print(f"   添加规则: {custom_rule.name}")
    
    # 创建高价值商品测试规则
    high_value_product = Product(
        url="https://item.taobao.com/item.htm?id=999999",
        title="Apple MacBook Pro 16英寸 M3 Max 芯片",
        platform="taobao",
        product_type=ProductType.COMPETITOR,
        status=ProductStatus.NEW,
        data_quality_score=0.98,
        specs=ProductSpecs(brand="Apple"),
        price=ProductPrice(current_price=25999.00),
        metrics=ProductMetrics(sales_count=5000, rating=4.95)
    )
    
    print(f"\n2. 高价值商品处理:")
    print(f"   商品: {high_value_product.title}")
    print(f"   质量分数: {high_value_product.data_quality_score:.2f}")
    
    # 触发创建事件
    success = await lifecycle_manager.track_lifecycle_event(
        product=high_value_product,
        event=LifecycleEvent.CREATED,
        metadata={"value_tier": "premium", "priority": "critical"}
    )
    
    print(f"   事件处理: {'成功' if success else '失败'}")
    
    # 检查监控配置
    if high_value_product.id in lifecycle_manager.monitoring_configs:
        config = lifecycle_manager.monitoring_configs[high_value_product.id]
        print(f"   监控优先级: {config.priority.value}")
        print(f"   监控频率: {config.frequency_minutes} 分钟")
    
    print(f"\n3. 监控失败处理演示:")
    
    # 模拟监控失败
    test_product = products[0]
    
    for i in range(3):
        await lifecycle_manager.record_monitoring_failure(test_product.id)
        config = lifecycle_manager.monitoring_configs.get(test_product.id)
        if config:
            print(f"   失败次数 {i+1}: 累计失败 {config.failure_count} 次")
    
    # 模拟监控成功（重置失败计数）
    await lifecycle_manager.record_monitoring_success(test_product.id)
    config = lifecycle_manager.monitoring_configs.get(test_product.id)
    if config:
        print(f"   监控成功: 失败计数重置为 {config.failure_count}")
    
    print(f"\n4. 生命周期统计:")
    
    stats = lifecycle_manager.get_lifecycle_statistics()
    
    print(f"   总商品数: {stats['total_products']}")
    print(f"   活跃监控: {stats['active_monitoring']}")
    print(f"   暂停监控: {stats['paused_monitoring']}")
    print(f"   总生命周期事件: {stats['total_lifecycle_events']}")
    print(f"   状态变更次数: {stats['total_status_changes']}")
    print(f"   监控失败次数: {stats['total_monitoring_failures']}")
    
    print(f"   优先级分布:")
    for priority, count in stats['priority_distribution'].items():
        print(f"     - {priority}: {count} 个")
    
    print(f"\n5. 生命周期规则管理:")
    
    rules = lifecycle_manager.get_lifecycle_rules()
    print(f"   总规则数: {len(rules)}")
    print(f"   启用规则: {stats['enabled_rules']}")
    
    # 显示部分规则
    print(f"   规则列表:")
    for rule in rules[:3]:
        print(f"     - {rule['name']}")
        print(f"       触发事件: {rule['trigger_event']}")
        print(f"       动作: {', '.join(rule['actions'])}")
        print(f"       优先级: {rule['priority']}")


async def demo_api_integration():
    """演示API集成"""
    print("\n=== API集成演示 ===")
    
    print(f"\n1. 生命周期事件API模拟:")
    
    # 模拟API请求
    api_requests = [
        {
            "endpoint": "POST /api/v1/product-management/lifecycle/events",
            "payload": {
                "product_id": "test_product_001",
                "event": "created",
                "metadata": {"source": "api", "user": "admin"}
            }
        },
        {
            "endpoint": "POST /api/v1/product-management/monitoring/config",
            "payload": {
                "product_id": "test_product_001",
                "priority": "high",
                "frequency_minutes": 30,
                "enabled": True
            }
        },
        {
            "endpoint": "GET /api/v1/product-management/lifecycle/statistics",
            "payload": None
        },
        {
            "endpoint": "POST /api/v1/product-management/monitoring/force/test_product_001?priority=critical",
            "payload": None
        }
    ]
    
    for i, request in enumerate(api_requests):
        print(f"   请求 {i+1}: {request['endpoint']}")
        if request['payload']:
            print(f"   载荷: {request['payload']}")
        print(f"   响应: 模拟成功响应")
    
    print(f"\n2. 监控调度器API模拟:")
    
    scheduler_apis = [
        "POST /api/v1/product-management/monitoring/start",
        "GET /api/v1/product-management/monitoring/status",
        "POST /api/v1/product-management/monitoring/pause",
        "POST /api/v1/product-management/monitoring/resume",
        "GET /api/v1/product-management/monitoring/tasks?limit=50",
        "POST /api/v1/product-management/monitoring/stop"
    ]
    
    for api in scheduler_apis:
        print(f"   {api}")
        print(f"     响应: {{'success': true, 'message': '操作成功'}}")
    
    print(f"\n3. 系统健康检查API:")
    
    health_response = {
        "success": True,
        "data": {
            "status": "healthy",
            "lifecycle": {
                "monitoring_configs": 4,
                "lifecycle_rules": 5,
                "metrics": 4
            },
            "scheduler": {
                "status": "running",
                "pending_tasks": 2,
                "running_tasks": 1,
                "uptime_seconds": 3600
            }
        }
    }
    
    print(f"   GET /api/v1/product-management/health")
    print(f"   响应: {health_response}")


async def main():
    """主演示函数"""
    print("🚀 商品状态跟踪系统演示")
    print("=" * 60)
    
    # 1. 生命周期管理演示
    products, lifecycle_manager = await demo_lifecycle_management()
    
    # 2. 监控调度器演示
    scheduler = await demo_monitoring_scheduler(products, lifecycle_manager)
    
    # 3. 高级生命周期功能演示
    await demo_advanced_lifecycle_features(products, lifecycle_manager)
    
    # 4. API集成演示
    await demo_api_integration()
    
    print("\n" + "=" * 60)
    print("✅ 商品状态跟踪系统演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 生命周期管理：10种生命周期事件，4个默认规则")
    print(f"- 监控调度：5种优先级，智能调度算法，并发任务控制")
    print(f"- 状态跟踪：完整的变更历史，版本管理，元数据支持")
    print(f"- 规则引擎：灵活的条件匹配，动作执行，优先级排序")
    print(f"- 监控控制：启动/停止/暂停/恢复，失败重试，超时处理")
    print(f"- API接口：12个RESTful端点，标准化响应格式")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: {len(products) + 1}")
    print(f"- 生命周期事件: 6个")
    print(f"- 监控任务: 2个")
    print(f"- 自定义规则: 1个")
    print(f"- API端点: 12个")
    
    print(f"\n🔧 技术特性:")
    print(f"- 异步处理：所有操作支持异步执行")
    print(f"- 并发控制：最大并发任务数限制")
    print(f"- 超时处理：任务执行超时保护")
    print(f"- 失败重试：智能重试机制")
    print(f"- 优先级调度：5级优先级智能调度")
    print(f"- 元数据支持：完整的事件元数据记录")
    print(f"- 统计分析：详细的运行统计和指标")
    print(f"- 规则配置：灵活的生命周期规则配置")
    
    print(f"\n🏗️ 架构优势:")
    print(f"- 模块化设计：生命周期管理器 + 监控调度器")
    print(f"- 事件驱动：基于生命周期事件的响应式架构")
    print(f"- 可扩展性：易于添加新的生命周期事件和规则")
    print(f"- 高可用性：故障恢复、状态持久化、健康检查")
    print(f"- 监控友好：完整的指标收集和状态报告")


if __name__ == "__main__":
    asyncio.run(main())
