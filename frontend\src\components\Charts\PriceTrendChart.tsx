/**
 * 价格趋势图表组件
 */

import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin, Empty } from 'antd';
import { analyticsApi } from '../../services/analyticsApi';

interface PriceTrendChartProps {
  productId?: string;
  category?: string;
  platform?: string;
  startDate?: string;
  endDate?: string;
  height?: number;
}

interface PriceTrendData {
  date: string;
  price: number;
  product_name?: string;
  platform?: string;
}

const PriceTrendChart: React.FC<PriceTrendChartProps> = ({
  productId,
  category,
  platform,
  startDate,
  endDate,
  height = 400,
}) => {
  const [data, setData] = useState<PriceTrendData[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, [productId, category, platform, startDate, endDate]);

  const loadData = async () => {
    try {
      setLoading(true);
      // 确保productId不为undefined
      if (!productId) {
        console.warn('Product ID is required for price trend analysis');
        return;
      }

      const response = await analyticsApi.getPriceTrend({
        product_id: productId,
        days: 30,
        interval: 'day',
      });
      setData(response.data);
    } catch (error) {
      console.error('Failed to load price trend data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const getOption = () => {
    if (!data || data.length === 0) {
      return {};
    }

    // 按产品分组数据
    const groupedData: { [key: string]: PriceTrendData[] } = {};
    data.forEach(item => {
      const key = item.product_name || 'Unknown Product';
      if (!groupedData[key]) {
        groupedData[key] = [];
      }
      groupedData[key].push(item);
    });

    const series = Object.keys(groupedData).map((productName, index) => {
      const productData = groupedData[productName];
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa8c16'];
      
      return {
        name: productName,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
        },
        itemStyle: {
          color: colors[index % colors.length],
        },
        data: productData.map(item => [item.date, item.price]),
      };
    });

    return {
      title: {
        text: '价格趋势分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: (params: any) => {
          let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
          params.forEach((param: any) => {
            result += `
              <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="margin-right: 8px;">${param.seriesName}:</span>
                <span style="font-weight: bold;">¥${param.value[1]}</span>
              </div>
            `;
          });
          return result;
        },
      },
      legend: {
        data: Object.keys(groupedData),
        bottom: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#d9d9d9',
          },
        },
        axisLabel: {
          color: '#666',
        },
      },
      yAxis: {
        type: 'value',
        name: '价格 (¥)',
        nameTextStyle: {
          color: '#666',
        },
        axisLine: {
          lineStyle: {
            color: '#d9d9d9',
          },
        },
        axisLabel: {
          color: '#666',
          formatter: '¥{value}',
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series,
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
        {
          start: 0,
          end: 100,
          height: 30,
          bottom: 50,
        },
      ],
    };
  };

  if (loading) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="暂无数据" />
      </div>
    );
  }

  return (
    <ReactECharts
      option={getOption()}
      style={{ height }}
      opts={{ renderer: 'canvas' }}
    />
  );
};

export default PriceTrendChart;
