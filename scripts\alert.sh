#!/bin/bash

# Moniit 系统告警通知脚本
# 支持邮件、Webhook、短信等多种通知方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置文件路径
CONFIG_FILE="./config/alert.conf"
ALERT_LOG="./logs/alerts.log"

# 默认配置
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_TLS="true"
EMAIL_FROM=""
EMAIL_TO=""
WEBHOOK_URL=""
WEBHOOK_TOKEN=""
SLACK_WEBHOOK=""
DINGTALK_WEBHOOK=""
WECHAT_WEBHOOK=""

# 显示帮助信息
show_help() {
    echo "Moniit 系统告警通知脚本"
    echo ""
    echo "用法: $0 [选项] <消息内容>"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE      告警类型 (info|warning|error|critical) [默认: info]"
    echo "  -s, --subject SUBJ   邮件主题"
    echo "  -e, --email          发送邮件通知"
    echo "  -w, --webhook        发送Webhook通知"
    echo "  -k, --slack          发送Slack通知"
    echo "  -d, --dingtalk       发送钉钉通知"
    echo "  -c, --wechat         发送企业微信通知"
    echo "  -a, --all            发送所有配置的通知"
    echo "  -f, --file FILE      从文件读取消息内容"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -t error -e \"系统出现错误\""
    echo "  $0 -a -t critical \"数据库连接失败\""
    echo "  $0 -w -f error.log"
}

# 加载配置文件
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
        log_info "已加载配置文件: $CONFIG_FILE"
    else
        log_warning "配置文件不存在: $CONFIG_FILE"
        log_info "使用默认配置或环境变量"
    fi
    
    # 从环境变量覆盖配置
    SMTP_HOST=${ALERT_SMTP_HOST:-$SMTP_HOST}
    SMTP_PORT=${ALERT_SMTP_PORT:-$SMTP_PORT}
    SMTP_USER=${ALERT_SMTP_USER:-$SMTP_USER}
    SMTP_PASSWORD=${ALERT_SMTP_PASSWORD:-$SMTP_PASSWORD}
    EMAIL_FROM=${ALERT_EMAIL_FROM:-$EMAIL_FROM}
    EMAIL_TO=${ALERT_EMAIL_TO:-$EMAIL_TO}
    WEBHOOK_URL=${ALERT_WEBHOOK_URL:-$WEBHOOK_URL}
    SLACK_WEBHOOK=${ALERT_SLACK_WEBHOOK:-$SLACK_WEBHOOK}
    DINGTALK_WEBHOOK=${ALERT_DINGTALK_WEBHOOK:-$DINGTALK_WEBHOOK}
    WECHAT_WEBHOOK=${ALERT_WECHAT_WEBHOOK:-$WECHAT_WEBHOOK}
}

# 记录告警日志
log_alert() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    mkdir -p "$(dirname "$ALERT_LOG")"
    echo "[$timestamp] [$level] $message" >> "$ALERT_LOG"
}

# 获取告警级别图标
get_alert_icon() {
    local level=$1
    
    case $level in
        "info")
            echo "ℹ️"
            ;;
        "warning")
            echo "⚠️"
            ;;
        "error")
            echo "❌"
            ;;
        "critical")
            echo "🚨"
            ;;
        *)
            echo "📢"
            ;;
    esac
}

# 发送邮件通知
send_email() {
    local subject=$1
    local message=$2
    local level=$3
    
    if [ -z "$SMTP_HOST" ] || [ -z "$EMAIL_FROM" ] || [ -z "$EMAIL_TO" ]; then
        log_error "邮件配置不完整，跳过邮件通知"
        return 1
    fi
    
    log_info "发送邮件通知..."
    
    local icon=$(get_alert_icon "$level")
    local full_subject="$icon Moniit告警: $subject"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    local email_body="
Moniit系统告警通知

时间: $timestamp
级别: $level
主题: $subject

详细信息:
$message

---
此邮件由Moniit监控系统自动发送
"
    
    # 使用sendmail或mail命令发送邮件
    if command -v sendmail &> /dev/null; then
        {
            echo "From: $EMAIL_FROM"
            echo "To: $EMAIL_TO"
            echo "Subject: $full_subject"
            echo "Content-Type: text/plain; charset=UTF-8"
            echo ""
            echo "$email_body"
        } | sendmail "$EMAIL_TO"
        
        log_success "邮件发送成功"
        log_alert "INFO" "邮件通知已发送: $subject"
        return 0
    elif command -v mail &> /dev/null; then
        echo "$email_body" | mail -s "$full_subject" "$EMAIL_TO"
        
        log_success "邮件发送成功"
        log_alert "INFO" "邮件通知已发送: $subject"
        return 0
    else
        log_error "未找到邮件发送命令 (sendmail/mail)"
        return 1
    fi
}

# 发送Webhook通知
send_webhook() {
    local subject=$1
    local message=$2
    local level=$3
    
    if [ -z "$WEBHOOK_URL" ]; then
        log_error "Webhook URL未配置，跳过Webhook通知"
        return 1
    fi
    
    log_info "发送Webhook通知..."
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local payload=$(cat <<EOF
{
    "timestamp": "$timestamp",
    "level": "$level",
    "subject": "$subject",
    "message": "$message",
    "source": "moniit-system"
}
EOF
)
    
    local headers="-H 'Content-Type: application/json'"
    if [ -n "$WEBHOOK_TOKEN" ]; then
        headers="$headers -H 'Authorization: Bearer $WEBHOOK_TOKEN'"
    fi
    
    if curl -X POST $headers -d "$payload" "$WEBHOOK_URL" &> /dev/null; then
        log_success "Webhook通知发送成功"
        log_alert "INFO" "Webhook通知已发送: $subject"
        return 0
    else
        log_error "Webhook通知发送失败"
        return 1
    fi
}

# 发送Slack通知
send_slack() {
    local subject=$1
    local message=$2
    local level=$3
    
    if [ -z "$SLACK_WEBHOOK" ]; then
        log_error "Slack Webhook未配置，跳过Slack通知"
        return 1
    fi
    
    log_info "发送Slack通知..."
    
    local icon=$(get_alert_icon "$level")
    local color=""
    
    case $level in
        "info")
            color="good"
            ;;
        "warning")
            color="warning"
            ;;
        "error"|"critical")
            color="danger"
            ;;
    esac
    
    local payload=$(cat <<EOF
{
    "text": "$icon Moniit系统告警",
    "attachments": [
        {
            "color": "$color",
            "fields": [
                {
                    "title": "告警级别",
                    "value": "$level",
                    "short": true
                },
                {
                    "title": "时间",
                    "value": "$(date '+%Y-%m-%d %H:%M:%S')",
                    "short": true
                },
                {
                    "title": "主题",
                    "value": "$subject",
                    "short": false
                },
                {
                    "title": "详细信息",
                    "value": "$message",
                    "short": false
                }
            ]
        }
    ]
}
EOF
)
    
    if curl -X POST -H 'Content-Type: application/json' -d "$payload" "$SLACK_WEBHOOK" &> /dev/null; then
        log_success "Slack通知发送成功"
        log_alert "INFO" "Slack通知已发送: $subject"
        return 0
    else
        log_error "Slack通知发送失败"
        return 1
    fi
}

# 发送钉钉通知
send_dingtalk() {
    local subject=$1
    local message=$2
    local level=$3
    
    if [ -z "$DINGTALK_WEBHOOK" ]; then
        log_error "钉钉Webhook未配置，跳过钉钉通知"
        return 1
    fi
    
    log_info "发送钉钉通知..."
    
    local icon=$(get_alert_icon "$level")
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    local text="$icon **Moniit系统告警**\n\n**级别**: $level\n**时间**: $timestamp\n**主题**: $subject\n\n**详细信息**:\n$message"
    
    local payload=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "title": "Moniit系统告警",
        "text": "$text"
    }
}
EOF
)
    
    if curl -X POST -H 'Content-Type: application/json' -d "$payload" "$DINGTALK_WEBHOOK" &> /dev/null; then
        log_success "钉钉通知发送成功"
        log_alert "INFO" "钉钉通知已发送: $subject"
        return 0
    else
        log_error "钉钉通知发送失败"
        return 1
    fi
}

# 发送企业微信通知
send_wechat() {
    local subject=$1
    local message=$2
    local level=$3
    
    if [ -z "$WECHAT_WEBHOOK" ]; then
        log_error "企业微信Webhook未配置，跳过企业微信通知"
        return 1
    fi
    
    log_info "发送企业微信通知..."
    
    local icon=$(get_alert_icon "$level")
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    local text="$icon Moniit系统告警\n级别: $level\n时间: $timestamp\n主题: $subject\n\n详细信息:\n$message"
    
    local payload=$(cat <<EOF
{
    "msgtype": "text",
    "text": {
        "content": "$text"
    }
}
EOF
)
    
    if curl -X POST -H 'Content-Type: application/json' -d "$payload" "$WECHAT_WEBHOOK" &> /dev/null; then
        log_success "企业微信通知发送成功"
        log_alert "INFO" "企业微信通知已发送: $subject"
        return 0
    else
        log_error "企业微信通知发送失败"
        return 1
    fi
}

# 主函数
main() {
    # 默认参数
    local alert_type="info"
    local subject="系统通知"
    local message=""
    local send_email=false
    local send_webhook=false
    local send_slack=false
    local send_dingtalk=false
    local send_wechat=false
    local send_all=false
    local message_file=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                alert_type="$2"
                shift 2
                ;;
            -s|--subject)
                subject="$2"
                shift 2
                ;;
            -e|--email)
                send_email=true
                shift
                ;;
            -w|--webhook)
                send_webhook=true
                shift
                ;;
            -k|--slack)
                send_slack=true
                shift
                ;;
            -d|--dingtalk)
                send_dingtalk=true
                shift
                ;;
            -c|--wechat)
                send_wechat=true
                shift
                ;;
            -a|--all)
                send_all=true
                shift
                ;;
            -f|--file)
                message_file="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
            *)
                message="$1"
                shift
                ;;
        esac
    done
    
    # 从文件读取消息
    if [ -n "$message_file" ]; then
        if [ -f "$message_file" ]; then
            message=$(cat "$message_file")
        else
            log_error "消息文件不存在: $message_file"
            exit 1
        fi
    fi
    
    # 检查消息内容
    if [ -z "$message" ]; then
        log_error "请提供消息内容"
        show_help
        exit 1
    fi
    
    # 验证告警类型
    if [[ "$alert_type" != "info" && "$alert_type" != "warning" && "$alert_type" != "error" && "$alert_type" != "critical" ]]; then
        log_error "无效的告警类型: $alert_type"
        exit 1
    fi
    
    # 加载配置
    load_config
    
    log_info "发送告警通知: $subject ($alert_type)"
    
    local success_count=0
    local total_count=0
    
    # 发送通知
    if [ "$send_all" = true ] || [ "$send_email" = true ]; then
        ((total_count++))
        if send_email "$subject" "$message" "$alert_type"; then
            ((success_count++))
        fi
    fi
    
    if [ "$send_all" = true ] || [ "$send_webhook" = true ]; then
        ((total_count++))
        if send_webhook "$subject" "$message" "$alert_type"; then
            ((success_count++))
        fi
    fi
    
    if [ "$send_all" = true ] || [ "$send_slack" = true ]; then
        ((total_count++))
        if send_slack "$subject" "$message" "$alert_type"; then
            ((success_count++))
        fi
    fi
    
    if [ "$send_all" = true ] || [ "$send_dingtalk" = true ]; then
        ((total_count++))
        if send_dingtalk "$subject" "$message" "$alert_type"; then
            ((success_count++))
        fi
    fi
    
    if [ "$send_all" = true ] || [ "$send_wechat" = true ]; then
        ((total_count++))
        if send_wechat "$subject" "$message" "$alert_type"; then
            ((success_count++))
        fi
    fi
    
    # 如果没有指定任何通知方式，默认尝试所有配置的方式
    if [ $total_count -eq 0 ]; then
        log_info "未指定通知方式，尝试所有配置的通知方式"
        
        if [ -n "$EMAIL_TO" ]; then
            ((total_count++))
            if send_email "$subject" "$message" "$alert_type"; then
                ((success_count++))
            fi
        fi
        
        if [ -n "$WEBHOOK_URL" ]; then
            ((total_count++))
            if send_webhook "$subject" "$message" "$alert_type"; then
                ((success_count++))
            fi
        fi
        
        if [ -n "$SLACK_WEBHOOK" ]; then
            ((total_count++))
            if send_slack "$subject" "$message" "$alert_type"; then
                ((success_count++))
            fi
        fi
    fi
    
    # 输出结果
    if [ $total_count -eq 0 ]; then
        log_warning "未配置任何通知方式"
        exit 1
    elif [ $success_count -eq $total_count ]; then
        log_success "所有通知发送成功 ($success_count/$total_count)"
        exit 0
    elif [ $success_count -gt 0 ]; then
        log_warning "部分通知发送成功 ($success_count/$total_count)"
        exit 1
    else
        log_error "所有通知发送失败 ($success_count/$total_count)"
        exit 1
    fi
}

# 执行主函数
main "$@"
