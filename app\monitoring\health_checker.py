"""
系统健康检查器

实现系统健康检查接口、数据库和缓存状态检查、资源使用监控
"""

import asyncio
import psutil
import time
import json
import os
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
import sqlite3
import redis

from app.core.logging import get_logger

logger = get_logger(__name__)


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class ComponentHealth:
    """组件健康状态"""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    response_time_ms: float = 0.0
    last_check: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "response_time_ms": self.response_time_ms,
            "last_check": self.last_check.isoformat()
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 默认配置
        self.default_config = {
            "check_interval_seconds": 60,
            "timeout_seconds": 30,
            "enable_database_check": True,
            "enable_cache_check": True,
            "enable_disk_check": True,
            "enable_memory_check": True,
            "enable_cpu_check": True,
            "enable_network_check": True,
            "database_path": "data/app.db",
            "redis_host": "localhost",
            "redis_port": 6379,
            "redis_db": 0,
            "disk_usage_warning_threshold": 80,
            "disk_usage_critical_threshold": 90,
            "memory_usage_warning_threshold": 80,
            "memory_usage_critical_threshold": 90,
            "cpu_usage_warning_threshold": 80,
            "cpu_usage_critical_threshold": 90
        }
        
        # 合并配置
        self.config = {**self.default_config, **self.config}
        
        # 健康检查结果
        self.health_results: Dict[str, ComponentHealth] = {}
        
        # 检查器注册表
        self.checkers: Dict[str, Callable] = {
            "system": self._check_system_resources,
            "database": self._check_database,
            "cache": self._check_cache,
            "disk": self._check_disk_space,
            "memory": self._check_memory_usage,
            "cpu": self._check_cpu_usage,
            "network": self._check_network_connectivity
        }
        
        # 健康检查统计
        self.health_stats = {
            "total_checks": 0,
            "healthy_checks": 0,
            "warning_checks": 0,
            "critical_checks": 0,
            "failed_checks": 0,
            "average_response_time": 0.0,
            "last_full_check": None,
            "uptime_start": datetime.now()
        }
        
        # 运行状态
        self.is_running = False
        self.check_task = None
    
    async def check_health(self, components: List[str] = None) -> Dict[str, Any]:
        """
        执行健康检查
        
        Args:
            components: 要检查的组件列表，None表示检查所有组件
        
        Returns:
            Dict[str, Any]: 健康检查结果
        """
        try:
            start_time = time.time()
            
            # 确定要检查的组件
            if components is None:
                components = list(self.checkers.keys())
            
            # 执行检查
            check_results = {}
            for component in components:
                if component in self.checkers:
                    try:
                        result = await self.checkers[component]()
                        check_results[component] = result
                        self.health_results[component] = result
                    except Exception as e:
                        logger.error(f"健康检查失败 - {component}: {e}")
                        check_results[component] = ComponentHealth(
                            name=component,
                            status=HealthStatus.CRITICAL,
                            message=f"检查失败: {str(e)}",
                            response_time_ms=0.0
                        )
                        self.health_results[component] = check_results[component]
            
            # 计算总体健康状态
            overall_status = self._calculate_overall_status(check_results)
            
            # 更新统计
            self._update_health_stats(check_results, time.time() - start_time)
            
            # 构建响应
            response = {
                "status": overall_status.value,
                "timestamp": datetime.now().isoformat(),
                "components": {name: result.to_dict() for name, result in check_results.items()},
                "summary": self._generate_health_summary(check_results),
                "uptime_seconds": (datetime.now() - self.health_stats["uptime_start"]).total_seconds()
            }
            
            return response
            
        except Exception as e:
            logger.error(f"执行健康检查失败: {e}")
            return {
                "status": HealthStatus.CRITICAL.value,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "components": {},
                "summary": {"healthy": 0, "warning": 0, "critical": 1, "unknown": 0}
            }
    
    async def _check_system_resources(self) -> ComponentHealth:
        """检查系统资源"""
        try:
            start_time = time.time()
            
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 判断状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > self.config["cpu_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
                messages.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            elif cpu_percent > self.config["cpu_usage_warning_threshold"]:
                status = HealthStatus.WARNING
                messages.append(f"CPU使用率较高: {cpu_percent:.1f}%")
            
            if memory.percent > self.config["memory_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
                messages.append(f"内存使用率过高: {memory.percent:.1f}%")
            elif memory.percent > self.config["memory_usage_warning_threshold"]:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                messages.append(f"内存使用率较高: {memory.percent:.1f}%")
            
            if disk.percent > self.config["disk_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
                messages.append(f"磁盘使用率过高: {disk.percent:.1f}%")
            elif disk.percent > self.config["disk_usage_warning_threshold"]:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                messages.append(f"磁盘使用率较高: {disk.percent:.1f}%")
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="system",
                status=status,
                message="; ".join(messages) if messages else "系统资源正常",
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_percent": disk.percent,
                    "disk_free_gb": disk.free / (1024**3),
                    "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"系统资源检查失败: {e}")
            return ComponentHealth(
                name="system",
                status=HealthStatus.CRITICAL,
                message=f"系统资源检查失败: {str(e)}"
            )
    
    async def _check_database(self) -> ComponentHealth:
        """检查数据库连接"""
        try:
            if not self.config["enable_database_check"]:
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.UNKNOWN,
                    message="数据库检查已禁用"
                )
            
            start_time = time.time()
            
            # 检查SQLite数据库
            db_path = self.config["database_path"]
            
            if not os.path.exists(db_path):
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.WARNING,
                    message=f"数据库文件不存在: {db_path}"
                )
            
            # 尝试连接数据库
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            
            # 执行简单查询
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            # 获取数据库信息
            cursor.execute("PRAGMA database_list")
            db_info = cursor.fetchall()
            
            cursor.execute("PRAGMA table_list")
            tables = cursor.fetchall()
            
            conn.close()
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="database",
                status=HealthStatus.HEALTHY,
                message="数据库连接正常",
                details={
                    "database_path": db_path,
                    "database_size_mb": os.path.getsize(db_path) / (1024**2),
                    "table_count": len(tables),
                    "connection_test": result[0] == 1
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"数据库检查失败: {e}")
            return ComponentHealth(
                name="database",
                status=HealthStatus.CRITICAL,
                message=f"数据库连接失败: {str(e)}"
            )
    
    async def _check_cache(self) -> ComponentHealth:
        """检查缓存服务"""
        try:
            if not self.config["enable_cache_check"]:
                return ComponentHealth(
                    name="cache",
                    status=HealthStatus.UNKNOWN,
                    message="缓存检查已禁用"
                )
            
            start_time = time.time()
            
            # 尝试连接Redis
            try:
                r = redis.Redis(
                    host=self.config["redis_host"],
                    port=self.config["redis_port"],
                    db=self.config["redis_db"],
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
                
                # 执行ping测试
                ping_result = r.ping()
                
                # 获取Redis信息
                info = r.info()
                
                response_time = (time.time() - start_time) * 1000
                
                return ComponentHealth(
                    name="cache",
                    status=HealthStatus.HEALTHY,
                    message="缓存服务正常",
                    details={
                        "redis_version": info.get("redis_version"),
                        "connected_clients": info.get("connected_clients"),
                        "used_memory_mb": info.get("used_memory", 0) / (1024**2),
                        "keyspace_hits": info.get("keyspace_hits", 0),
                        "keyspace_misses": info.get("keyspace_misses", 0),
                        "ping_result": ping_result
                    },
                    response_time_ms=response_time
                )
                
            except redis.ConnectionError:
                return ComponentHealth(
                    name="cache",
                    status=HealthStatus.WARNING,
                    message="Redis服务不可用，使用内存缓存"
                )
            
        except Exception as e:
            logger.error(f"缓存检查失败: {e}")
            return ComponentHealth(
                name="cache",
                status=HealthStatus.CRITICAL,
                message=f"缓存检查失败: {str(e)}"
            )
    
    async def _check_disk_space(self) -> ComponentHealth:
        """检查磁盘空间"""
        try:
            start_time = time.time()
            
            disk_usage = psutil.disk_usage('/')
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            status = HealthStatus.HEALTHY
            if usage_percent > self.config["disk_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
            elif usage_percent > self.config["disk_usage_warning_threshold"]:
                status = HealthStatus.WARNING
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="disk",
                status=status,
                message=f"磁盘使用率: {usage_percent:.1f}%",
                details={
                    "total_gb": disk_usage.total / (1024**3),
                    "used_gb": disk_usage.used / (1024**3),
                    "free_gb": disk_usage.free / (1024**3),
                    "usage_percent": usage_percent
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"磁盘空间检查失败: {e}")
            return ComponentHealth(
                name="disk",
                status=HealthStatus.CRITICAL,
                message=f"磁盘空间检查失败: {str(e)}"
            )
    
    async def _check_memory_usage(self) -> ComponentHealth:
        """检查内存使用"""
        try:
            start_time = time.time()
            
            memory = psutil.virtual_memory()
            
            status = HealthStatus.HEALTHY
            if memory.percent > self.config["memory_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
            elif memory.percent > self.config["memory_usage_warning_threshold"]:
                status = HealthStatus.WARNING
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="memory",
                status=status,
                message=f"内存使用率: {memory.percent:.1f}%",
                details={
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "used_gb": memory.used / (1024**3),
                    "usage_percent": memory.percent,
                    "swap_percent": psutil.swap_memory().percent
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"内存使用检查失败: {e}")
            return ComponentHealth(
                name="memory",
                status=HealthStatus.CRITICAL,
                message=f"内存使用检查失败: {str(e)}"
            )
    
    async def _check_cpu_usage(self) -> ComponentHealth:
        """检查CPU使用"""
        try:
            start_time = time.time()
            
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            status = HealthStatus.HEALTHY
            if cpu_percent > self.config["cpu_usage_critical_threshold"]:
                status = HealthStatus.CRITICAL
            elif cpu_percent > self.config["cpu_usage_warning_threshold"]:
                status = HealthStatus.WARNING
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="cpu",
                status=status,
                message=f"CPU使用率: {cpu_percent:.1f}%",
                details={
                    "usage_percent": cpu_percent,
                    "cpu_count": cpu_count,
                    "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None,
                    "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"CPU使用检查失败: {e}")
            return ComponentHealth(
                name="cpu",
                status=HealthStatus.CRITICAL,
                message=f"CPU使用检查失败: {str(e)}"
            )
    
    async def _check_network_connectivity(self) -> ComponentHealth:
        """检查网络连接"""
        try:
            start_time = time.time()
            
            # 检查网络接口
            network_stats = psutil.net_io_counters()
            network_connections = len(psutil.net_connections())
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="network",
                status=HealthStatus.HEALTHY,
                message="网络连接正常",
                details={
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_recv": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_recv": network_stats.packets_recv,
                    "active_connections": network_connections
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            logger.error(f"网络连接检查失败: {e}")
            return ComponentHealth(
                name="network",
                status=HealthStatus.WARNING,
                message=f"网络连接检查失败: {str(e)}"
            )
    
    def _calculate_overall_status(self, results: Dict[str, ComponentHealth]) -> HealthStatus:
        """计算总体健康状态"""
        if not results:
            return HealthStatus.UNKNOWN
        
        statuses = [result.status for result in results.values()]
        
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif HealthStatus.HEALTHY in statuses:
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def _generate_health_summary(self, results: Dict[str, ComponentHealth]) -> Dict[str, int]:
        """生成健康状态摘要"""
        summary = {
            "healthy": 0,
            "warning": 0,
            "critical": 0,
            "unknown": 0
        }
        
        for result in results.values():
            summary[result.status.value] += 1
        
        return summary
    
    def _update_health_stats(self, results: Dict[str, ComponentHealth], total_time: float):
        """更新健康检查统计"""
        try:
            self.health_stats["total_checks"] += 1
            self.health_stats["last_full_check"] = datetime.now().isoformat()
            
            # 统计各状态数量
            for result in results.values():
                if result.status == HealthStatus.HEALTHY:
                    self.health_stats["healthy_checks"] += 1
                elif result.status == HealthStatus.WARNING:
                    self.health_stats["warning_checks"] += 1
                elif result.status == HealthStatus.CRITICAL:
                    self.health_stats["critical_checks"] += 1
                else:
                    self.health_stats["failed_checks"] += 1
            
            # 更新平均响应时间
            total_response_time = sum(result.response_time_ms for result in results.values())
            if results:
                current_avg = total_response_time / len(results)
                if self.health_stats["average_response_time"] == 0:
                    self.health_stats["average_response_time"] = current_avg
                else:
                    # 使用指数移动平均
                    self.health_stats["average_response_time"] = (
                        0.8 * self.health_stats["average_response_time"] + 0.2 * current_avg
                    )
            
        except Exception as e:
            logger.error(f"更新健康检查统计失败: {e}")
    
    async def start_periodic_checks(self):
        """启动定期健康检查"""
        try:
            if self.is_running:
                logger.warning("健康检查已在运行")
                return
            
            self.is_running = True
            self.check_task = asyncio.create_task(self._periodic_check_loop())
            logger.info("启动定期健康检查")
            
        except Exception as e:
            logger.error(f"启动定期健康检查失败: {e}")
    
    async def stop_periodic_checks(self):
        """停止定期健康检查"""
        try:
            if not self.is_running:
                return
            
            self.is_running = False
            if self.check_task:
                self.check_task.cancel()
                try:
                    await self.check_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("停止定期健康检查")
            
        except Exception as e:
            logger.error(f"停止定期健康检查失败: {e}")
    
    async def _periodic_check_loop(self):
        """定期检查循环"""
        try:
            while self.is_running:
                try:
                    await self.check_health()
                    await asyncio.sleep(self.config["check_interval_seconds"])
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"定期健康检查失败: {e}")
                    await asyncio.sleep(self.config["check_interval_seconds"])
                    
        except asyncio.CancelledError:
            logger.info("定期健康检查已取消")
    
    def get_health_statistics(self) -> Dict[str, Any]:
        """获取健康检查统计"""
        return {
            "stats": self.health_stats.copy(),
            "config": self.config.copy(),
            "current_results": {
                name: result.to_dict() 
                for name, result in self.health_results.items()
            }
        }
