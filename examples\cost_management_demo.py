"""
成本管理系统演示

展示成本管理、利润计算、供应商对比、机会发现等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.profit_analysis.cost_manager import (
    CostManager, CostRecord, CostType, AlertLevel
)
from app.services.profit_analysis.profit_calculator import (
    ProfitCalculator, PricingStrategy
)
from app.services.profit_analysis.supplier_comparator import (
    SupplierComparator, ComparisonCriteria
)
from app.services.profit_analysis.opportunity_finder import (
    OpportunityFinder, OpportunityType
)


async def demo_cost_management():
    """演示成本管理功能"""
    print("=== 成本管理系统演示 ===")
    
    cost_manager = CostManager()
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=111111",
            title="Apple iPhone 15 Pro 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=7999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=15000, rating=4.8)
        ),
        Product(
            url="https://item.jd.com/item.htm?id=222222",
            title="Samsung Galaxy S24 256GB 钛金灰",
            platform="jd",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="Samsung"),
            metrics=ProductMetrics(sales_count=12000, rating=4.7)
        )
    ]
    
    # 供应商信息
    suppliers = [
        {"id": "supplier_001", "name": "深圳科技供应商"},
        {"id": "supplier_002", "name": "广州电子供应商"},
        {"id": "supplier_003", "name": "上海贸易供应商"}
    ]
    
    print(f"\n1. 成本记录管理:")
    print(f"   测试商品数: {len(test_products)}")
    print(f"   供应商数: {len(suppliers)}")
    
    # 批量添加成本记录
    cost_records = []
    for product in test_products:
        for supplier in suppliers:
            # 为每个商品-供应商组合添加多种成本类型
            base_cost = 5000 + hash(supplier["id"]) % 1000  # 基础成本
            
            cost_types_data = [
                (CostType.PURCHASE_COST, base_cost),
                (CostType.SHIPPING_COST, base_cost * 0.05),
                (CostType.STORAGE_COST, base_cost * 0.02),
                (CostType.HANDLING_COST, base_cost * 0.01),
                (CostType.TAX_COST, base_cost * 0.13)
            ]
            
            for cost_type, cost_value in cost_types_data:
                # 添加历史成本记录（模拟成本变化）
                for days_ago in [30, 20, 10, 5, 0]:
                    variation = 1 + (hash(f"{days_ago}_{supplier['id']}") % 20 - 10) / 100  # ±10%变化
                    record = CostRecord(
                        supplier_id=supplier["id"],
                        product_id=product.id,
                        cost_type=cost_type,
                        cost_value=cost_value * variation,
                        currency="CNY",
                        unit="piece",
                        effective_date=datetime.now() - timedelta(days=days_ago),
                        notes=f"成本记录 - {supplier['name']}"
                    )
                    cost_records.append(record)
    
    # 批量添加成本记录
    batch_result = await cost_manager.batch_add_cost_records(cost_records)
    print(f"\n   批量添加结果:")
    print(f"     总记录数: {batch_result['total']}")
    print(f"     成功: {batch_result['success']}")
    print(f"     失败: {batch_result['failed']}")
    
    # 获取成本历史
    print(f"\n2. 成本历史分析:")
    target_product = test_products[0]
    target_supplier = suppliers[0]
    
    cost_history = await cost_manager.get_cost_history(
        target_supplier["id"], target_product.id, CostType.PURCHASE_COST
    )
    
    if cost_history:
        print(f"   商品: {target_product.title[:30]}...")
        print(f"   供应商: {target_supplier['name']}")
        print(f"   成本类型: {cost_history.cost_type.value}")
        print(f"   记录数量: {len(cost_history.cost_records)}")
        print(f"   平均成本: ¥{cost_history.average_cost:.2f}")
        print(f"   成本区间: ¥{cost_history.min_cost:.2f} - ¥{cost_history.max_cost:.2f}")
        print(f"   成本趋势: {cost_history.cost_trend.value}")
        print(f"   波动性: {cost_history.volatility:.2%}")
        print(f"   最后更新: {cost_history.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 供应商成本对比
    print(f"\n3. 供应商成本对比:")
    supplier_ids = [s["id"] for s in suppliers]
    
    comparison = await cost_manager.compare_supplier_costs(
        target_product.id, supplier_ids, CostType.PURCHASE_COST
    )
    
    print(f"   对比商品: {target_product.title[:30]}...")
    print(f"   对比供应商数: {len(comparison['suppliers'])}")
    print(f"   成本范围: ¥{comparison['cost_range']['min']:.2f} - ¥{comparison['cost_range']['max']:.2f}")
    print(f"   平均成本: ¥{comparison['average_cost']:.2f}")
    print(f"   最优供应商: {comparison['best_supplier']}")
    print(f"   最差供应商: {comparison['worst_supplier']}")
    
    print(f"\n   详细对比:")
    for supplier_id, data in comparison['suppliers'].items():
        supplier_name = next(s['name'] for s in suppliers if s['id'] == supplier_id)
        print(f"     {supplier_name}:")
        print(f"       当前成本: ¥{data['current_cost']:.2f}")
        print(f"       平均成本: ¥{data['average_cost']:.2f}")
        print(f"       成本趋势: {data['cost_trend']}")
        print(f"       波动性: {data['volatility']:.2%}")
        print(f"       记录数: {data['record_count']}")
    
    print(f"\n   对比建议:")
    for recommendation in comparison['recommendations']:
        print(f"     • {recommendation}")
    
    # 成本预警
    print(f"\n4. 成本预警系统:")
    alerts = await cost_manager.get_cost_alerts()
    
    print(f"   当前预警数: {len(alerts)}")
    
    if alerts:
        print(f"\n   预警详情:")
        for i, alert in enumerate(alerts[:3], 1):  # 显示前3个预警
            supplier_name = next(s['name'] for s in suppliers if s['id'] == alert.supplier_id)
            print(f"     预警{i}: {alert.alert_id}")
            print(f"       供应商: {supplier_name}")
            print(f"       成本类型: {alert.cost_type.value}")
            print(f"       预警级别: {alert.alert_level.value}")
            print(f"       预警类型: {alert.alert_type}")
            print(f"       当前成本: ¥{alert.current_cost:.2f}")
            print(f"       之前成本: ¥{alert.previous_cost:.2f}")
            print(f"       变化幅度: {alert.change_percent:+.1f}%")
            print(f"       描述: {alert.description}")
            print(f"       建议: {', '.join(alert.recommendations[:2])}")
            print(f"       创建时间: {alert.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("   暂无预警")
    
    # 供应商成本汇总
    print(f"\n5. 供应商成本汇总:")
    
    for supplier in suppliers[:2]:  # 显示前2个供应商
        summary = await cost_manager.get_supplier_cost_summary(supplier["id"])
        
        if summary:
            print(f"\n   {supplier['name']}:")
            print(f"     供应商ID: {summary.supplier_id}")
            print(f"     商品数量: {summary.total_products}")
            print(f"     平均成本: ¥{summary.average_cost:.2f}")
            print(f"     总成本价值: ¥{summary.total_cost_value:.2f}")
            print(f"     成本趋势: {summary.cost_trend.value}")
            print(f"     成本稳定性: {summary.cost_stability:.2%}")
            print(f"     性能评分: {summary.performance_score:.1f}分")
            print(f"     最后更新: {summary.last_update.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print(f"     成本分解:")
            for cost_type, value in summary.cost_breakdown.items():
                print(f"       {cost_type.value}: ¥{value:.2f}")
    
    # 统计信息
    print(f"\n6. 成本管理统计:")
    stats = cost_manager.get_cost_statistics()
    
    print(f"   总成本记录数: {stats['total_cost_records']}")
    print(f"   供应商数量: {stats['total_suppliers']}")
    print(f"   商品数量: {stats['total_products']}")
    print(f"   总预警数: {stats['total_alerts']}")
    print(f"   未解决预警: {stats['unresolved_alerts']}")
    print(f"   成本类型: {', '.join(stats['cost_types'])}")
    print(f"   预警级别: {', '.join(stats['alert_levels'])}")
    
    return test_products, suppliers, cost_manager


async def demo_profit_calculation():
    """演示利润计算功能"""
    print("\n=== 利润计算系统演示 ===")
    
    # 使用之前的数据
    test_products, suppliers, cost_manager = await demo_cost_management()
    profit_calculator = ProfitCalculator(cost_manager)
    
    print(f"\n1. 利润计算:")
    target_product = test_products[0]
    target_supplier = suppliers[0]
    
    # 计算利润
    profit_calc = await profit_calculator.calculate_profit(target_product, target_supplier["id"])
    
    if profit_calc:
        print(f"   商品: {target_product.title[:30]}...")
        print(f"   供应商: {target_supplier['name']}")
        print(f"   销售价格: ¥{profit_calc.selling_price:.2f}")
        print(f"   总成本: ¥{profit_calc.total_cost:.2f}")
        print(f"   毛利润: ¥{profit_calc.gross_profit:.2f}")
        print(f"   利润率: {profit_calc.profit_margin:.2%}")
        print(f"   利润率水平: {profit_calc.profit_margin_level.value}")
        print(f"   计算时间: {profit_calc.calculation_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n   成本分解:")
        for cost_type, value in profit_calc.cost_breakdown.items():
            print(f"     {cost_type.value}: ¥{value:.2f}")
    
    # 利润趋势分析
    print(f"\n2. 利润趋势分析:")
    
    # 先为多个时间点计算利润（模拟历史数据）
    for days_ago in [20, 15, 10, 5]:
        # 这里简化处理，实际应该有历史价格数据
        await profit_calculator.calculate_profit(target_product, target_supplier["id"])
    
    trend = await profit_calculator.analyze_profit_trend(target_product.id, target_supplier["id"])
    
    if trend:
        print(f"   商品: {target_product.title[:30]}...")
        print(f"   供应商: {target_supplier['name']}")
        print(f"   历史记录数: {len(trend.profit_history)}")
        print(f"   趋势方向: {trend.trend_direction}")
        print(f"   平均利润率: {trend.average_margin:.2%}")
        print(f"   利润率波动: {trend.margin_volatility:.2%}")
        print(f"   最佳利润率: {trend.best_margin:.2%}")
        print(f"   最差利润率: {trend.worst_margin:.2%}")
        print(f"   趋势分析: {trend.trend_analysis}")
    
    # 定价建议
    print(f"\n3. 定价建议:")
    
    pricing_rec = await profit_calculator.generate_pricing_recommendation(
        target_product, target_supplier["id"], target_margin=0.25
    )
    
    if pricing_rec:
        print(f"   商品: {target_product.title[:30]}...")
        print(f"   供应商: {target_supplier['name']}")
        print(f"   当前价格: ¥{pricing_rec.current_price:.2f}")
        print(f"   建议价格: ¥{pricing_rec.recommended_price:.2f}")
        print(f"   价格变化: ¥{pricing_rec.price_change:+.2f} ({pricing_rec.price_change_percent:+.1%})")
        print(f"   定价策略: {pricing_rec.strategy.value}")
        print(f"   预期利润率: {pricing_rec.expected_margin:.2%}")
        print(f"   建议理由: {pricing_rec.reasoning}")
        print(f"   风险评估: {pricing_rec.risk_assessment}")
        
        print(f"\n   实施建议:")
        for note in pricing_rec.implementation_notes[:3]:
            print(f"     • {note}")
    
    # 供应商盈利能力对比
    print(f"\n4. 供应商盈利能力对比:")
    
    supplier_ids = [s["id"] for s in suppliers]
    profitability_comparison = await profit_calculator.compare_supplier_profitability(
        target_product, supplier_ids
    )
    
    print(f"   对比商品: {target_product.title[:30]}...")
    print(f"   对比供应商数: {len(profitability_comparison['suppliers'])}")
    print(f"   利润率范围: {profitability_comparison['profit_range']['min']:.2%} - {profitability_comparison['profit_range']['max']:.2%}")
    print(f"   平均利润率: {profitability_comparison['average_margin']:.2%}")
    print(f"   最优供应商: {profitability_comparison['best_supplier']}")
    print(f"   最差供应商: {profitability_comparison['worst_supplier']}")
    
    print(f"\n   详细对比:")
    for supplier_id, data in profitability_comparison['suppliers'].items():
        supplier_name = next(s['name'] for s in suppliers if s['id'] == supplier_id)
        print(f"     {supplier_name}:")
        print(f"       利润率: {data['profit_margin']:.2%}")
        print(f"       毛利润: ¥{data['gross_profit']:.2f}")
        print(f"       总成本: ¥{data['total_cost']:.2f}")
        print(f"       利润水平: {data['margin_level']}")
    
    print(f"\n   盈利建议:")
    for recommendation in profitability_comparison['recommendations']:
        print(f"     • {recommendation}")
    
    # 利润统计
    print(f"\n5. 利润计算统计:")
    profit_stats = profit_calculator.get_profit_statistics()
    
    print(f"   总计算次数: {profit_stats['total_calculations']}")
    print(f"   商品数量: {profit_stats['total_products']}")
    print(f"   平均利润率: {profit_stats['average_margin']:.2%}")
    
    print(f"\n   利润率分布:")
    for level, count in profit_stats['margin_distribution'].items():
        print(f"     {level}: {count} 次")
    
    return test_products, suppliers, cost_manager, profit_calculator


async def demo_supplier_comparison():
    """演示供应商对比功能"""
    print("\n=== 供应商对比系统演示 ===")
    
    # 使用之前的数据
    test_products, suppliers, cost_manager, profit_calculator = await demo_profit_calculation()
    supplier_comparator = SupplierComparator(cost_manager, profit_calculator)
    
    print(f"\n1. 供应商综合对比:")
    target_product = test_products[0]
    supplier_ids = [s["id"] for s in suppliers]
    
    # 综合对比
    comparison = await supplier_comparator.compare_suppliers(
        target_product, supplier_ids, ComparisonCriteria.PERFORMANCE
    )
    
    print(f"   对比商品: {target_product.title[:30]}...")
    print(f"   对比标准: {comparison.criteria.value}")
    print(f"   参与对比供应商: {len(comparison.suppliers)}")
    print(f"   最优供应商: {comparison.best_supplier}")
    print(f"   最差供应商: {comparison.worst_supplier}")
    print(f"   对比摘要: {comparison.comparison_summary}")
    
    print(f"\n   供应商评分详情:")
    for supplier_metrics in comparison.suppliers:
        supplier_name = next(s['name'] for s in suppliers if s['id'] == supplier_metrics.supplier_id)
        print(f"     {supplier_name} ({supplier_metrics.rank.value}):")
        print(f"       综合评分: {supplier_metrics.overall_score:.1f}分")
        print(f"       成本评分: {supplier_metrics.cost_score:.1f}分")
        print(f"       利润评分: {supplier_metrics.profit_score:.1f}分")
        print(f"       稳定性评分: {supplier_metrics.stability_score:.1f}分")
        print(f"       可靠性评分: {supplier_metrics.reliability_score:.1f}分")
        
        if supplier_metrics.strengths:
            print(f"       优势: {', '.join(supplier_metrics.strengths[:2])}")
        if supplier_metrics.weaknesses:
            print(f"       劣势: {', '.join(supplier_metrics.weaknesses[:2])}")
    
    print(f"\n   对比建议:")
    for recommendation in comparison.recommendations:
        print(f"     • {recommendation}")
    
    # 最优供应商推荐
    print(f"\n2. 最优供应商推荐:")
    
    optimal_rec = await supplier_comparator.recommend_optimal_supplier(target_product, supplier_ids)
    
    recommended_supplier_name = next(s['name'] for s in suppliers if s['id'] == optimal_rec.recommended_supplier)
    print(f"   推荐供应商: {recommended_supplier_name}")
    print(f"   推荐理由: {optimal_rec.recommendation_reason}")
    print(f"   信心评分: {optimal_rec.confidence_score:.2%}")
    
    print(f"\n   备选供应商:")
    for alt_supplier_id in optimal_rec.alternative_suppliers:
        alt_supplier_name = next(s['name'] for s in suppliers if s['id'] == alt_supplier_id)
        print(f"     • {alt_supplier_name}")
    
    print(f"\n   预期收益:")
    for benefit in optimal_rec.expected_benefits:
        print(f"     • {benefit}")
    
    print(f"\n   潜在风险:")
    for risk in optimal_rec.potential_risks:
        print(f"     • {risk}")
    
    print(f"\n   实施计划:")
    for i, step in enumerate(optimal_rec.implementation_plan, 1):
        print(f"     {i}. {step}")
    
    # 对比统计
    print(f"\n3. 对比系统统计:")
    comparison_stats = supplier_comparator.get_comparison_statistics()
    
    print(f"   总对比次数: {comparison_stats['total_comparisons']}")
    print(f"   对比标准: {', '.join(comparison_stats['comparison_criteria'])}")
    print(f"   供应商等级: {', '.join(comparison_stats['supplier_ranks'])}")
    
    return test_products, suppliers, cost_manager, profit_calculator, supplier_comparator


async def main():
    """主演示函数"""
    print("🚀 成本管理系统演示")
    print("=" * 60)
    
    # 1. 成本管理演示
    test_products, suppliers, cost_manager = await demo_cost_management()
    
    # 2. 利润计算演示
    test_products, suppliers, cost_manager, profit_calculator = await demo_profit_calculation()
    
    # 3. 供应商对比演示
    test_products, suppliers, cost_manager, profit_calculator, supplier_comparator = await demo_supplier_comparison()
    
    print("\n" + "=" * 60)
    print("✅ 成本管理系统演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 成本管理：多类型成本记录，历史跟踪，变化预警")
    print(f"- 利润计算：实时利润率计算，趋势分析，定价建议")
    print(f"- 供应商对比：多维度评分，综合排名，最优推荐")
    print(f"- 预警系统：智能预警，风险识别，缓解策略")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: {len(test_products)}")
    print(f"- 供应商数: {len(suppliers)}")
    print(f"- 成本记录: {cost_manager.get_cost_statistics()['total_cost_records']}")
    print(f"- 利润计算: {profit_calculator.get_profit_statistics()['total_calculations']}")
    print(f"- 供应商对比: {supplier_comparator.get_comparison_statistics()['total_comparisons']}")
    print(f"- 成本预警: {cost_manager.get_cost_statistics()['total_alerts']}")
    
    print(f"\n🔧 技术特性:")
    print(f"- 多维度成本管理：5种成本类型，历史跟踪，趋势分析")
    print(f"- 智能预警系统：5级预警，多种检测算法，缓解策略")
    print(f"- 利润分析引擎：实时计算，趋势分析，定价建议")
    print(f"- 供应商评估：4维度评分，5级等级，智能推荐")
    print(f"- 批量处理：并发处理，缓存优化，统计分析")


if __name__ == "__main__":
    asyncio.run(main())
