"""
翻译服务系统演示

展示翻译服务系统的核心功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.translation.translation_engine import (
    TranslationEngine, TranslationRequest, LanguageCode, TextType, TranslationStatus
)
from app.services.translation.provider_manager import ProviderManager
from app.services.translation.quality_assessor import QualityAssessor, QualityRule, QualityMetric
from app.services.translation.cache_manager import CacheManager, CacheType


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"=== {title} ===")
    print()


async def demo_provider_manager():
    """演示提供商管理器"""
    print_section("提供商管理器演示")
    
    provider_manager = ProviderManager()
    
    print("1. 提供商管理器配置:")
    print(f"   总提供商数: {len(provider_manager.providers)}")
    print(f"   默认提供商: {provider_manager.default_provider_name}")
    
    print("\n2. 可用提供商:")
    for name, provider in provider_manager.providers.items():
        print(f"   {name}:")
        print(f"     类型: {provider.config.provider_type.value}")
        print(f"     优先级: {provider.config.priority}")
        print(f"     质量评分: {provider.config.quality_score}")
        print(f"     支持语言: {len(provider.config.supported_languages)}种")
        print(f"     状态: {provider.status.value}")
    
    # 测试提供商翻译
    print("\n3. 提供商翻译测试:")
    
    # OpenAI提供商
    openai_provider = provider_manager.providers["openai"]
    result = await openai_provider.translate("iPhone smartphone", "en", "zh")
    print(f"   OpenAI翻译: 'iPhone smartphone' -> '{result}'")
    
    # Claude提供商
    claude_provider = provider_manager.providers["claude"]
    result = await claude_provider.translate("laptop computer", "en", "zh")
    print(f"   Claude翻译: 'laptop computer' -> '{result}'")
    
    # 百度提供商
    baidu_provider = provider_manager.providers["baidu"]
    result = await baidu_provider.translate("Hello world", "en", "zh")
    print(f"   百度翻译: 'Hello world' -> '{result}'")
    
    # 选择最佳提供商
    print("\n4. 智能提供商选择:")
    best_provider = await provider_manager.select_best_provider(
        LanguageCode.ENGLISH, LanguageCode.CHINESE, TextType.PRODUCT_TITLE
    )
    print(f"   最佳提供商: {best_provider.name}")
    print(f"   选择原因: 优先级 {best_provider.config.priority}, 质量评分 {best_provider.config.quality_score}")
    
    # 健康检查
    print("\n5. 提供商健康检查:")
    health_status = await provider_manager.check_all_providers_health()
    for name, is_healthy in health_status.items():
        status = "健康" if is_healthy else "异常"
        print(f"   {name}: {status}")
    
    # 获取统计信息
    print("\n6. 提供商统计:")
    stats = provider_manager.get_provider_statistics()
    print(f"   总提供商数: {stats['total_providers']}")
    print(f"   活跃提供商数: {stats['active_providers']}")
    
    return provider_manager


async def demo_quality_assessor():
    """演示质量评估器"""
    print_section("质量评估器演示")
    
    quality_assessor = QualityAssessor()
    
    print("1. 质量评估器配置:")
    print(f"   质量规则数: {len(quality_assessor.quality_rules)}")
    print(f"   自动评估: {quality_assessor.assessment_config['enable_automatic_assessment']}")
    print(f"   规则评估: {quality_assessor.assessment_config['enable_rule_based_assessment']}")
    print(f"   统计评估: {quality_assessor.assessment_config['enable_statistical_assessment']}")
    
    print("\n2. 质量规则:")
    for rule_id, rule in quality_assessor.quality_rules.items():
        print(f"   {rule_id}:")
        print(f"     名称: {rule.name}")
        print(f"     指标: {rule.metric.value}")
        print(f"     权重: {rule.weight}")
        print(f"     启用: {rule.enabled}")
    
    # 测试翻译质量评估
    print("\n3. 翻译质量评估测试:")
    
    # 良好翻译
    print("\n   测试1: 良好翻译")
    score1 = await quality_assessor.assess_translation(
        "iPhone 15 Pro 256GB smartphone",
        "iPhone 15 Pro 256GB 智能手机",
        LanguageCode.ENGLISH,
        LanguageCode.CHINESE,
        TextType.PRODUCT_TITLE
    )
    print(f"     原文: 'iPhone 15 Pro 256GB smartphone'")
    print(f"     译文: 'iPhone 15 Pro 256GB 智能手机'")
    print(f"     总体评分: {score1.overall_score:.2f}")
    print(f"     质量等级: {score1.quality_level.value}")
    print(f"     置信度: {score1.confidence:.2f}")
    print(f"     指标评分: {score1.metric_scores}")
    if score1.issues:
        print(f"     发现问题: {score1.issues}")
    
    # 空翻译
    print("\n   测试2: 空翻译")
    score2 = await quality_assessor.assess_translation(
        "Test product description",
        "",
        LanguageCode.ENGLISH,
        LanguageCode.CHINESE,
        TextType.PRODUCT_DESCRIPTION
    )
    print(f"     原文: 'Test product description'")
    print(f"     译文: ''")
    print(f"     总体评分: {score2.overall_score:.2f}")
    print(f"     质量等级: {score2.quality_level.value}")
    print(f"     发现问题: {score2.issues}")
    print(f"     改进建议: {score2.suggestions}")
    
    # 数字不一致
    print("\n   测试3: 数字不一致")
    score3 = await quality_assessor.assess_translation(
        "iPhone 15 Pro 256GB",
        "iPhone 15 Pro 128GB",
        LanguageCode.ENGLISH,
        LanguageCode.CHINESE,
        TextType.PRODUCT_TITLE
    )
    print(f"     原文: 'iPhone 15 Pro 256GB'")
    print(f"     译文: 'iPhone 15 Pro 128GB'")
    print(f"     总体评分: {score3.overall_score:.2f}")
    print(f"     质量等级: {score3.quality_level.value}")
    print(f"     发现问题: {score3.issues}")
    
    # 添加自定义规则
    print("\n4. 添加自定义质量规则:")
    custom_rule = QualityRule(
        rule_id="custom_brand_check",
        name="自定义品牌检查",
        description="检查特定品牌名称翻译",
        metric=QualityMetric.TERMINOLOGY,
        weight=1.5,
        enabled=True
    )
    
    success = quality_assessor.add_quality_rule(custom_rule)
    print(f"   规则添加结果: {'成功' if success else '失败'}")
    print(f"   当前规则总数: {len(quality_assessor.quality_rules)}")
    
    # 获取质量统计
    print("\n5. 质量评估统计:")
    stats = quality_assessor.get_quality_statistics()
    print(f"   总评估次数: {stats['total_assessments']}")
    print(f"   平均评分: {stats['average_score']:.2f}")
    print(f"   平均置信度: {stats['average_confidence']:.2f}")
    print(f"   总规则数: {stats['total_rules']}")
    print(f"   启用规则数: {stats['enabled_rules']}")
    
    return quality_assessor


async def demo_cache_manager():
    """演示缓存管理器"""
    print_section("缓存管理器演示")
    
    cache_manager = CacheManager(CacheType.MEMORY)
    
    print("1. 缓存管理器配置:")
    print(f"   缓存类型: {cache_manager.cache_type.value}")
    print(f"   最大大小: {cache_manager.max_size_bytes // (1024*1024)}MB")
    print(f"   默认过期时间: {cache_manager.cache_config['default_expire_seconds']}秒")
    print(f"   最大条目数: {cache_manager.cache_config['max_entries']}")
    print(f"   淘汰策略: {cache_manager.cache_config['eviction_policy']}")
    
    # 测试缓存操作
    print("\n2. 缓存操作测试:")
    
    # 设置缓存
    test_data = {
        "original": "Hello world",
        "translated": "你好世界",
        "provider": "openai",
        "score": 8.5
    }
    
    success = await cache_manager.set("test_key_1", test_data, expire_seconds=3600)
    print(f"   设置缓存: {'成功' if success else '失败'}")
    
    # 获取缓存
    cached_data = await cache_manager.get("test_key_1")
    print(f"   获取缓存: {'成功' if cached_data else '失败'}")
    if cached_data:
        print(f"     缓存内容: {cached_data}")
    
    # 设置多个缓存条目
    print("\n3. 批量缓存测试:")
    for i in range(5):
        key = f"batch_key_{i}"
        value = {
            "text": f"Test text {i}",
            "translation": f"测试文本 {i}",
            "timestamp": datetime.now().isoformat()
        }
        await cache_manager.set(key, value)
    
    print(f"   批量设置: 5个缓存条目")
    
    # 测试缓存命中
    hit_count = 0
    for i in range(5):
        key = f"batch_key_{i}"
        data = await cache_manager.get(key)
        if data:
            hit_count += 1
    
    print(f"   批量获取: {hit_count}/5 命中")
    
    # 测试缓存过期
    print("\n4. 缓存过期测试:")
    await cache_manager.set("expire_test", "expire_value", expire_seconds=1)
    
    # 立即获取
    value = await cache_manager.get("expire_test")
    print(f"   立即获取: {'成功' if value else '失败'}")
    
    # 等待过期
    print("   等待1秒...")
    await asyncio.sleep(1.1)
    
    # 过期后获取
    value = await cache_manager.get("expire_test")
    print(f"   过期后获取: {'失败（正常）' if not value else '异常（应该过期）'}")
    
    # 清理过期缓存
    print("\n5. 缓存清理测试:")
    await cache_manager.cleanup_expired()
    print("   过期缓存清理完成")
    
    # 获取缓存统计
    print("\n6. 缓存统计:")
    stats = cache_manager.get_cache_statistics()
    print(f"   总条目数: {stats['total_entries']}")
    print(f"   活跃条目数: {stats['active_entries']}")
    print(f"   过期条目数: {stats['expired_entries']}")
    print(f"   总大小: {stats['total_size_mb']:.2f}MB")
    print(f"   命中次数: {stats['hit_count']}")
    print(f"   未命中次数: {stats['miss_count']}")
    print(f"   命中率: {stats['hit_rate']:.1f}%")
    print(f"   淘汰次数: {stats['eviction_count']}")
    
    return cache_manager


async def demo_translation_engine():
    """演示翻译引擎"""
    print_section("翻译引擎演示")
    
    # 创建组件
    provider_manager = ProviderManager()
    quality_assessor = QualityAssessor()
    cache_manager = CacheManager(CacheType.MEMORY)
    
    # 创建翻译引擎
    translation_engine = TranslationEngine(provider_manager, quality_assessor, cache_manager)
    
    print("1. 翻译引擎配置:")
    print(f"   最大并发数: {translation_engine.translation_config['max_concurrent']}")
    print(f"   超时时间: {translation_engine.translation_config['timeout_seconds']}秒")
    print(f"   重试次数: {translation_engine.translation_config['retry_attempts']}")
    print(f"   缓存启用: {translation_engine.translation_config['cache_enabled']}")
    print(f"   质量检查启用: {translation_engine.translation_config['quality_check_enabled']}")
    print(f"   自动选择提供商: {translation_engine.translation_config['auto_provider_selection']}")
    
    # 单个翻译测试
    print("\n2. 单个翻译测试:")
    
    # 英文到中文
    request1 = TranslationRequest(
        request_id="demo_001",
        text="iPhone 15 Pro Max smartphone with 256GB storage",
        source_lang=LanguageCode.ENGLISH,
        target_lang=LanguageCode.CHINESE,
        text_type=TextType.PRODUCT_TITLE,
        priority=1
    )
    
    result1 = await translation_engine.translate(request1)
    print(f"   请求ID: {result1.request_id}")
    print(f"   原文: {result1.original_text}")
    print(f"   译文: {result1.translated_text}")
    print(f"   提供商: {result1.provider}")
    print(f"   状态: {result1.status.value}")
    print(f"   处理时间: {result1.processing_time:.3f}秒")
    print(f"   成本: ${result1.cost:.4f}")
    print(f"   缓存: {'是' if result1.cached else '否'}")
    if result1.quality_score:
        print(f"   质量评分: {result1.quality_score.overall_score:.2f}")
        print(f"   质量等级: {result1.quality_score.quality_level.value}")
    
    # 中文到英文
    print("\n   中文到英文翻译:")
    request2 = TranslationRequest(
        request_id="demo_002",
        text="苹果手机 iPhone 15 Pro Max 256GB 存储",
        source_lang=LanguageCode.CHINESE,
        target_lang=LanguageCode.ENGLISH,
        text_type=TextType.PRODUCT_TITLE,
        priority=2
    )
    
    result2 = await translation_engine.translate(request2)
    print(f"   原文: {result2.original_text}")
    print(f"   译文: {result2.translated_text}")
    print(f"   提供商: {result2.provider}")
    print(f"   处理时间: {result2.processing_time:.3f}秒")
    
    # 测试缓存命中
    print("\n3. 缓存命中测试:")
    request3 = TranslationRequest(
        request_id="demo_003",
        text="iPhone 15 Pro Max smartphone with 256GB storage",  # 相同文本
        source_lang=LanguageCode.ENGLISH,
        target_lang=LanguageCode.CHINESE,
        text_type=TextType.PRODUCT_TITLE,
        priority=1
    )
    
    result3 = await translation_engine.translate(request3)
    print(f"   状态: {result3.status.value}")
    print(f"   缓存命中: {'是' if result3.cached else '否'}")
    print(f"   处理时间: {result3.processing_time:.3f}秒")
    
    # 批量翻译测试
    print("\n4. 批量翻译测试:")
    batch_requests = [
        TranslationRequest(
            request_id=f"batch_{i}",
            text=f"Product {i}: High quality smartphone device",
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.PRODUCT_DESCRIPTION,
            priority=i % 3 + 1
        )
        for i in range(5)
    ]
    
    batch_results = await translation_engine.batch_translate(batch_requests)
    print(f"   批量请求数: {len(batch_requests)}")
    print(f"   批量结果数: {len(batch_results)}")
    print(f"   成功翻译数: {len([r for r in batch_results if r.status == TranslationStatus.COMPLETED])}")
    print(f"   缓存命中数: {len([r for r in batch_results if r.status == TranslationStatus.CACHED])}")
    
    # 获取翻译统计
    print("\n5. 翻译引擎统计:")
    stats = translation_engine.get_translation_statistics()
    print(f"   总请求数: {stats['total_requests']}")
    print(f"   成功翻译数: {stats['successful_translations']}")
    print(f"   失败翻译数: {stats['failed_translations']}")
    print(f"   缓存命中数: {stats['cache_hits']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    print(f"   缓存命中率: {stats['cache_hit_rate']:.1f}%")
    print(f"   总成本: ${stats['total_cost']:.4f}")
    print(f"   平均处理时间: {stats['avg_processing_time']:.3f}秒")
    
    print("\n   语言对分布:")
    for pair, count in stats['language_pairs'].items():
        print(f"     {pair}: {count} 次")
    
    print("\n   文本类型分布:")
    for text_type, count in stats['text_types'].items():
        print(f"     {text_type}: {count} 次")
    
    print("\n   提供商分布:")
    for provider, count in stats['providers'].items():
        print(f"     {provider}: {count} 次")
    
    return translation_engine


async def main():
    """主演示函数"""
    print("🚀 翻译服务系统演示")
    print("="*60)
    
    # 演示提供商管理器
    provider_manager = await demo_provider_manager()
    
    # 演示质量评估器
    quality_assessor = await demo_quality_assessor()
    
    # 演示缓存管理器
    cache_manager = await demo_cache_manager()
    
    # 演示翻译引擎
    translation_engine = await demo_translation_engine()
    
    # 总结
    print_section("翻译服务系统演示完成")
    
    print("🎯 核心功能:")
    print("- 提供商管理器：多LLM提供商支持，智能选择，健康监控")
    print("- 质量评估器：多维度质量评估，规则引擎，统计分析")
    print("- 缓存管理器：高效缓存机制，过期管理，统计监控")
    print("- 翻译引擎：统一翻译接口，批量处理，成本控制")
    
    print("\n📊 演示统计:")
    print(f"- 提供商管理器：{len(provider_manager.providers)} 个提供商")
    print(f"- 质量评估器：{len(quality_assessor.quality_rules)} 个质量规则")
    print(f"- 缓存管理器：{cache_manager.stats.total_entries} 个缓存条目")
    print(f"- 翻译引擎：{translation_engine.stats['total_requests']} 个翻译请求")
    
    print("\n🔧 技术特性:")
    print("- 多提供商支持：OpenAI、Claude、百度翻译等")
    print("- 智能质量评估：6个质量规则，多维度评分")
    print("- 高效缓存机制：内存缓存，自动过期，智能淘汰")
    print("- 批量处理优化：并发翻译，优先级队列，成本控制")
    print("- 统计监控：全面的使用统计和性能监控")
    
    # 停止缓存清理任务
    cache_manager.stop_cleanup_task()


if __name__ == "__main__":
    asyncio.run(main())
