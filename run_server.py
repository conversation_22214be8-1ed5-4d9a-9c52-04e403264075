#!/usr/bin/env python3
"""
Moniit 服务器启动脚本
"""

import uvicorn
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动 Moniit 服务器...")
    print("📍 访问地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("🔧 ReDoc文档: http://localhost:8001/redoc")
    print("❤️ 健康检查: http://localhost:8001/health")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8001,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)
