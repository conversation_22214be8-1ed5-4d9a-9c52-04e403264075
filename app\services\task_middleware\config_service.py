"""
平台配置管理服务

提供完整的配置CRUD操作、模板管理和验证功能
支持动态配置更新和配置继承机制
"""

import asyncio
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml
import json
from datetime import datetime
import shutil

from app.core.logging import get_logger
from .config_manager import (
    PlatformConfigManager, Platform, ProductType, 
    PlatformConfig, PlatformBaseConfig, ProductTypeConfig,
    SelectorConfig, RateLimitConfig
)

logger = get_logger(__name__)


@dataclass
class ConfigValidationResult:
    """配置验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    score: float  # 配置质量分数 0-1


@dataclass
class ConfigTemplate:
    """配置模板"""
    name: str
    description: str
    platform: Platform
    template_data: Dict[str, Any]
    version: str = "1.0"
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class PlatformConfigService:
    """平台配置管理服务"""
    
    def __init__(self, config_dir: str = "config/platforms", template_dir: str = "config/templates"):
        self.config_manager = PlatformConfigManager(config_dir)
        self.config_dir = Path(config_dir)
        self.template_dir = Path(template_dir)
        self.backup_dir = Path(config_dir) / "backups"
        
        # 创建目录
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self._templates: Dict[str, ConfigTemplate] = {}
        self._load_templates()
        
    def _load_templates(self):
        """加载配置模板"""
        try:
            for template_file in self.template_dir.glob("*.yaml"):
                with open(template_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    template = ConfigTemplate(
                        name=data['name'],
                        description=data['description'],
                        platform=Platform(data['platform']),
                        template_data=data['template_data'],
                        version=data.get('version', '1.0'),
                        created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat()))
                    )
                    self._templates[template.name] = template
                    
            logger.info(f"加载了 {len(self._templates)} 个配置模板")
        except Exception as e:
            logger.error(f"加载配置模板失败: {e}")
    
    # CRUD操作
    async def create_platform_config(self, platform: Platform, config_data: Dict[str, Any], 
                                   template_name: Optional[str] = None) -> bool:
        """
        创建平台配置
        
        Args:
            platform: 平台类型
            config_data: 配置数据
            template_name: 模板名称（可选）
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 如果指定了模板，先应用模板
            if template_name and template_name in self._templates:
                template = self._templates[template_name]
                merged_data = self._merge_template_config(template.template_data, config_data)
            else:
                merged_data = config_data
            
            # 验证配置
            validation_result = await self.validate_config(merged_data)
            if not validation_result.is_valid:
                logger.error(f"配置验证失败: {validation_result.errors}")
                return False
            
            # 转换为PlatformConfig对象
            platform_config = self.config_manager._dict_to_platform_config(merged_data)
            if not platform_config:
                logger.error("配置转换失败")
                return False
            
            # 保存配置
            self.config_manager.save_config(platform, platform_config)
            
            logger.info(f"成功创建平台配置: {platform.value}")
            return True
            
        except Exception as e:
            logger.error(f"创建平台配置失败: {e}")
            return False
    
    async def get_platform_config(self, platform: Platform) -> Optional[Dict[str, Any]]:
        """
        获取平台配置
        
        Args:
            platform: 平台类型
            
        Returns:
            Optional[Dict[str, Any]]: 配置数据
        """
        try:
            config = self.config_manager.get_platform_config(platform)
            if config:
                return asdict(config)
            
            # 尝试从文件加载
            config = self.config_manager.load_config_from_file(platform)
            if config:
                return asdict(config)
                
            return None
            
        except Exception as e:
            logger.error(f"获取平台配置失败: {e}")
            return None
    
    async def update_platform_config(self, platform: Platform, updates: Dict[str, Any]) -> bool:
        """
        更新平台配置
        
        Args:
            platform: 平台类型
            updates: 更新数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 获取当前配置
            current_config = await self.get_platform_config(platform)
            if not current_config:
                logger.error(f"平台配置不存在: {platform.value}")
                return False
            
            # 备份当前配置
            await self._backup_config(platform)
            
            # 合并更新
            merged_config = self._deep_merge(current_config, updates)
            
            # 验证更新后的配置
            validation_result = await self.validate_config(merged_config)
            if not validation_result.is_valid:
                logger.error(f"更新后配置验证失败: {validation_result.errors}")
                return False
            
            # 转换并保存
            platform_config = self.config_manager._dict_to_platform_config(merged_config)
            if platform_config:
                self.config_manager.save_config(platform, platform_config)
                logger.info(f"成功更新平台配置: {platform.value}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新平台配置失败: {e}")
            return False
    
    async def delete_platform_config(self, platform: Platform) -> bool:
        """
        删除平台配置
        
        Args:
            platform: 平台类型
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 备份配置
            await self._backup_config(platform)
            
            # 删除内存中的配置
            if platform in self.config_manager._configs:
                del self.config_manager._configs[platform]
            
            # 删除配置文件
            config_file = self.config_dir / f"{platform.value}.yaml"
            if config_file.exists():
                config_file.unlink()
                
            logger.info(f"成功删除平台配置: {platform.value}")
            return True
            
        except Exception as e:
            logger.error(f"删除平台配置失败: {e}")
            return False
    
    async def list_platform_configs(self) -> List[Dict[str, Any]]:
        """
        列出所有平台配置
        
        Returns:
            List[Dict[str, Any]]: 配置列表
        """
        configs = []
        
        try:
            # 获取内存中的配置
            for platform in self.config_manager.list_platforms():
                config_data = await self.get_platform_config(platform)
                if config_data:
                    configs.append({
                        'platform': platform.value,
                        'name': config_data['base_config']['name'],
                        'enabled': config_data.get('enabled', True),
                        'product_types': list(config_data['product_types'].keys())
                    })
            
            # 检查文件系统中的其他配置
            for config_file in self.config_dir.glob("*.yaml"):
                platform_name = config_file.stem
                try:
                    platform = Platform(platform_name)
                    if platform not in self.config_manager._configs:
                        config = self.config_manager.load_config_from_file(platform)
                        if config:
                            configs.append({
                                'platform': platform.value,
                                'name': config.base_config.name,
                                'enabled': config.enabled,
                                'product_types': list(config.product_types.keys())
                            })
                except ValueError:
                    # 不是有效的平台名称，跳过
                    continue
                    
            return configs
            
        except Exception as e:
            logger.error(f"列出平台配置失败: {e}")
            return []
    
    # 模板管理
    async def create_template(self, template: ConfigTemplate) -> bool:
        """
        创建配置模板
        
        Args:
            template: 配置模板
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 验证模板数据
            validation_result = await self.validate_config(template.template_data)
            if not validation_result.is_valid:
                logger.error(f"模板验证失败: {validation_result.errors}")
                return False
            
            # 保存模板
            self._templates[template.name] = template
            
            # 保存到文件
            template_file = self.template_dir / f"{template.name}.yaml"
            template_data = asdict(template)
            template_data['created_at'] = template.created_at.isoformat()
            
            with open(template_file, 'w', encoding='utf-8') as f:
                yaml.dump(template_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"成功创建配置模板: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"创建配置模板失败: {e}")
            return False
    
    def get_template(self, template_name: str) -> Optional[ConfigTemplate]:
        """
        获取配置模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            Optional[ConfigTemplate]: 配置模板
        """
        return self._templates.get(template_name)
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """
        列出所有配置模板
        
        Returns:
            List[Dict[str, Any]]: 模板列表
        """
        return [
            {
                'name': template.name,
                'description': template.description,
                'platform': template.platform.value,
                'version': template.version,
                'created_at': template.created_at.isoformat()
            }
            for template in self._templates.values()
        ]
    
    def _merge_template_config(self, template_data: Dict[str, Any], 
                             config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并模板配置和用户配置
        
        Args:
            template_data: 模板数据
            config_data: 用户配置数据
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        return self._deep_merge(template_data.copy(), config_data)
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并字典
        
        Args:
            base: 基础字典
            updates: 更新字典
            
        Returns:
            Dict[str, Any]: 合并后的字典
        """
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result

    # 配置验证
    async def validate_config(self, config_data: Dict[str, Any]) -> ConfigValidationResult:
        """
        验证配置数据

        Args:
            config_data: 配置数据

        Returns:
            ConfigValidationResult: 验证结果
        """
        errors = []
        warnings = []
        score = 1.0

        try:
            # 必需字段检查
            required_fields = ['platform', 'base_config', 'product_types']
            for field in required_fields:
                if field not in config_data:
                    errors.append(f"缺少必需字段: {field}")
                    score -= 0.3

            if errors:
                return ConfigValidationResult(False, errors, warnings, max(score, 0))

            # 验证平台类型
            try:
                Platform(config_data['platform'])
            except ValueError:
                errors.append(f"无效的平台类型: {config_data['platform']}")
                score -= 0.2

            # 验证基础配置
            base_config = config_data['base_config']
            base_required = ['name', 'base_url', 'selectors', 'rate_limit']
            for field in base_required:
                if field not in base_config:
                    errors.append(f"基础配置缺少字段: {field}")
                    score -= 0.1

            # 验证选择器配置
            if 'selectors' in base_config:
                selectors = base_config['selectors']
                selector_required = ['title', 'price']
                for field in selector_required:
                    if field not in selectors:
                        errors.append(f"选择器配置缺少字段: {field}")
                        score -= 0.1
                    elif not selectors[field]:
                        warnings.append(f"选择器字段为空: {field}")
                        score -= 0.05

            # 验证速率限制配置
            if 'rate_limit' in base_config:
                rate_limit = base_config['rate_limit']
                if 'requests_per_minute' in rate_limit:
                    rpm = rate_limit['requests_per_minute']
                    if not isinstance(rpm, int) or rpm <= 0:
                        errors.append("requests_per_minute必须是正整数")
                        score -= 0.1
                    elif rpm > 100:
                        warnings.append("requests_per_minute过高，可能被限制")
                        score -= 0.05

            # 验证商品类型配置
            product_types = config_data['product_types']
            if not product_types:
                warnings.append("没有配置商品类型")
                score -= 0.1
            else:
                for ptype, pconfig in product_types.items():
                    try:
                        ProductType(ptype)
                    except ValueError:
                        errors.append(f"无效的商品类型: {ptype}")
                        score -= 0.1

                    # 验证监控频率
                    if 'monitoring_frequency' in pconfig:
                        freq = pconfig['monitoring_frequency']
                        if not isinstance(freq, int) or freq < 60:
                            warnings.append(f"商品类型 {ptype} 的监控频率过低")
                            score -= 0.05

            # URL格式验证
            if 'base_url' in base_config:
                base_url = base_config['base_url']
                if not base_url.startswith(('http://', 'https://')):
                    errors.append("base_url必须以http://或https://开头")
                    score -= 0.1

            is_valid = len(errors) == 0
            return ConfigValidationResult(is_valid, errors, warnings, max(score, 0))

        except Exception as e:
            logger.error(f"配置验证异常: {e}")
            return ConfigValidationResult(False, [f"验证异常: {e}"], [], 0)

    async def test_config(self, platform: Platform, test_urls: List[str] = None) -> Dict[str, Any]:
        """
        测试平台配置

        Args:
            platform: 平台类型
            test_urls: 测试URL列表

        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            config = self.config_manager.get_platform_config(platform)
            if not config:
                return {
                    'success': False,
                    'error': f'平台配置不存在: {platform.value}',
                    'tests': []
                }

            test_results = []

            # 测试1: 配置验证
            config_dict = asdict(config)
            validation_result = await self.validate_config(config_dict)
            test_results.append({
                'test': 'config_validation',
                'success': validation_result.is_valid,
                'score': validation_result.score,
                'errors': validation_result.errors,
                'warnings': validation_result.warnings
            })

            # 测试2: 查询构建
            try:
                for product_type in ProductType:
                    query = self.config_manager.build_crawl_query(platform, product_type)
                    test_results.append({
                        'test': f'query_build_{product_type.value}',
                        'success': bool(query),
                        'query': query
                    })
            except Exception as e:
                test_results.append({
                    'test': 'query_build',
                    'success': False,
                    'error': str(e)
                })

            # 测试3: 监控频率配置
            try:
                frequencies = {}
                for product_type in ProductType:
                    freq = self.config_manager.get_monitoring_frequency(platform, product_type)
                    frequencies[product_type.value] = freq

                test_results.append({
                    'test': 'monitoring_frequency',
                    'success': True,
                    'frequencies': frequencies
                })
            except Exception as e:
                test_results.append({
                    'test': 'monitoring_frequency',
                    'success': False,
                    'error': str(e)
                })

            # 测试4: 选择器有效性（如果提供了测试URL）
            if test_urls:
                selector_tests = []
                selectors = config.base_config.selectors

                for url in test_urls[:3]:  # 最多测试3个URL
                    selector_tests.append({
                        'url': url,
                        'selectors': {
                            'title': selectors.title,
                            'price': selectors.price,
                            'stock': selectors.stock,
                            'sales_count': selectors.sales_count
                        },
                        'note': '需要实际爬取测试'
                    })

                test_results.append({
                    'test': 'selector_validation',
                    'success': True,
                    'selector_tests': selector_tests
                })

            # 计算总体成功率
            successful_tests = sum(1 for test in test_results if test.get('success', False))
            total_tests = len(test_results)
            success_rate = successful_tests / total_tests if total_tests > 0 else 0

            return {
                'success': success_rate > 0.8,
                'success_rate': success_rate,
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'tests': test_results,
                'summary': f'配置测试完成: {successful_tests}/{total_tests} 通过'
            }

        except Exception as e:
            logger.error(f"配置测试失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'tests': []
            }

    # 备份和恢复
    async def _backup_config(self, platform: Platform):
        """
        备份平台配置

        Args:
            platform: 平台类型
        """
        try:
            config_file = self.config_dir / f"{platform.value}.yaml"
            if config_file.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = self.backup_dir / f"{platform.value}_{timestamp}.yaml"
                shutil.copy2(config_file, backup_file)
                logger.info(f"配置已备份: {backup_file}")
        except Exception as e:
            logger.error(f"配置备份失败: {e}")

    async def restore_config(self, platform: Platform, backup_timestamp: str) -> bool:
        """
        恢复平台配置

        Args:
            platform: 平台类型
            backup_timestamp: 备份时间戳

        Returns:
            bool: 是否恢复成功
        """
        try:
            backup_file = self.backup_dir / f"{platform.value}_{backup_timestamp}.yaml"
            if not backup_file.exists():
                logger.error(f"备份文件不存在: {backup_file}")
                return False

            config_file = self.config_dir / f"{platform.value}.yaml"
            shutil.copy2(backup_file, config_file)

            # 重新加载配置
            self.config_manager.load_config_from_file(platform)

            logger.info(f"配置已恢复: {platform.value} from {backup_timestamp}")
            return True

        except Exception as e:
            logger.error(f"配置恢复失败: {e}")
            return False

    def list_backups(self, platform: Platform) -> List[str]:
        """
        列出平台配置备份

        Args:
            platform: 平台类型

        Returns:
            List[str]: 备份时间戳列表
        """
        try:
            backups = []
            pattern = f"{platform.value}_*.yaml"

            for backup_file in self.backup_dir.glob(pattern):
                # 提取时间戳
                timestamp = backup_file.stem.replace(f"{platform.value}_", "")
                backups.append(timestamp)

            return sorted(backups, reverse=True)  # 最新的在前

        except Exception as e:
            logger.error(f"列出备份失败: {e}")
            return []
