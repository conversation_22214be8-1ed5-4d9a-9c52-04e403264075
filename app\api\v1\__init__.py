"""
API v1 模块 - 架构重构后

⚠️ 注意：根据新架构设计，部分API模块已废弃
- analytics: 数据分析API已废弃，等待新架构实现
- profit: 利润分析API已废弃，将重新设计为利差计算模块
- suppliers: 供货商管理API已废弃，将整合到利差计算模块
- monitoring: 监控管理API已废弃，将重新设计为Task-Middleware集成

保留的模块：
- products: 商品管理API（将调整为新的商品管理业务层）
- system: 系统管理API（基础功能保留）

参考：RPD/tasks_simplified.md 第773行开始的阶段2架构
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,  # 新增：认证相关API
    products,
    analytics,  # 已废弃，保留空路由器避免导入错误
    platform_config,  # 新增：平台配置管理
    task_scheduler,  # 新增：任务调度管理
    product_management,  # 新增：商品管理业务层
    monitor,  # 新增：监控管理API
    suppliers,  # 新增：供货商管理API
    system
)

# 创建API路由器
api_router = APIRouter()

# 注册认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["用户认证"]
)

# 注册保留的模块路由
api_router.include_router(
    products.router,
    prefix="/products",
    tags=["商品管理"]
)

# 注册废弃但保留空路由器的模块（避免导入错误）
api_router.include_router(
    analytics.router,
    prefix="/analytics",
    tags=["数据分析 (已废弃)"]
)

api_router.include_router(
    platform_config.router,
    prefix="/platform-config",
    tags=["平台配置管理"]
)

api_router.include_router(
    task_scheduler.router,
    prefix="/task-scheduler",
    tags=["任务调度管理"]
)

api_router.include_router(
    product_management.router,
    prefix="/product-management",
    tags=["商品管理业务层"]
)

api_router.include_router(
    monitor.router,
    prefix="/monitor",
    tags=["监控管理"]
)

api_router.include_router(
    suppliers.router,
    prefix="/suppliers",
    tags=["供货商管理"]
)

api_router.include_router(
    system.router,
    prefix="/system",
    tags=["系统管理"]
)

# 以下模块已完全移除：
# - profit: 利润分析API (将重新设计为利差计算模块)
# - suppliers: 供货商管理API (将整合到利差计算模块)
# - monitoring: 监控管理API (将重新设计为Task-Middleware集成)
