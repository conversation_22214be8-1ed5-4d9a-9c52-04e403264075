"""
导出管理器

提供报表导出和分享功能
"""

import os
import io
import uuid
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import base64

from app.core.logging import get_logger
from .report_engine import ReportResult, ReportFormat

logger = get_logger(__name__)


class ExportFormat(Enum):
    """导出格式"""
    PDF = "pdf"             # PDF格式
    EXCEL = "excel"         # Excel格式
    CSV = "csv"             # CSV格式
    HTML = "html"           # HTML格式
    JSON = "json"           # JSON格式
    PNG = "png"             # PNG图片
    ZIP = "zip"             # ZIP压缩包


class ShareType(Enum):
    """分享类型"""
    PUBLIC = "public"       # 公开分享
    PRIVATE = "private"     # 私有分享
    PASSWORD = "password"   # 密码保护
    EXPIRE = "expire"       # 有期限分享


@dataclass
class ExportTask:
    """导出任务"""
    task_id: str
    report_id: str
    export_format: ExportFormat
    status: str = "pending"
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    error_message: Optional[str] = None


@dataclass
class ShareLink:
    """分享链接"""
    link_id: str
    report_id: str
    share_type: ShareType
    url: str
    password: Optional[str] = None
    expire_at: Optional[datetime] = None
    access_count: int = 0
    max_access: Optional[int] = None
    created_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    enabled: bool = True


class ExportManager:
    """导出管理器"""
    
    def __init__(self):
        # 导出存储
        self.export_tasks: List[ExportTask] = []
        self.share_links: Dict[str, ShareLink] = {}
        
        # 导出配置
        self.export_config = {
            "export_dir": "exports",
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "cleanup_days": 7,
            "share_base_url": "https://reports.company.com/share",
            "default_expire_hours": 24 * 7  # 7天
        }
        
        # 确保导出目录存在
        os.makedirs(self.export_config["export_dir"], exist_ok=True)
    
    async def export_report(self, report_result: ReportResult, 
                          export_format: ExportFormat) -> ExportTask:
        """
        导出报表
        
        Args:
            report_result: 报表结果
            export_format: 导出格式
        
        Returns:
            ExportTask: 导出任务
        """
        task = ExportTask(
            task_id=f"export_{uuid.uuid4().hex[:8]}",
            report_id=report_result.report_id,
            export_format=export_format
        )
        
        try:
            logger.info(f"开始导出报表: {task.task_id}")
            task.status = "running"
            task.started_at = datetime.now()
            
            if export_format == ExportFormat.PDF:
                file_path = await self._export_to_pdf(report_result, task)
            elif export_format == ExportFormat.EXCEL:
                file_path = await self._export_to_excel(report_result, task)
            elif export_format == ExportFormat.CSV:
                file_path = await self._export_to_csv(report_result, task)
            elif export_format == ExportFormat.HTML:
                file_path = await self._export_to_html(report_result, task)
            elif export_format == ExportFormat.JSON:
                file_path = await self._export_to_json(report_result, task)
            elif export_format == ExportFormat.PNG:
                file_path = await self._export_to_png(report_result, task)
            else:
                raise ValueError(f"不支持的导出格式: {export_format.value}")
            
            # 获取文件大小
            if file_path and os.path.exists(file_path):
                task.file_path = file_path
                task.file_size = os.path.getsize(file_path)
                task.status = "completed"
                logger.info(f"报表导出完成: {task.task_id}, 文件: {file_path}")
            else:
                raise Exception("导出文件不存在")
                
        except Exception as e:
            logger.error(f"报表导出失败: {task.task_id}, {e}")
            task.status = "failed"
            task.error_message = str(e)
        
        finally:
            task.completed_at = datetime.now()
            self.export_tasks.append(task)
        
        return task
    
    async def _export_to_pdf(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为PDF"""
        try:
            # 模拟PDF导出（实际应用中需要使用如WeasyPrint或ReportLab）
            filename = f"{task.report_id}_{task.task_id}.pdf"
            file_path = os.path.join(self.export_config["export_dir"], filename)
            
            # 创建模拟PDF内容
            pdf_content = f"""PDF报表导出
报表ID: {report_result.report_id}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
内容: {report_result.content[:500]}...
"""
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"PDF导出失败: {e}")
            raise
    
    async def _export_to_excel(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为Excel"""
        try:
            # 模拟Excel导出（实际应用中需要使用openpyxl或xlsxwriter）
            filename = f"{task.report_id}_{task.task_id}.xlsx"
            file_path = os.path.join(self.export_config["export_dir"], filename)
            
            # 创建模拟Excel内容
            excel_content = f"""Excel报表导出
报表ID: {report_result.report_id}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

数据表格:
"""
            
            # 添加表格数据
            if report_result.data and report_result.data.sections:
                for section in report_result.data.sections:
                    if section.get("tables"):
                        for table in section["tables"]:
                            excel_content += f"\n{table['title']}:\n"
                            excel_content += ",".join(table["headers"]) + "\n"
                            for row in table["rows"]:
                                excel_content += ",".join(str(cell) for cell in row) + "\n"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(excel_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
            raise
    
    async def _export_to_csv(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为CSV"""
        try:
            filename = f"{task.report_id}_{task.task_id}.csv"
            file_path = os.path.join(self.export_config["export_dir"], filename)
            
            # 使用报表结果中的CSV内容
            if report_result.format == ReportFormat.CSV:
                csv_content = report_result.content
            else:
                # 生成CSV内容
                csv_content = f"报表ID,{report_result.report_id}\n"
                csv_content += f"生成时间,{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                
                if report_result.data and report_result.data.summary:
                    csv_content += "\n摘要\n"
                    for key, value in report_result.data.summary.items():
                        csv_content += f"{key},{value}\n"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(csv_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            raise
    
    async def _export_to_html(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为HTML"""
        try:
            filename = f"{task.report_id}_{task.task_id}.html"
            file_path = os.path.join(self.export_config["export_dir"], filename)
            
            # 使用报表结果中的HTML内容
            html_content = report_result.content
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"HTML导出失败: {e}")
            raise
    
    async def _export_to_json(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为JSON"""
        try:
            filename = f"{task.report_id}_{task.task_id}.json"
            file_path = os.path.join(self.export_config["export_dir"], filename)

            # 创建JSON数据，处理Mock对象
            json_data = {
                "report_id": report_result.report_id,
                "config": {},
                "data": {},
                "generation_time": getattr(report_result, 'generation_time', 0),
                "status": getattr(report_result, 'status', 'unknown')
            }

            # 安全地提取配置信息
            if hasattr(report_result, 'config') and report_result.config:
                try:
                    json_data["config"] = {
                        "report_name": getattr(report_result.config, 'report_name', ''),
                        "report_type": getattr(report_result.config.report_type, 'value', '') if hasattr(report_result.config, 'report_type') else '',
                    }
                except:
                    json_data["config"] = {"error": "配置信息提取失败"}

            # 安全地提取数据信息
            if hasattr(report_result, 'data') and report_result.data:
                try:
                    # 处理generated_at字段
                    generated_at = getattr(report_result.data, 'generated_at', datetime.now())
                    if hasattr(generated_at, 'isoformat'):
                        generated_at_str = generated_at.isoformat()
                    else:
                        generated_at_str = str(generated_at)

                    # 处理summary字段，确保可序列化
                    summary = getattr(report_result.data, 'summary', {})
                    if hasattr(summary, '__dict__'):
                        # 如果是Mock对象，转换为字典
                        summary = dict(summary) if hasattr(summary, '__iter__') else {"mock_summary": str(summary)}

                    # 处理sections字段
                    sections = getattr(report_result.data, 'sections', [])
                    if hasattr(sections, '__dict__'):
                        # 如果是Mock对象，转换为列表
                        sections = list(sections) if hasattr(sections, '__iter__') else [str(sections)]

                    json_data["data"] = {
                        "title": str(getattr(report_result.data, 'title', '')),
                        "subtitle": str(getattr(report_result.data, 'subtitle', '')),
                        "generated_at": generated_at_str,
                        "summary": summary,
                        "sections": sections
                    }
                except Exception as e:
                    json_data["data"] = {"error": f"数据信息提取失败: {str(e)}"}

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            return file_path

        except Exception as e:
            logger.error(f"JSON导出失败: {e}")
            raise
    
    async def _export_to_png(self, report_result: ReportResult, task: ExportTask) -> str:
        """导出为PNG"""
        try:
            # 模拟PNG导出（实际应用中需要使用如Selenium或Playwright截图）
            filename = f"{task.report_id}_{task.task_id}.png"
            file_path = os.path.join(self.export_config["export_dir"], filename)
            
            # 创建模拟PNG内容（实际应该是二进制图片数据）
            png_content = f"""PNG图片导出模拟
报表ID: {report_result.report_id}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(png_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"PNG导出失败: {e}")
            raise
    
    def create_share_link(self, report_id: str, share_type: ShareType = ShareType.PRIVATE,
                         expire_hours: Optional[int] = None, password: Optional[str] = None,
                         max_access: Optional[int] = None) -> ShareLink:
        """
        创建分享链接
        
        Args:
            report_id: 报表ID
            share_type: 分享类型
            expire_hours: 过期小时数
            password: 访问密码
            max_access: 最大访问次数
        
        Returns:
            ShareLink: 分享链接
        """
        try:
            link_id = uuid.uuid4().hex[:12]
            url = f"{self.export_config['share_base_url']}/{link_id}"
            
            # 设置过期时间
            expire_at = None
            if expire_hours or share_type == ShareType.EXPIRE:
                hours = expire_hours or self.export_config["default_expire_hours"]
                expire_at = datetime.now() + timedelta(hours=hours)
            
            # 生成密码
            if share_type == ShareType.PASSWORD and not password:
                password = uuid.uuid4().hex[:8]
            
            share_link = ShareLink(
                link_id=link_id,
                report_id=report_id,
                share_type=share_type,
                url=url,
                password=password,
                expire_at=expire_at,
                max_access=max_access
            )
            
            self.share_links[link_id] = share_link
            
            logger.info(f"分享链接已创建: {link_id}")
            return share_link
            
        except Exception as e:
            logger.error(f"创建分享链接失败: {report_id}, {e}")
            raise
    
    def access_share_link(self, link_id: str, password: Optional[str] = None) -> Tuple[bool, str, Optional[ShareLink]]:
        """
        访问分享链接
        
        Args:
            link_id: 链接ID
            password: 访问密码
        
        Returns:
            Tuple[bool, str, Optional[ShareLink]]: (是否成功, 消息, 分享链接)
        """
        try:
            if link_id not in self.share_links:
                return False, "分享链接不存在", None
            
            share_link = self.share_links[link_id]
            
            # 检查是否启用
            if not share_link.enabled:
                return False, "分享链接已禁用", None
            
            # 检查是否过期
            if share_link.expire_at and datetime.now() > share_link.expire_at:
                return False, "分享链接已过期", None
            
            # 检查访问次数
            if share_link.max_access and share_link.access_count >= share_link.max_access:
                return False, "分享链接访问次数已达上限", None
            
            # 检查密码
            if share_link.share_type == ShareType.PASSWORD:
                if not password or password != share_link.password:
                    return False, "密码错误", None
            
            # 增加访问次数
            share_link.access_count += 1
            
            logger.info(f"分享链接访问成功: {link_id}")
            return True, "访问成功", share_link
            
        except Exception as e:
            logger.error(f"访问分享链接失败: {link_id}, {e}")
            return False, f"访问失败: {e}", None
    
    def update_share_link(self, link_id: str, updates: Dict[str, Any]) -> bool:
        """更新分享链接"""
        try:
            if link_id not in self.share_links:
                logger.error(f"分享链接不存在: {link_id}")
                return False
            
            share_link = self.share_links[link_id]
            
            # 更新属性
            for key, value in updates.items():
                if hasattr(share_link, key):
                    if key == "share_type":
                        share_link.share_type = ShareType(value)
                    else:
                        setattr(share_link, key, value)
            
            logger.info(f"分享链接已更新: {link_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新分享链接失败: {link_id}, {e}")
            return False
    
    def delete_share_link(self, link_id: str) -> bool:
        """删除分享链接"""
        try:
            if link_id in self.share_links:
                del self.share_links[link_id]
                logger.info(f"分享链接已删除: {link_id}")
                return True
            else:
                logger.error(f"分享链接不存在: {link_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除分享链接失败: {link_id}, {e}")
            return False
    
    def get_export_tasks(self, limit: Optional[int] = None) -> List[ExportTask]:
        """获取导出任务"""
        tasks = sorted(self.export_tasks, key=lambda x: x.created_at, reverse=True)
        
        if limit:
            tasks = tasks[:limit]
        
        return tasks
    
    def get_share_links(self, limit: Optional[int] = None) -> List[ShareLink]:
        """获取分享链接"""
        links = sorted(self.share_links.values(), key=lambda x: x.created_at, reverse=True)
        
        if limit:
            links = links[:limit]
        
        return links
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """获取导出统计信息"""
        try:
            total_exports = len(self.export_tasks)
            completed_exports = len([t for t in self.export_tasks if t.status == "completed"])
            
            # 按格式统计
            format_distribution = {}
            for task in self.export_tasks:
                format_name = task.export_format.value
                format_distribution[format_name] = format_distribution.get(format_name, 0) + 1
            
            # 分享链接统计
            total_shares = len(self.share_links)
            active_shares = len([s for s in self.share_links.values() if s.enabled])
            
            # 按分享类型统计
            share_type_distribution = {}
            for share_link in self.share_links.values():
                share_type = share_link.share_type.value
                share_type_distribution[share_type] = share_type_distribution.get(share_type, 0) + 1
            
            # 成功率
            success_rate = (completed_exports / total_exports * 100) if total_exports > 0 else 0
            
            return {
                "total_exports": total_exports,
                "completed_exports": completed_exports,
                "failed_exports": total_exports - completed_exports,
                "success_rate": success_rate,
                "total_shares": total_shares,
                "active_shares": active_shares,
                "format_distribution": format_distribution,
                "share_type_distribution": share_type_distribution,
                "available_export_formats": [f.value for f in ExportFormat],
                "available_share_types": [t.value for t in ShareType]
            }
            
        except Exception as e:
            logger.error(f"获取导出统计失败: {e}")
            return {
                "total_exports": 0,
                "completed_exports": 0,
                "failed_exports": 0,
                "success_rate": 0,
                "total_shares": 0,
                "active_shares": 0,
                "format_distribution": {},
                "share_type_distribution": {},
                "available_export_formats": [f.value for f in ExportFormat],
                "available_share_types": [t.value for t in ShareType]
            }
