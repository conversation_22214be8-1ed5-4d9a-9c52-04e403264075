"""
平台配置管理系统演示

展示配置CRUD操作、模板管理和验证功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.task_middleware.config_service import (
    PlatformConfigService, ConfigTemplate
)
from app.services.task_middleware.config_manager import Platform, ProductType


async def demo_config_crud_operations():
    """演示配置CRUD操作"""
    print("=== 平台配置CRUD操作演示 ===")
    
    # 创建配置服务
    config_service = PlatformConfigService("demo_config", "demo_templates")
    
    # 示例配置数据
    jd_config = {
        "platform": "jd",
        "base_config": {
            "name": "京东商城",
            "base_url": "https://www.jd.com",
            "selectors": {
                "title": ".sku-name",
                "price": ".price",
                "stock": ".stock",
                "sales_count": ".comment-count"
            },
            "rate_limit": {
                "requests_per_minute": 25,
                "concurrent_requests": 2,
                "delay_between_requests": 2.5
            },
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "javascript_required": True
        },
        "product_types": {
            "competitor": {
                "additional_selectors": {
                    "brand": ".brand-name",
                    "shop_name": ".shop-name"
                },
                "custom_query_template": "提取京东竞品信息：标题、价格、销量、品牌、店铺名称",
                "priority_boost": 2,
                "monitoring_frequency": 2400  # 40分钟
            },
            "supplier": {
                "additional_selectors": {
                    "seller_type": ".seller-type",
                    "delivery_info": ".delivery-info"
                },
                "custom_query_template": "提取京东供货商信息：标题、价格、卖家类型、配送信息",
                "priority_boost": 1,
                "monitoring_frequency": 4800  # 80分钟
            },
            "other": {
                "custom_query_template": "提取京东基础商品信息：标题、价格、库存、评论数",
                "monitoring_frequency": 9600  # 160分钟
            }
        },
        "enabled": True
    }
    
    try:
        # 1. 创建配置
        print("\n1. 创建京东平台配置...")
        success = await config_service.create_platform_config(Platform.JD, jd_config)
        print(f"   创建结果: {'成功' if success else '失败'}")
        
        # 2. 获取配置
        print("\n2. 获取京东平台配置...")
        config = await config_service.get_platform_config(Platform.JD)
        if config:
            print(f"   平台名称: {config['base_config']['name']}")
            print(f"   基础URL: {config['base_config']['base_url']}")
            print(f"   商品类型数量: {len(config['product_types'])}")
            print(f"   启用状态: {config['enabled']}")
        
        # 3. 更新配置
        print("\n3. 更新京东平台配置...")
        updates = {
            "base_config": {
                "rate_limit": {
                    "requests_per_minute": 30  # 提高请求频率
                }
            },
            "product_types": {
                "competitor": {
                    "monitoring_frequency": 1800  # 缩短监控间隔到30分钟
                }
            }
        }
        
        success = await config_service.update_platform_config(Platform.JD, updates)
        print(f"   更新结果: {'成功' if success else '失败'}")
        
        # 验证更新
        updated_config = await config_service.get_platform_config(Platform.JD)
        if updated_config:
            print(f"   更新后请求频率: {updated_config['base_config']['rate_limit']['requests_per_minute']}")
            print(f"   更新后竞品监控频率: {updated_config['product_types']['competitor']['monitoring_frequency']}")
        
        # 4. 列出所有配置
        print("\n4. 列出所有平台配置...")
        configs = await config_service.list_platform_configs()
        print(f"   总配置数量: {len(configs)}")
        for config_info in configs:
            print(f"   - {config_info['platform']}: {config_info['name']} ({'启用' if config_info['enabled'] else '禁用'})")
        
    except Exception as e:
        print(f"配置操作失败: {e}")


async def demo_template_management():
    """演示模板管理"""
    print("\n=== 配置模板管理演示 ===")
    
    config_service = PlatformConfigService("demo_config", "demo_templates")
    
    # 创建自定义模板
    custom_template = ConfigTemplate(
        name="pinduoduo_template",
        description="拼多多平台配置模板",
        platform=Platform.PINDUODUO,
        template_data={
            "platform": "pdd",
            "base_config": {
                "name": "拼多多",
                "base_url": "https://www.pinduoduo.com",
                "selectors": {
                    "title": ".goods-title",
                    "price": ".goods-price",
                    "sales_count": ".goods-sales"
                },
                "rate_limit": {
                    "requests_per_minute": 15,
                    "concurrent_requests": 1,
                    "delay_between_requests": 4.0
                },
                "proxy_required": True,
                "javascript_required": True
            },
            "product_types": {
                "competitor": {
                    "priority_boost": 1,
                    "monitoring_frequency": 3600
                },
                "other": {
                    "monitoring_frequency": 7200
                }
            },
            "enabled": True
        },
        version="1.0"
    )
    
    try:
        # 1. 创建模板
        print("\n1. 创建拼多多配置模板...")
        success = await config_service.create_template(custom_template)
        print(f"   创建结果: {'成功' if success else '失败'}")
        
        # 2. 列出所有模板
        print("\n2. 列出所有配置模板...")
        templates = config_service.list_templates()
        print(f"   模板总数: {len(templates)}")
        for template in templates:
            print(f"   - {template['name']}: {template['description']} (v{template['version']})")
        
        # 3. 获取特定模板
        print("\n3. 获取拼多多模板详情...")
        template = config_service.get_template("pinduoduo_template")
        if template:
            print(f"   模板名称: {template.name}")
            print(f"   适用平台: {template.platform.value}")
            print(f"   创建时间: {template.created_at}")
            print(f"   请求频率: {template.template_data['base_config']['rate_limit']['requests_per_minute']}")
        
        # 4. 使用模板创建配置
        print("\n4. 使用模板创建拼多多平台配置...")
        
        # 用户自定义数据（会覆盖模板中的相同字段）
        user_config = {
            "base_config": {
                "name": "拼多多商城 - 定制版",
                "rate_limit": {
                    "requests_per_minute": 20  # 覆盖模板中的15
                }
            },
            "product_types": {
                "supplier": {  # 添加模板中没有的商品类型
                    "additional_selectors": {
                        "bulk_price": ".bulk-price"
                    },
                    "custom_query_template": "提取拼多多供货商信息",
                    "monitoring_frequency": 5400
                }
            }
        }
        
        success = await config_service.create_platform_config(
            Platform.PINDUODUO, 
            user_config, 
            template_name="pinduoduo_template"
        )
        print(f"   使用模板创建配置: {'成功' if success else '失败'}")
        
        # 验证模板合并结果
        if success:
            final_config = await config_service.get_platform_config(Platform.PINDUODUO)
            print(f"   最终配置名称: {final_config['base_config']['name']}")
            print(f"   最终请求频率: {final_config['base_config']['rate_limit']['requests_per_minute']}")
            print(f"   商品类型数量: {len(final_config['product_types'])}")
            print(f"   包含供货商类型: {'supplier' in final_config['product_types']}")
        
    except Exception as e:
        print(f"模板管理失败: {e}")


async def demo_config_validation():
    """演示配置验证"""
    print("\n=== 配置验证演示 ===")
    
    config_service = PlatformConfigService("demo_config", "demo_templates")
    
    # 测试用例1: 有效配置
    print("\n1. 测试有效配置...")
    valid_config = {
        "platform": "taobao",
        "base_config": {
            "name": "淘宝网",
            "base_url": "https://www.taobao.com",
            "selectors": {
                "title": ".tb-title",
                "price": ".tb-price"
            },
            "rate_limit": {
                "requests_per_minute": 20
            }
        },
        "product_types": {
            "competitor": {
                "monitoring_frequency": 1800
            }
        }
    }
    
    result = await config_service.validate_config(valid_config)
    print(f"   验证结果: {'通过' if result.is_valid else '失败'}")
    print(f"   质量分数: {result.score:.2f}")
    print(f"   错误数量: {len(result.errors)}")
    print(f"   警告数量: {len(result.warnings)}")
    
    # 测试用例2: 缺少必需字段
    print("\n2. 测试缺少必需字段的配置...")
    invalid_config = {
        "platform": "taobao"
        # 缺少 base_config 和 product_types
    }
    
    result = await config_service.validate_config(invalid_config)
    print(f"   验证结果: {'通过' if result.is_valid else '失败'}")
    print(f"   质量分数: {result.score:.2f}")
    print(f"   错误信息:")
    for error in result.errors:
        print(f"     - {error}")
    
    # 测试用例3: 有警告的配置
    print("\n3. 测试有警告的配置...")
    warning_config = {
        "platform": "1688",
        "base_config": {
            "name": "1688平台",
            "base_url": "https://www.1688.com",
            "selectors": {
                "title": ".title",
                "price": ".price"
            },
            "rate_limit": {
                "requests_per_minute": 150  # 过高，会产生警告
            }
        },
        "product_types": {
            "competitor": {
                "monitoring_frequency": 30  # 过低，会产生警告
            }
        }
    }
    
    result = await config_service.validate_config(warning_config)
    print(f"   验证结果: {'通过' if result.is_valid else '失败'}")
    print(f"   质量分数: {result.score:.2f}")
    print(f"   警告信息:")
    for warning in result.warnings:
        print(f"     - {warning}")


async def demo_config_testing():
    """演示配置测试"""
    print("\n=== 配置测试演示 ===")
    
    config_service = PlatformConfigService("demo_config", "demo_templates")
    
    # 确保有配置可以测试
    test_config = {
        "platform": "1688",
        "base_config": {
            "name": "1688测试平台",
            "base_url": "https://www.1688.com",
            "selectors": {
                "title": ".d-title",
                "price": ".price-now",
                "stock": ".amount-text"
            },
            "rate_limit": {
                "requests_per_minute": 30
            }
        },
        "product_types": {
            "competitor": {
                "monitoring_frequency": 1800
            },
            "supplier": {
                "monitoring_frequency": 3600
            },
            "other": {
                "monitoring_frequency": 7200
            }
        }
    }
    
    try:
        # 创建测试配置
        await config_service.create_platform_config(Platform.ALIBABA_1688, test_config)
        
        # 执行配置测试
        print("\n执行1688平台配置测试...")
        test_urls = [
            "https://detail.1688.com/offer/123456.html",
            "https://detail.1688.com/offer/789012.html"
        ]
        
        test_result = await config_service.test_config(Platform.ALIBABA_1688, test_urls)
        
        print(f"测试总体结果: {'通过' if test_result['success'] else '失败'}")
        print(f"成功率: {test_result.get('success_rate', 0):.2f}")
        print(f"测试项目: {test_result.get('successful_tests', 0)}/{test_result.get('total_tests', 0)}")
        
        print("\n详细测试结果:")
        for test in test_result.get('tests', []):
            status = '✓' if test.get('success', False) else '✗'
            print(f"  {status} {test['test']}")
            if 'error' in test:
                print(f"    错误: {test['error']}")
            elif 'score' in test:
                print(f"    分数: {test['score']:.2f}")
            elif 'frequencies' in test:
                print(f"    监控频率: {test['frequencies']}")
        
    except Exception as e:
        print(f"配置测试失败: {e}")


async def demo_backup_and_restore():
    """演示备份和恢复"""
    print("\n=== 配置备份和恢复演示 ===")
    
    config_service = PlatformConfigService("demo_config", "demo_templates")
    
    try:
        # 确保有配置存在
        if not await config_service.get_platform_config(Platform.ALIBABA_1688):
            print("创建测试配置用于备份演示...")
            test_config = {
                "platform": "1688",
                "base_config": {
                    "name": "1688备份测试",
                    "base_url": "https://www.1688.com",
                    "selectors": {"title": ".title", "price": ".price"},
                    "rate_limit": {"requests_per_minute": 30}
                },
                "product_types": {
                    "competitor": {"monitoring_frequency": 1800}
                }
            }
            await config_service.create_platform_config(Platform.ALIBABA_1688, test_config)
        
        # 1. 创建备份
        print("\n1. 创建配置备份...")
        await config_service._backup_config(Platform.ALIBABA_1688)
        print("   备份创建完成")
        
        # 2. 列出备份
        print("\n2. 列出配置备份...")
        backups = config_service.list_backups(Platform.ALIBABA_1688)
        print(f"   备份数量: {len(backups)}")
        for backup in backups[:3]:  # 显示最新的3个备份
            print(f"   - {backup}")
        
        # 3. 修改配置
        print("\n3. 修改配置...")
        updates = {
            "base_config": {
                "name": "1688备份测试 - 已修改"
            }
        }
        await config_service.update_platform_config(Platform.ALIBABA_1688, updates)
        
        modified_config = await config_service.get_platform_config(Platform.ALIBABA_1688)
        print(f"   修改后名称: {modified_config['base_config']['name']}")
        
        # 4. 恢复配置
        if backups:
            print(f"\n4. 恢复配置 (使用备份: {backups[0]})...")
            success = await config_service.restore_config(Platform.ALIBABA_1688, backups[0])
            print(f"   恢复结果: {'成功' if success else '失败'}")
            
            if success:
                restored_config = await config_service.get_platform_config(Platform.ALIBABA_1688)
                print(f"   恢复后名称: {restored_config['base_config']['name']}")
        
    except Exception as e:
        print(f"备份恢复操作失败: {e}")


async def main():
    """主演示函数"""
    print("🚀 平台配置管理系统演示")
    print("=" * 50)
    
    # 各功能演示
    await demo_config_crud_operations()
    await demo_template_management()
    await demo_config_validation()
    await demo_config_testing()
    await demo_backup_and_restore()
    
    print("\n" + "=" * 50)
    print("✅ 平台配置管理系统演示完成！")
    print("\n核心功能:")
    print("- 完整的配置CRUD操作")
    print("- 灵活的配置模板系统")
    print("- 智能的配置验证机制")
    print("- 全面的配置测试功能")
    print("- 可靠的备份恢复机制")
    print("- 动态配置更新支持")


if __name__ == "__main__":
    asyncio.run(main())
