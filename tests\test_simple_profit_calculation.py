"""
简化的利润计算逻辑测试
使用模拟数据测试利润计算的核心业务逻辑
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List


class TestSimpleProfitCalculation:
    """简化的利润计算测试"""
    
    @pytest.fixture
    def sample_cost_data(self):
        """示例成本数据"""
        return {
            "purchase_cost": Decimal("7500.00"),
            "shipping_cost": Decimal("50.00"),
            "handling_cost": Decimal("25.00"),
            "tax_rate": Decimal("0.13"),  # 13%税率
            "currency": "CNY"
        }
    
    @pytest.fixture
    def sample_supplier_data(self):
        """示例供货商数据"""
        return [
            {
                "supplier_id": "supplier_001",
                "name": "优质供应商A",
                "price": 7500.00,
                "quality_rating": 0.95,
                "delivery_rating": 0.90,
                "service_rating": 0.88,
                "market_avg_price": 8000.00
            },
            {
                "supplier_id": "supplier_002",
                "name": "经济供应商B",
                "price": 7200.00,
                "quality_rating": 0.85,
                "delivery_rating": 0.88,
                "service_rating": 0.82,
                "market_avg_price": 8000.00
            },
            {
                "supplier_id": "supplier_003",
                "name": "新兴供应商C",
                "price": 7800.00,
                "quality_rating": 0.80,
                "delivery_rating": 0.85,
                "service_rating": 0.85,
                "market_avg_price": 8000.00
            }
        ]
    
    def test_basic_profit_calculation(self, sample_cost_data):
        """测试基础利润计算"""
        def calculate_profit_margin(selling_price: Decimal, cost_price: Decimal) -> Dict:
            """计算利润率"""
            if cost_price <= 0:
                return {"profit_margin": 0, "profit_amount": 0, "error": "Invalid cost price"}
            
            profit_amount = selling_price - cost_price
            profit_margin = (profit_amount / selling_price) * 100
            
            return {
                "selling_price": selling_price,
                "cost_price": cost_price,
                "profit_amount": profit_amount,
                "profit_margin": profit_margin,
                "profit_margin_category": _categorize_margin(profit_margin)
            }
        
        def _categorize_margin(margin: float) -> str:
            """分类利润率"""
            if margin >= 50:
                return "excellent"
            elif margin >= 30:
                return "good"
            elif margin >= 15:
                return "acceptable"
            elif margin >= 5:
                return "low"
            else:
                return "poor"
        
        # 测试数据
        selling_price = Decimal("8999.00")
        cost_price = sample_cost_data["purchase_cost"]
        
        result = calculate_profit_margin(selling_price, cost_price)
        
        assert "profit_amount" in result
        assert "profit_margin" in result
        assert result["profit_amount"] == Decimal("1499.00")
        assert abs(float(result["profit_margin"]) - 16.66) < 0.01  # 约16.66%
        
        print(f"✅ 利润计算测试通过:")
        print(f"   售价: ¥{result['selling_price']}")
        print(f"   成本: ¥{result['cost_price']}")
        print(f"   利润: ¥{result['profit_amount']}")
        print(f"   利润率: {result['profit_margin']:.2f}%")
    
    def test_total_cost_calculation(self, sample_cost_data):
        """测试总成本计算"""
        def calculate_total_cost(base_cost: Decimal, additional_costs: Dict[str, Decimal]) -> Dict:
            """计算总成本"""
            total_additional = sum(additional_costs.values())
            total_cost = base_cost + total_additional
            
            return {
                "base_cost": base_cost,
                "additional_costs": additional_costs,
                "total_additional_cost": total_additional,
                "total_cost": total_cost
            }
        
        base_cost = sample_cost_data["purchase_cost"]
        additional_costs = {
            "shipping": sample_cost_data["shipping_cost"],
            "handling": sample_cost_data["handling_cost"]
        }
        
        result = calculate_total_cost(base_cost, additional_costs)
        
        assert result["base_cost"] == Decimal("7500.00")
        assert result["total_additional_cost"] == Decimal("75.00")
        assert result["total_cost"] == Decimal("7575.00")
        
        print(f"✅ 总成本计算测试通过:")
        print(f"   基础成本: ¥{result['base_cost']}")
        print(f"   附加成本: ¥{result['total_additional_cost']}")
        print(f"   总成本: ¥{result['total_cost']}")
    
    def test_roi_calculation(self):
        """测试投资回报率计算"""
        def calculate_roi(profit: Decimal, investment: Decimal) -> Dict:
            """计算投资回报率"""
            if investment <= 0:
                return {"roi": 0, "error": "Invalid investment amount"}
            
            roi = (profit / investment) * 100
            
            return {
                "profit": profit,
                "investment": investment,
                "roi": roi,
                "roi_category": _categorize_roi(roi)
            }
        
        def _categorize_roi(roi: float) -> str:
            """分类ROI"""
            if roi >= 100:
                return "excellent"
            elif roi >= 50:
                return "good"
            elif roi >= 20:
                return "acceptable"
            elif roi >= 10:
                return "low"
            else:
                return "poor"
        
        # 测试数据
        profit = Decimal("1500.00")
        investment = Decimal("7500.00")
        
        result = calculate_roi(profit, investment)
        
        assert result["roi"] == 20.0  # 20% ROI
        assert result["roi_category"] == "acceptable"
        
        print(f"✅ ROI计算测试通过:")
        print(f"   利润: ¥{result['profit']}")
        print(f"   投资: ¥{result['investment']}")
        print(f"   ROI: {result['roi']:.1f}%")
        print(f"   ROI等级: {result['roi_category']}")
    
    def test_supplier_comparison(self, sample_supplier_data):
        """测试供货商比较"""
        def compare_suppliers(suppliers_data: List[Dict]) -> Dict:
            """比较供货商"""
            if not suppliers_data:
                return {"comparison_result": [], "best_supplier": None}
            
            # 计算每个供货商的综合评分
            scored_suppliers = []
            for supplier_data in suppliers_data:
                score = _calculate_supplier_score(supplier_data)
                scored_suppliers.append({
                    **supplier_data,
                    "overall_score": score
                })
            
            # 按评分排序
            scored_suppliers.sort(key=lambda x: x["overall_score"], reverse=True)
            
            return {
                "comparison_result": scored_suppliers,
                "best_supplier": scored_suppliers[0] if scored_suppliers else None,
                "total_suppliers": len(scored_suppliers)
            }
        
        def _calculate_supplier_score(supplier_data: Dict) -> float:
            """计算供货商综合评分"""
            # 权重设置
            weights = {
                "price_score": 0.4,      # 价格权重40%
                "quality_score": 0.3,    # 质量权重30%
                "delivery_score": 0.2,   # 交付权重20%
                "service_score": 0.1     # 服务权重10%
            }
            
            # 计算各维度得分
            price_score = _calculate_price_score(supplier_data.get("price", 0), supplier_data.get("market_avg_price", 0))
            quality_score = supplier_data.get("quality_rating", 0.8) * 100
            delivery_score = supplier_data.get("delivery_rating", 0.8) * 100
            service_score = supplier_data.get("service_rating", 0.8) * 100
            
            # 加权计算总分
            total_score = (
                price_score * weights["price_score"] +
                quality_score * weights["quality_score"] +
                delivery_score * weights["delivery_score"] +
                service_score * weights["service_score"]
            )
            
            return round(total_score, 2)
        
        def _calculate_price_score(supplier_price: float, market_avg: float) -> float:
            """计算价格得分（价格越低得分越高）"""
            if market_avg <= 0:
                return 50  # 默认中等分数
            
            price_ratio = supplier_price / market_avg
            
            if price_ratio <= 0.8:  # 比市场价低20%以上
                return 100
            elif price_ratio <= 0.9:  # 比市场价低10-20%
                return 90
            elif price_ratio <= 1.0:  # 比市场价低0-10%
                return 80
            elif price_ratio <= 1.1:  # 比市场价高0-10%
                return 60
            else:  # 比市场价高10%以上
                return 40
        
        result = compare_suppliers(sample_supplier_data)
        
        assert result["total_suppliers"] == 3
        assert result["best_supplier"] is not None
        assert len(result["comparison_result"]) == 3
        
        # 验证排序（最佳供货商应该有最高分）
        best_supplier = result["best_supplier"]
        all_scores = [s["overall_score"] for s in result["comparison_result"]]
        assert best_supplier["overall_score"] == max(all_scores)
        
        print(f"✅ 供货商比较测试通过:")
        print(f"   最佳供货商: {best_supplier['name']}")
        print(f"   综合评分: {best_supplier['overall_score']:.2f}")
        print(f"   价格: ¥{best_supplier['price']:.2f}")
        
        print("   所有供货商排名:")
        for i, supplier in enumerate(result["comparison_result"]):
            print(f"     {i+1}. {supplier['name']}: {supplier['overall_score']:.2f}分")
    
    def test_profit_optimization_scenario(self, sample_cost_data, sample_supplier_data):
        """测试利润优化场景"""
        def optimize_profit(selling_price: Decimal, suppliers_data: List[Dict], additional_costs: Dict[str, Decimal]) -> Dict:
            """优化利润"""
            best_scenarios = []
            
            for supplier in suppliers_data:
                # 计算使用该供货商的总成本
                supplier_cost = Decimal(str(supplier["price"]))
                total_additional = sum(additional_costs.values())
                total_cost = supplier_cost + total_additional
                
                # 计算利润
                profit = selling_price - total_cost
                profit_margin = (profit / selling_price) * 100 if selling_price > 0 else 0
                
                scenario = {
                    "supplier_name": supplier["name"],
                    "supplier_id": supplier["supplier_id"],
                    "supplier_cost": supplier_cost,
                    "total_cost": total_cost,
                    "profit": profit,
                    "profit_margin": profit_margin,
                    "quality_rating": supplier["quality_rating"],
                    "overall_score": supplier.get("overall_score", 0)
                }
                
                best_scenarios.append(scenario)
            
            # 按利润排序
            best_scenarios.sort(key=lambda x: x["profit"], reverse=True)
            
            return {
                "selling_price": selling_price,
                "scenarios": best_scenarios,
                "best_profit_scenario": best_scenarios[0] if best_scenarios else None,
                "optimization_summary": {
                    "max_profit": best_scenarios[0]["profit"] if best_scenarios else 0,
                    "min_profit": best_scenarios[-1]["profit"] if best_scenarios else 0,
                    "profit_range": best_scenarios[0]["profit"] - best_scenarios[-1]["profit"] if len(best_scenarios) > 1 else 0
                }
            }
        
        selling_price = Decimal("8999.00")
        additional_costs = {
            "shipping": sample_cost_data["shipping_cost"],
            "handling": sample_cost_data["handling_cost"]
        }
        
        result = optimize_profit(selling_price, sample_supplier_data, additional_costs)
        
        assert result["selling_price"] == selling_price
        assert len(result["scenarios"]) == 3
        assert result["best_profit_scenario"] is not None
        
        best_scenario = result["best_profit_scenario"]
        assert best_scenario["profit"] > 0
        assert best_scenario["profit_margin"] > 0
        
        print(f"✅ 利润优化测试通过:")
        print(f"   售价: ¥{result['selling_price']}")
        print(f"   最佳利润方案: {best_scenario['supplier_name']}")
        print(f"   最大利润: ¥{best_scenario['profit']:.2f}")
        print(f"   利润率: {best_scenario['profit_margin']:.2f}%")
        print(f"   利润范围: ¥{result['optimization_summary']['profit_range']:.2f}")
    
    def test_cost_trend_analysis(self):
        """测试成本趋势分析"""
        def analyze_cost_trends(historical_costs: List[Dict]) -> Dict:
            """分析成本趋势"""
            if len(historical_costs) < 2:
                return {"trend": "insufficient_data", "change_rate": 0}
            
            costs = [cost["total_cost"] for cost in historical_costs]
            
            # 简单的趋势分析
            recent_avg = sum(costs[-3:]) / min(3, len(costs))
            earlier_avg = sum(costs[:3]) / min(3, len(costs))
            
            change_rate = ((recent_avg - earlier_avg) / earlier_avg * 100) if earlier_avg > 0 else 0
            
            if change_rate > 10:
                trend = "increasing"
            elif change_rate < -10:
                trend = "decreasing"
            else:
                trend = "stable"
            
            return {
                "trend": trend,
                "change_rate": change_rate,
                "recent_average": recent_avg,
                "earlier_average": earlier_avg,
                "total_periods": len(historical_costs)
            }
        
        # 模拟6个月的成本数据
        historical_costs = [
            {"month": "2024-01", "total_cost": 7500},
            {"month": "2024-02", "total_cost": 7520},
            {"month": "2024-03", "total_cost": 7480},
            {"month": "2024-04", "total_cost": 7600},
            {"month": "2024-05", "total_cost": 7650},
            {"month": "2024-06", "total_cost": 7700}
        ]
        
        result = analyze_cost_trends(historical_costs)
        
        assert result["trend"] in ["increasing", "decreasing", "stable"]
        assert result["total_periods"] == 6
        assert result["recent_average"] > 0
        assert result["earlier_average"] > 0
        
        print(f"✅ 成本趋势分析测试通过:")
        print(f"   趋势: {result['trend']}")
        print(f"   变化率: {result['change_rate']:.2f}%")
        print(f"   早期平均: ¥{result['earlier_average']:.2f}")
        print(f"   近期平均: ¥{result['recent_average']:.2f}")
