# Moniit 常见问题解答 (FAQ)

## 📋 目录

1. [系统安装与配置](#系统安装与配置)
2. [商品管理](#商品管理)
3. [价格监控](#价格监控)
4. [数据分析](#数据分析)
5. [系统维护](#系统维护)
6. [故障排除](#故障排除)

## 🔧 系统安装与配置

### Q1: 如何安装Moniit系统？

**A:** Moniit支持多种安装方式：

**Docker安装（推荐）**:
```bash
# 克隆项目
git clone https://github.com/your-org/moniit.git
cd moniit

# 启动服务
docker-compose up -d

# 访问系统
http://localhost:8000
```

**手动安装**:
```bash
# 安装依赖
pip install -r requirements.txt

# 配置数据库
python manage.py migrate

# 启动服务
python manage.py runserver
```

### Q2: 系统有哪些环境要求？

**A:** 系统环境要求：
- **Python**: 3.11+
- **数据库**: PostgreSQL 13+ 或 TimescaleDB 2.0+
- **Redis**: 6.0+ (用于缓存和任务队列)
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低20GB，推荐100GB+

### Q3: 如何配置环境变量？

**A:** 复制`.env.example`文件为`.env`，然后修改配置：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/moniit

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password

# 安全配置
SECRET_KEY=your-secret-key
DEBUG=False
```

### Q4: 如何设置HTTPS？

**A:** 生产环境建议使用HTTPS：

**使用Nginx反向代理**:
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🛍️ 商品管理

### Q5: 支持哪些电商平台？

**A:** 目前支持的平台：
- **淘宝/天猫**: 支持商品页面和店铺监控
- **京东**: 支持自营和第三方商品
- **拼多多**: 支持普通商品监控
- **亚马逊**: 支持中国区商品
- **1688**: 支持批发商品监控

**注意**: 不同平台的反爬虫策略不同，建议合理设置监控频率。

### Q6: 如何批量导入商品？

**A:** 使用Excel批量导入：

1. **下载模板**: 在商品管理页面点击"下载模板"
2. **填写信息**: 按模板格式填写商品信息
3. **上传文件**: 点击"批量导入"上传Excel文件
4. **验证数据**: 系统会验证数据格式和URL有效性
5. **确认导入**: 检查无误后确认导入

**模板格式**:
```
商品名称 | 商品URL | 平台 | 分类 | 品牌 | 描述
iPhone 15 | https://... | 天猫 | 手机 | Apple | 最新款iPhone
```

### Q7: 商品URL无效怎么办？

**A:** URL无效的常见原因和解决方法：

**原因1: URL格式错误**
- 确保URL完整，包含`http://`或`https://`
- 检查URL中是否有特殊字符需要编码

**原因2: 商品已下架**
- 联系供货商确认商品状态
- 寻找替代商品或更新URL

**原因3: 平台限制**
- 某些平台限制爬虫访问
- 尝试使用移动版URL或API接口

### Q8: 如何设置商品分类？

**A:** 商品分类设置：

1. **系统预设分类**: 使用系统提供的标准分类
2. **自定义分类**: 创建符合业务需求的分类
3. **多级分类**: 支持最多3级分类结构
4. **分类管理**: 可以修改、合并、删除分类

**分类示例**:
```
电子产品
├── 手机数码
│   ├── 智能手机
│   ├── 平板电脑
│   └── 智能手表
├── 电脑办公
│   ├── 笔记本电脑
│   ├── 台式电脑
│   └── 办公设备
```

## 📊 价格监控

### Q9: 监控频率如何选择？

**A:** 监控频率选择建议：

**实时监控 (5分钟)**:
- 高价值商品（>10000元）
- 价格波动频繁的商品
- 促销期间的重点商品

**高频监控 (1小时)**:
- 重要商品（1000-10000元）
- 竞争激烈的商品
- 新品上市期间

**标准监控 (4小时)**:
- 一般商品（100-1000元）
- 价格相对稳定的商品
- 日常监控商品

**低频监控 (24小时)**:
- 低价值商品（<100元）
- 价格变化缓慢的商品
- 参考性商品

### Q10: 价格数据不准确怎么办？

**A:** 价格数据准确性问题排查：

**步骤1: 检查URL**
- 确认URL指向正确的商品页面
- 检查是否跳转到其他页面

**步骤2: 检查页面结构**
- 网站可能更新了页面结构
- 价格元素的CSS选择器可能变化

**步骤3: 检查反爬虫**
- 网站可能加强了反爬虫措施
- 尝试降低监控频率

**步骤4: 手动验证**
- 人工访问页面确认实际价格
- 对比系统采集的价格数据

### Q11: 如何设置价格预警？

**A:** 价格预警设置：

**阈值设置**:
- **涨价预警**: 价格上涨超过X%或X元
- **降价预警**: 价格下降超过X%或X元
- **绝对价格**: 价格高于或低于指定金额

**通知方式**:
- **邮件通知**: 发送详细的价格变化报告
- **短信通知**: 发送简要的价格变化信息
- **系统通知**: 在系统内显示通知消息

**预警示例**:
```
商品: iPhone 15 Pro
当前价格: ¥8,999
预警条件: 降价超过5%或降价超过¥500
触发条件: 价格降至¥8,499 (降价¥500, 5.6%)
```

### Q12: 监控任务失败怎么处理？

**A:** 监控任务失败处理：

**自动重试机制**:
- 系统会自动重试失败的任务
- 重试间隔: 5分钟、15分钟、1小时
- 最大重试次数: 3次

**失败原因分析**:
- **网络问题**: 检查网络连接
- **网站问题**: 目标网站可能临时不可访问
- **反爬虫**: 网站检测到爬虫行为
- **页面变化**: 网站更新了页面结构

**手动处理**:
1. 查看错误日志确定失败原因
2. 更新商品URL或监控配置
3. 手动重新启动监控任务
4. 联系技术支持获取帮助

## 📈 数据分析

### Q13: 如何解读价格趋势图？

**A:** 价格趋势图解读：

**趋势线**:
- **上升趋势**: 价格整体向上，建议关注高点
- **下降趋势**: 价格整体向下，可能是购买时机
- **震荡趋势**: 价格在区间内波动，关注支撑阻力位
- **稳定趋势**: 价格变化很小，市场相对稳定

**技术指标**:
- **移动平均线**: 平滑价格波动，显示趋势方向
- **波动率**: 衡量价格变化的剧烈程度
- **支撑位**: 价格下跌时的支撑水平
- **阻力位**: 价格上涨时的阻力水平

### Q14: 利润分析结果如何使用？

**A:** 利润分析结果应用：

**定价决策**:
- 根据目标利润率确定销售价格
- 考虑市场竞争情况调整价格策略
- 评估价格变化对利润的影响

**成本控制**:
- 识别成本占比较高的环节
- 寻找降低成本的机会
- 优化供应链和采购策略

**产品组合**:
- 重点推广高利润产品
- 调整低利润产品的策略
- 优化产品组合结构

### Q15: 如何导出分析报告？

**A:** 分析报告导出：

**导出格式**:
- **PDF**: 适合打印和分享
- **Excel**: 适合进一步分析
- **CSV**: 适合数据处理
- **图片**: 适合演示使用

**导出内容**:
- 价格趋势图表
- 统计分析数据
- 利润分析结果
- 供货商评估报告

**导出步骤**:
1. 选择要导出的时间范围
2. 选择要包含的商品或分类
3. 选择导出格式和内容
4. 点击导出并下载文件

## 🔧 系统维护

### Q16: 如何备份数据？

**A:** 数据备份方法：

**自动备份**:
```bash
# 设置定时任务
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup_script.sh
```

**手动备份**:
```bash
# 数据库备份
pg_dump -h localhost -U moniit -d moniit > backup_$(date +%Y%m%d).sql

# 文件备份
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/files
```

**云备份**:
- 使用云存储服务（阿里云OSS、腾讯云COS等）
- 设置自动同步和版本控制
- 定期测试备份恢复

### Q17: 如何监控系统性能？

**A:** 系统性能监控：

**关键指标**:
- **CPU使用率**: 应保持在70%以下
- **内存使用率**: 应保持在80%以下
- **磁盘使用率**: 应保持在85%以下
- **网络延迟**: 应保持在100ms以下

**监控工具**:
- **系统监控**: htop, iostat, netstat
- **应用监控**: Django Debug Toolbar
- **数据库监控**: pg_stat_activity
- **日志监控**: ELK Stack

### Q18: 如何更新系统？

**A:** 系统更新步骤：

**准备工作**:
1. 备份数据库和文件
2. 记录当前系统版本
3. 阅读更新说明文档

**更新步骤**:
```bash
# 停止服务
docker-compose down

# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 重启服务
docker-compose up -d
```

**验证更新**:
1. 检查系统版本信息
2. 测试核心功能
3. 查看错误日志
4. 监控系统性能

## ⚠️ 故障排除

### Q19: 系统无法访问怎么办？

**A:** 系统访问问题排查：

**步骤1: 检查服务状态**
```bash
# 检查Docker容器状态
docker-compose ps

# 检查端口占用
netstat -tlnp | grep 8000
```

**步骤2: 检查日志**
```bash
# 查看应用日志
docker-compose logs web

# 查看数据库日志
docker-compose logs db
```

**步骤3: 检查网络**
```bash
# 测试本地访问
curl http://localhost:8000

# 检查防火墙设置
sudo ufw status
```

### Q20: 数据库连接失败怎么办？

**A:** 数据库连接问题排查：

**检查数据库服务**:
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 检查数据库连接
psql -h localhost -U moniit -d moniit
```

**检查配置**:
- 确认数据库URL配置正确
- 检查用户名和密码
- 验证数据库名称和端口

**常见解决方法**:
- 重启数据库服务
- 检查数据库用户权限
- 确认网络连接正常
- 查看数据库错误日志

### Q21: 监控任务大量失败怎么办？

**A:** 监控任务失败排查：

**检查系统资源**:
- CPU和内存使用情况
- 网络连接状态
- 磁盘空间是否充足

**检查目标网站**:
- 网站是否正常访问
- 是否更新了反爬虫策略
- 页面结构是否发生变化

**调整策略**:
- 降低监控频率
- 增加请求间隔
- 使用代理IP
- 更新爬虫规则

### Q22: 如何联系技术支持？

**A:** 技术支持联系方式：

**在线支持**:
- **邮箱**: <EMAIL>
- **工单系统**: https://support.moniit.com
- **在线聊天**: 工作时间9:00-18:00

**社区支持**:
- **GitHub Issues**: 报告Bug和功能请求
- **用户论坛**: 与其他用户交流经验
- **知识库**: 查看详细文档和教程

**紧急支持**:
- **电话**: +86-400-xxx-xxxx (7×24小时)
- **微信群**: 扫码加入用户群
- **QQ群**: 123456789

---

## 📚 相关文档

- [用户操作手册](user_manual.md)
- [业务流程指南](business_workflow_guide.md)
- [API接口文档](api_documentation.md)
- [部署运维指南](deployment_guide.md)

---

*本FAQ最后更新时间: 2025-08-24*

**如果您的问题没有在此FAQ中找到答案，请联系我们的技术支持团队，我们将竭诚为您服务！**
