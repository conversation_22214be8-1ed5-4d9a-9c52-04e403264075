"""
图表生成器

提供多种图表类型的生成功能
"""

import io
import base64
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from app.core.logging import get_logger

logger = get_logger(__name__)


class ChartType(Enum):
    """图表类型"""
    LINE = "line"           # 折线图
    BAR = "bar"             # 柱状图
    PIE = "pie"             # 饼图
    SCATTER = "scatter"     # 散点图
    AREA = "area"           # 面积图
    HISTOGRAM = "histogram" # 直方图
    HEATMAP = "heatmap"     # 热力图


@dataclass
class ChartData:
    """图表数据"""
    chart_id: str
    title: str
    chart_type: ChartType
    data: Dict[str, Any]
    options: Dict[str, Any]
    width: int = 800
    height: int = 400


@dataclass
class ChartResult:
    """图表结果"""
    chart_id: str
    chart_data: ChartData
    image_data: Optional[str] = None  # Base64编码的图片数据
    html_content: Optional[str] = None  # HTML内容
    svg_content: Optional[str] = None   # SVG内容
    status: str = "completed"
    error_message: Optional[str] = None


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self):
        self.chart_results: List[ChartResult] = []
        
        # 图表配置
        self.chart_config = {
            "default_colors": [
                "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
                "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
            ],
            "font_family": "Arial, sans-serif",
            "font_size": 12,
            "background_color": "#FFFFFF",
            "grid_color": "#E0E0E0"
        }
    
    async def generate_chart(self, chart_data: ChartData, output_format: str = "html") -> ChartResult:
        """
        生成图表
        
        Args:
            chart_data: 图表数据
            output_format: 输出格式 (html, svg, png)
        
        Returns:
            ChartResult: 图表结果
        """
        try:
            logger.info(f"开始生成图表: {chart_data.chart_id}")
            
            if chart_data.chart_type == ChartType.LINE:
                result = await self._generate_line_chart(chart_data, output_format)
            elif chart_data.chart_type == ChartType.BAR:
                result = await self._generate_bar_chart(chart_data, output_format)
            elif chart_data.chart_type == ChartType.PIE:
                result = await self._generate_pie_chart(chart_data, output_format)
            elif chart_data.chart_type == ChartType.SCATTER:
                result = await self._generate_scatter_chart(chart_data, output_format)
            elif chart_data.chart_type == ChartType.AREA:
                result = await self._generate_area_chart(chart_data, output_format)
            else:
                # 默认生成简单的HTML图表
                result = await self._generate_simple_chart(chart_data, output_format)
            
            # 保存结果
            self.chart_results.append(result)
            
            logger.info(f"图表生成完成: {chart_data.chart_id}")
            return result
            
        except Exception as e:
            logger.error(f"图表生成失败: {chart_data.chart_id}, {e}")
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_line_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成折线图"""
        try:
            # 生成HTML内容（使用Chart.js）
            html_content = self._generate_chartjs_html(chart_data, "line")
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成折线图失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_bar_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成柱状图"""
        try:
            # 生成HTML内容（使用Chart.js）
            html_content = self._generate_chartjs_html(chart_data, "bar")
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成柱状图失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_pie_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成饼图"""
        try:
            # 生成HTML内容（使用Chart.js）
            html_content = self._generate_chartjs_html(chart_data, "pie")
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成饼图失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_scatter_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成散点图"""
        try:
            # 生成HTML内容（使用Chart.js）
            html_content = self._generate_chartjs_html(chart_data, "scatter")
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成散点图失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_area_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成面积图"""
        try:
            # 生成HTML内容（使用Chart.js）
            html_content = self._generate_chartjs_html(chart_data, "line", fill=True)
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成面积图失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    async def _generate_simple_chart(self, chart_data: ChartData, output_format: str) -> ChartResult:
        """生成简单图表"""
        try:
            # 生成简单的HTML表格作为图表
            html_content = f"""
            <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                <h3>{chart_data.title}</h3>
                <p>图表类型: {chart_data.chart_type.value}</p>
                <p>数据: {json.dumps(chart_data.data, ensure_ascii=False, indent=2)}</p>
            </div>
            """
            
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                html_content=html_content,
                status="completed"
            )
            
        except Exception as e:
            logger.error(f"生成简单图表失败: {e}")
            return ChartResult(
                chart_id=chart_data.chart_id,
                chart_data=chart_data,
                status="failed",
                error_message=str(e)
            )
    
    def _generate_chartjs_html(self, chart_data: ChartData, chart_type: str, fill: bool = False) -> str:
        """生成Chart.js HTML内容"""
        try:
            # 准备数据
            labels = chart_data.data.get("labels", [])
            datasets = chart_data.data.get("datasets", [])
            
            # 为数据集添加颜色
            for i, dataset in enumerate(datasets):
                if "backgroundColor" not in dataset:
                    color_index = i % len(self.chart_config["default_colors"])
                    color = self.chart_config["default_colors"][color_index]
                    dataset["backgroundColor"] = color
                    dataset["borderColor"] = color
                    if fill:
                        dataset["fill"] = True
            
            # 生成Chart.js配置
            chart_config = {
                "type": chart_type,
                "data": {
                    "labels": labels,
                    "datasets": datasets
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": chart_data.title
                        },
                        "legend": {
                            "display": True,
                            "position": "top"
                        }
                    }
                }
            }
            
            # 合并用户选项
            if chart_data.options:
                chart_config["options"].update(chart_data.options)
            
            # 生成HTML
            html_content = f"""
            <div style="width: {chart_data.width}px; height: {chart_data.height}px; margin: 15px 0;">
                <canvas id="chart_{chart_data.chart_id}"></canvas>
            </div>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script>
                const ctx_{chart_data.chart_id} = document.getElementById('chart_{chart_data.chart_id}').getContext('2d');
                const chart_{chart_data.chart_id} = new Chart(ctx_{chart_data.chart_id}, {json.dumps(chart_config, ensure_ascii=False)});
            </script>
            """
            
            return html_content
            
        except Exception as e:
            logger.error(f"生成Chart.js HTML失败: {e}")
            return f"<div>图表生成失败: {e}</div>"
    
    def get_chart_results(self, limit: Optional[int] = None) -> List[ChartResult]:
        """获取图表结果"""
        results = sorted(self.chart_results, key=lambda x: x.chart_id, reverse=True)
        
        if limit:
            results = results[:limit]
        
        return results
    
    def get_chart_statistics(self) -> Dict[str, Any]:
        """获取图表统计信息"""
        try:
            total_charts = len(self.chart_results)
            successful_charts = len([r for r in self.chart_results if r.status == "completed"])
            
            # 按类型统计
            type_distribution = {}
            for result in self.chart_results:
                chart_type = result.chart_data.chart_type.value
                type_distribution[chart_type] = type_distribution.get(chart_type, 0) + 1
            
            # 成功率
            success_rate = (successful_charts / total_charts * 100) if total_charts > 0 else 0
            
            return {
                "total_charts": total_charts,
                "successful_charts": successful_charts,
                "failed_charts": total_charts - successful_charts,
                "success_rate": success_rate,
                "type_distribution": type_distribution,
                "available_types": [t.value for t in ChartType]
            }
            
        except Exception as e:
            logger.error(f"获取图表统计失败: {e}")
            return {
                "total_charts": 0,
                "successful_charts": 0,
                "failed_charts": 0,
                "success_rate": 0,
                "type_distribution": {},
                "available_types": [t.value for t in ChartType]
            }
