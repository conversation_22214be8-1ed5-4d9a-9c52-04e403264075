# Moniit API 接口文档

## 📋 目录

1. [API概述](#api概述)
2. [认证授权](#认证授权)
3. [商品管理API](#商品管理api)
4. [价格监控API](#价格监控api)
5. [数据分析API](#数据分析api)
6. [系统管理API](#系统管理api)
7. [错误处理](#错误处理)
8. [SDK和示例](#sdk和示例)

## 🎯 API概述

### 基础信息
- **Base URL**: `https://api.moniit.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

### 请求格式
```http
GET /api/v1/products HTTP/1.1
Host: api.moniit.com
Authorization: Bearer {access_token}
Content-Type: application/json
```

### 响应格式
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2025-08-24T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 状态码说明
| 状态码 | 说明 | 描述 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |

## 🔐 认证授权

### OAuth 2.0 认证

**获取访问令牌**:
```http
POST /api/v1/auth/token
Content-Type: application/json

{
  "grant_type": "client_credentials",
  "client_id": "your_client_id",
  "client_secret": "your_client_secret"
}
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "read write"
}
```

### API Key 认证

**请求头设置**:
```http
Authorization: ApiKey your_api_key
```

### 权限范围
- **read**: 读取数据权限
- **write**: 写入数据权限
- **admin**: 管理员权限

## 🛍️ 商品管理API

### 获取商品列表

**请求**:
```http
GET /api/v1/products?page=1&limit=20&category=electronics
```

**参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20，最大100 |
| category | string | 否 | 商品分类 |
| platform | string | 否 | 电商平台 |
| status | string | 否 | 商品状态 |
| search | string | 否 | 搜索关键词 |

**响应**:
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "prod_123456",
        "name": "iPhone 15 Pro",
        "url": "https://www.apple.com/iphone-15-pro/",
        "platform": "apple",
        "category": "smartphones",
        "status": "active",
        "current_price": 8999.00,
        "currency": "CNY",
        "created_at": "2025-08-24T10:00:00Z",
        "updated_at": "2025-08-24T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### 创建商品

**请求**:
```http
POST /api/v1/products
Content-Type: application/json

{
  "name": "iPhone 15 Pro",
  "url": "https://www.apple.com/iphone-15-pro/",
  "platform": "apple",
  "category": "smartphones",
  "brand": "Apple",
  "description": "最新款iPhone",
  "tags": ["手机", "苹果", "5G"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "prod_123456",
    "name": "iPhone 15 Pro",
    "url": "https://www.apple.com/iphone-15-pro/",
    "platform": "apple",
    "category": "smartphones",
    "status": "pending",
    "created_at": "2025-08-24T10:00:00Z"
  },
  "message": "商品创建成功"
}
```

### 更新商品

**请求**:
```http
PUT /api/v1/products/{product_id}
Content-Type: application/json

{
  "name": "iPhone 15 Pro Max",
  "description": "更新的商品描述",
  "tags": ["手机", "苹果", "5G", "大屏"]
}
```

### 删除商品

**请求**:
```http
DELETE /api/v1/products/{product_id}
```

**响应**:
```json
{
  "success": true,
  "message": "商品删除成功"
}
```

### 批量操作

**批量创建商品**:
```http
POST /api/v1/products/batch
Content-Type: application/json

{
  "products": [
    {
      "name": "商品1",
      "url": "https://example.com/product1",
      "platform": "taobao",
      "category": "electronics"
    },
    {
      "name": "商品2",
      "url": "https://example.com/product2",
      "platform": "jd",
      "category": "electronics"
    }
  ]
}
```

## 📊 价格监控API

### 获取价格历史

**请求**:
```http
GET /api/v1/products/{product_id}/prices?start_date=2025-08-01&end_date=2025-08-24
```

**参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| start_date | string | 否 | 开始日期 (YYYY-MM-DD) |
| end_date | string | 否 | 结束日期 (YYYY-MM-DD) |
| interval | string | 否 | 数据间隔 (hour/day/week) |

**响应**:
```json
{
  "success": true,
  "data": {
    "product_id": "prod_123456",
    "prices": [
      {
        "price": 8999.00,
        "currency": "CNY",
        "recorded_at": "2025-08-24T10:00:00Z",
        "source": "official_website"
      },
      {
        "price": 8899.00,
        "currency": "CNY",
        "recorded_at": "2025-08-23T10:00:00Z",
        "source": "official_website"
      }
    ],
    "statistics": {
      "min_price": 8799.00,
      "max_price": 9199.00,
      "avg_price": 8949.50,
      "price_change": -100.00,
      "change_percentage": -1.11
    }
  }
}
```

### 设置监控任务

**请求**:
```http
POST /api/v1/products/{product_id}/monitoring
Content-Type: application/json

{
  "frequency": "hourly",
  "price_threshold": {
    "increase_percentage": 5.0,
    "decrease_percentage": 5.0,
    "absolute_high": 10000.00,
    "absolute_low": 8000.00
  },
  "notifications": {
    "email": true,
    "sms": false,
    "webhook": "https://your-webhook.com/price-alert"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "monitoring_id": "mon_123456",
    "product_id": "prod_123456",
    "status": "active",
    "next_check": "2025-08-24T11:00:00Z"
  },
  "message": "监控任务设置成功"
}
```

### 获取监控状态

**请求**:
```http
GET /api/v1/monitoring/{monitoring_id}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "monitoring_id": "mon_123456",
    "product_id": "prod_123456",
    "status": "active",
    "frequency": "hourly",
    "last_check": "2025-08-24T10:00:00Z",
    "next_check": "2025-08-24T11:00:00Z",
    "success_rate": 98.5,
    "total_checks": 720,
    "failed_checks": 11
  }
}
```

## 📈 数据分析API

### 价格趋势分析

**请求**:
```http
GET /api/v1/analytics/price-trends/{product_id}?period=30d
```

**参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | string | 否 | 分析周期 (7d/30d/90d/1y) |
| algorithm | string | 否 | 分析算法 (linear/polynomial) |

**响应**:
```json
{
  "success": true,
  "data": {
    "product_id": "prod_123456",
    "period": "30d",
    "trend_analysis": {
      "direction": "decreasing",
      "slope": -2.5,
      "r_squared": 0.85,
      "confidence": "high"
    },
    "volatility": {
      "standard_deviation": 125.50,
      "coefficient_variation": 0.014,
      "volatility_level": "low"
    },
    "predictions": [
      {
        "date": "2025-08-25",
        "predicted_price": 8950.00,
        "confidence_interval": [8900.00, 9000.00]
      }
    ]
  }
}
```

### 利润分析

**请求**:
```http
POST /api/v1/analytics/profit-analysis
Content-Type: application/json

{
  "product_id": "prod_123456",
  "cost_structure": {
    "purchase_cost": 7500.00,
    "shipping_cost": 50.00,
    "handling_cost": 25.00,
    "platform_fee_rate": 0.03
  },
  "selling_price": 8999.00
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "product_id": "prod_123456",
    "profit_analysis": {
      "selling_price": 8999.00,
      "total_cost": 7845.00,
      "gross_profit": 1154.00,
      "gross_margin": 12.83,
      "net_profit": 884.03,
      "net_margin": 9.82,
      "roi": 11.27
    },
    "break_even_analysis": {
      "break_even_price": 7845.00,
      "break_even_quantity": 1,
      "safety_margin": 12.83
    }
  }
}
```

### 市场对比分析

**请求**:
```http
GET /api/v1/analytics/market-comparison?category=smartphones&brand=Apple
```

**响应**:
```json
{
  "success": true,
  "data": {
    "category": "smartphones",
    "brand": "Apple",
    "comparison": [
      {
        "product_id": "prod_123456",
        "name": "iPhone 15 Pro",
        "current_price": 8999.00,
        "market_position": "premium",
        "price_rank": 3,
        "price_percentile": 85.2
      }
    ],
    "market_statistics": {
      "avg_price": 6500.00,
      "median_price": 5999.00,
      "price_range": [2999.00, 12999.00],
      "total_products": 156
    }
  }
}
```

## ⚙️ 系统管理API

### 获取系统状态

**请求**:
```http
GET /api/v1/system/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "system": {
      "version": "1.0.0",
      "uptime": "15d 8h 32m",
      "status": "healthy"
    },
    "database": {
      "status": "connected",
      "response_time": 12,
      "connections": 25
    },
    "cache": {
      "status": "connected",
      "hit_rate": 94.5,
      "memory_usage": "512MB"
    },
    "monitoring": {
      "active_tasks": 1250,
      "success_rate": 98.7,
      "avg_response_time": 850
    }
  }
}
```

### 获取系统统计

**请求**:
```http
GET /api/v1/system/statistics?period=7d
```

**响应**:
```json
{
  "success": true,
  "data": {
    "period": "7d",
    "products": {
      "total": 1250,
      "active": 1180,
      "monitoring": 1050
    },
    "price_records": {
      "total": 125000,
      "today": 8500,
      "success_rate": 98.7
    },
    "api_usage": {
      "total_requests": 45000,
      "daily_average": 6428,
      "error_rate": 0.5
    }
  }
}
```

## ❌ 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "请求参数无效",
    "details": "商品URL格式不正确",
    "field": "url"
  },
  "timestamp": "2025-08-24T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| INVALID_PARAMETER | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

### 错误处理最佳实践

**重试机制**:
```javascript
async function apiRequest(url, options, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url, options);
      if (response.ok) {
        return await response.json();
      }
      if (response.status === 429) {
        // 频率限制，等待后重试
        await sleep(Math.pow(2, i) * 1000);
        continue;
      }
      throw new Error(`HTTP ${response.status}`);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await sleep(1000);
    }
  }
}
```

## 🛠️ SDK和示例

### JavaScript SDK

**安装**:
```bash
npm install @moniit/sdk
```

**使用示例**:
```javascript
import { MoniitClient } from '@moniit/sdk';

const client = new MoniitClient({
  apiKey: 'your_api_key',
  baseURL: 'https://api.moniit.com/v1'
});

// 获取商品列表
const products = await client.products.list({
  category: 'electronics',
  limit: 20
});

// 创建商品
const product = await client.products.create({
  name: 'iPhone 15 Pro',
  url: 'https://www.apple.com/iphone-15-pro/',
  platform: 'apple',
  category: 'smartphones'
});

// 设置监控
const monitoring = await client.monitoring.create(product.id, {
  frequency: 'hourly',
  price_threshold: {
    increase_percentage: 5.0,
    decrease_percentage: 5.0
  }
});
```

### Python SDK

**安装**:
```bash
pip install moniit-sdk
```

**使用示例**:
```python
from moniit_sdk import MoniitClient

client = MoniitClient(
    api_key='your_api_key',
    base_url='https://api.moniit.com/v1'
)

# 获取商品列表
products = client.products.list(
    category='electronics',
    limit=20
)

# 创建商品
product = client.products.create({
    'name': 'iPhone 15 Pro',
    'url': 'https://www.apple.com/iphone-15-pro/',
    'platform': 'apple',
    'category': 'smartphones'
})

# 获取价格历史
prices = client.products.get_prices(
    product['id'],
    start_date='2025-08-01',
    end_date='2025-08-24'
)
```

### cURL 示例

**获取商品列表**:
```bash
curl -X GET "https://api.moniit.com/v1/products" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**创建商品**:
```bash
curl -X POST "https://api.moniit.com/v1/products" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "iPhone 15 Pro",
    "url": "https://www.apple.com/iphone-15-pro/",
    "platform": "apple",
    "category": "smartphones"
  }'
```

## 📝 API使用限制

### 频率限制
- **免费版**: 1000次/小时
- **专业版**: 10000次/小时
- **企业版**: 100000次/小时

### 数据限制
- **单次请求**: 最大100条记录
- **批量操作**: 最大1000条记录
- **文件上传**: 最大10MB

### 超时设置
- **连接超时**: 10秒
- **读取超时**: 30秒
- **总超时**: 60秒

---

## 📞 技术支持

- **API文档**: https://docs.moniit.com/api
- **SDK仓库**: https://github.com/moniit/sdk
- **技术支持**: <EMAIL>
- **状态页面**: https://status.moniit.com

---

## 📚 相关文档

- [用户操作手册](user_manual.md)
- [部署运维指南](deployment_guide.md)
- [架构设计文档](architecture_design.md)
- [常见问题解答](faq.md)

---

*本文档最后更新时间: 2025-08-24*
