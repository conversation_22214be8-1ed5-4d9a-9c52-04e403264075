"""
测试配置文件
"""

import pytest
import asyncio
import os
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient


# 设置测试环境变量
os.environ.update({
    "DATABASE_URL": "postgresql://test:test@localhost:5432/test_db",
    "REDIS_URL": "redis://localhost:6379/1",
    "SECRET_KEY": "test-secret-key",
    "ENVIRONMENT": "testing",
    "DEBUG": "true"
})


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_database():
    """模拟数据库连接"""
    with patch('app.core.database.init_database') as mock_init_db, \
         patch('app.core.database.get_database') as mock_get_db, \
         patch('app.core.database.get_db_session') as mock_get_session:
        
        # 模拟数据库管理器
        mock_db_manager = AsyncMock()
        mock_db_manager.execute = AsyncMock()
        mock_db_manager.fetch_all = AsyncMock(return_value=[])
        mock_db_manager.fetch_one = AsyncMock(return_value=None)
        mock_db_manager.get_database_size = AsyncMock(return_value="10MB")
        mock_db_manager.get_connection_count = AsyncMock(return_value=5)
        
        mock_init_db.return_value = None
        mock_get_db.return_value = mock_db_manager
        
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock()
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.close = AsyncMock()
        
        async def mock_session_generator():
            yield mock_session
        
        mock_get_session.return_value = mock_session_generator()
        
        yield {
            'init_db': mock_init_db,
            'get_db': mock_get_db,
            'get_session': mock_get_session,
            'db_manager': mock_db_manager,
            'session': mock_session
        }


@pytest.fixture
def mock_cache():
    """模拟缓存管理器"""
    with patch('app.core.cache.init_cache_manager') as mock_init_cache, \
         patch('app.core.cache.get_cache_manager') as mock_get_cache:
        
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.connect = AsyncMock()
        mock_cache_manager.disconnect = AsyncMock()
        mock_cache_manager.get = AsyncMock(return_value=None)
        mock_cache_manager.set = AsyncMock(return_value=True)
        mock_cache_manager.delete = AsyncMock(return_value=True)
        mock_cache_manager.exists = AsyncMock(return_value=False)
        mock_cache_manager.clear_pattern = AsyncMock(return_value=0)
        mock_cache_manager.get_info = AsyncMock(return_value={
            "stats": {
                "hits": 10,
                "misses": 5,
                "sets": 8,
                "deletes": 2,
                "errors": 0,
                "hit_rate": 0.667,
                "total_requests": 15
            },
            "local_cache": {
                "size": 5,
                "max_size": 1000
            },
            "redis": {
                "connected": True,
                "version": "7.0.0",
                "used_memory": "1MB",
                "connected_clients": 3,
                "total_commands_processed": 1000
            }
        })
        
        mock_init_cache.return_value = mock_cache_manager
        mock_get_cache.return_value = mock_cache_manager
        
        yield {
            'init_cache': mock_init_cache,
            'get_cache': mock_get_cache,
            'cache_manager': mock_cache_manager
        }


@pytest.fixture
def mock_config():
    """模拟配置管理"""
    with patch('app.core.config.get_settings') as mock_get_settings:
        from app.core.config import AppSettings, DatabaseSettings, RedisSettings, SecuritySettings, LoggingSettings, NotificationSettings, CelerySettings, TaskMiddlewareSettings, TranslationSettings
        
        # 创建测试配置
        test_settings = AppSettings(
            name="电商商品监控系统",
            version="1.0.0",
            debug=True,
            host="0.0.0.0",
            port=8000,
            environment="testing",
            database=DatabaseSettings(url="postgresql://test:test@localhost:5432/test_db"),
            redis=RedisSettings(url="redis://localhost:6379/1"),
            celery=CelerySettings(
                broker_url="redis://localhost:6379/1",
                result_backend="redis://localhost:6379/2"
            ),
            task_middleware=TaskMiddlewareSettings(base_url="http://localhost:3000"),
            translation=TranslationSettings(),
            security=SecuritySettings(secret_key="test-secret-key"),
            logging=LoggingSettings(),
            notification=NotificationSettings()
        )
        
        mock_get_settings.return_value = test_settings
        yield test_settings


@pytest.fixture
def app_client(mock_database, mock_cache, mock_config):
    """测试客户端"""
    from app.main import create_app
    
    app = create_app()
    client = TestClient(app)
    return client


@pytest.fixture
def sample_product_data():
    """示例商品数据"""
    return {
        "id": "prod_001",
        "url": "https://www.1688.com/product/123456.html",
        "platform": "1688",
        "title": "测试商品",
        "title_translated": "Test Product",
        "category": "电子产品",
        "status": "active",
        "monitoring_frequency": 24,
        "is_active": True,
        "tags": {"brand": "测试品牌", "type": "电子设备"},
        "notes": "这是一个测试商品"
    }


@pytest.fixture
def sample_supplier_data():
    """示例供货商数据"""
    return {
        "name": "测试供货商",
        "contact_person": "张经理",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "address": "深圳市南山区测试地址",
        "payment_terms": "30天付款",
        "delivery_time": 7,
        "min_order_quantity": 100,
        "is_active": True,
        "rating": 4.5,
        "notes": "优质供货商"
    }


@pytest.fixture
def sample_cost_data():
    """示例成本数据"""
    from uuid import uuid4
    from datetime import datetime, timedelta
    
    return {
        "product_id": uuid4(),
        "supplier_id": uuid4(),
        "unit_cost": 25.50,
        "currency": "USD",
        "shipping_cost": 5.00,
        "other_costs": 2.50,
        "total_cost": 33.00,
        "min_quantity": 100,
        "max_quantity": 1000,
        "valid_from": datetime.utcnow(),
        "valid_until": datetime.utcnow() + timedelta(days=90),
        "is_active": True,
        "notes": "测试成本数据"
    }


@pytest.fixture
def sample_history_data():
    """示例历史数据"""
    from datetime import datetime
    from uuid import uuid4
    
    return {
        "time": datetime.utcnow(),
        "product_id": uuid4(),
        "platform": "1688",
        "title": "测试商品",
        "title_translated": "Test Product",
        "price": 29.99,
        "currency": "USD",
        "sales_count": 150,
        "stock_quantity": 500,
        "rating": 4.5,
        "review_count": 200,
        "change_type": "price_change",
        "change_value": -2.00,
        "data_quality_score": 0.95,
        "raw_data": {
            "source": "test",
            "crawl_time": datetime.utcnow().isoformat(),
            "quality": "high"
        }
    }


@pytest.fixture
def mock_external_apis():
    """模拟外部API"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "data": {}}
        mock_response.text = "Mock response"
        
        mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
        mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
        
        yield mock_client


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置测试环境变量
    original_env = os.environ.copy()
    
    test_env = {
        "TESTING": "true",
        "DATABASE_URL": "postgresql://test:test@localhost:5432/test_db",
        "REDIS_URL": "redis://localhost:6379/1",
        "SECRET_KEY": "test-secret-key-for-testing",
        "ENVIRONMENT": "testing",
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG"
    }
    
    os.environ.update(test_env)
    
    yield
    
    # 恢复原始环境变量
    os.environ.clear()
    os.environ.update(original_env)


# 异步测试标记
pytest_plugins = ['pytest_asyncio']


@pytest.fixture
def sample_raw_crawl_data():
    """示例原始爬取数据"""
    return {
        "title": "iPhone 15 Pro 钛金属",
        "price": "¥8,999.00",
        "currency": "人民币",
        "availability": "现货",
        "description": "A17 Pro芯片，钛金属设计",
        "images": [
            "https://www.apple.com/v/iphone-15-pro/images/hero.jpg"
        ],
        "specifications": {
            "storage": "128GB",
            "color": "原色钛金属",
            "model": "iPhone 15 Pro"
        },
        "seller": {
            "name": "Apple官方旗舰店",
            "rating": 4.9
        }
    }


@pytest.fixture
def price_trend_data():
    """生成价格趋势测试数据"""
    from datetime import datetime, timedelta
    from decimal import Decimal
    from dataclasses import dataclass

    @dataclass
    class MockPriceRecord:
        product_id: str
        price: Decimal
        currency: str
        recorded_at: datetime
        source_url: str

    base_time = datetime.now()
    base_price = Decimal("1000.00")

    # 生成30天的价格数据，包含上升趋势
    data = []
    for i in range(30):
        # 添加一些随机波动和整体上升趋势
        price_change = Decimal(str(i * 2 + (i % 5 - 2) * 5))  # 简化随机数生成
        price = base_price + price_change

        data.append(MockPriceRecord(
            product_id="prod_001",
            price=max(price, Decimal("500.00")),  # 确保价格不会太低
            currency="CNY",
            recorded_at=base_time - timedelta(days=29-i),
            source_url="https://example.com/product"
        ))

    return data


@pytest.fixture
def volatile_price_data():
    """生成波动性价格测试数据"""
    from datetime import datetime, timedelta
    from decimal import Decimal
    from dataclasses import dataclass

    @dataclass
    class MockPriceRecord:
        product_id: str
        price: Decimal
        currency: str
        recorded_at: datetime
        source_url: str

    base_time = datetime.now()
    base_price = Decimal("1000.00")

    # 生成高波动性价格数据
    data = []
    volatility_values = [50, -80, 120, -30, 90, -60, 150, -40, 70, -100,
                       110, -20, 80, -90, 130, -50, 60, -70, 100, -110]

    for i in range(20):
        # 添加大幅波动
        volatility = Decimal(str(volatility_values[i]))
        price = base_price + volatility

        data.append(MockPriceRecord(
            product_id="prod_002",
            price=max(price, Decimal("100.00")),
            currency="CNY",
            recorded_at=base_time - timedelta(hours=i),
            source_url="https://example.com/product"
        ))

    return data


@pytest.fixture
def seasonal_price_data():
    """生成季节性价格测试数据"""
    from datetime import datetime, timedelta
    from decimal import Decimal
    from dataclasses import dataclass
    import math

    @dataclass
    class MockPriceRecord:
        product_id: str
        price: Decimal
        currency: str
        recorded_at: datetime
        source_url: str

    base_time = datetime.now()
    base_price = Decimal("1000.00")

    # 生成具有季节性模式的价格数据（365天）
    data = []
    for i in range(365):
        # 添加季节性波动（年度周期）
        seasonal_factor = math.sin(2 * math.pi * i / 365) * 200
        daily_noise = (i % 7 - 3) * 5  # 简化的"随机"噪声
        price = base_price + Decimal(str(int(seasonal_factor + daily_noise)))

        data.append(MockPriceRecord(
            product_id="prod_003",
            price=max(price, Decimal("200.00")),
            currency="CNY",
            recorded_at=base_time - timedelta(days=364-i),
            source_url="https://example.com/product"
        ))

    return data
