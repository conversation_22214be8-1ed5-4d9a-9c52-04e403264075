# 🔧 前端后端API修复方案

## 📋 修复计划概述

基于API对比分析，制定以下修复方案来解决前端与后端API不匹配的问题。

## 🎯 修复优先级

### 🔴 高优先级 (立即修复)
1. **供货商管理API路径修复** - 简单路径调整
2. **监控管理API创建** - 创建缺失的API端点
3. **数据分析API调整** - 调整端点匹配

### 🟡 中优先级 (后续修复)
1. **商品管理缺失API补充** - 添加批量操作等功能
2. **系统配置API路径调整** - 微调配置更新API

## 🔧 具体修复方案

### 1. 供货商管理API路径修复 ✅

**问题**: 前端调用 `/suppliers` 但后端提供 `/api/v1/suppliers/`

**修复方案**: 更新前端API路径

```typescript
// 修改文件: frontend/src/services/supplierApi.ts

// 修改前
getSuppliers: () => api.get('/suppliers')

// 修改后  
getSuppliers: () => api.get('/api/v1/suppliers/')
```

**修复代码**:
```typescript
export const supplierApi = {
  // 获取供货商列表
  getSuppliers: (params?: SupplierQueryParams): Promise<ApiResponse<PaginatedResponse<Supplier>>> => {
    return api.get('/api/v1/suppliers/', { params });
  },

  // 获取供货商详情
  getSupplierById: (supplierId: string): Promise<ApiResponse<Supplier>> => {
    return api.get(`/api/v1/suppliers/${supplierId}`);
  },

  // 创建供货商
  createSupplier: (supplierData: SupplierForm): Promise<ApiResponse<Supplier>> => {
    return api.post('/api/v1/suppliers/', supplierData);
  },

  // 更新供货商
  updateSupplier: (supplierId: string, supplierData: Partial<SupplierForm>): Promise<ApiResponse<Supplier>> => {
    return api.put(`/api/v1/suppliers/${supplierId}`, supplierData);
  },

  // 删除供货商
  deleteSupplier: (supplierId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/api/v1/suppliers/${supplierId}`);
  },

  // 获取供货商的商品列表
  getSupplierProducts: (supplierId: string): Promise<ApiResponse<any[]>> => {
    return api.get(`/api/v1/suppliers/${supplierId}/products`);
  },

  // 获取供货商统计信息
  getSupplierStats: (supplierId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/v1/suppliers/${supplierId}/stats`);
  },
};
```

### 2. 监控管理API创建 🔧

**问题**: 前端调用 `/monitor/tasks` 但后端未提供对应API

**修复方案**: 创建监控管理API端点

**需要创建的API端点**:
```python
# 创建文件: app/api/v1/endpoints/monitor.py

@router.get("/tasks")
async def get_monitor_tasks():
    """获取监控任务列表"""
    pass

@router.post("/tasks")  
async def create_monitor_task():
    """创建监控任务"""
    pass

@router.get("/tasks/{task_id}")
async def get_monitor_task():
    """获取监控任务详情"""
    pass

@router.put("/tasks/{task_id}")
async def update_monitor_task():
    """更新监控任务"""
    pass

@router.delete("/tasks/{task_id}")
async def delete_monitor_task():
    """删除监控任务"""
    pass

@router.post("/tasks/{task_id}/start")
async def start_monitor_task():
    """启动监控任务"""
    pass

@router.post("/tasks/{task_id}/pause")
async def pause_monitor_task():
    """暂停监控任务"""
    pass

@router.get("/tasks/{task_id}/logs")
async def get_task_logs():
    """获取任务执行日志"""
    pass
```

**或者修复方案**: 更新前端API路径使用现有的任务调度API

```typescript
// 修改文件: frontend/src/services/monitorApi.ts

export const monitorApi = {
  // 获取监控任务列表
  getTasks: (params?: MonitorQueryParams): Promise<ApiResponse<PaginatedResponse<MonitorTask>>> => {
    return api.get('/api/v1/task-scheduler/tasks', { params });
  },

  // 获取任务详情
  getTaskById: (taskId: string): Promise<ApiResponse<MonitorTask>> => {
    return api.get(`/api/v1/task-scheduler/tasks/${taskId}`);
  },

  // 创建监控任务
  createTask: (taskData: MonitorTaskForm): Promise<ApiResponse<MonitorTask>> => {
    return api.post('/api/v1/task-scheduler/crawl/single', taskData);
  },

  // 删除监控任务
  deleteTask: (taskId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/api/v1/task-scheduler/tasks/${taskId}`);
  },
};
```

### 3. 数据分析API调整 🔧

**问题**: 前端调用的API端点与后端提供的不完全匹配

**修复方案**: 调整前端API调用以匹配后端端点

```typescript
// 修改文件: frontend/src/services/analyticsApi.ts

class AnalyticsApi {
  /**
   * 获取价格趋势分析 - 调整为匹配后端端点
   */
  async getPriceTrend(params: { product_id: string; days?: number; interval?: string }): Promise<{ data: PriceTrendPoint[] }> {
    const response = await api.get(`/api/v1/analytics/price-trends/${params.product_id}`, { 
      params: { days: params.days, interval: params.interval } 
    });
    return response;
  }

  /**
   * 获取统计数据 - 使用后端提供的端点
   */
  async getStatistics(params?: { platform?: string; category?: string; days?: number }): Promise<{ data: any }> {
    const response = await api.get('/api/v1/analytics/statistics', { params });
    return response;
  }

  /**
   * 生成报表 - 使用后端提供的端点
   */
  async generateReport(params: { 
    report_type?: string; 
    format?: string; 
    filters: any 
  }): Promise<{ data: any }> {
    const response = await api.post('/api/v1/analytics/reports/generate', params.filters, {
      params: { 
        report_type: params.report_type, 
        format: params.format 
      }
    });
    return response;
  }

  /**
   * 数据搜索 - 使用后端提供的端点
   */
  async searchData(params: {
    keyword?: string;
    platform?: string;
    category?: string;
    price_min?: number;
    price_max?: number;
    sort_by?: string;
    sort_order?: string;
    skip?: number;
    limit?: number;
  }): Promise<{ data: any }> {
    const response = await api.get('/api/v1/analytics/search', { params });
    return response;
  }
}
```

### 4. 商品管理缺失API补充 🔧

**问题**: 前端调用的部分商品API后端未提供

**修复方案**: 在后端添加缺失的API端点

```python
# 修改文件: app/api/v1/endpoints/products.py

@router.post("/batch-operation")
async def batch_operation_products(operation_data: dict):
    """批量操作商品"""
    pass

@router.patch("/{product_id}/status")
async def toggle_product_status(product_id: str, status_data: dict):
    """切换商品状态"""
    pass

@router.get("/categories")
async def get_product_categories():
    """获取商品分类列表"""
    pass

@router.get("/brands")
async def get_product_brands():
    """获取商品品牌列表"""
    pass

@router.get("/search")
async def search_products(keyword: str, filters: dict = None):
    """搜索商品"""
    pass
```

### 5. 系统配置API路径调整 🔧

**问题**: 配置更新API路径不匹配

**修复方案**: 调整前端API调用

```typescript
// 修改文件: frontend/src/services/systemApi.ts

// 更新系统配置 - 调整为匹配后端端点
updateSystemConfig: (configKey: string, config: any): Promise<ApiResponse<any>> => {
  return api.put(`/api/v1/system/config/${configKey}`, config);
},
```

## 📋 修复检查清单

### 前端修复清单
- [ ] 修复供货商API路径 (`supplierApi.ts`)
- [ ] 修复监控管理API路径 (`monitorApi.ts`)  
- [ ] 调整数据分析API端点 (`analyticsApi.ts`)
- [ ] 调整系统配置API路径 (`systemApi.ts`)

### 后端修复清单
- [ ] 创建监控管理API端点 (`monitor.py`)
- [ ] 补充商品管理缺失API (`products.py`)
- [ ] 验证所有API端点正常工作

### 测试验证清单
- [ ] 运行前端应用验证API调用
- [ ] 运行后端API测试验证端点
- [ ] 端到端功能测试
- [ ] API文档更新

## 🚀 修复执行顺序

1. **第一步**: 修复供货商API路径 (5分钟)
2. **第二步**: 调整数据分析API端点 (15分钟)
3. **第三步**: 创建监控管理API端点 (30分钟)
4. **第四步**: 补充商品管理缺失API (20分钟)
5. **第五步**: 系统配置API微调 (5分钟)
6. **第六步**: 全面测试验证 (15分钟)

**总预计时间**: 90分钟

## 🎯 修复后效果

修复完成后将实现：
- ✅ 前端与后端API 100% 匹配
- ✅ 所有页面功能正常工作
- ✅ API调用无404错误
- ✅ 完整的业务功能实现

这将确保整个系统的前后端完全集成，用户可以正常使用所有功能。
