"""
商品管理业务层演示

展示商品信息处理、分类归档、质量检查等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductStatus, DataQuality,
    ProductPrice, ProductImage, ProductSeller, ProductSpecs, ProductMetrics
)
from app.services.product_management.data_processor import ProductDataProcessor
from app.services.product_management.quality_checker import DataQualityChecker
from app.services.product_management.product_classifier import ProductClassifier
from app.services.product_management.archive_manager import ArchiveManager, ArchiveReason
from app.services.task_middleware.config_manager import Platform


async def demo_data_processing():
    """演示数据处理功能"""
    print("=== 商品数据处理演示 ===")
    
    processor = ProductDataProcessor()
    
    # 模拟不同平台的爬取数据
    test_data = [
        {
            "platform": Platform.ALIBABA_1688,
            "url": "https://detail.1688.com/offer/123456.html",
            "raw_data": {
                "title": "苹果iPhone 15 Pro Max手机壳 透明硅胶保护套 工厂批发",
                "price": "12.80",
                "original_price": "25.60",
                "images": [
                    "https://img.1688.com/image1.jpg",
                    "https://img.1688.com/image2.jpg",
                    "https://img.1688.com/image3.jpg"
                ],
                "seller_name": "深圳市华强北手机配件工厂",
                "min_order": "50",
                "wholesale_price": "8.50",
                "brand": "苹果",
                "sales_count": "2580",
                "stock": "9999"
            },
            "product_type": ProductType.SUPPLIER
        },
        {
            "platform": Platform.TAOBAO,
            "url": "https://item.taobao.com/item.htm?id=789012",
            "raw_data": {
                "title": "Apple iPhone 15 Pro Max 256GB 深空黑色 5G手机 官方正品",
                "price": "8999.00",
                "images": [
                    "https://img.taobao.com/image1.jpg",
                    "https://img.taobao.com/image2.jpg"
                ],
                "shop_name": "Apple官方旗舰店",
                "sales_count": "月销1.5万+",
                "rating": "4.9",
                "review_count": "25000",
                "brand": "Apple"
            },
            "product_type": ProductType.COMPETITOR
        }
    ]
    
    processed_products = []
    
    for i, data in enumerate(test_data):
        print(f"\n{i+1}. 处理 {data['platform'].value} 平台数据...")
        
        product = await processor.process_crawl_result(
            raw_data=data["raw_data"],
            url=data["url"],
            platform=data["platform"],
            product_type=data["product_type"]
        )
        
        processed_products.append(product)
        
        print(f"   商品ID: {product.id}")
        print(f"   标题: {product.title}")
        print(f"   平台: {product.platform}")
        print(f"   商品类型: {product.product_type.value}")
        print(f"   价格: {product.get_price_display()}")
        print(f"   图片数量: {len(product.images)}")
        print(f"   数据质量分数: {product.data_quality_score:.2f}")
        print(f"   质量等级: {product.data_quality.value}")
        
        if product.seller:
            print(f"   卖家: {product.seller.name}")
        
        if product.metrics and product.metrics.sales_count:
            print(f"   销量: {product.metrics.sales_count}")
    
    return processed_products


async def demo_quality_checking(products):
    """演示质量检查功能"""
    print("\n=== 数据质量检查演示 ===")
    
    checker = DataQualityChecker()
    
    print(f"\n对 {len(products)} 个商品进行质量检查...")
    
    reports = await checker.batch_check_quality(products)
    
    for i, report in enumerate(reports):
        product = products[i]
        print(f"\n{i+1}. 商品质量报告:")
        print(f"   商品ID: {report.product_id}")
        print(f"   标题: {product.title[:50]}...")
        print(f"   总体分数: {report.overall_score:.2f}")
        print(f"   质量等级: {report.quality_level.value}")
        print(f"   通过检查: {report.passed_checks}/{report.total_checks}")
        
        if report.issues:
            print(f"   发现问题 ({len(report.issues)}个):")
            for issue in report.issues[:3]:  # 只显示前3个问题
                severity_icon = {"critical": "🔴", "warning": "🟡", "info": "🔵"}.get(issue.severity, "⚪")
                print(f"     {severity_icon} {issue.field}: {issue.message}")
            
            if len(report.issues) > 3:
                print(f"     ... 还有 {len(report.issues) - 3} 个问题")
        else:
            print("   ✅ 未发现质量问题")
    
    # 质量统计
    excellent_count = sum(1 for r in reports if r.quality_level == DataQuality.EXCELLENT)
    good_count = sum(1 for r in reports if r.quality_level == DataQuality.GOOD)
    fair_count = sum(1 for r in reports if r.quality_level == DataQuality.FAIR)
    poor_count = sum(1 for r in reports if r.quality_level == DataQuality.POOR)
    bad_count = sum(1 for r in reports if r.quality_level == DataQuality.BAD)
    
    print(f"\n📊 质量分布统计:")
    print(f"   优秀: {excellent_count} 个")
    print(f"   良好: {good_count} 个")
    print(f"   一般: {fair_count} 个")
    print(f"   较差: {poor_count} 个")
    print(f"   很差: {bad_count} 个")
    
    avg_score = sum(r.overall_score for r in reports) / len(reports)
    print(f"   平均质量分数: {avg_score:.2f}")


async def demo_product_classification(products):
    """演示商品分类功能"""
    print("\n=== 商品分类演示 ===")
    
    classifier = ProductClassifier()
    
    print(f"\n对 {len(products)} 个商品进行自动分类...")
    
    results = await classifier.batch_classify_products(products)
    
    for i, result in enumerate(results):
        product = products[i]
        print(f"\n{i+1}. 商品分类结果:")
        print(f"   商品标题: {product.title[:50]}...")
        print(f"   原始类型: {product.product_type.value}")
        print(f"   分类结果: {result.product_type.value}")
        print(f"   置信度: {result.confidence:.2f}")
        print(f"   匹配规则: {', '.join(result.matched_rules) if result.matched_rules else '无'}")
        
        if result.categories:
            category_names = [cat.name for cat in result.categories]
            print(f"   商品分类: {', '.join(category_names)}")
        
        if result.tags:
            tag_names = [tag.name for tag in result.tags]
            print(f"   商品标签: {', '.join(tag_names)}")
        
        print(f"   分类推理: {result.reasoning}")
        
        # 更新商品的分类信息
        product.product_type = result.product_type
        product.categories = result.categories
        product.tags = result.tags
    
    # 分类统计
    type_counts = {}
    for result in results:
        ptype = result.product_type.value
        type_counts[ptype] = type_counts.get(ptype, 0) + 1
    
    print(f"\n📊 分类统计:")
    for ptype, count in type_counts.items():
        print(f"   {ptype}: {count} 个")


async def demo_archive_management(products):
    """演示归档管理功能"""
    print("\n=== 归档管理演示 ===")
    
    archive_manager = ArchiveManager()
    
    # 创建一些测试商品（模拟不同状态）
    test_products = products.copy()
    
    # 添加一个不活跃的商品
    inactive_product = Product(
        url="https://example.com/inactive",
        title="长期不活跃的商品",
        platform="1688",
        status=ProductStatus.INACTIVE,
        data_quality_score=0.6,
        last_crawled_at=datetime.now() - timedelta(days=35),
        created_at=datetime.now() - timedelta(days=60)
    )
    test_products.append(inactive_product)
    
    # 添加一个低质量商品
    poor_quality_product = Product(
        url="https://example.com/poor",
        title="数据质量很差的商品",
        platform="taobao",
        status=ProductStatus.ACTIVE,
        data_quality_score=0.2,
        created_at=datetime.now() - timedelta(days=10)
    )
    test_products.append(poor_quality_product)
    
    print(f"\n1. 手动归档演示:")
    
    # 手动归档第一个商品
    first_product = test_products[0]
    print(f"   归档商品: {first_product.title[:30]}...")
    
    success = await archive_manager.archive_product(
        product=first_product,
        reason=ArchiveReason.MANUAL,
        operator="demo_user",
        notes="演示手动归档功能"
    )
    
    print(f"   归档结果: {'成功' if success else '失败'}")
    print(f"   商品状态: {first_product.status.value}")
    print(f"   变更记录数: {len(first_product.change_history)}")
    
    print(f"\n2. 自动归档演示:")
    
    # 自动归档检查
    auto_result = await archive_manager.auto_archive_products(test_products)
    
    print(f"   检查商品数: {auto_result['total_checked']}")
    print(f"   归档商品数: {auto_result['total_archived']}")
    print(f"   应用规则数: {auto_result['rules_applied']}")
    
    for rule_name, rule_result in auto_result['rule_results'].items():
        print(f"   规则 '{rule_name}':")
        print(f"     匹配商品: {rule_result['matched_products']}")
        print(f"     归档数量: {rule_result['archived_count']}")
        print(f"     归档原因: {rule_result['reason']}")
    
    print(f"\n3. 恢复归档演示:")
    
    # 恢复第一个商品
    print(f"   恢复商品: {first_product.title[:30]}...")
    
    restore_success = await archive_manager.restore_product(
        product=first_product,
        operator="demo_user",
        notes="演示恢复功能"
    )
    
    print(f"   恢复结果: {'成功' if restore_success else '失败'}")
    print(f"   商品状态: {first_product.status.value}")
    
    print(f"\n4. 归档统计:")
    
    # 获取归档统计
    stats = await archive_manager.get_archive_statistics(test_products)
    
    print(f"   总商品数: {stats['total_products']}")
    print(f"   状态分布:")
    for status, count in stats['status_breakdown'].items():
        print(f"     {status}: {count} 个")
    
    print(f"   归档操作:")
    print(f"     总操作数: {stats['archive_operations']['total']}")
    
    if stats['archive_operations']['by_reason']:
        print(f"     按原因分布:")
        for reason, count in stats['archive_operations']['by_reason'].items():
            print(f"       {reason}: {count} 次")
    
    if stats['archive_operations']['recent_operations']:
        print(f"     最近操作:")
        for op in stats['archive_operations']['recent_operations'][:3]:
            print(f"       {op['operation_type']} - {op['reason']} ({op['created_at'][:19]})")


async def demo_product_lifecycle():
    """演示商品生命周期管理"""
    print("\n=== 商品生命周期演示 ===")
    
    # 创建一个新商品
    product = Product(
        url="https://example.com/lifecycle",
        title="生命周期演示商品",
        platform="1688",
        status=ProductStatus.NEW
    )
    
    print(f"1. 新建商品:")
    print(f"   ID: {product.id}")
    print(f"   状态: {product.status.value}")
    print(f"   版本: {product.version}")
    print(f"   创建时间: {product.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 模拟状态变更
    print(f"\n2. 状态变更:")
    
    # 激活商品
    product.add_change_record(
        change_type="status_change",
        old_value=ProductStatus.NEW.value,
        new_value=ProductStatus.ACTIVE.value,
        reason="商品审核通过"
    )
    product.status = ProductStatus.ACTIVE
    
    print(f"   状态更新: NEW -> ACTIVE")
    print(f"   版本: {product.version}")
    print(f"   变更记录数: {len(product.change_history)}")
    
    # 价格变更
    old_price = 29.90
    new_price = 25.90
    
    product.add_change_record(
        change_type="price_change",
        old_value=old_price,
        new_value=new_price,
        reason="促销活动"
    )
    
    print(f"   价格更新: ¥{old_price} -> ¥{new_price}")
    print(f"   版本: {product.version}")
    
    # 暂停监控
    product.add_change_record(
        change_type="status_change",
        old_value=ProductStatus.ACTIVE.value,
        new_value=ProductStatus.PAUSED.value,
        reason="临时暂停监控"
    )
    product.status = ProductStatus.PAUSED
    
    print(f"   状态更新: ACTIVE -> PAUSED")
    print(f"   版本: {product.version}")
    
    print(f"\n3. 变更历史:")
    for i, change in enumerate(product.change_history):
        print(f"   {i+1}. {change.change_type}:")
        print(f"      时间: {change.changed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"      变更: {change.old_value} -> {change.new_value}")
        print(f"      原因: {change.change_reason}")
    
    print(f"\n4. 最终状态:")
    print(f"   当前状态: {product.status.value}")
    print(f"   当前版本: {product.version}")
    print(f"   最后更新: {product.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   总变更次数: {len(product.change_history)}")


async def main():
    """主演示函数"""
    print("🚀 商品管理业务层演示")
    print("=" * 50)
    
    # 1. 数据处理演示
    products = await demo_data_processing()
    
    # 2. 质量检查演示
    await demo_quality_checking(products)
    
    # 3. 商品分类演示
    await demo_product_classification(products)
    
    # 4. 归档管理演示
    await demo_archive_management(products)
    
    # 5. 生命周期演示
    await demo_product_lifecycle()
    
    print("\n" + "=" * 50)
    print("✅ 商品管理业务层演示完成！")
    print("\n核心功能:")
    print("- 多平台数据处理和标准化")
    print("- 智能数据质量检查和评分")
    print("- 自动商品分类和标签生成")
    print("- 完整的归档管理和生命周期跟踪")
    print("- 版本管理和变更历史记录")
    
    print(f"\n演示统计:")
    print(f"- 处理商品数: {len(products)}")
    print(f"- 支持平台: 1688, 淘宝, 京东, 拼多多")
    print(f"- 质量检查规则: 8项")
    print(f"- 分类规则: 6项")
    print(f"- 归档规则: 3项")


if __name__ == "__main__":
    asyncio.run(main())
