/**
 * 商品状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, ProductForm, PaginatedResponse, SearchParams } from '../../types';
import { productApi } from '../../services/productApi';

// 状态类型
interface ProductState {
  products: Product[];
  currentProduct: Product | null;
  total: number;
  page: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
  searchParams: SearchParams;
  selectedProducts: string[];
}

// 初始状态
const initialState: ProductState = {
  products: [],
  currentProduct: null,
  total: 0,
  page: 1,
  pageSize: 20,
  isLoading: false,
  error: null,
  searchParams: {},
  selectedProducts: [],
};

// 异步actions
export const fetchProductsAsync = createAsyncThunk(
  'product/fetchProducts',
  async (params: { page?: number; page_size?: number; search?: SearchParams }, { rejectWithValue }) => {
    try {
      const response = await productApi.getProducts(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取商品列表失败');
    }
  }
);

export const fetchProductByIdAsync = createAsyncThunk(
  'product/fetchProductById',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response = await productApi.getProductById(productId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取商品详情失败');
    }
  }
);

export const createProductAsync = createAsyncThunk(
  'product/createProduct',
  async (productData: ProductForm, { rejectWithValue }) => {
    try {
      const response = await productApi.createProduct(productData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建商品失败');
    }
  }
);

export const updateProductAsync = createAsyncThunk(
  'product/updateProduct',
  async ({ productId, productData }: { productId: string; productData: Partial<ProductForm> }, { rejectWithValue }) => {
    try {
      const response = await productApi.updateProduct(productId, productData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新商品失败');
    }
  }
);

export const deleteProductAsync = createAsyncThunk(
  'product/deleteProduct',
  async (productId: string, { rejectWithValue }) => {
    try {
      await productApi.deleteProduct(productId);
      return productId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除商品失败');
    }
  }
);

export const batchDeleteProductsAsync = createAsyncThunk(
  'product/batchDeleteProducts',
  async (productIds: string[], { rejectWithValue }) => {
    try {
      await productApi.batchDeleteProducts(productIds);
      return productIds;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '批量删除商品失败');
    }
  }
);

export const importProductsAsync = createAsyncThunk(
  'product/importProducts',
  async (file: File, { rejectWithValue }) => {
    try {
      const response = await productApi.importProducts(file);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '导入商品失败');
    }
  }
);

export const exportProductsAsync = createAsyncThunk(
  'product/exportProducts',
  async (params: { format: 'excel' | 'csv'; filters?: SearchParams }, { rejectWithValue }) => {
    try {
      await productApi.exportProducts(params);
      return { success: true };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '导出商品失败');
    }
  }
);

// Slice
const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentProduct: (state, action: PayloadAction<Product | null>) => {
      state.currentProduct = action.payload;
    },
    setSearchParams: (state, action: PayloadAction<SearchParams>) => {
      state.searchParams = action.payload;
      state.page = 1; // 重置页码
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.page = 1; // 重置页码
    },
    setSelectedProducts: (state, action: PayloadAction<string[]>) => {
      state.selectedProducts = action.payload;
    },
    toggleProductSelection: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      const index = state.selectedProducts.indexOf(productId);
      if (index > -1) {
        state.selectedProducts.splice(index, 1);
      } else {
        state.selectedProducts.push(productId);
      }
    },
    selectAllProducts: (state) => {
      state.selectedProducts = state.products.map(p => p.product_id);
    },
    clearSelection: (state) => {
      state.selectedProducts = [];
    },
  },
  extraReducers: (builder) => {
    // 获取商品列表
    builder
      .addCase(fetchProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload as PaginatedResponse<Product>;
        state.products = data.items;
        state.total = data.total;
        state.page = data.page;
        state.pageSize = data.page_size;
        state.error = null;
      })
      .addCase(fetchProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取商品详情
    builder
      .addCase(fetchProductByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProductByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload;
        state.error = null;
      })
      .addCase(fetchProductByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建商品
    builder
      .addCase(createProductAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createProductAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products.unshift(action.payload);
        state.total += 1;
        state.error = null;
      })
      .addCase(createProductAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 更新商品
    builder
      .addCase(updateProductAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProductAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const updatedProduct = action.payload;
        const index = state.products.findIndex(p => p.product_id === updatedProduct.product_id);
        if (index > -1) {
          state.products[index] = updatedProduct;
        }
        if (state.currentProduct?.product_id === updatedProduct.product_id) {
          state.currentProduct = updatedProduct;
        }
        state.error = null;
      })
      .addCase(updateProductAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 删除商品
    builder
      .addCase(deleteProductAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteProductAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const productId = action.payload;
        state.products = state.products.filter(p => p.product_id !== productId);
        state.total -= 1;
        if (state.currentProduct?.product_id === productId) {
          state.currentProduct = null;
        }
        // 从选中列表中移除
        state.selectedProducts = state.selectedProducts.filter(id => id !== productId);
        state.error = null;
      })
      .addCase(deleteProductAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 批量删除商品
    builder
      .addCase(batchDeleteProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(batchDeleteProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedIds = action.payload;
        state.products = state.products.filter(p => !deletedIds.includes(p.product_id));
        state.total -= deletedIds.length;
        state.selectedProducts = [];
        state.error = null;
      })
      .addCase(batchDeleteProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 导入商品
    builder
      .addCase(importProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(importProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        // 导入成功后重新获取列表
        state.error = null;
      })
      .addCase(importProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 导出商品
    builder
      .addCase(exportProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(exportProductsAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(exportProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// 导出actions
export const {
  clearError,
  setCurrentProduct,
  setSearchParams,
  setPage,
  setPageSize,
  setSelectedProducts,
  toggleProductSelection,
  selectAllProducts,
  clearSelection,
} = productSlice.actions;

// 选择器
export const selectProducts = (state: { product: ProductState }) => state.product.products;
export const selectCurrentProduct = (state: { product: ProductState }) => state.product.currentProduct;
export const selectProductsLoading = (state: { product: ProductState }) => state.product.isLoading;
export const selectProductsError = (state: { product: ProductState }) => state.product.error;
export const selectProductsPagination = (state: { product: ProductState }) => ({
  total: state.product.total,
  page: state.product.page,
  pageSize: state.product.pageSize,
});
export const selectProductsSearchParams = (state: { product: ProductState }) => state.product.searchParams;
export const selectSelectedProducts = (state: { product: ProductState }) => state.product.selectedProducts;

export default productSlice.reducer;
