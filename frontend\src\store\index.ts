/**
 * Redux Store 配置
 */

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

import authSlice from './slices/authSlice';
import productSlice from './slices/productSlice';
import monitorSlice from './slices/monitorSlice';
import supplierSlice from './slices/supplierSlice';
import systemSlice from './slices/systemSlice';
import uiSlice from './slices/uiSlice';

// 配置store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    product: productSlice,
    monitor: monitorSlice,
    supplier: supplierSlice,
    system: systemSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 类型定义
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 类型化的hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
