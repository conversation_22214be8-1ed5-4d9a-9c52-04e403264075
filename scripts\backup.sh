#!/bin/bash

# Moniit 系统备份脚本
# 支持数据库、配置文件、上传文件的备份

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="moniit_backup_${TIMESTAMP}"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"

# 数据库配置
DB_CONTAINER="moniit-timescaledb-dev"
DB_NAME="moniit"
DB_USER="moniit"

# 显示帮助信息
show_help() {
    echo "Moniit 系统备份脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE      备份类型 (full|db|config|files) [默认: full]"
    echo "  -o, --output DIR     备份输出目录 [默认: ./backups]"
    echo "  -c, --compress       压缩备份文件"
    echo "  -r, --retention DAYS 保留天数 [默认: 30]"
    echo "  -l, --list           列出所有备份"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "备份类型:"
    echo "  full    - 完整备份（数据库+配置+文件）"
    echo "  db      - 仅备份数据库"
    echo "  config  - 仅备份配置文件"
    echo "  files   - 仅备份上传文件"
    echo ""
    echo "示例:"
    echo "  $0                   执行完整备份"
    echo "  $0 -t db            仅备份数据库"
    echo "  $0 -c -r 7          压缩备份并保留7天"
    echo "  $0 -l               列出所有备份"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        log_error "数据库容器 $DB_CONTAINER 未运行"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录: $BACKUP_PATH"
    
    mkdir -p "$BACKUP_PATH"
    
    log_success "备份目录创建完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local db_backup_file="${BACKUP_PATH}/database.sql"
    
    # 使用pg_dump备份数据库
    docker exec "$DB_CONTAINER" pg_dump -U "$DB_USER" -d "$DB_NAME" > "$db_backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $db_backup_file"
        
        # 记录备份信息
        echo "Database: $DB_NAME" >> "${BACKUP_PATH}/backup_info.txt"
        echo "Container: $DB_CONTAINER" >> "${BACKUP_PATH}/backup_info.txt"
        echo "Backup Time: $(date)" >> "${BACKUP_PATH}/backup_info.txt"
        echo "File Size: $(du -h "$db_backup_file" | cut -f1)" >> "${BACKUP_PATH}/backup_info.txt"
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    local config_dir="${BACKUP_PATH}/config"
    mkdir -p "$config_dir"
    
    # 备份主要配置文件
    local config_files=(
        ".env"
        "docker-compose.yml"
        "docker-compose.dev.yml"
        "docker-compose.prod.yml"
        "config/"
        "nginx/"
    )
    
    for file in "${config_files[@]}"; do
        if [ -e "$file" ]; then
            cp -r "$file" "$config_dir/"
            log_info "已备份: $file"
        else
            log_warning "文件不存在: $file"
        fi
    done
    
    log_success "配置文件备份完成"
}

# 备份上传文件
backup_files() {
    log_info "备份上传文件..."
    
    local files_dir="${BACKUP_PATH}/files"
    mkdir -p "$files_dir"
    
    # 备份上传文件目录
    if [ -d "uploads" ]; then
        cp -r uploads "$files_dir/"
        log_success "上传文件备份完成"
    else
        log_warning "uploads 目录不存在"
    fi
    
    # 备份数据目录
    if [ -d "data" ]; then
        cp -r data "$files_dir/"
        log_success "数据文件备份完成"
    else
        log_warning "data 目录不存在"
    fi
    
    # 备份日志文件
    if [ -d "logs" ]; then
        cp -r logs "$files_dir/"
        log_success "日志文件备份完成"
    else
        log_warning "logs 目录不存在"
    fi
}

# 压缩备份
compress_backup() {
    log_info "压缩备份文件..."
    
    local archive_name="${BACKUP_NAME}.tar.gz"
    local archive_path="${BACKUP_DIR}/${archive_name}"
    
    cd "$BACKUP_DIR"
    tar -czf "$archive_name" "$BACKUP_NAME"
    
    if [ $? -eq 0 ]; then
        # 删除原始目录
        rm -rf "$BACKUP_NAME"
        log_success "备份压缩完成: $archive_path"
        echo "Archive: $archive_path" >> "${BACKUP_DIR}/backup_log.txt"
    else
        log_error "备份压缩失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local retention_days=$1
    log_info "清理 ${retention_days} 天前的备份..."
    
    # 清理旧的备份目录
    find "$BACKUP_DIR" -name "moniit_backup_*" -type d -mtime +$retention_days -exec rm -rf {} \;
    
    # 清理旧的压缩文件
    find "$BACKUP_DIR" -name "moniit_backup_*.tar.gz" -type f -mtime +$retention_days -delete
    
    log_success "旧备份清理完成"
}

# 列出所有备份
list_backups() {
    log_info "备份列表:"
    echo ""
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在"
        return
    fi
    
    # 列出备份目录
    echo "备份目录:"
    find "$BACKUP_DIR" -name "moniit_backup_*" -type d | sort | while read -r backup; do
        local size=$(du -sh "$backup" 2>/dev/null | cut -f1)
        local date=$(basename "$backup" | sed 's/moniit_backup_//' | sed 's/_/ /')
        echo "  $backup ($size) - $date"
    done
    
    echo ""
    echo "压缩备份:"
    find "$BACKUP_DIR" -name "moniit_backup_*.tar.gz" -type f | sort | while read -r backup; do
        local size=$(du -sh "$backup" 2>/dev/null | cut -f1)
        local date=$(basename "$backup" .tar.gz | sed 's/moniit_backup_//' | sed 's/_/ /')
        echo "  $backup ($size) - $date"
    done
}

# 验证备份
verify_backup() {
    log_info "验证备份..."
    
    local errors=0
    
    # 检查数据库备份
    if [ "$BACKUP_TYPE" = "full" ] || [ "$BACKUP_TYPE" = "db" ]; then
        if [ ! -f "${BACKUP_PATH}/database.sql" ]; then
            log_error "数据库备份文件不存在"
            ((errors++))
        else
            local db_size=$(stat -f%z "${BACKUP_PATH}/database.sql" 2>/dev/null || stat -c%s "${BACKUP_PATH}/database.sql" 2>/dev/null)
            if [ "$db_size" -lt 1000 ]; then
                log_error "数据库备份文件太小，可能备份失败"
                ((errors++))
            fi
        fi
    fi
    
    # 检查配置备份
    if [ "$BACKUP_TYPE" = "full" ] || [ "$BACKUP_TYPE" = "config" ]; then
        if [ ! -d "${BACKUP_PATH}/config" ]; then
            log_error "配置备份目录不存在"
            ((errors++))
        fi
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "备份验证通过"
        return 0
    else
        log_error "备份验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 主函数
main() {
    # 默认参数
    BACKUP_TYPE="full"
    COMPRESS=false
    RETENTION_DAYS=30
    LIST_BACKUPS=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                BACKUP_TYPE="$2"
                shift 2
                ;;
            -o|--output)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -c|--compress)
                COMPRESS=true
                shift
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -l|--list)
                LIST_BACKUPS=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 列出备份
    if [ "$LIST_BACKUPS" = true ]; then
        list_backups
        exit 0
    fi
    
    # 验证备份类型
    if [[ "$BACKUP_TYPE" != "full" && "$BACKUP_TYPE" != "db" && "$BACKUP_TYPE" != "config" && "$BACKUP_TYPE" != "files" ]]; then
        log_error "无效的备份类型: $BACKUP_TYPE"
        exit 1
    fi
    
    log_info "开始 Moniit 系统备份 (类型: $BACKUP_TYPE)"
    
    # 执行备份
    check_dependencies
    create_backup_dir
    
    case $BACKUP_TYPE in
        "full")
            backup_database
            backup_config
            backup_files
            ;;
        "db")
            backup_database
            ;;
        "config")
            backup_config
            ;;
        "files")
            backup_files
            ;;
    esac
    
    # 验证备份
    if ! verify_backup; then
        log_error "备份验证失败，请检查备份文件"
        exit 1
    fi
    
    # 压缩备份
    if [ "$COMPRESS" = true ]; then
        compress_backup
    fi
    
    # 清理旧备份
    cleanup_old_backups $RETENTION_DAYS
    
    log_success "Moniit 系统备份完成!"
    log_info "备份位置: $BACKUP_PATH"
    
    # 显示备份信息
    if [ -f "${BACKUP_PATH}/backup_info.txt" ]; then
        echo ""
        log_info "备份信息:"
        cat "${BACKUP_PATH}/backup_info.txt"
    fi
}

# 执行主函数
main "$@"
