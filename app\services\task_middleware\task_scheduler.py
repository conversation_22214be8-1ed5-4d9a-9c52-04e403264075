"""
任务调度器

集成Celery实现异步任务调度
支持任务优先级、批量处理和智能调度
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import hashlib

from app.core.logging import get_logger
from .client import TaskMiddlewareClient, CrawlConfig, TaskPriority, BatchTaskResult
from .config_manager import PlatformConfigManager, Platform, ProductType

logger = get_logger(__name__)


class ScheduleStrategy(str, Enum):
    """调度策略枚举"""
    IMMEDIATE = "immediate"      # 立即执行
    BATCH_OPTIMIZE = "batch"     # 批量优化
    TIME_SPREAD = "spread"       # 时间分散
    PRIORITY_FIRST = "priority"  # 优先级优先


@dataclass
class ScheduleTask:
    """调度任务"""
    urls: List[str]
    platform: Platform
    product_type: ProductType
    priority: TaskPriority = TaskPriority.MEDIUM
    custom_fields: Optional[List[str]] = None
    callback_url: Optional[str] = None
    schedule_time: Optional[datetime] = None
    batch_name: Optional[str] = None
    
    def __post_init__(self):
        if self.schedule_time is None:
            self.schedule_time = datetime.now()


@dataclass
class ScheduleResult:
    """调度结果"""
    success: bool
    scheduled_tasks: int
    batch_results: List[BatchTaskResult]
    failed_tasks: int
    message: str
    schedule_id: str


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, client: TaskMiddlewareClient, config_manager: PlatformConfigManager):
        self.client = client
        self.config_manager = config_manager
        self._pending_tasks: List[ScheduleTask] = []
        self._batch_size_limits = {
            Platform.ALIBABA_1688: 50,
            Platform.TAOBAO: 30,
            Platform.JD: 40,
            Platform.PINDUODUO: 35
        }
        
    async def schedule_single_task(self, task: ScheduleTask) -> ScheduleResult:
        """
        调度单个任务
        
        Args:
            task: 调度任务
            
        Returns:
            ScheduleResult: 调度结果
        """
        try:
            logger.info(f"调度单个任务: {task.platform.value}, URLs: {len(task.urls)}")
            
            # 构建爬取配置
            crawl_config = await self._build_crawl_config(task)
            
            # 提交任务
            batch_result = await self.client.submit_crawl_task(crawl_config)
            
            schedule_id = self._generate_schedule_id(task)
            
            return ScheduleResult(
                success=batch_result.success,
                scheduled_tasks=batch_result.valid_tasks,
                batch_results=[batch_result],
                failed_tasks=batch_result.invalid_tasks,
                message=f"任务调度成功: {batch_result.batch_id}",
                schedule_id=schedule_id
            )
            
        except Exception as e:
            logger.error(f"单个任务调度失败: {e}")
            return ScheduleResult(
                success=False,
                scheduled_tasks=0,
                batch_results=[],
                failed_tasks=len(task.urls),
                message=f"任务调度失败: {e}",
                schedule_id=self._generate_schedule_id(task)
            )
    
    async def schedule_batch_tasks(self, tasks: List[ScheduleTask], 
                                 strategy: ScheduleStrategy = ScheduleStrategy.BATCH_OPTIMIZE) -> ScheduleResult:
        """
        批量调度任务
        
        Args:
            tasks: 调度任务列表
            strategy: 调度策略
            
        Returns:
            ScheduleResult: 调度结果
        """
        try:
            logger.info(f"批量调度任务: {len(tasks)} 个任务, 策略: {strategy.value}")
            
            if strategy == ScheduleStrategy.IMMEDIATE:
                return await self._schedule_immediate(tasks)
            elif strategy == ScheduleStrategy.BATCH_OPTIMIZE:
                return await self._schedule_batch_optimize(tasks)
            elif strategy == ScheduleStrategy.TIME_SPREAD:
                return await self._schedule_time_spread(tasks)
            elif strategy == ScheduleStrategy.PRIORITY_FIRST:
                return await self._schedule_priority_first(tasks)
            else:
                return await self._schedule_batch_optimize(tasks)  # 默认策略
                
        except Exception as e:
            logger.error(f"批量任务调度失败: {e}")
            total_urls = sum(len(task.urls) for task in tasks)
            return ScheduleResult(
                success=False,
                scheduled_tasks=0,
                batch_results=[],
                failed_tasks=total_urls,
                message=f"批量任务调度失败: {e}",
                schedule_id=self._generate_batch_schedule_id(tasks)
            )
    
    async def _schedule_immediate(self, tasks: List[ScheduleTask]) -> ScheduleResult:
        """立即执行策略"""
        batch_results = []
        total_scheduled = 0
        total_failed = 0
        
        # 并发提交所有任务
        semaphore = asyncio.Semaphore(5)  # 限制并发数
        
        async def submit_task(task: ScheduleTask):
            async with semaphore:
                result = await self.schedule_single_task(task)
                return result
        
        # 创建并发任务
        submit_tasks = [submit_task(task) for task in tasks]
        results = await asyncio.gather(*submit_tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"任务提交异常: {result}")
                total_failed += 1
            else:
                batch_results.extend(result.batch_results)
                total_scheduled += result.scheduled_tasks
                total_failed += result.failed_tasks
        
        return ScheduleResult(
            success=total_scheduled > 0,
            scheduled_tasks=total_scheduled,
            batch_results=batch_results,
            failed_tasks=total_failed,
            message=f"立即执行完成: 成功 {total_scheduled}, 失败 {total_failed}",
            schedule_id=self._generate_batch_schedule_id(tasks)
        )
    
    async def _schedule_batch_optimize(self, tasks: List[ScheduleTask]) -> ScheduleResult:
        """批量优化策略"""
        # 按平台分组
        platform_groups = {}
        for task in tasks:
            if task.platform not in platform_groups:
                platform_groups[task.platform] = []
            platform_groups[task.platform].append(task)
        
        batch_results = []
        total_scheduled = 0
        total_failed = 0
        
        # 为每个平台优化批次
        for platform, platform_tasks in platform_groups.items():
            logger.info(f"优化平台 {platform.value} 的 {len(platform_tasks)} 个任务")
            
            # 合并同平台任务
            optimized_batches = self._optimize_platform_batches(platform, platform_tasks)
            
            # 提交优化后的批次
            for batch_config in optimized_batches:
                try:
                    batch_result = await self.client.submit_crawl_task(batch_config)
                    batch_results.append(batch_result)
                    total_scheduled += batch_result.valid_tasks
                    total_failed += batch_result.invalid_tasks
                except Exception as e:
                    logger.error(f"批次提交失败: {e}")
                    total_failed += len(batch_config.urls)
        
        return ScheduleResult(
            success=total_scheduled > 0,
            scheduled_tasks=total_scheduled,
            batch_results=batch_results,
            failed_tasks=total_failed,
            message=f"批量优化完成: 成功 {total_scheduled}, 失败 {total_failed}",
            schedule_id=self._generate_batch_schedule_id(tasks)
        )
    
    async def _schedule_time_spread(self, tasks: List[ScheduleTask]) -> ScheduleResult:
        """时间分散策略"""
        # 按优先级排序
        sorted_tasks = sorted(tasks, key=lambda t: (
            -self._get_priority_value(t.priority),
            t.schedule_time or datetime.now()
        ))
        
        batch_results = []
        total_scheduled = 0
        total_failed = 0
        
        # 分散执行时间
        delay_interval = 30  # 30秒间隔
        
        for i, task in enumerate(sorted_tasks):
            try:
                # 计算延迟时间
                delay = i * delay_interval
                if delay > 0:
                    logger.info(f"任务将在 {delay} 秒后执行")
                    await asyncio.sleep(delay)
                
                result = await self.schedule_single_task(task)
                batch_results.extend(result.batch_results)
                total_scheduled += result.scheduled_tasks
                total_failed += result.failed_tasks
                
            except Exception as e:
                logger.error(f"时间分散任务执行失败: {e}")
                total_failed += len(task.urls)
        
        return ScheduleResult(
            success=total_scheduled > 0,
            scheduled_tasks=total_scheduled,
            batch_results=batch_results,
            failed_tasks=total_failed,
            message=f"时间分散完成: 成功 {total_scheduled}, 失败 {total_failed}",
            schedule_id=self._generate_batch_schedule_id(tasks)
        )
    
    async def _schedule_priority_first(self, tasks: List[ScheduleTask]) -> ScheduleResult:
        """优先级优先策略"""
        # 按优先级分组
        priority_groups = {
            TaskPriority.HIGH: [],
            TaskPriority.MEDIUM: [],
            TaskPriority.LOW: []
        }
        
        for task in tasks:
            priority_groups[task.priority].append(task)
        
        batch_results = []
        total_scheduled = 0
        total_failed = 0
        
        # 按优先级顺序执行
        for priority in [TaskPriority.HIGH, TaskPriority.MEDIUM, TaskPriority.LOW]:
            priority_tasks = priority_groups[priority]
            if not priority_tasks:
                continue
                
            logger.info(f"执行 {priority.value} 优先级任务: {len(priority_tasks)} 个")
            
            # 高优先级任务立即执行，其他批量优化
            if priority == TaskPriority.HIGH:
                result = await self._schedule_immediate(priority_tasks)
            else:
                result = await self._schedule_batch_optimize(priority_tasks)
            
            batch_results.extend(result.batch_results)
            total_scheduled += result.scheduled_tasks
            total_failed += result.failed_tasks
        
        return ScheduleResult(
            success=total_scheduled > 0,
            scheduled_tasks=total_scheduled,
            batch_results=batch_results,
            failed_tasks=total_failed,
            message=f"优先级调度完成: 成功 {total_scheduled}, 失败 {total_failed}",
            schedule_id=self._generate_batch_schedule_id(tasks)
        )
    
    def _optimize_platform_batches(self, platform: Platform, tasks: List[ScheduleTask]) -> List[CrawlConfig]:
        """优化平台批次"""
        batch_limit = self._batch_size_limits.get(platform, 30)
        optimized_batches = []
        
        # 按商品类型分组
        type_groups = {}
        for task in tasks:
            if task.product_type not in type_groups:
                type_groups[task.product_type] = []
            type_groups[task.product_type].append(task)
        
        # 为每个商品类型创建批次
        for product_type, type_tasks in type_groups.items():
            # 合并URLs
            all_urls = []
            for task in type_tasks:
                all_urls.extend(task.urls)
            
            # 分批处理
            for i in range(0, len(all_urls), batch_limit):
                batch_urls = all_urls[i:i + batch_limit]
                
                # 使用第一个任务的配置作为基础
                base_task = type_tasks[0]
                
                # 构建批次配置
                batch_config = CrawlConfig(
                    urls=batch_urls,
                    query=self.config_manager.build_crawl_query(platform, product_type),
                    priority=base_task.priority,
                    batch_name=f"{platform.value}_{product_type.value}_batch_{i//batch_limit + 1}",
                    batch_description=f"优化批次: {platform.value} {product_type.value} 商品",
                    callback_url=base_task.callback_url
                )
                
                optimized_batches.append(batch_config)
        
        logger.info(f"平台 {platform.value} 优化为 {len(optimized_batches)} 个批次")
        return optimized_batches
    
    async def _build_crawl_config(self, task: ScheduleTask) -> CrawlConfig:
        """构建爬取配置"""
        # 获取查询指令
        query = self.config_manager.build_crawl_query(
            task.platform, 
            task.product_type, 
            task.custom_fields
        )
        
        # 应用优先级提升
        priority_boost = self.config_manager.get_priority_boost(task.platform, task.product_type)
        adjusted_priority = self._adjust_priority(task.priority, priority_boost)
        
        return CrawlConfig(
            urls=task.urls,
            query=query,
            priority=adjusted_priority,
            batch_name=task.batch_name or f"{task.platform.value}_{task.product_type.value}",
            batch_description=f"{task.platform.value} {task.product_type.value} 商品爬取",
            callback_url=task.callback_url
        )
    
    def _adjust_priority(self, base_priority: TaskPriority, boost: int) -> TaskPriority:
        """调整任务优先级"""
        priority_values = {
            TaskPriority.LOW: 1,
            TaskPriority.MEDIUM: 2,
            TaskPriority.HIGH: 3
        }
        
        current_value = priority_values[base_priority]
        adjusted_value = min(current_value + boost, 3)
        
        for priority, value in priority_values.items():
            if value == adjusted_value:
                return priority
        
        return base_priority
    
    def _get_priority_value(self, priority: TaskPriority) -> int:
        """获取优先级数值"""
        return {
            TaskPriority.LOW: 1,
            TaskPriority.MEDIUM: 2,
            TaskPriority.HIGH: 3
        }[priority]
    
    def _generate_schedule_id(self, task: ScheduleTask) -> str:
        """生成调度ID"""
        content = f"{task.platform.value}_{task.product_type.value}_{len(task.urls)}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _generate_batch_schedule_id(self, tasks: List[ScheduleTask]) -> str:
        """生成批量调度ID"""
        content = f"batch_{len(tasks)}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
