/**
 * 主布局组件
 */

import React, { useState } from 'react';
import { Layout as AntdLayout, Menu, Avatar, Dropdown, Bad<PERSON>, <PERSON><PERSON>, Drawer } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ShoppingOutlined,
  MonitorOutlined,
  BarChartOutlined,
  TeamOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../store';
import { logoutAsync, selectUser } from '../../store/slices/authSlice';
import { toggleSidebar, selectSidebarCollapsed } from '../../store/slices/uiSlice';
import { selectUnreadNotifications } from '../../store/slices/systemSlice';

const { Header, Sider, Content } = AntdLayout;

interface LayoutProps {
  children: React.ReactNode;
}

// 菜单配置
const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/products',
    icon: <ShoppingOutlined />,
    label: '商品管理 [部分未完成]',
  },
  {
    key: '/monitor',
    icon: <MonitorOutlined />,
    label: '监控管理 [未完成]',
  },
  {
    key: '/analytics',
    icon: <BarChartOutlined />,
    label: '数据分析 [未完成]',
  },
  {
    key: '/suppliers',
    icon: <TeamOutlined />,
    label: '供货商管理 [未完成]',
  },
  {
    key: 'system',
    icon: <SettingOutlined />,
    label: '系统管理',
    children: [
      {
        key: '/system/settings',
        label: '系统设置 [未完成]',
      },
      {
        key: '/system/users',
        label: '用户管理 [未完成]',
      },
    ],
  },
];

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  const user = useAppSelector(selectUser);
  const collapsed = useAppSelector(selectSidebarCollapsed);
  const unreadNotifications = useAppSelector(selectUnreadNotifications);
  
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'logout':
        dispatch(logoutAsync());
        break;
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;
    
    // 精确匹配
    if (pathname === '/') return ['/'];
    
    // 查找匹配的菜单项
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (pathname.startsWith(child.key)) {
            return [child.key];
          }
        }
      } else if (pathname.startsWith(item.key)) {
        return [item.key];
      }
    }
    
    return [];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const openKeys: string[] = [];
    
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (pathname.startsWith(child.key)) {
            openKeys.push(item.key);
            break;
          }
        }
      }
    }
    
    return openKeys;
  };

  // 侧边栏内容
  const sidebarContent = (
    <div className="h-100 d-flex flex-column">
      {/* Logo */}
      <div className="d-flex align-items-center justify-content-center" style={{ height: 64, borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ fontSize: collapsed ? 16 : 20, fontWeight: 'bold', color: '#1890ff' }}>
          {collapsed ? 'M' : 'Moniit'}
        </div>
      </div>
      
      {/* 菜单 */}
      <div className="flex-1" style={{ overflow: 'auto' }}>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </div>
    </div>
  );

  return (
    <AntdLayout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        className="d-none-mobile"
        style={{
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
        }}
      >
        {sidebarContent}
      </Sider>

      {/* 移动端侧边栏 */}
      <Drawer
        title="Moniit"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        styles={{ body: { padding: 0 } }}
        className="d-none-desktop"
        width={240}
      >
        {sidebarContent}
      </Drawer>

      <AntdLayout>
        {/* 头部 */}
        <Header
          style={{
            padding: '0 16px',
            background: '#fff',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div className="d-flex align-items-center">
            {/* 折叠按钮 - 桌面端 */}
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => dispatch(toggleSidebar())}
              className="d-none-mobile"
              style={{ fontSize: 16, width: 40, height: 40 }}
            />
            
            {/* 菜单按钮 - 移动端 */}
            <Button
              type="text"
              icon={<MenuUnfoldOutlined />}
              onClick={() => setMobileMenuVisible(true)}
              className="d-none-desktop"
              style={{ fontSize: 16, width: 40, height: 40 }}
            />
          </div>

          <div className="d-flex align-items-center">
            {/* 通知 */}
            <Badge count={unreadNotifications.length} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16, width: 40, height: 40 }}
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
              placement="bottomRight"
              arrow
            >
              <div className="d-flex align-items-center ml-2" style={{ cursor: 'pointer' }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <span className="ml-1 d-none-mobile">{user?.full_name || user?.username}</span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: 16,
            padding: 24,
            background: '#fff',
            borderRadius: 6,
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          {children}
        </Content>
      </AntdLayout>
    </AntdLayout>
  );
};

export default Layout;
