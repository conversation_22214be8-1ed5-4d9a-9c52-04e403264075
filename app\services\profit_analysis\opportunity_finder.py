"""
机会发现器

提供利润机会识别、投资建议、ROI计算等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product
from .cost_manager import CostManager
from .profit_calculator import ProfitCalculator
from .supplier_comparator import SupplierComparator

logger = get_logger(__name__)


class OpportunityType(Enum):
    """机会类型"""
    HIGH_PROFIT_MARGIN = "high_profit_margin"       # 高利润率
    COST_OPTIMIZATION = "cost_optimization"         # 成本优化
    SUPPLIER_SWITCH = "supplier_switch"             # 供应商切换
    PRICE_ADJUSTMENT = "price_adjustment"           # 价格调整
    VOLUME_DISCOUNT = "volume_discount"             # 批量折扣
    MARKET_EXPANSION = "market_expansion"           # 市场扩张


class RiskLevel(Enum):
    """风险级别"""
    VERY_LOW = "very_low"       # 极低风险
    LOW = "low"                 # 低风险
    MEDIUM = "medium"           # 中等风险
    HIGH = "high"               # 高风险
    VERY_HIGH = "very_high"     # 极高风险


class InvestmentPriority(Enum):
    """投资优先级"""
    CRITICAL = "critical"       # 关键
    HIGH = "high"              # 高
    MEDIUM = "medium"          # 中等
    LOW = "low"                # 低
    DEFERRED = "deferred"      # 延期


@dataclass
class ProfitOpportunity:
    """利润机会"""
    opportunity_id: str
    product_id: str
    supplier_id: str
    opportunity_type: OpportunityType
    description: str
    current_profit_margin: float
    potential_profit_margin: float
    profit_improvement: float
    revenue_impact: float
    cost_impact: float
    risk_level: RiskLevel
    confidence_score: float  # 0-1
    implementation_effort: str  # low, medium, high
    time_to_realize: int  # days
    required_investment: float
    expected_roi: float
    success_probability: float  # 0-1
    action_items: List[str]
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class InvestmentRecommendation:
    """投资建议"""
    product_id: str
    supplier_id: str
    investment_amount: float
    expected_return: float
    roi_percentage: float
    payback_period: int  # months
    priority: InvestmentPriority
    risk_assessment: str
    investment_rationale: str
    implementation_timeline: List[str]
    success_metrics: List[str]
    contingency_plan: List[str]


@dataclass
class OpportunityPortfolio:
    """机会组合"""
    portfolio_id: str
    opportunities: List[ProfitOpportunity]
    total_potential_profit: float
    total_required_investment: float
    portfolio_roi: float
    risk_diversification: float  # 0-1
    implementation_complexity: str
    recommended_sequence: List[str]  # opportunity_ids in order
    portfolio_summary: str


class OpportunityFinder:
    """机会发现器"""
    
    def __init__(self, cost_manager: CostManager, profit_calculator: ProfitCalculator,
                 supplier_comparator: SupplierComparator):
        self.cost_manager = cost_manager
        self.profit_calculator = profit_calculator
        self.supplier_comparator = supplier_comparator
        
        # 机会发现历史
        self.discovered_opportunities: List[ProfitOpportunity] = []
        
        # 机会识别阈值
        self.opportunity_thresholds = {
            OpportunityType.HIGH_PROFIT_MARGIN: 0.3,  # 30%以上利润率
            OpportunityType.COST_OPTIMIZATION: 0.1,   # 10%以上成本节省
            OpportunityType.SUPPLIER_SWITCH: 0.15,    # 15%以上利润提升
            OpportunityType.PRICE_ADJUSTMENT: 0.05,   # 5%以上价格调整空间
        }
    
    async def discover_opportunities(self, products: List[Product],
                                   supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """
        发现利润机会
        
        Args:
            products: 商品列表
            supplier_ids: 供应商ID列表
        
        Returns:
            List[ProfitOpportunity]: 发现的机会列表
        """
        try:
            logger.info(f"开始发现利润机会: {len(products)} 个商品, {len(supplier_ids)} 个供应商")
            
            opportunities = []
            
            for product in products:
                # 为每个商品发现机会
                product_opportunities = await self._discover_product_opportunities(product, supplier_ids)
                opportunities.extend(product_opportunities)
            
            # 按潜在收益排序
            opportunities.sort(key=lambda x: x.profit_improvement, reverse=True)
            
            # 保存到历史记录
            self.discovered_opportunities.extend(opportunities)
            
            logger.info(f"发现利润机会: {len(opportunities)} 个")
            return opportunities
            
        except Exception as e:
            logger.error(f"发现利润机会失败: {e}")
            return []
    
    async def generate_investment_recommendation(self, opportunity: ProfitOpportunity) -> InvestmentRecommendation:
        """
        生成投资建议
        
        Args:
            opportunity: 利润机会
        
        Returns:
            InvestmentRecommendation: 投资建议
        """
        try:
            # 计算投资回报
            expected_return = opportunity.revenue_impact - opportunity.cost_impact
            roi_percentage = (expected_return / opportunity.required_investment * 100) if opportunity.required_investment > 0 else 0
            
            # 计算回收期
            monthly_return = expected_return / 12  # 假设年化收益
            payback_period = int(opportunity.required_investment / monthly_return) if monthly_return > 0 else 999
            
            # 确定优先级
            priority = self._determine_investment_priority(opportunity, roi_percentage, payback_period)
            
            # 风险评估
            risk_assessment = self._assess_investment_risk(opportunity)
            
            # 投资理由
            investment_rationale = self._generate_investment_rationale(opportunity, roi_percentage)
            
            # 实施时间线
            implementation_timeline = self._generate_implementation_timeline(opportunity)
            
            # 成功指标
            success_metrics = self._generate_success_metrics(opportunity)
            
            # 应急计划
            contingency_plan = self._generate_contingency_plan(opportunity)
            
            return InvestmentRecommendation(
                product_id=opportunity.product_id,
                supplier_id=opportunity.supplier_id,
                investment_amount=opportunity.required_investment,
                expected_return=expected_return,
                roi_percentage=roi_percentage,
                payback_period=payback_period,
                priority=priority,
                risk_assessment=risk_assessment,
                investment_rationale=investment_rationale,
                implementation_timeline=implementation_timeline,
                success_metrics=success_metrics,
                contingency_plan=contingency_plan
            )
            
        except Exception as e:
            logger.error(f"生成投资建议失败: {e}")
            raise
    
    async def create_opportunity_portfolio(self, opportunities: List[ProfitOpportunity],
                                         budget_limit: Optional[float] = None) -> OpportunityPortfolio:
        """
        创建机会组合
        
        Args:
            opportunities: 机会列表
            budget_limit: 预算限制
        
        Returns:
            OpportunityPortfolio: 机会组合
        """
        try:
            # 按ROI和风险筛选机会
            filtered_opportunities = self._filter_opportunities_for_portfolio(opportunities, budget_limit)
            
            # 计算组合指标
            total_potential_profit = sum(op.profit_improvement for op in filtered_opportunities)
            total_required_investment = sum(op.required_investment for op in filtered_opportunities)
            portfolio_roi = (total_potential_profit / total_required_investment) if total_required_investment > 0 else 0
            
            # 计算风险分散度
            risk_diversification = self._calculate_risk_diversification(filtered_opportunities)
            
            # 评估实施复杂度
            implementation_complexity = self._assess_implementation_complexity(filtered_opportunities)
            
            # 推荐实施顺序
            recommended_sequence = self._recommend_implementation_sequence(filtered_opportunities)
            
            # 生成组合摘要
            portfolio_summary = self._generate_portfolio_summary(
                filtered_opportunities, total_potential_profit, total_required_investment, portfolio_roi
            )
            
            return OpportunityPortfolio(
                portfolio_id=f"portfolio_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                opportunities=filtered_opportunities,
                total_potential_profit=total_potential_profit,
                total_required_investment=total_required_investment,
                portfolio_roi=portfolio_roi,
                risk_diversification=risk_diversification,
                implementation_complexity=implementation_complexity,
                recommended_sequence=recommended_sequence,
                portfolio_summary=portfolio_summary
            )
            
        except Exception as e:
            logger.error(f"创建机会组合失败: {e}")
            raise
    
    async def _discover_product_opportunities(self, product: Product, 
                                            supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """发现单个商品的机会"""
        opportunities = []
        
        try:
            # 1. 高利润率机会
            high_margin_ops = await self._find_high_margin_opportunities(product, supplier_ids)
            opportunities.extend(high_margin_ops)
            
            # 2. 成本优化机会
            cost_optimization_ops = await self._find_cost_optimization_opportunities(product, supplier_ids)
            opportunities.extend(cost_optimization_ops)
            
            # 3. 供应商切换机会
            supplier_switch_ops = await self._find_supplier_switch_opportunities(product, supplier_ids)
            opportunities.extend(supplier_switch_ops)
            
            # 4. 价格调整机会
            price_adjustment_ops = await self._find_price_adjustment_opportunities(product, supplier_ids)
            opportunities.extend(price_adjustment_ops)
            
        except Exception as e:
            logger.error(f"发现商品机会失败: {product.id}, {e}")
        
        return opportunities
    
    async def _find_high_margin_opportunities(self, product: Product, 
                                            supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """发现高利润率机会"""
        opportunities = []
        
        for supplier_id in supplier_ids:
            try:
                profit_calc = await self.profit_calculator.calculate_profit(product, supplier_id)
                if not profit_calc:
                    continue
                
                # 检查是否达到高利润率阈值
                threshold = self.opportunity_thresholds[OpportunityType.HIGH_PROFIT_MARGIN]
                if profit_calc.profit_margin >= threshold:
                    opportunity = ProfitOpportunity(
                        opportunity_id=f"high_margin_{product.id}_{supplier_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        product_id=product.id,
                        supplier_id=supplier_id,
                        opportunity_type=OpportunityType.HIGH_PROFIT_MARGIN,
                        description=f"商品具有高利润率潜力 ({profit_calc.profit_margin:.1%})",
                        current_profit_margin=profit_calc.profit_margin,
                        potential_profit_margin=profit_calc.profit_margin * 1.1,  # 假设可提升10%
                        profit_improvement=profit_calc.gross_profit * 0.1,
                        revenue_impact=profit_calc.selling_price * 0.05,  # 假设5%收入增长
                        cost_impact=0,
                        risk_level=RiskLevel.LOW,
                        confidence_score=0.8,
                        implementation_effort="low",
                        time_to_realize=30,
                        required_investment=1000,  # 假设投资
                        expected_roi=0.5,
                        success_probability=0.8,
                        action_items=[
                            "加大营销推广力度",
                            "优化产品定位",
                            "扩大销售渠道"
                        ]
                    )
                    opportunities.append(opportunity)
                    
            except Exception as e:
                logger.error(f"发现高利润率机会失败: {product.id}, {supplier_id}, {e}")
        
        return opportunities
    
    async def _find_cost_optimization_opportunities(self, product: Product,
                                                  supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """发现成本优化机会"""
        opportunities = []
        
        for supplier_id in supplier_ids:
            try:
                # 获取成本历史
                cost_history = await self.cost_manager.get_cost_history(supplier_id, product.id)
                if not cost_history or not cost_history.cost_records:
                    continue
                
                # 分析成本优化潜力
                recent_costs = [r.cost_value for r in cost_history.cost_records[-5:]]  # 最近5个记录
                if len(recent_costs) < 2:
                    continue
                
                avg_cost = statistics.mean(recent_costs)
                min_cost = min(recent_costs)
                
                # 如果最低成本比平均成本低10%以上，认为有优化机会
                if (avg_cost - min_cost) / avg_cost >= self.opportunity_thresholds[OpportunityType.COST_OPTIMIZATION]:
                    cost_saving = avg_cost - min_cost
                    
                    opportunity = ProfitOpportunity(
                        opportunity_id=f"cost_opt_{product.id}_{supplier_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        product_id=product.id,
                        supplier_id=supplier_id,
                        opportunity_type=OpportunityType.COST_OPTIMIZATION,
                        description=f"成本优化机会，可节省 {cost_saving:.2f} 元",
                        current_profit_margin=0,  # 需要计算
                        potential_profit_margin=0,  # 需要计算
                        profit_improvement=cost_saving,
                        revenue_impact=0,
                        cost_impact=-cost_saving,  # 负值表示成本降低
                        risk_level=RiskLevel.MEDIUM,
                        confidence_score=0.7,
                        implementation_effort="medium",
                        time_to_realize=60,
                        required_investment=500,
                        expected_roi=cost_saving / 500 if cost_saving > 0 else 0,
                        success_probability=0.7,
                        action_items=[
                            "与供应商重新谈判价格",
                            "优化采购流程",
                            "寻找成本更低的替代方案"
                        ]
                    )
                    opportunities.append(opportunity)
                    
            except Exception as e:
                logger.error(f"发现成本优化机会失败: {product.id}, {supplier_id}, {e}")
        
        return opportunities

    async def _find_supplier_switch_opportunities(self, product: Product,
                                                supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """发现供应商切换机会"""
        opportunities = []

        if len(supplier_ids) < 2:
            return opportunities

        try:
            # 对比供应商
            comparison = await self.supplier_comparator.compare_suppliers(product, supplier_ids)

            if not comparison.suppliers or len(comparison.suppliers) < 2:
                return opportunities

            best_supplier = comparison.suppliers[0]
            current_supplier = comparison.suppliers[1]  # 假设第二个是当前供应商

            # 计算切换收益
            score_improvement = best_supplier.overall_score - current_supplier.overall_score

            if score_improvement >= 15:  # 评分提升15分以上
                # 估算利润提升
                profit_improvement = score_improvement * 10  # 简化计算

                opportunity = ProfitOpportunity(
                    opportunity_id=f"supplier_switch_{product.id}_{best_supplier.supplier_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    product_id=product.id,
                    supplier_id=best_supplier.supplier_id,
                    opportunity_type=OpportunityType.SUPPLIER_SWITCH,
                    description=f"切换到供应商 {best_supplier.supplier_name} 可提升 {score_improvement:.1f} 分",
                    current_profit_margin=0,  # 需要计算
                    potential_profit_margin=0,  # 需要计算
                    profit_improvement=profit_improvement,
                    revenue_impact=profit_improvement * 0.8,
                    cost_impact=profit_improvement * 0.2,
                    risk_level=RiskLevel.MEDIUM,
                    confidence_score=0.6,
                    implementation_effort="high",
                    time_to_realize=90,
                    required_investment=2000,
                    expected_roi=profit_improvement / 2000 if profit_improvement > 0 else 0,
                    success_probability=0.6,
                    action_items=[
                        f"与供应商 {best_supplier.supplier_name} 建立合作关系",
                        "制定供应商切换计划",
                        "评估切换风险和成本",
                        "建立新供应商质量保证体系"
                    ]
                )
                opportunities.append(opportunity)

        except Exception as e:
            logger.error(f"发现供应商切换机会失败: {product.id}, {e}")

        return opportunities

    async def _find_price_adjustment_opportunities(self, product: Product,
                                                 supplier_ids: List[str]) -> List[ProfitOpportunity]:
        """发现价格调整机会"""
        opportunities = []

        for supplier_id in supplier_ids:
            try:
                # 生成定价建议
                pricing_rec = await self.profit_calculator.generate_pricing_recommendation(product, supplier_id)
                if not pricing_rec:
                    continue

                # 检查价格调整空间
                price_change_threshold = self.opportunity_thresholds[OpportunityType.PRICE_ADJUSTMENT]
                if abs(pricing_rec.price_change_percent) >= price_change_threshold:

                    # 计算利润影响
                    profit_impact = pricing_rec.price_change * 0.8  # 假设80%转化为利润

                    opportunity = ProfitOpportunity(
                        opportunity_id=f"price_adj_{product.id}_{supplier_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        product_id=product.id,
                        supplier_id=supplier_id,
                        opportunity_type=OpportunityType.PRICE_ADJUSTMENT,
                        description=f"价格调整机会：{pricing_rec.price_change_percent:+.1%} ({pricing_rec.price_change:+.2f}元)",
                        current_profit_margin=0,  # 需要从定价建议中获取
                        potential_profit_margin=pricing_rec.expected_margin,
                        profit_improvement=abs(profit_impact),
                        revenue_impact=pricing_rec.price_change if pricing_rec.price_change > 0 else 0,
                        cost_impact=0,
                        risk_level=self._assess_price_adjustment_risk(pricing_rec.price_change_percent),
                        confidence_score=0.7,
                        implementation_effort="low",
                        time_to_realize=7,
                        required_investment=100,  # 价格调整成本很低
                        expected_roi=abs(profit_impact) / 100 if profit_impact != 0 else 0,
                        success_probability=0.8,
                        action_items=[
                            f"调整价格至 {pricing_rec.recommended_price:.2f} 元",
                            "监控价格调整后的销量变化",
                            "评估市场反应",
                            "必要时进行微调"
                        ]
                    )
                    opportunities.append(opportunity)

            except Exception as e:
                logger.error(f"发现价格调整机会失败: {product.id}, {supplier_id}, {e}")

        return opportunities

    def _assess_price_adjustment_risk(self, price_change_percent: float) -> RiskLevel:
        """评估价格调整风险"""
        abs_change = abs(price_change_percent)

        if abs_change >= 0.3:  # 30%以上变化
            return RiskLevel.VERY_HIGH
        elif abs_change >= 0.2:  # 20%以上变化
            return RiskLevel.HIGH
        elif abs_change >= 0.1:  # 10%以上变化
            return RiskLevel.MEDIUM
        elif abs_change >= 0.05:  # 5%以上变化
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW

    def _determine_investment_priority(self, opportunity: ProfitOpportunity,
                                     roi_percentage: float, payback_period: int) -> InvestmentPriority:
        """确定投资优先级"""
        # 基于ROI和回收期确定优先级
        if roi_percentage >= 100 and payback_period <= 6:  # 高ROI，短回收期
            return InvestmentPriority.CRITICAL
        elif roi_percentage >= 50 and payback_period <= 12:
            return InvestmentPriority.HIGH
        elif roi_percentage >= 25 and payback_period <= 24:
            return InvestmentPriority.MEDIUM
        elif roi_percentage >= 10 and payback_period <= 36:
            return InvestmentPriority.LOW
        else:
            return InvestmentPriority.DEFERRED

    def _assess_investment_risk(self, opportunity: ProfitOpportunity) -> str:
        """评估投资风险"""
        risk_factors = []

        # 基于风险级别
        if opportunity.risk_level == RiskLevel.VERY_HIGH:
            risk_factors.append("风险极高")
        elif opportunity.risk_level == RiskLevel.HIGH:
            risk_factors.append("风险较高")

        # 基于成功概率
        if opportunity.success_probability < 0.5:
            risk_factors.append("成功概率较低")

        # 基于实施难度
        if opportunity.implementation_effort == "high":
            risk_factors.append("实施难度大")

        # 基于投资金额
        if opportunity.required_investment > 10000:
            risk_factors.append("投资金额较大")

        if not risk_factors:
            return "风险可控，建议投资"
        else:
            return "；".join(risk_factors) + "，需要谨慎评估"

    def _generate_investment_rationale(self, opportunity: ProfitOpportunity, roi_percentage: float) -> str:
        """生成投资理由"""
        rationale_parts = []

        # ROI理由
        if roi_percentage >= 50:
            rationale_parts.append(f"投资回报率高达 {roi_percentage:.1f}%")
        elif roi_percentage >= 25:
            rationale_parts.append(f"投资回报率良好 ({roi_percentage:.1f}%)")

        # 机会类型理由
        type_descriptions = {
            OpportunityType.HIGH_PROFIT_MARGIN: "高利润率商品具有良好的盈利潜力",
            OpportunityType.COST_OPTIMIZATION: "成本优化可直接提升利润空间",
            OpportunityType.SUPPLIER_SWITCH: "供应商切换可改善整体供应链效率",
            OpportunityType.PRICE_ADJUSTMENT: "价格调整可快速提升盈利能力"
        }

        if opportunity.opportunity_type in type_descriptions:
            rationale_parts.append(type_descriptions[opportunity.opportunity_type])

        # 利润改善理由
        if opportunity.profit_improvement > 1000:
            rationale_parts.append(f"预期利润提升 {opportunity.profit_improvement:.0f} 元")

        return "；".join(rationale_parts) + "。"

    def _generate_implementation_timeline(self, opportunity: ProfitOpportunity) -> List[str]:
        """生成实施时间线"""
        timeline = []

        if opportunity.opportunity_type == OpportunityType.PRICE_ADJUSTMENT:
            timeline = [
                "第1周：制定价格调整方案",
                "第2周：实施价格调整",
                "第3-4周：监控市场反应"
            ]
        elif opportunity.opportunity_type == OpportunityType.COST_OPTIMIZATION:
            timeline = [
                "第1-2周：分析成本结构",
                "第3-4周：与供应商谈判",
                "第5-8周：实施成本优化措施"
            ]
        elif opportunity.opportunity_type == OpportunityType.SUPPLIER_SWITCH:
            timeline = [
                "第1-2周：评估新供应商",
                "第3-4周：商务谈判",
                "第5-8周：建立合作关系",
                "第9-12周：逐步切换供应商"
            ]
        else:
            timeline = [
                "第1周：制定实施计划",
                "第2-4周：执行关键行动",
                "第5-8周：监控效果并优化"
            ]

        return timeline

    def _generate_success_metrics(self, opportunity: ProfitOpportunity) -> List[str]:
        """生成成功指标"""
        metrics = []

        if opportunity.profit_improvement > 0:
            metrics.append(f"利润提升达到 {opportunity.profit_improvement:.0f} 元")

        if opportunity.revenue_impact > 0:
            metrics.append(f"收入增长 {opportunity.revenue_impact:.0f} 元")

        if opportunity.cost_impact < 0:
            metrics.append(f"成本节省 {abs(opportunity.cost_impact):.0f} 元")

        # 通用指标
        metrics.extend([
            "ROI达到预期目标",
            "实施按时完成",
            "无重大风险事件"
        ])

        return metrics

    def _generate_contingency_plan(self, opportunity: ProfitOpportunity) -> List[str]:
        """生成应急计划"""
        plan = []

        if opportunity.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            plan.append("建立风险监控机制")
            plan.append("制定风险应对预案")

        if opportunity.success_probability < 0.7:
            plan.append("准备备选方案")
            plan.append("设置阶段性评估点")

        # 通用应急措施
        plan.extend([
            "定期评估实施进展",
            "必要时调整实施策略",
            "保持与相关方的沟通"
        ])

        return plan

    def _filter_opportunities_for_portfolio(self, opportunities: List[ProfitOpportunity],
                                          budget_limit: Optional[float]) -> List[ProfitOpportunity]:
        """为组合筛选机会"""
        # 按ROI排序
        sorted_opportunities = sorted(opportunities, key=lambda x: x.expected_roi, reverse=True)

        if budget_limit is None:
            return sorted_opportunities

        # 在预算限制内选择机会
        selected = []
        total_investment = 0

        for opportunity in sorted_opportunities:
            if total_investment + opportunity.required_investment <= budget_limit:
                selected.append(opportunity)
                total_investment += opportunity.required_investment

        return selected

    def _calculate_risk_diversification(self, opportunities: List[ProfitOpportunity]) -> float:
        """计算风险分散度"""
        if not opportunities:
            return 0.0

        # 统计不同类型和风险级别的机会
        types = set(op.opportunity_type for op in opportunities)
        risk_levels = set(op.risk_level for op in opportunities)

        # 分散度 = (类型多样性 + 风险多样性) / 2
        type_diversity = len(types) / len(OpportunityType)
        risk_diversity = len(risk_levels) / len(RiskLevel)

        return (type_diversity + risk_diversity) / 2

    def _assess_implementation_complexity(self, opportunities: List[ProfitOpportunity]) -> str:
        """评估实施复杂度"""
        if not opportunities:
            return "low"

        # 统计实施难度
        effort_counts = {"low": 0, "medium": 0, "high": 0}
        for op in opportunities:
            effort_counts[op.implementation_effort] += 1

        # 基于最高难度确定复杂度
        if effort_counts["high"] > 0:
            return "high"
        elif effort_counts["medium"] > effort_counts["low"]:
            return "medium"
        else:
            return "low"

    def _recommend_implementation_sequence(self, opportunities: List[ProfitOpportunity]) -> List[str]:
        """推荐实施顺序"""
        # 按优先级排序：ROI高、风险低、实施简单的优先
        def priority_score(op):
            risk_penalty = {
                RiskLevel.VERY_LOW: 0, RiskLevel.LOW: 1, RiskLevel.MEDIUM: 2,
                RiskLevel.HIGH: 3, RiskLevel.VERY_HIGH: 4
            }
            effort_penalty = {"low": 0, "medium": 1, "high": 2}

            return op.expected_roi - risk_penalty[op.risk_level] - effort_penalty[op.implementation_effort]

        sorted_opportunities = sorted(opportunities, key=priority_score, reverse=True)
        return [op.opportunity_id for op in sorted_opportunities]

    def _generate_portfolio_summary(self, opportunities: List[ProfitOpportunity],
                                  total_profit: float, total_investment: float, roi: float) -> str:
        """生成组合摘要"""
        summary_parts = [
            f"机会组合包含 {len(opportunities)} 个投资机会",
            f"总投资需求 {total_investment:.0f} 元",
            f"预期利润提升 {total_profit:.0f} 元",
            f"组合ROI {roi:.1%}"
        ]

        # 机会类型分布
        type_counts = {}
        for op in opportunities:
            type_counts[op.opportunity_type.value] = type_counts.get(op.opportunity_type.value, 0) + 1

        if type_counts:
            main_type = max(type_counts, key=type_counts.get)
            summary_parts.append(f"主要机会类型为 {main_type}")

        return "；".join(summary_parts) + "。"

    def get_opportunity_statistics(self) -> Dict[str, Any]:
        """获取机会统计信息"""
        if not self.discovered_opportunities:
            return {
                "total_opportunities": 0,
                "opportunity_types": [ot.value for ot in OpportunityType],
                "risk_levels": [rl.value for rl in RiskLevel],
                "investment_priorities": [ip.value for ip in InvestmentPriority]
            }

        # 统计机会分布
        type_distribution = {}
        risk_distribution = {}

        for op in self.discovered_opportunities:
            type_distribution[op.opportunity_type.value] = type_distribution.get(op.opportunity_type.value, 0) + 1
            risk_distribution[op.risk_level.value] = risk_distribution.get(op.risk_level.value, 0) + 1

        # 计算平均指标
        avg_roi = statistics.mean([op.expected_roi for op in self.discovered_opportunities])
        avg_investment = statistics.mean([op.required_investment for op in self.discovered_opportunities])
        avg_profit_improvement = statistics.mean([op.profit_improvement for op in self.discovered_opportunities])

        return {
            "total_opportunities": len(self.discovered_opportunities),
            "type_distribution": type_distribution,
            "risk_distribution": risk_distribution,
            "average_roi": avg_roi,
            "average_investment": avg_investment,
            "average_profit_improvement": avg_profit_improvement,
            "opportunity_types": [ot.value for ot in OpportunityType],
            "risk_levels": [rl.value for rl in RiskLevel],
            "investment_priorities": [ip.value for ip in InvestmentPriority]
        }
