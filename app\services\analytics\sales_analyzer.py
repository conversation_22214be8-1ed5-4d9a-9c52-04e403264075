"""
销量分析引擎

负责商品销量趋势分析、销售表现评估和市场份额分析
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductMetrics

logger = get_logger(__name__)


class SalesTrend(Enum):
    """销量趋势"""
    GROWING = "growing"         # 增长
    DECLINING = "declining"     # 下降
    STABLE = "stable"          # 稳定
    SEASONAL = "seasonal"      # 季节性
    UNKNOWN = "unknown"        # 未知


class SalesPerformance(Enum):
    """销售表现"""
    EXCELLENT = "excellent"    # 优秀
    GOOD = "good"             # 良好
    AVERAGE = "average"       # 一般
    POOR = "poor"             # 较差
    UNKNOWN = "unknown"       # 未知


@dataclass
class SalesPoint:
    """销量数据点"""
    timestamp: datetime
    sales_count: int
    revenue: Optional[float] = None
    source: str = "crawl"
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SalesAnalysisResult:
    """销量分析结果"""
    product_id: str
    product_type: ProductType
    analysis_period: Tuple[datetime, datetime]
    current_sales: int
    sales_trend: SalesTrend
    trend_strength: float
    sales_growth_rate: float
    average_daily_sales: float
    total_sales: int
    peak_sales: int
    sales_performance: SalesPerformance
    market_share_estimate: float
    insights: List[str]
    analysis_timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class CompetitorSalesAnalysis:
    """竞品销量分析"""
    product_id: str
    competitor_products: List[str]
    sales_comparison: Dict[str, int]
    market_ranking: int
    market_share: float
    competitive_advantage: str
    recommendations: List[str]


@dataclass
class CategorySalesAnalysis:
    """品类销量分析"""
    category_id: str
    category_name: str
    total_products: int
    total_sales: int
    average_sales_per_product: float
    top_performers: List[Dict[str, Any]]
    sales_distribution: Dict[str, int]
    growth_rate: float


class SalesAnalyzer:
    """销量分析引擎"""
    
    def __init__(self):
        self.sales_history: Dict[str, List[SalesPoint]] = {}
        self.analysis_cache: Dict[str, SalesAnalysisResult] = {}
        self.performance_benchmarks = {
            ProductType.COMPETITOR: {
                "excellent": 10000,  # 月销量
                "good": 5000,
                "average": 1000,
                "poor": 100
            },
            ProductType.SUPPLIER: {
                "excellent": 50000,  # 批发量更大
                "good": 20000,
                "average": 5000,
                "poor": 1000
            },
            ProductType.OTHER: {
                "excellent": 5000,
                "good": 2000,
                "average": 500,
                "poor": 50
            }
        }
    
    async def add_sales_data(self, product_id: str, sales_count: int,
                           timestamp: Optional[datetime] = None,
                           revenue: Optional[float] = None,
                           source: str = "crawl",
                           metadata: Optional[Dict[str, Any]] = None):
        """
        添加销量数据点
        
        Args:
            product_id: 商品ID
            sales_count: 销量
            timestamp: 时间戳
            revenue: 收入
            source: 数据源
            metadata: 元数据
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        sales_point = SalesPoint(
            timestamp=timestamp,
            sales_count=sales_count,
            revenue=revenue,
            source=source,
            metadata=metadata or {}
        )
        
        if product_id not in self.sales_history:
            self.sales_history[product_id] = []
        
        self.sales_history[product_id].append(sales_point)
        
        # 按时间排序
        self.sales_history[product_id].sort(key=lambda x: x.timestamp)
        
        # 清除缓存
        if product_id in self.analysis_cache:
            del self.analysis_cache[product_id]
        
        logger.debug(f"添加销量数据: {product_id} - {sales_count} 件")
    
    async def analyze_sales_trend(self, product: Product, 
                                days: int = 30) -> SalesAnalysisResult:
        """
        分析商品销量趋势
        
        Args:
            product: 商品对象
            days: 分析天数
        
        Returns:
            SalesAnalysisResult: 销量分析结果
        """
        try:
            logger.info(f"开始销量趋势分析: {product.id}")
            
            # 检查缓存
            cache_key = f"{product.id}_{days}"
            if cache_key in self.analysis_cache:
                cached_result = self.analysis_cache[cache_key]
                # 如果缓存不超过1小时，直接返回
                if (datetime.now() - cached_result.analysis_timestamp).seconds < 3600:
                    return cached_result
            
            # 获取销量历史数据
            sales_data = await self._get_sales_data(product.id, days)
            
            if not sales_data:
                # 如果没有历史数据，从商品对象获取当前销量
                current_sales = product.metrics.sales_count if product.metrics else 0
                return self._create_empty_sales_analysis(product, current_sales)
            
            # 执行销量分析
            analysis_result = await self._perform_sales_analysis(product, sales_data, days)
            
            # 缓存结果
            self.analysis_cache[cache_key] = analysis_result
            
            logger.info(f"销量趋势分析完成: {product.id} - 趋势: {analysis_result.sales_trend.value}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"销量趋势分析失败: {e}")
            current_sales = product.metrics.sales_count if product.metrics else 0
            return self._create_empty_sales_analysis(product, current_sales)
    
    async def _get_sales_data(self, product_id: str, days: int) -> List[SalesPoint]:
        """获取指定天数的销量数据"""
        if product_id not in self.sales_history:
            return []
        
        cutoff_date = datetime.now() - timedelta(days=days)
        sales_data = [
            point for point in self.sales_history[product_id]
            if point.timestamp >= cutoff_date
        ]
        
        return sales_data
    
    async def _perform_sales_analysis(self, product: Product,
                                    sales_data: List[SalesPoint],
                                    days: int) -> SalesAnalysisResult:
        """执行销量分析"""
        sales_counts = [point.sales_count for point in sales_data]
        timestamps = [point.timestamp for point in sales_data]
        
        # 基础统计
        current_sales = sales_counts[-1]
        total_sales = sum(sales_counts)
        peak_sales = max(sales_counts)
        average_daily_sales = total_sales / days if days > 0 else 0
        
        # 销量增长率
        if len(sales_counts) > 1:
            first_sales = sales_counts[0]
            sales_growth_rate = ((current_sales - first_sales) / first_sales * 100 
                               if first_sales > 0 else 0)
        else:
            sales_growth_rate = 0
        
        # 趋势分析
        sales_trend, trend_strength = await self._calculate_sales_trend(sales_counts, timestamps)
        
        # 销售表现评估
        sales_performance = self._evaluate_sales_performance(product, current_sales)
        
        # 市场份额估算（简化）
        market_share_estimate = self._estimate_market_share(product, current_sales)
        
        # 生成洞察
        insights = self._generate_sales_insights(
            product, sales_trend, sales_growth_rate, sales_performance
        )
        
        return SalesAnalysisResult(
            product_id=product.id,
            product_type=product.product_type,
            analysis_period=(timestamps[0], timestamps[-1]),
            current_sales=current_sales,
            sales_trend=sales_trend,
            trend_strength=trend_strength,
            sales_growth_rate=sales_growth_rate,
            average_daily_sales=average_daily_sales,
            total_sales=total_sales,
            peak_sales=peak_sales,
            sales_performance=sales_performance,
            market_share_estimate=market_share_estimate,
            insights=insights
        )
    
    async def _calculate_sales_trend(self, sales_counts: List[int],
                                   timestamps: List[datetime]) -> Tuple[SalesTrend, float]:
        """计算销量趋势"""
        if len(sales_counts) < 3:
            return SalesTrend.UNKNOWN, 0.0
        
        # 使用移动平均来平滑数据
        window_size = min(7, len(sales_counts) // 3)
        smoothed_sales = self._moving_average(sales_counts, window_size)
        
        # 计算趋势斜率
        n = len(smoothed_sales)
        x_values = list(range(n))
        
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(smoothed_sales)
        
        numerator = sum((x_values[i] - x_mean) * (smoothed_sales[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return SalesTrend.STABLE, 0.0
        
        slope = numerator / denominator
        
        # 计算相关系数（趋势强度）
        y_variance = sum((smoothed_sales[i] - y_mean) ** 2 for i in range(n))
        if y_variance == 0:
            correlation = 0.0
        else:
            correlation = abs(numerator) / (denominator * y_variance) ** 0.5
        
        # 判断趋势
        slope_threshold = y_mean * 0.01  # 1%的变化阈值
        
        if slope > slope_threshold:
            trend = SalesTrend.GROWING
        elif slope < -slope_threshold:
            trend = SalesTrend.DECLINING
        else:
            trend = SalesTrend.STABLE
        
        # 检查季节性模式
        if self._detect_seasonality(sales_counts):
            trend = SalesTrend.SEASONAL
        
        return trend, correlation
    
    def _moving_average(self, data: List[int], window_size: int) -> List[float]:
        """计算移动平均"""
        if window_size >= len(data):
            return [statistics.mean(data)] * len(data)
        
        result = []
        for i in range(len(data)):
            start = max(0, i - window_size // 2)
            end = min(len(data), i + window_size // 2 + 1)
            result.append(statistics.mean(data[start:end]))
        
        return result
    
    def _detect_seasonality(self, sales_counts: List[int]) -> bool:
        """检测季节性模式"""
        if len(sales_counts) < 14:  # 至少需要两周数据
            return False
        
        # 简化的季节性检测：检查周期性模式
        # 这里使用简单的方法，实际应用中可以使用更复杂的时间序列分析
        
        # 检查7天周期（周模式）
        weekly_pattern = self._check_weekly_pattern(sales_counts)
        
        return weekly_pattern
    
    def _check_weekly_pattern(self, sales_counts: List[int]) -> bool:
        """检查周模式"""
        if len(sales_counts) < 14:
            return False
        
        # 计算每周同一天的平均销量
        weekly_averages = [[] for _ in range(7)]
        
        for i, sales in enumerate(sales_counts):
            day_of_week = i % 7
            weekly_averages[day_of_week].append(sales)
        
        # 计算每天的平均销量
        daily_averages = [statistics.mean(day_sales) if day_sales else 0 
                         for day_sales in weekly_averages]
        
        # 检查是否有明显的周模式
        if len(set(daily_averages)) <= 1:  # 所有天销量相同
            return False
        
        max_avg = max(daily_averages)
        min_avg = min(daily_averages)
        
        # 如果最大值和最小值差异超过30%，认为有周模式
        return (max_avg - min_avg) / max_avg > 0.3 if max_avg > 0 else False
    
    def _evaluate_sales_performance(self, product: Product, current_sales: int) -> SalesPerformance:
        """评估销售表现"""
        benchmarks = self.performance_benchmarks.get(product.product_type,
                                                   self.performance_benchmarks[ProductType.OTHER])
        
        if current_sales >= benchmarks["excellent"]:
            return SalesPerformance.EXCELLENT
        elif current_sales >= benchmarks["good"]:
            return SalesPerformance.GOOD
        elif current_sales >= benchmarks["average"]:
            return SalesPerformance.AVERAGE
        elif current_sales >= benchmarks["poor"]:
            return SalesPerformance.POOR
        else:
            return SalesPerformance.POOR
    
    def _estimate_market_share(self, product: Product, current_sales: int) -> float:
        """估算市场份额（简化）"""
        # 这是一个简化的市场份额估算
        # 实际应用中需要更复杂的市场数据和分析
        
        if product.product_type == ProductType.COMPETITOR:
            # 假设竞品市场总量
            estimated_market_size = 1000000  # 100万
            return min(current_sales / estimated_market_size * 100, 100.0)
        elif product.product_type == ProductType.SUPPLIER:
            # 供应商市场份额计算不同
            estimated_market_size = 5000000  # 500万
            return min(current_sales / estimated_market_size * 100, 100.0)
        else:
            return 0.0
    
    def _generate_sales_insights(self, product: Product, sales_trend: SalesTrend,
                               sales_growth_rate: float, 
                               sales_performance: SalesPerformance) -> List[str]:
        """生成销量洞察"""
        insights = []
        
        # 趋势洞察
        if sales_trend == SalesTrend.GROWING:
            insights.append(f"销量呈增长趋势，增长率 {sales_growth_rate:.1f}%")
            if sales_growth_rate > 50:
                insights.append("销量增长迅速，可能是热门商品")
        elif sales_trend == SalesTrend.DECLINING:
            insights.append(f"销量呈下降趋势，下降率 {abs(sales_growth_rate):.1f}%")
            if abs(sales_growth_rate) > 30:
                insights.append("销量下降明显，需要关注市场变化")
        elif sales_trend == SalesTrend.SEASONAL:
            insights.append("销量呈现季节性模式，建议按季节调整策略")
        elif sales_trend == SalesTrend.STABLE:
            insights.append("销量相对稳定，市场表现平稳")
        
        # 表现洞察
        if sales_performance == SalesPerformance.EXCELLENT:
            insights.append("销售表现优秀，是明星商品")
        elif sales_performance == SalesPerformance.GOOD:
            insights.append("销售表现良好，有进一步提升空间")
        elif sales_performance == SalesPerformance.POOR:
            insights.append("销售表现较差，需要优化营销策略")
        
        # 商品类型特定洞察
        if product.product_type == ProductType.COMPETITOR:
            insights.append("作为竞品，需要密切关注其市场策略")
        elif product.product_type == ProductType.SUPPLIER:
            insights.append("作为供应商商品，关注批发量变化")
        
        return insights
    
    def _create_empty_sales_analysis(self, product: Product, current_sales: int) -> SalesAnalysisResult:
        """创建空的销量分析结果"""
        now = datetime.now()
        return SalesAnalysisResult(
            product_id=product.id,
            product_type=product.product_type,
            analysis_period=(now, now),
            current_sales=current_sales,
            sales_trend=SalesTrend.UNKNOWN,
            trend_strength=0.0,
            sales_growth_rate=0.0,
            average_daily_sales=0.0,
            total_sales=current_sales,
            peak_sales=current_sales,
            sales_performance=self._evaluate_sales_performance(product, current_sales),
            market_share_estimate=self._estimate_market_share(product, current_sales),
            insights=["数据不足，无法进行趋势分析"]
        )
    
    async def analyze_competitor_sales(self, product: Product,
                                     competitor_products: List[Product]) -> CompetitorSalesAnalysis:
        """分析竞品销量"""
        try:
            logger.info(f"开始竞品销量分析: {product.id}")
            
            current_sales = product.metrics.sales_count if product.metrics else 0
            
            # 收集竞品销量
            sales_comparison = {}
            competitor_sales = []
            
            for competitor in competitor_products:
                if competitor.metrics and competitor.metrics.sales_count:
                    comp_sales = competitor.metrics.sales_count
                    sales_comparison[competitor.id] = comp_sales
                    competitor_sales.append(comp_sales)
            
            if not competitor_sales:
                return CompetitorSalesAnalysis(
                    product_id=product.id,
                    competitor_products=[],
                    sales_comparison={},
                    market_ranking=0,
                    market_share=0.0,
                    competitive_advantage="unknown",
                    recommendations=["无竞品销量数据"]
                )
            
            # 计算市场排名
            all_sales = competitor_sales + [current_sales]
            all_sales.sort(reverse=True)
            market_ranking = all_sales.index(current_sales) + 1
            
            # 计算市场份额
            total_sales = sum(all_sales)
            market_share = (current_sales / total_sales * 100) if total_sales > 0 else 0.0
            
            # 评估竞争优势
            competitive_advantage = self._assess_competitive_advantage(
                current_sales, competitor_sales, market_ranking
            )
            
            # 生成建议
            recommendations = self._generate_sales_recommendations(
                current_sales, competitor_sales, market_ranking, competitive_advantage
            )
            
            return CompetitorSalesAnalysis(
                product_id=product.id,
                competitor_products=[comp.id for comp in competitor_products],
                sales_comparison=sales_comparison,
                market_ranking=market_ranking,
                market_share=market_share,
                competitive_advantage=competitive_advantage,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"竞品销量分析失败: {e}")
            return CompetitorSalesAnalysis(
                product_id=product.id,
                competitor_products=[],
                sales_comparison={},
                market_ranking=0,
                market_share=0.0,
                competitive_advantage="unknown",
                recommendations=[f"分析失败: {str(e)}"]
            )
    
    def _assess_competitive_advantage(self, current_sales: int,
                                    competitor_sales: List[int],
                                    market_ranking: int) -> str:
        """评估竞争优势"""
        if not competitor_sales:
            return "unknown"
        
        avg_competitor_sales = statistics.mean(competitor_sales)
        
        if market_ranking == 1:
            return "leading"
        elif current_sales > avg_competitor_sales * 1.2:
            return "strong"
        elif current_sales > avg_competitor_sales * 0.8:
            return "competitive"
        else:
            return "weak"
    
    def _generate_sales_recommendations(self, current_sales: int,
                                      competitor_sales: List[int],
                                      market_ranking: int,
                                      competitive_advantage: str) -> List[str]:
        """生成销量建议"""
        recommendations = []
        
        if competitive_advantage == "leading":
            recommendations.append("销量领先市场，保持现有策略")
        elif competitive_advantage == "strong":
            recommendations.append("销量表现强劲，可考虑扩大市场份额")
        elif competitive_advantage == "weak":
            recommendations.append("销量落后竞品，需要加强营销推广")
            recommendations.append("分析竞品成功因素，优化产品策略")
        
        if market_ranking > len(competitor_sales) // 2:
            recommendations.append("市场排名靠后，建议重新评估产品定位")
        
        return recommendations
    
    async def batch_analyze_sales(self, products: List[Product],
                                days: int = 30) -> List[SalesAnalysisResult]:
        """批量分析商品销量"""
        logger.info(f"开始批量销量分析: {len(products)} 个商品")
        
        tasks = [self.analyze_sales_trend(product, days) for product in products]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"商品 {products[i].id} 销量分析失败: {result}")
                error_result = self._create_empty_sales_analysis(products[i], 0)
                valid_results.append(error_result)
            else:
                valid_results.append(result)
        
        logger.info(f"批量销量分析完成: {len(valid_results)} 个结果")
        return valid_results
    
    def get_sales_statistics(self) -> Dict[str, Any]:
        """获取销量分析统计信息"""
        total_products = len(self.sales_history)
        total_sales_points = sum(len(points) for points in self.sales_history.values())
        cached_analyses = len(self.analysis_cache)
        
        # 统计趋势分布
        trend_distribution = {}
        performance_distribution = {}
        
        for analysis in self.analysis_cache.values():
            trend = analysis.sales_trend.value
            performance = analysis.sales_performance.value
            
            trend_distribution[trend] = trend_distribution.get(trend, 0) + 1
            performance_distribution[performance] = performance_distribution.get(performance, 0) + 1
        
        return {
            "total_products": total_products,
            "total_sales_points": total_sales_points,
            "cached_analyses": cached_analyses,
            "trend_distribution": trend_distribution,
            "performance_distribution": performance_distribution,
            "performance_benchmarks": self.performance_benchmarks
        }
