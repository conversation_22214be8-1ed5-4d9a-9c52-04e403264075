"""
报表生成系统演示

展示报表生成系统的核心功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.report_system.report_engine import (
    ReportEngine, ReportConfig, ReportType, ReportPeriod, ReportFormat
)
from app.services.report_system.chart_generator import (
    ChartGenerator, ChartData, ChartType
)
from app.services.report_system.report_scheduler import (
    ReportScheduler, ScheduleRule
)
from app.services.report_system.export_manager import (
    ExportManager, ExportFormat, ShareType
)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"=== {title} ===")
    print()


async def demo_report_engine():
    """演示报表引擎"""
    print_section("报表引擎演示")
    
    # 创建模拟依赖
    comprehensive_analyzer = Mock()
    cost_manager = Mock()
    profit_calculator = Mock()
    alert_engine = Mock()
    
    # 模拟预警汇总
    alert_summary = Mock()
    alert_summary.total_alerts = 15
    alert_summary.active_alerts = 5
    alert_summary.critical_alerts = 2
    alert_summary.high_alerts = 3
    alert_engine.get_alert_summary = AsyncMock(return_value=alert_summary)
    
    # 创建报表引擎
    report_engine = ReportEngine(
        comprehensive_analyzer, cost_manager, profit_calculator, alert_engine
    )
    
    print("1. 报表引擎配置:")
    print(f"   默认报表配置数: {len(report_engine.report_configs)}")
    print(f"   可用报表类型: {len([t for t in ReportType])}")
    print(f"   可用报表周期: {len([p for p in ReportPeriod])}")
    print(f"   可用报表格式: {len([f for f in ReportFormat])}")
    
    # 生成竞品分析报表
    print("\n2. 生成竞品分析报表:")
    result = await report_engine.generate_report(
        "daily_competitor_analysis",
        format=ReportFormat.HTML
    )
    
    print(f"   报表ID: {result.report_id}")
    print(f"   生成状态: {result.status}")
    print(f"   生成时间: {result.generation_time:.3f}秒")
    print(f"   报表标题: {result.data.title if result.data else 'N/A'}")
    print(f"   报表摘要: {len(result.data.summary) if result.data else 0}项")
    print(f"   报表章节: {len(result.data.sections) if result.data else 0}个")
    
    # 生成供应商对比报表
    print("\n3. 生成供应商对比报表:")
    result = await report_engine.generate_report(
        "weekly_supplier_comparison",
        format=ReportFormat.JSON
    )
    
    print(f"   报表ID: {result.report_id}")
    print(f"   生成状态: {result.status}")
    print(f"   报表格式: {result.format.value}")
    print(f"   内容长度: {len(result.content)}字符")
    
    # 生成利润分析报表
    print("\n4. 生成利润分析报表:")
    result = await report_engine.generate_report(
        "monthly_profit_analysis",
        format=ReportFormat.CSV
    )
    
    print(f"   报表ID: {result.report_id}")
    print(f"   生成状态: {result.status}")
    print(f"   报表格式: {result.format.value}")
    print(f"   CSV行数: {result.content.count(chr(10)) if result.content else 0}")
    
    # 添加自定义报表配置
    print("\n5. 添加自定义报表配置:")
    custom_config = ReportConfig(
        report_id="custom_market_overview",
        report_name="自定义市场概览报表",
        report_type=ReportType.MARKET_OVERVIEW,
        report_period=ReportPeriod.WEEKLY,
        auto_generate=True,
        recipients=["<EMAIL>"]
    )
    
    success = report_engine.add_report_config(custom_config)
    print(f"   配置添加结果: {'成功' if success else '失败'}")
    print(f"   当前配置总数: {len(report_engine.report_configs)}")
    
    # 获取报表统计
    print("\n6. 报表引擎统计:")
    stats = report_engine.get_report_statistics()
    print(f"   总报表数: {stats['total_reports']}")
    print(f"   成功报表数: {stats['successful_reports']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    print(f"   平均生成时间: {stats['avg_generation_time']:.3f}秒")
    print(f"   报表配置数: {stats['total_configs']}")
    
    print("\n   报表类型分布:")
    for report_type, count in stats['type_distribution'].items():
        print(f"     {report_type}: {count} 个")
    
    return report_engine


async def demo_chart_generator():
    """演示图表生成器"""
    print_section("图表生成器演示")
    
    chart_generator = ChartGenerator()
    
    print("1. 图表生成器配置:")
    print(f"   默认颜色数: {len(chart_generator.chart_config['default_colors'])}")
    print(f"   字体: {chart_generator.chart_config['font_family']}")
    print(f"   背景色: {chart_generator.chart_config['background_color']}")
    
    # 生成折线图
    print("\n2. 生成折线图:")
    line_chart_data = ChartData(
        chart_id="sales_trend",
        title="销量趋势图",
        chart_type=ChartType.LINE,
        data={
            "labels": ["1月", "2月", "3月", "4月", "5月", "6月"],
            "datasets": [{
                "label": "iPhone销量",
                "data": [1200, 1350, 1100, 1450, 1600, 1750]
            }, {
                "label": "Samsung销量",
                "data": [800, 950, 900, 1100, 1200, 1300]
            }]
        },
        options={}
    )
    
    result = await chart_generator.generate_chart(line_chart_data)
    print(f"   图表ID: {result.chart_id}")
    print(f"   生成状态: {result.status}")
    print(f"   图表类型: {result.chart_data.chart_type.value}")
    print(f"   HTML内容长度: {len(result.html_content) if result.html_content else 0}字符")
    
    # 生成柱状图
    print("\n3. 生成柱状图:")
    bar_chart_data = ChartData(
        chart_id="market_share",
        title="市场份额对比",
        chart_type=ChartType.BAR,
        data={
            "labels": ["iPhone", "Samsung", "华为", "小米", "OPPO"],
            "datasets": [{
                "label": "市场份额(%)",
                "data": [25, 22, 18, 12, 8]
            }]
        },
        options={}
    )
    
    result = await chart_generator.generate_chart(bar_chart_data)
    print(f"   图表ID: {result.chart_id}")
    print(f"   生成状态: {result.status}")
    print(f"   图表尺寸: {result.chart_data.width}x{result.chart_data.height}")
    
    # 生成饼图
    print("\n4. 生成饼图:")
    pie_chart_data = ChartData(
        chart_id="profit_distribution",
        title="利润分布",
        chart_type=ChartType.PIE,
        data={
            "labels": ["高端产品", "中端产品", "低端产品", "配件"],
            "datasets": [{
                "data": [45, 30, 15, 10]
            }]
        },
        options={}
    )
    
    result = await chart_generator.generate_chart(pie_chart_data)
    print(f"   图表ID: {result.chart_id}")
    print(f"   生成状态: {result.status}")
    print(f"   数据点数: {len(result.chart_data.data['datasets'][0]['data'])}")
    
    # 获取图表统计
    print("\n5. 图表生成器统计:")
    stats = chart_generator.get_chart_statistics()
    print(f"   总图表数: {stats['total_charts']}")
    print(f"   成功图表数: {stats['successful_charts']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    
    print("\n   图表类型分布:")
    for chart_type, count in stats['type_distribution'].items():
        print(f"     {chart_type}: {count} 个")
    
    return chart_generator


async def demo_report_scheduler():
    """演示报表调度器"""
    print_section("报表调度器演示")
    
    # 创建模拟报表引擎
    mock_report_engine = Mock()
    
    # 模拟报表配置
    config = Mock()
    config.report_id = "scheduled_report"
    config.auto_generate = True
    config.report_period = ReportPeriod.DAILY
    config.report_name = "定时报表"
    
    mock_report_engine.get_report_configs.return_value = [config]
    
    # 模拟报表生成结果
    result = Mock()
    result.report_id = "scheduled_report_result"
    result.status = "completed"
    
    mock_report_engine.generate_report = AsyncMock(return_value=result)
    
    # 创建报表调度器
    scheduler = ReportScheduler(mock_report_engine)
    
    print("1. 报表调度器配置:")
    print(f"   检查间隔: {scheduler.scheduler_config['check_interval']}秒")
    print(f"   最大并发数: {scheduler.scheduler_config['max_concurrent']}")
    print(f"   任务超时: {scheduler.scheduler_config['task_timeout']}秒")
    print(f"   清理天数: {scheduler.scheduler_config['cleanup_days']}天")
    
    print("\n2. 默认调度规则:")
    rules = scheduler.get_schedule_rules()
    for rule in rules:
        print(f"   规则ID: {rule.rule_id}")
        print(f"   报表ID: {rule.report_id}")
        print(f"   调度周期: {rule.period.value}")
        print(f"   启用状态: {rule.enabled}")
        print(f"   下次运行: {rule.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if rule.next_run_time else 'N/A'}")
    
    # 添加自定义调度规则
    print("\n3. 添加自定义调度规则:")
    custom_rule = ScheduleRule(
        rule_id="weekly_summary",
        report_id="weekly_report",
        period=ReportPeriod.WEEKLY,
        enabled=True
    )
    
    success = scheduler.add_schedule_rule(custom_rule)
    print(f"   规则添加结果: {'成功' if success else '失败'}")
    print(f"   当前规则总数: {len(scheduler.schedule_rules)}")
    
    # 更新调度规则
    print("\n4. 更新调度规则:")
    updates = {
        "enabled": False,
        "period": "monthly"
    }
    
    success = scheduler.update_schedule_rule("weekly_summary", updates)
    print(f"   规则更新结果: {'成功' if success else '失败'}")
    
    updated_rule = scheduler.schedule_rules["weekly_summary"]
    print(f"   更新后状态: {updated_rule.enabled}")
    print(f"   更新后周期: {updated_rule.period.value}")
    
    # 获取调度器统计
    print("\n5. 调度器统计:")
    stats = scheduler.get_scheduler_statistics()
    print(f"   总任务数: {stats['total_tasks']}")
    print(f"   总规则数: {stats['total_rules']}")
    print(f"   启用规则数: {stats['enabled_rules']}")
    print(f"   当前并发数: {stats['current_concurrent']}")
    print(f"   最大并发数: {stats['max_concurrent']}")
    print(f"   调度器状态: {'运行中' if stats['is_running'] else '已停止'}")
    
    return scheduler


async def demo_export_manager():
    """演示导出管理器"""
    print_section("导出管理器演示")
    
    export_manager = ExportManager()
    
    print("1. 导出管理器配置:")
    print(f"   导出目录: {export_manager.export_config['export_dir']}")
    print(f"   最大文件大小: {export_manager.export_config['max_file_size'] // (1024*1024)}MB")
    print(f"   清理天数: {export_manager.export_config['cleanup_days']}天")
    print(f"   分享基础URL: {export_manager.export_config['share_base_url']}")
    
    # 创建模拟报表结果
    mock_result = Mock()
    mock_result.report_id = "export_test_report"
    mock_result.content = "<html><body><h1>测试报表</h1><p>这是一个测试报表内容</p></body></html>"
    mock_result.format = ReportFormat.HTML
    mock_result.data = Mock()
    mock_result.data.title = "测试导出报表"
    mock_result.data.summary = {"总销量": 1000, "总收入": "¥50,000"}
    mock_result.data.sections = []
    mock_result.config = Mock()
    mock_result.config.report_name = "测试报表"
    mock_result.config.report_type = ReportType.COMPETITOR_ANALYSIS
    mock_result.generation_time = 2.5
    mock_result.status = "completed"
    
    # 导出为HTML
    print("\n2. 导出为HTML:")
    task = await export_manager.export_report(mock_result, ExportFormat.HTML)
    print(f"   任务ID: {task.task_id}")
    print(f"   导出状态: {task.status}")
    print(f"   文件路径: {task.file_path}")
    print(f"   文件大小: {task.file_size}字节")
    
    # 导出为JSON
    print("\n3. 导出为JSON:")
    task = await export_manager.export_report(mock_result, ExportFormat.JSON)
    print(f"   任务ID: {task.task_id}")
    print(f"   导出格式: {task.export_format.value}")
    print(f"   导出状态: {task.status}")
    
    # 导出为CSV
    print("\n4. 导出为CSV:")
    task = await export_manager.export_report(mock_result, ExportFormat.CSV)
    print(f"   任务ID: {task.task_id}")
    print(f"   导出格式: {task.export_format.value}")
    print(f"   导出状态: {task.status}")
    
    # 创建分享链接
    print("\n5. 创建分享链接:")
    
    # 公开分享
    public_link = export_manager.create_share_link("test_report", ShareType.PUBLIC)
    print(f"   公开分享链接:")
    print(f"     链接ID: {public_link.link_id}")
    print(f"     分享URL: {public_link.url}")
    print(f"     分享类型: {public_link.share_type.value}")
    
    # 密码保护分享
    password_link = export_manager.create_share_link(
        "test_report", 
        ShareType.PASSWORD, 
        password="demo123"
    )
    print(f"   密码保护分享链接:")
    print(f"     链接ID: {password_link.link_id}")
    print(f"     分享URL: {password_link.url}")
    print(f"     访问密码: {password_link.password}")
    
    # 有期限分享
    expire_link = export_manager.create_share_link(
        "test_report", 
        ShareType.EXPIRE, 
        expire_hours=24
    )
    print(f"   有期限分享链接:")
    print(f"     链接ID: {expire_link.link_id}")
    print(f"     过期时间: {expire_link.expire_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 访问分享链接
    print("\n6. 访问分享链接:")
    
    # 访问公开链接
    success, message, link = export_manager.access_share_link(public_link.link_id)
    print(f"   公开链接访问: {'成功' if success else '失败'} - {message}")
    print(f"   访问次数: {link.access_count if link else 0}")
    
    # 访问密码保护链接（错误密码）
    success, message, link = export_manager.access_share_link(
        password_link.link_id, 
        "wrong_password"
    )
    print(f"   密码链接访问（错误密码）: {'成功' if success else '失败'} - {message}")
    
    # 访问密码保护链接（正确密码）
    success, message, link = export_manager.access_share_link(
        password_link.link_id, 
        "demo123"
    )
    print(f"   密码链接访问（正确密码）: {'成功' if success else '失败'} - {message}")
    print(f"   访问次数: {link.access_count if link else 0}")
    
    # 获取导出统计
    print("\n7. 导出管理器统计:")
    stats = export_manager.get_export_statistics()
    print(f"   总导出数: {stats['total_exports']}")
    print(f"   成功导出数: {stats['completed_exports']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    print(f"   总分享数: {stats['total_shares']}")
    print(f"   活跃分享数: {stats['active_shares']}")
    
    print("\n   导出格式分布:")
    for format_name, count in stats['format_distribution'].items():
        print(f"     {format_name}: {count} 个")
    
    print("\n   分享类型分布:")
    for share_type, count in stats['share_type_distribution'].items():
        print(f"     {share_type}: {count} 个")
    
    return export_manager


async def main():
    """主演示函数"""
    print("🚀 报表生成系统演示")
    print("="*60)
    
    # 演示报表引擎
    report_engine = await demo_report_engine()
    
    # 演示图表生成器
    chart_generator = await demo_chart_generator()
    
    # 演示报表调度器
    scheduler = await demo_report_scheduler()
    
    # 演示导出管理器
    export_manager = await demo_export_manager()
    
    # 总结
    print_section("报表生成系统演示完成")
    
    print("🎯 核心功能:")
    print("- 报表引擎：多类型报表生成，模板化内容，多格式输出")
    print("- 图表生成器：多种图表类型，Chart.js集成，自定义样式")
    print("- 报表调度器：定时任务调度，自动生成报表，灵活配置")
    print("- 导出管理器：多格式导出，分享链接管理，权限控制")
    
    print("\n📊 演示统计:")
    print(f"- 报表引擎：{len(report_engine.report_configs)} 个配置，{len(report_engine.report_results)} 个报表")
    print(f"- 图表生成器：{len(chart_generator.chart_results)} 个图表")
    print(f"- 报表调度器：{len(scheduler.schedule_rules)} 个规则，{len(scheduler.schedule_tasks)} 个任务")
    print(f"- 导出管理器：{len(export_manager.export_tasks)} 个导出，{len(export_manager.share_links)} 个分享")
    
    print("\n🔧 技术特性:")
    print("- 多类型报表：竞品分析、供应商对比、利润分析、预警汇总、市场概览")
    print("- 多种格式：HTML、JSON、CSV、PDF、Excel、PNG等")
    print("- 图表支持：折线图、柱状图、饼图、散点图、面积图等")
    print("- 定时调度：日报、周报、月报、季报、年报自动生成")
    print("- 导出分享：多格式导出，公开/私有/密码/期限分享")
    print("- 模板引擎：Jinja2模板，变量替换，样式定制")


if __name__ == "__main__":
    asyncio.run(main())
