/**
 * 数据分析页面
 */

import React, { useEffect, useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Space,
  Spin,
  message,
  Dropdown,
  Tabs
} from 'antd';
import {
  RiseOutlined,
  FallOutlined,
  ReloadOutlined,
  DownloadOutlined,
  Line<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { analyticsApi, AnalyticsSummary } from '../../services/analyticsApi';
import { PriceTrendChart, SalesAnalysisChart, PlatformComparisonChart, CategoryAnalysisChart } from '../../components/Charts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const AnalyticsPage: React.FC = () => {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs()
  ]);
  const [selectedDays, setSelectedDays] = useState<number>(7);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadSummaryData();
  }, [selectedDays]);

  const loadSummaryData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getSummary({ days: selectedDays });
      setSummary(response.data);
    } catch (error: any) {
      message.error(`获取分析数据失败：${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (days: number) => {
    setSelectedDays(days);
    setDateRange([dayjs().subtract(days, 'day'), dayjs()]);
  };

  const handleExport = async (reportType: 'price_trend' | 'sales_analysis' | 'comprehensive', format: 'excel' | 'pdf' | 'csv') => {
    try {
      await analyticsApi.exportReport({
        report_type: reportType,
        format,
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
      });
      message.success('报告导出成功');
    } catch (error: any) {
      message.error(`导出失败：${error.message}`);
    }
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>数据分析</h2>
        <Space>
          <Select
            value={selectedDays}
            style={{ width: 120 }}
            onChange={handleDateRangeChange}
          >
            <Option value={7}>最近7天</Option>
            <Option value={30}>最近30天</Option>
            <Option value={90}>最近90天</Option>
          </Select>
          <RangePicker
            value={dateRange}
            onChange={(dates) => {
              if (dates) {
                setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs]);
              }
            }}
          />
          <Button
            icon={<ReloadOutlined />}
            onClick={loadSummaryData}
            loading={loading}
          >
            刷新
          </Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'price_trend_excel',
                  label: '价格趋势报告 (Excel)',
                  onClick: () => handleExport('price_trend', 'excel'),
                },
                {
                  key: 'sales_analysis_excel',
                  label: '销量分析报告 (Excel)',
                  onClick: () => handleExport('sales_analysis', 'excel'),
                },
                {
                  key: 'comprehensive_pdf',
                  label: '综合报告 (PDF)',
                  onClick: () => handleExport('comprehensive', 'pdf'),
                },
              ],
            }}
          >
            <Button icon={<DownloadOutlined />}>
              导出报告
            </Button>
          </Dropdown>
        </Space>
      </div>

      {/* 数据概览 */}
      <Spin spinning={loading}>
        <Row gutter={16} className="mb-4">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="商品总数"
                value={summary?.total_products || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="平均价格"
                value={summary?.average_price || 0}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="价格变动"
                value={Math.abs(summary?.price_change_percentage || 0)}
                precision={2}
                suffix="%"
                prefix={summary?.price_change_percentage && summary.price_change_percentage >= 0 ? <RiseOutlined /> : <FallOutlined />}
                valueStyle={{
                  color: summary?.price_change_percentage && summary.price_change_percentage >= 0 ? '#cf1322' : '#3f8600'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃监控"
                value={summary?.active_monitors || 0}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16} className="mb-4">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="最低价格"
                value={summary?.min_price || 0}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="最高价格"
                value={summary?.max_price || 0}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总销量"
                value={summary?.total_sales || 0}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="销量变动"
                value={Math.abs(summary?.sales_change_percentage || 0)}
                precision={2}
                suffix="%"
                prefix={summary?.sales_change_percentage && summary.sales_change_percentage >= 0 ? <RiseOutlined /> : <FallOutlined />}
                valueStyle={{
                  color: summary?.sales_change_percentage && summary.sales_change_percentage >= 0 ? '#52c41a' : '#f5222d'
                }}
              />
            </Card>
          </Col>
        </Row>
      </Spin>

      {/* 分析图表 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <LineChartOutlined />
              价格趋势
            </span>
          }
          key="price_trend"
        >
          <Card>
            <PriceTrendChart
              startDate={dateRange[0].format('YYYY-MM-DD')}
              endDate={dateRange[1].format('YYYY-MM-DD')}
              height={400}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              销量分析
            </span>
          }
          key="sales_analysis"
        >
          <Row gutter={16}>
            <Col xs={24} lg={16}>
              <Card title="销量趋势">
                <SalesAnalysisChart
                  startDate={dateRange[0].format('YYYY-MM-DD')}
                  endDate={dateRange[1].format('YYYY-MM-DD')}
                  height={350}
                />
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="平台分布">
                <PlatformComparisonChart
                  startDate={dateRange[0].format('YYYY-MM-DD')}
                  endDate={dateRange[1].format('YYYY-MM-DD')}
                  height={350}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span>
              <PieChartOutlined />
              综合报告
            </span>
          }
          key="comprehensive"
        >
          <Row gutter={16}>
            <Col xs={24}>
              <Card title="分类分析">
                <CategoryAnalysisChart
                  startDate={dateRange[0].format('YYYY-MM-DD')}
                  endDate={dateRange[1].format('YYYY-MM-DD')}
                  height={350}
                />
              </Card>
            </Col>
          </Row>

          <Card title="平台综合对比" className="mt-4">
            <Row gutter={16}>
              <Col xs={24} lg={12}>
                <PlatformComparisonChart
                  startDate={dateRange[0].format('YYYY-MM-DD')}
                  endDate={dateRange[1].format('YYYY-MM-DD')}
                  height={300}
                />
              </Col>
              <Col xs={24} lg={12}>
                <PriceTrendChart
                  startDate={dateRange[0].format('YYYY-MM-DD')}
                  endDate={dateRange[1].format('YYYY-MM-DD')}
                  height={300}
                />
              </Col>
            </Row>
          </Card>
        </TabPane>
      </Tabs>

      {/* 最后更新时间 */}
      {summary?.last_updated && (
        <div className="text-center mt-4" style={{ color: '#999', fontSize: 12 }}>
          最后更新时间：{new Date(summary.last_updated).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
