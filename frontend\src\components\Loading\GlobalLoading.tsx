/**
 * 全局加载组件
 */

import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

interface GlobalLoadingProps {
  loading?: boolean;
  tip?: string;
  size?: 'small' | 'default' | 'large';
  children?: React.ReactNode;
}

const GlobalLoading: React.FC<GlobalLoadingProps> = ({
  loading = false,
  tip = '加载中...',
  size = 'large',
  children,
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  if (loading) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 9999,
        }}
      >
        <Spin indicator={antIcon} size={size} tip={tip} />
      </div>
    );
  }

  return <>{children}</>;
};

export default GlobalLoading;
