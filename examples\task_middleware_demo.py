"""
Task-Middleware集成演示

展示统一数据获取层的使用方法
"""

import asyncio
import sys
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.task_middleware import (
    TaskMiddlewareClient,
    PlatformConfigManager,
    DataNormalizer,
    TaskScheduler
)
from app.services.task_middleware.client import CrawlConfig, TaskPriority
from app.services.task_middleware.config_manager import Platform, ProductType
from app.services.task_middleware.task_scheduler import ScheduleTask, ScheduleStrategy


async def demo_basic_client_usage():
    """演示基础客户端使用"""
    print("=== Task-Middleware客户端基础使用演示 ===")
    
    # 创建客户端
    async with TaskMiddlewareClient("http://localhost:11238") as client:
        # 创建爬取配置
        config = CrawlConfig(
            urls=[
                "https://detail.1688.com/offer/123456.html",
                "https://detail.1688.com/offer/789012.html"
            ],
            query="提取商品信息：标题、价格、库存、销量、评分",
            priority=TaskPriority.HIGH,
            batch_name="demo_batch",
            batch_description="演示批次"
        )
        
        try:
            # 提交任务
            print(f"提交爬取任务，URLs数量: {len(config.urls)}")
            result = await client.submit_crawl_task(config)
            
            print(f"任务提交结果:")
            print(f"  - 成功: {result.success}")
            print(f"  - 批次ID: {result.batch_id}")
            print(f"  - 有效任务: {result.valid_tasks}")
            print(f"  - 无效任务: {result.invalid_tasks}")
            
            # 查询任务状态
            if result.task_ids:
                task_id = result.task_ids[0]
                print(f"\n查询任务状态: {task_id}")
                task_status = await client.get_task_status(task_id)
                print(f"  - 状态: {task_status.status.value}")
                print(f"  - 优先级: {task_status.priority.value}")
                print(f"  - 重试次数: {task_status.retry_count}")
                
        except Exception as e:
            print(f"演示失败 (这是正常的，因为Task-Middleware可能未运行): {e}")


def demo_platform_config_manager():
    """演示平台配置管理器"""
    print("\n=== 平台配置管理器演示 ===")
    
    # 创建配置管理器
    config_manager = PlatformConfigManager()
    
    # 获取平台配置
    alibaba_config = config_manager.get_platform_config(Platform.ALIBABA_1688)
    print(f"1688平台配置:")
    print(f"  - 平台名称: {alibaba_config.base_config.name}")
    print(f"  - 基础URL: {alibaba_config.base_config.base_url}")
    print(f"  - 启用状态: {alibaba_config.enabled}")
    
    # 获取商品类型配置
    competitor_config = config_manager.get_product_type_config(
        Platform.ALIBABA_1688, ProductType.COMPETITOR
    )
    print(f"\n竞品配置:")
    print(f"  - 优先级提升: {competitor_config.priority_boost}")
    print(f"  - 监控频率: {competitor_config.monitoring_frequency}秒")
    print(f"  - 查询模板: {competitor_config.custom_query_template}")
    
    # 构建查询指令
    query = config_manager.build_crawl_query(
        Platform.ALIBABA_1688,
        ProductType.SUPPLIER,
        custom_fields=["供应商评级", "发货地址"]
    )
    print(f"\n供货商查询指令: {query}")
    
    # 获取监控频率
    frequencies = {}
    for product_type in ProductType:
        freq = config_manager.get_monitoring_frequency(Platform.ALIBABA_1688, product_type)
        frequencies[product_type.value] = freq
    
    print(f"\n监控频率设置:")
    for ptype, freq in frequencies.items():
        print(f"  - {ptype}: {freq}秒 ({freq//60}分钟)")


def demo_data_normalizer():
    """演示数据标准化处理器"""
    print("\n=== 数据标准化处理器演示 ===")
    
    # 创建标准化处理器
    normalizer = DataNormalizer()
    
    # 模拟原始爬取数据
    raw_data = {
        "title": "高品质手机壳 iPhone 15 Pro Max 透明防摔",
        "price": "¥29.90",
        "stock": "库存：1000件",
        "sales": "月销量：5万+",
        "rating": "4.8分",
        "main_image": "https://example.com/image1.jpg",
        "images": [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        ],
        "seller": "优质供应商旗舰店",
        "min_order": "起订量：100件",
        "wholesale_price": "批发价：¥25.00",
        "brand": "Apple",
        "description": "高品质透明手机壳，防摔防刮，完美贴合..."
    }
    
    # 标准化数据
    normalized = normalizer.normalize_crawl_result(
        raw_data,
        "https://detail.1688.com/offer/123456.html",
        Platform.ALIBABA_1688,
        ProductType.SUPPLIER
    )
    
    print(f"标准化结果:")
    print(f"  - 标题: {normalized.title}")
    print(f"  - 价格: {normalized.price} {normalized.currency}")
    print(f"  - 库存: {normalized.stock}")
    print(f"  - 销量: {normalized.sales_count}")
    print(f"  - 评分: {normalized.rating}")
    print(f"  - 图片数量: {len(normalized.images)}")
    print(f"  - 卖家: {normalized.seller_name}")
    print(f"  - 最小起订量: {normalized.min_order_quantity}")
    print(f"  - 批发价: {normalized.wholesale_price}")
    print(f"  - 品牌: {normalized.brand}")
    print(f"  - 数据质量分数: {normalized.data_quality_score:.2f}")


async def demo_task_scheduler():
    """演示任务调度器"""
    print("\n=== 任务调度器演示 ===")
    
    # 创建组件
    client = TaskMiddlewareClient("http://localhost:11238")
    config_manager = PlatformConfigManager()
    scheduler = TaskScheduler(client, config_manager)
    
    # 创建调度任务
    tasks = [
        ScheduleTask(
            urls=[f"https://detail.1688.com/offer/{i}.html" for i in range(1, 6)],
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.COMPETITOR,
            priority=TaskPriority.HIGH,
            batch_name="竞品监控批次"
        ),
        ScheduleTask(
            urls=[f"https://detail.1688.com/offer/{i}.html" for i in range(6, 11)],
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.SUPPLIER,
            priority=TaskPriority.MEDIUM,
            batch_name="供货商商品批次"
        ),
        ScheduleTask(
            urls=[f"https://item.taobao.com/{i}.htm" for i in range(1, 4)],
            platform=Platform.TAOBAO,
            product_type=ProductType.OTHER,
            priority=TaskPriority.LOW,
            batch_name="其他商品批次"
        )
    ]
    
    print(f"创建了 {len(tasks)} 个调度任务:")
    for i, task in enumerate(tasks, 1):
        print(f"  {i}. {task.platform.value} - {task.product_type.value} - {len(task.urls)}个URL")
    
    # 演示不同调度策略
    strategies = [
        (ScheduleStrategy.BATCH_OPTIMIZE, "批量优化策略"),
        (ScheduleStrategy.PRIORITY_FIRST, "优先级优先策略"),
    ]
    
    for strategy, name in strategies:
        print(f"\n使用 {name}:")
        try:
            result = await scheduler.schedule_batch_tasks(tasks, strategy)
            print(f"  - 调度成功: {result.success}")
            print(f"  - 成功任务: {result.scheduled_tasks}")
            print(f"  - 失败任务: {result.failed_tasks}")
            print(f"  - 批次数量: {len(result.batch_results)}")
            print(f"  - 调度ID: {result.schedule_id}")
        except Exception as e:
            print(f"  - 调度失败 (Task-Middleware可能未运行): {e}")
    
    await client.close()


def demo_integration_workflow():
    """演示完整的集成工作流程"""
    print("\n=== 完整集成工作流程演示 ===")
    
    print("1. 配置管理 -> 2. 任务调度 -> 3. 数据获取 -> 4. 数据标准化")
    
    # 1. 配置管理
    config_manager = PlatformConfigManager()
    platforms = config_manager.list_platforms()
    print(f"\n支持的平台: {[p.value for p in platforms]}")
    
    # 2. 查询构建
    queries = {}
    for platform in platforms:
        for product_type in ProductType:
            query = config_manager.build_crawl_query(platform, product_type)
            key = f"{platform.value}_{product_type.value}"
            queries[key] = query
    
    print(f"\n生成的查询指令数量: {len(queries)}")
    print("示例查询:")
    for key, query in list(queries.items())[:3]:
        print(f"  - {key}: {query}")
    
    # 3. 数据标准化演示
    normalizer = DataNormalizer()
    
    # 模拟不同平台的数据格式
    platform_data_samples = {
        "1688": {
            "title": "工厂直销手机壳",
            "price": "¥15.80",
            "min_order": "起订量：500件"
        },
        "taobao": {
            "title": "热销手机壳",
            "price": "29.9元",
            "sales": "月销1000+"
        }
    }
    
    print(f"\n数据标准化演示:")
    for platform_name, data in platform_data_samples.items():
        platform = Platform.ALIBABA_1688 if platform_name == "1688" else Platform.TAOBAO
        normalized = normalizer.normalize_crawl_result(
            data, f"https://{platform_name}.com/item/123", 
            platform, ProductType.OTHER
        )
        print(f"  - {platform_name}: 标题='{normalized.title}', 价格={normalized.price}")


async def main():
    """主演示函数"""
    print("🚀 Task-Middleware统一数据获取层演示")
    print("=" * 50)
    
    # 基础功能演示
    await demo_basic_client_usage()
    demo_platform_config_manager()
    demo_data_normalizer()
    await demo_task_scheduler()
    demo_integration_workflow()
    
    print("\n" + "=" * 50)
    print("✅ 演示完成！")
    print("\n核心特性:")
    print("- 统一的Task-Middleware API客户端")
    print("- 多平台配置管理和查询构建")
    print("- 智能数据标准化和质量评分")
    print("- 灵活的任务调度策略")
    print("- 完整的异步处理支持")


if __name__ == "__main__":
    asyncio.run(main())
