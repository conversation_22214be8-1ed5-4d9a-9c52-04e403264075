-- TimescaleDB 初始化脚本
-- 为时序数据创建超表和优化配置

-- 启用TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 将价格记录表转换为超表（时序表）
-- 使用recorded_at作为时间列，按天分区
SELECT create_hypertable('price_records', 'recorded_at', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- 创建价格记录的连续聚合视图 - 每小时汇总
CREATE MATERIALIZED VIEW IF NOT EXISTS price_records_hourly
WITH (timescaledb.continuous) AS
SELECT 
    product_id,
    time_bucket('1 hour', recorded_at) AS hour,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    COUNT(*) as record_count,
    FIRST(price, recorded_at) as first_price,
    LAST(price, recorded_at) as last_price
FROM price_records
GROUP BY product_id, hour;

-- 创建价格记录的连续聚合视图 - 每日汇总
CREATE MATERIALIZED VIEW IF NOT EXISTS price_records_daily
WITH (timescaledb.continuous) AS
SELECT 
    product_id,
    time_bucket('1 day', recorded_at) AS day,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    COUNT(*) as record_count,
    FIRST(price, recorded_at) as first_price,
    LAST(price, recorded_at) as last_price,
    STDDEV(price) as price_stddev
FROM price_records
GROUP BY product_id, day;

-- 创建价格记录的连续聚合视图 - 每周汇总
CREATE MATERIALIZED VIEW IF NOT EXISTS price_records_weekly
WITH (timescaledb.continuous) AS
SELECT 
    product_id,
    time_bucket('1 week', recorded_at) AS week,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    COUNT(*) as record_count,
    FIRST(price, recorded_at) as first_price,
    LAST(price, recorded_at) as last_price,
    STDDEV(price) as price_stddev
FROM price_records
GROUP BY product_id, week;

-- 添加刷新策略 - 自动更新连续聚合视图
SELECT add_continuous_aggregate_policy('price_records_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy('price_records_daily',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy('price_records_weekly',
    start_offset => INTERVAL '3 weeks',
    end_offset => INTERVAL '1 week',
    schedule_interval => INTERVAL '1 week',
    if_not_exists => TRUE
);

-- 创建数据保留策略 - 自动删除旧数据
-- 保留原始数据90天
SELECT add_retention_policy('price_records', INTERVAL '90 days', if_not_exists => TRUE);

-- 创建压缩策略 - 压缩旧数据以节省空间
-- 压缩7天前的数据
ALTER TABLE price_records SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'product_id',
    timescaledb.compress_orderby = 'recorded_at DESC'
);

SELECT add_compression_policy('price_records', INTERVAL '7 days', if_not_exists => TRUE);

-- 创建系统日志的超表
SELECT create_hypertable('system_logs', 'created_at', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- 为系统日志添加保留策略 - 保留30天
SELECT add_retention_policy('system_logs', INTERVAL '30 days', if_not_exists => TRUE);

-- 创建系统日志的连续聚合视图 - 每小时错误统计
CREATE MATERIALIZED VIEW IF NOT EXISTS system_logs_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', created_at) AS hour,
    level,
    module,
    COUNT(*) as log_count
FROM system_logs
GROUP BY hour, level, module;

-- 为系统日志聚合视图添加刷新策略
SELECT add_continuous_aggregate_policy('system_logs_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- 创建有用的索引
CREATE INDEX IF NOT EXISTS idx_price_records_product_time ON price_records (product_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_price_records_price ON price_records (price);
CREATE INDEX IF NOT EXISTS idx_system_logs_level_time ON system_logs (level, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_module_time ON system_logs (module, created_at DESC);

-- 创建有用的函数

-- 获取商品最新价格（优化版）
CREATE OR REPLACE FUNCTION get_latest_price_optimized(product_id_param VARCHAR(100))
RETURNS TABLE(price DECIMAL(10,2), recorded_at TIMESTAMP) AS $$
BEGIN
    RETURN QUERY
    SELECT pr.price, pr.recorded_at
    FROM price_records pr
    WHERE pr.product_id = product_id_param
    ORDER BY pr.recorded_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 获取商品价格趋势
CREATE OR REPLACE FUNCTION get_price_trend(
    product_id_param VARCHAR(100),
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE(
    day DATE,
    avg_price DECIMAL(10,2),
    min_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    price_change DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH daily_prices AS (
        SELECT 
            d.day::DATE,
            d.avg_price,
            d.min_price,
            d.max_price,
            LAG(d.avg_price) OVER (ORDER BY d.day) as prev_avg_price
        FROM price_records_daily d
        WHERE d.product_id = product_id_param
        AND d.day >= NOW() - INTERVAL '1 day' * days_back
        ORDER BY d.day
    )
    SELECT 
        dp.day,
        dp.avg_price,
        dp.min_price,
        dp.max_price,
        COALESCE(dp.avg_price - dp.prev_avg_price, 0) as price_change
    FROM daily_prices dp;
END;
$$ LANGUAGE plpgsql;

-- 获取价格异常检测
CREATE OR REPLACE FUNCTION detect_price_anomalies(
    product_id_param VARCHAR(100),
    threshold_multiplier DECIMAL DEFAULT 2.0
)
RETURNS TABLE(
    recorded_at TIMESTAMP,
    price DECIMAL(10,2),
    avg_price DECIMAL(10,2),
    deviation DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH price_stats AS (
        SELECT 
            AVG(price) as avg_price,
            STDDEV(price) as stddev_price
        FROM price_records
        WHERE product_id = product_id_param
        AND recorded_at >= NOW() - INTERVAL '30 days'
    )
    SELECT 
        pr.recorded_at,
        pr.price,
        ps.avg_price,
        ABS(pr.price - ps.avg_price) as deviation
    FROM price_records pr, price_stats ps
    WHERE pr.product_id = product_id_param
    AND pr.recorded_at >= NOW() - INTERVAL '7 days'
    AND ABS(pr.price - ps.avg_price) > (ps.stddev_price * threshold_multiplier)
    ORDER BY pr.recorded_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 插入示例时序数据
INSERT INTO price_records (product_id, price, currency, source_url, recorded_at, task_id) 
VALUES 
    ('prod_001', 8999.00, 'CNY', 'https://www.apple.com/iphone-15-pro/', NOW() - INTERVAL '1 day', 'task_001'),
    ('prod_001', 8999.00, 'CNY', 'https://www.apple.com/iphone-15-pro/', NOW() - INTERVAL '12 hours', 'task_001'),
    ('prod_001', 8899.00, 'CNY', 'https://www.apple.com/iphone-15-pro/', NOW() - INTERVAL '6 hours', 'task_001'),
    ('prod_001', 8899.00, 'CNY', 'https://www.apple.com/iphone-15-pro/', NOW(), 'task_001'),
    ('prod_002', 15999.00, 'CNY', 'https://www.apple.com/macbook-pro/', NOW() - INTERVAL '1 day', 'task_002'),
    ('prod_002', 15999.00, 'CNY', 'https://www.apple.com/macbook-pro/', NOW() - INTERVAL '12 hours', 'task_002'),
    ('prod_002', 15899.00, 'CNY', 'https://www.apple.com/macbook-pro/', NOW() - INTERVAL '6 hours', 'task_002'),
    ('prod_002', 15899.00, 'CNY', 'https://www.apple.com/macbook-pro/', NOW(), 'task_002')
ON CONFLICT DO NOTHING;

-- 记录TimescaleDB初始化完成
INSERT INTO system_logs (level, message, module) 
VALUES ('INFO', 'TimescaleDB初始化完成，超表和连续聚合视图已创建', 'timescaledb');

COMMIT;
