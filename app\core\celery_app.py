"""
Celery应用配置

异步任务队列配置，支持任务调度、优先级和重试机制
"""

from celery import Celery
from kombu import Queue, Exchange
from datetime import timedelta

from app.core.config import get_settings

settings = get_settings()

# 创建Celery应用
celery_app = Celery(
    "moniit",
    broker=settings.celery.broker_url,
    backend=settings.celery.result_backend,
    include=[
        "app.tasks.crawl_tasks",
        "app.tasks.analysis_tasks",
        "app.tasks.notification_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由和队列
    task_routes={
        "app.tasks.crawl_tasks.*": {"queue": "crawl"},
        "app.tasks.analysis_tasks.*": {"queue": "analysis"},
        "app.tasks.notification_tasks.*": {"queue": "notification"},
    },
    
    # 队列配置
    task_queues=(
        # 高优先级爬取队列
        Queue("crawl.high", Exchange("crawl"), routing_key="crawl.high", 
              queue_arguments={"x-max-priority": 10}),
        # 中优先级爬取队列
        Queue("crawl.medium", Exchange("crawl"), routing_key="crawl.medium",
              queue_arguments={"x-max-priority": 5}),
        # 低优先级爬取队列
        Queue("crawl.low", Exchange("crawl"), routing_key="crawl.low",
              queue_arguments={"x-max-priority": 1}),
        # 分析队列
        Queue("analysis", Exchange("analysis"), routing_key="analysis"),
        # 通知队列
        Queue("notification", Exchange("notification"), routing_key="notification"),
    ),
    
    # 默认队列
    task_default_queue="crawl.medium",
    task_default_exchange="crawl",
    task_default_routing_key="crawl.medium",
    
    # 任务执行配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,
    
    # 重试配置
    task_retry_delay=60,  # 重试延迟60秒
    task_max_retries=3,   # 最大重试3次
    
    # 任务超时
    task_soft_time_limit=300,  # 软超时5分钟
    task_time_limit=600,       # 硬超时10分钟
    
    # 结果过期时间
    result_expires=3600,  # 结果保存1小时
    
    # 定时任务
    beat_schedule={
        # 每5分钟检查高优先级任务
        "check-high-priority-tasks": {
            "task": "app.tasks.crawl_tasks.check_priority_tasks",
            "schedule": timedelta(minutes=5),
            "options": {"queue": "crawl.high"}
        },
        # 每30分钟执行竞品监控
        "competitor-monitoring": {
            "task": "app.tasks.crawl_tasks.competitor_monitoring",
            "schedule": timedelta(minutes=30),
            "options": {"queue": "crawl.high"}
        },
        # 每小时执行供货商监控
        "supplier-monitoring": {
            "task": "app.tasks.crawl_tasks.supplier_monitoring", 
            "schedule": timedelta(hours=1),
            "options": {"queue": "crawl.medium"}
        },
        # 每2小时执行其他商品监控
        "other-products-monitoring": {
            "task": "app.tasks.crawl_tasks.other_products_monitoring",
            "schedule": timedelta(hours=2),
            "options": {"queue": "crawl.low"}
        },
        # 每天凌晨2点执行数据分析
        "daily-analysis": {
            "task": "app.tasks.analysis_tasks.daily_analysis",
            "schedule": timedelta(hours=24),
            "options": {"queue": "analysis"}
        }
    },
    beat_scheduler="django_celery_beat.schedulers:DatabaseScheduler",
)

# 任务发现
celery_app.autodiscover_tasks()


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f"Request: {self.request!r}")


# 任务状态回调
@celery_app.task(bind=True, base=celery_app.Task)
def task_success_callback(self, retval, task_id, args, kwargs):
    """任务成功回调"""
    print(f"Task {task_id} succeeded with result: {retval}")


@celery_app.task(bind=True, base=celery_app.Task)
def task_failure_callback(self, task_id, error, traceback, args, kwargs):
    """任务失败回调"""
    print(f"Task {task_id} failed with error: {error}")
    print(f"Traceback: {traceback}")


# 配置回调
celery_app.conf.task_annotations = {
    "*": {
        "on_success": task_success_callback,
        "on_failure": task_failure_callback,
    }
}
