/**
 * 批量操作栏组件
 */

import React, { useState } from 'react';
import { 
  Card, 
  Space, 
  Button, 
  Dropdown, 
  Progress, 
  Typography, 
  Divider,
  Tooltip,
  Badge
} from 'antd';
import { 
  CheckOutlined, 
  CloseOutlined, 
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export interface BatchAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  danger?: boolean;
  disabled?: boolean;
  onClick: (selectedKeys: string[]) => void | Promise<void>;
}

interface BatchOperationBarProps {
  selectedKeys: string[];
  totalCount: number;
  actions: BatchAction[];
  loading?: boolean;
  progress?: {
    percent: number;
    status?: 'normal' | 'exception' | 'success';
    text?: string;
  };
  onSelectAll?: () => void;
  onClearSelection?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

const BatchOperationBar: React.FC<BatchOperationBarProps> = ({
  selectedKeys,
  totalCount,
  actions,
  loading = false,
  progress,
  onSelectAll,
  onClearSelection,
  className,
  style,
}) => {
  const [executing, setExecuting] = useState<string | null>(null);

  const handleAction = async (action: BatchAction) => {
    if (selectedKeys.length === 0 || action.disabled) return;

    try {
      setExecuting(action.key);
      await action.onClick(selectedKeys);
    } catch (error) {
      console.error('Batch operation failed:', error);
    } finally {
      setExecuting(null);
    }
  };

  const primaryActions = actions.filter(action => !action.danger).slice(0, 3);
  const dangerActions = actions.filter(action => action.danger);
  const moreActions = actions.slice(3);

  const dropdownItems = [
    ...moreActions.map(action => ({
      key: action.key,
      label: action.label,
      icon: action.icon,
      disabled: action.disabled || selectedKeys.length === 0,
      danger: action.danger,
      onClick: () => handleAction(action),
    })),
    ...(dangerActions.length > 0 ? [
      { type: 'divider' as const },
      ...dangerActions.map(action => ({
        key: action.key,
        label: action.label,
        icon: action.icon,
        disabled: action.disabled || selectedKeys.length === 0,
        danger: true,
        onClick: () => handleAction(action),
      }))
    ] : [])
  ];

  if (selectedKeys.length === 0 && !progress) {
    return null;
  }

  return (
    <Card 
      className={className}
      style={{ 
        marginBottom: 16, 
        borderColor: selectedKeys.length > 0 ? '#1890ff' : undefined,
        ...style 
      }}
      size="small"
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          {/* 选择状态 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge count={selectedKeys.length} showZero={false}>
              <CheckOutlined style={{ color: '#1890ff' }} />
            </Badge>
            <Text>
              已选择 <Text strong>{selectedKeys.length}</Text> 项
              {totalCount > 0 && (
                <Text type="secondary"> / 共 {totalCount} 项</Text>
              )}
            </Text>
          </div>

          {/* 全选/清空 */}
          {(onSelectAll || onClearSelection) && (
            <>
              <Divider type="vertical" />
              <Space size="small">
                {onSelectAll && selectedKeys.length < totalCount && (
                  <Button 
                    type="link" 
                    size="small"
                    onClick={onSelectAll}
                  >
                    全选
                  </Button>
                )}
                {onClearSelection && selectedKeys.length > 0 && (
                  <Button 
                    type="link" 
                    size="small"
                    onClick={onClearSelection}
                    icon={<CloseOutlined />}
                  >
                    清空
                  </Button>
                )}
              </Space>
            </>
          )}

          {/* 进度条 */}
          {progress && (
            <>
              <Divider type="vertical" />
              <div style={{ minWidth: 200 }}>
                <Progress
                  percent={progress.percent}
                  status={progress.status}
                  size="small"
                  format={() => progress.text || `${progress.percent}%`}
                />
              </div>
            </>
          )}
        </div>

        {/* 操作按钮 */}
        <Space>
          {primaryActions.map(action => (
            <Tooltip key={action.key} title={action.label}>
              <Button
                type={action.key === 'delete' ? 'default' : 'primary'}
                size="small"
                icon={action.icon}
                disabled={action.disabled || selectedKeys.length === 0}
                loading={executing === action.key || loading}
                onClick={() => handleAction(action)}
                danger={action.danger}
              >
                {action.label}
              </Button>
            </Tooltip>
          ))}

          {dropdownItems.length > 0 && (
            <Dropdown
              menu={{ items: dropdownItems }}
              disabled={selectedKeys.length === 0 || loading}
            >
              <Button 
                size="small" 
                icon={<MoreOutlined />}
                disabled={selectedKeys.length === 0 || loading}
              >
                更多
              </Button>
            </Dropdown>
          )}
        </Space>
      </div>
    </Card>
  );
};

// 常用批量操作配置
export const commonBatchActions = {
  delete: (onDelete: (keys: string[]) => void | Promise<void>): BatchAction => ({
    key: 'delete',
    label: '批量删除',
    icon: <DeleteOutlined />,
    danger: true,
    onClick: onDelete,
  }),

  edit: (onEdit: (keys: string[]) => void | Promise<void>): BatchAction => ({
    key: 'edit',
    label: '批量编辑',
    icon: <EditOutlined />,
    onClick: onEdit,
  }),

  export: (onExport: (keys: string[]) => void | Promise<void>): BatchAction => ({
    key: 'export',
    label: '批量导出',
    icon: <ExportOutlined />,
    onClick: onExport,
  }),

  import: (onImport: (keys: string[]) => void | Promise<void>): BatchAction => ({
    key: 'import',
    label: '批量导入',
    icon: <ImportOutlined />,
    onClick: onImport,
  }),

  refresh: (onRefresh: (keys: string[]) => void | Promise<void>): BatchAction => ({
    key: 'refresh',
    label: '批量刷新',
    icon: <ReloadOutlined />,
    onClick: onRefresh,
  }),
};

export default BatchOperationBar;
