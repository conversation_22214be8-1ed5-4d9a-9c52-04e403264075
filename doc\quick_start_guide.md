# Moniit 快速入门指南

## 🚀 5分钟快速上手

### 第一步：系统安装

**使用Docker（推荐）**:
```bash
# 克隆项目
git clone https://github.com/your-org/moniit.git
cd moniit

# 启动服务
docker-compose up -d

# 等待服务启动完成
docker-compose logs -f web
```

**访问系统**:
- 打开浏览器访问: `http://localhost:8000`
- 默认管理员账号: `admin`
- 默认密码: `admin123`

### 第二步：添加第一个商品

1. **登录系统**
   - 点击右上角"登录"按钮
   - 输入管理员账号密码

2. **进入商品管理**
   - 点击左侧菜单"商品管理"
   - 点击"添加商品"按钮

3. **填写商品信息**
   ```
   商品名称: iPhone 15 Pro
   商品URL: https://www.apple.com/cn/iphone-15-pro/
   平台: Apple官网
   分类: 手机数码 > 智能手机
   ```

4. **保存商品**
   - 点击"保存"按钮
   - 系统会自动验证URL有效性

### 第三步：设置价格监控

1. **配置监控参数**
   - 在商品列表中找到刚添加的商品
   - 点击"设置监控"按钮
   - 选择监控频率: "每小时"
   - 设置价格阈值: 涨跌超过5%时提醒

2. **启动监控**
   - 点击"启动监控"按钮
   - 系统开始自动采集价格数据

### 第四步：查看监控结果

1. **等待数据采集**
   - 首次采集通常需要1-2分钟
   - 可以在"监控任务"页面查看执行状态

2. **查看价格趋势**
   - 进入"价格监控"页面
   - 选择商品查看价格趋势图
   - 查看价格变化统计

## 📊 核心功能演示

### 商品批量导入

**准备Excel文件**:
```
商品名称          | 商品URL                    | 平台    | 分类
iPhone 15 Pro    | https://apple.com/...      | Apple   | 手机
MacBook Pro      | https://apple.com/...      | Apple   | 电脑
AirPods Pro      | https://apple.com/...      | Apple   | 耳机
```

**导入步骤**:
1. 下载导入模板
2. 填写商品信息
3. 上传Excel文件
4. 确认导入结果

### 利润分析设置

**成本配置**:
```python
# 示例：iPhone 15 Pro成本结构
{
    "purchase_cost": 7500.00,    # 采购成本
    "shipping_cost": 50.00,      # 运输成本
    "handling_cost": 25.00,      # 处理成本
    "platform_fee_rate": 0.03    # 平台费率3%
}
```

**利润计算**:
- 毛利润 = 售价 - 采购成本
- 净利润 = 毛利润 - 其他成本
- 利润率 = 净利润 / 售价 × 100%

### 供货商管理

**添加供货商**:
1. 进入"供货商管理"页面
2. 点击"添加供货商"
3. 填写供货商基本信息
4. 设置评估标准和权重

**供货商评估**:
- 质量评分 (30%权重)
- 交付评分 (25%权重)
- 服务评分 (25%权重)
- 成本评分 (20%权重)

## 🔧 高级配置

### 监控频率优化

**根据商品特性选择频率**:
- **高价值商品**: 实时监控(5分钟)
- **热门商品**: 高频监控(1小时)
- **一般商品**: 标准监控(4小时)
- **参考商品**: 低频监控(24小时)

### 价格预警设置

**智能预警策略**:
```python
# 预警配置示例
alert_config = {
    "price_increase": {
        "percentage": 5.0,      # 涨价超过5%
        "absolute": 500.0       # 或涨价超过500元
    },
    "price_decrease": {
        "percentage": 5.0,      # 降价超过5%
        "absolute": 500.0       # 或降价超过500元
    },
    "notification": {
        "email": True,          # 邮件通知
        "sms": False,           # 短信通知
        "webhook": "https://your-webhook.com/alert"
    }
}
```

### 数据导出配置

**支持的导出格式**:
- **Excel**: 适合数据分析
- **CSV**: 适合数据处理
- **PDF**: 适合报告分享
- **JSON**: 适合API集成

**导出内容定制**:
- 选择时间范围
- 选择商品范围
- 选择数据字段
- 设置导出格式

## 📱 API使用示例

### 获取商品列表

```bash
curl -X GET "http://localhost:8000/api/v1/products" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json"
```

### 创建商品

```bash
curl -X POST "http://localhost:8000/api/v1/products" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "iPhone 15 Pro",
    "url": "https://www.apple.com/cn/iphone-15-pro/",
    "platform": "apple",
    "category": "smartphones"
  }'
```

### 获取价格历史

```bash
curl -X GET "http://localhost:8000/api/v1/products/{product_id}/prices" \
  -H "Authorization: Bearer your_token" \
  -G -d "start_date=2025-08-01" -d "end_date=2025-08-24"
```

## 🎯 最佳实践

### 商品管理最佳实践

1. **URL选择**:
   - 使用商品详情页URL，避免列表页
   - 确保URL稳定，避免包含临时参数
   - 优先选择官方或主要销售渠道

2. **分类管理**:
   - 建立清晰的分类层次结构
   - 使用标准化的分类名称
   - 定期整理和优化分类

3. **批量操作**:
   - 使用Excel模板批量导入
   - 定期清理无效商品
   - 建立商品生命周期管理

### 监控策略最佳实践

1. **频率设置**:
   - 根据商品价值和变化频率设置
   - 避免过于频繁的监控导致被封
   - 定期评估和调整监控频率

2. **预警设置**:
   - 设置合理的价格变化阈值
   - 区分绝对变化和相对变化
   - 避免过多无效预警

3. **数据质量**:
   - 定期检查数据准确性
   - 及时处理异常数据
   - 建立数据质量监控机制

### 性能优化最佳实践

1. **缓存使用**:
   - 合理使用缓存减少数据库压力
   - 设置适当的缓存过期时间
   - 及时清理无效缓存

2. **查询优化**:
   - 使用索引优化查询性能
   - 避免全表扫描
   - 合理使用分页和限制

3. **资源管理**:
   - 监控系统资源使用情况
   - 及时清理日志和临时文件
   - 定期进行系统维护

## 🔍 故障排除

### 常见问题及解决方案

**问题1: 商品URL无法访问**
```
原因: URL失效或网站限制
解决: 
1. 检查URL是否正确
2. 尝试使用浏览器访问
3. 更新为有效的URL
4. 联系技术支持
```

**问题2: 价格数据不准确**
```
原因: 网站结构变化或反爬虫
解决:
1. 人工验证实际价格
2. 检查网站页面结构
3. 调整监控频率
4. 更新爬虫规则
```

**问题3: 监控任务失败**
```
原因: 网络问题或系统异常
解决:
1. 查看错误日志
2. 检查网络连接
3. 重启监控任务
4. 联系技术支持
```

## 📞 获取帮助

### 文档资源
- [用户操作手册](user_manual.md) - 详细的功能说明
- [API接口文档](api_documentation.md) - API使用指南
- [常见问题解答](faq.md) - 常见问题解决方案

### 技术支持
- **邮箱**: <EMAIL>
- **在线客服**: 工作时间 9:00-18:00
- **用户社区**: https://community.moniit.com
- **GitHub**: https://github.com/your-org/moniit

### 培训资源
- **视频教程**: https://learn.moniit.com/videos
- **在线培训**: 每周三下午2:00-4:00
- **用户手册**: 完整的PDF版本用户手册
- **最佳实践**: 行业最佳实践案例分享

---

## 🎉 恭喜！

您已经完成了Moniit系统的快速入门！现在您可以：

✅ 添加和管理商品  
✅ 设置价格监控  
✅ 查看价格趋势  
✅ 进行利润分析  
✅ 管理供货商关系  

继续探索更多高级功能，让Moniit为您的业务带来更大价值！

---

*本指南最后更新时间: 2025-08-24*
