"""
销量季节性分析器

提供深度季节性分析、季节性预测和季节性策略建议
"""

import asyncio
import statistics
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import calendar

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.trend_calculator import TrendCalculator
from app.services.analytics.advanced_sales_analyzer import SalesSeasonality

logger = get_logger(__name__)


class SeasonType(Enum):
    """季节类型"""
    SPRING = "spring"       # 春季
    SUMMER = "summer"       # 夏季
    AUTUMN = "autumn"       # 秋季
    WINTER = "winter"       # 冬季


class SeasonalPattern(Enum):
    """季节性模式"""
    WEEKLY = "weekly"           # 周模式
    MONTHLY = "monthly"         # 月模式
    QUARTERLY = "quarterly"     # 季度模式
    YEARLY = "yearly"          # 年模式
    HOLIDAY = "holiday"        # 节假日模式
    PROMOTIONAL = "promotional" # 促销模式


@dataclass
class SeasonalFactor:
    """季节因子"""
    period: str
    factor: float
    confidence: float
    description: str


@dataclass
class SeasonalInsight:
    """季节性洞察"""
    insight_type: str
    description: str
    impact_score: float  # 0-1
    recommendations: List[str]
    supporting_data: Dict[str, Any]


@dataclass
class SeasonalForecast:
    """季节性预测"""
    period: Tuple[datetime, datetime]
    predicted_sales: List[int]
    seasonal_adjustment: List[float]
    confidence_intervals: List[Tuple[float, float]]
    key_events: List[str]


@dataclass
class SeasonalStrategy:
    """季节性策略"""
    season: str
    strategy_type: str
    recommendations: List[str]
    expected_impact: float
    implementation_timeline: str
    success_metrics: List[str]


class SalesSeasonalityAnalyzer:
    """销量季节性分析器"""
    
    def __init__(self):
        self.trend_calculator = TrendCalculator()
        
        # 中国节假日和促销节点
        self.chinese_holidays = {
            "春节": [(1, 21), (2, 20)],      # 农历新年期间
            "清明节": [(4, 4), (4, 6)],
            "劳动节": [(5, 1), (5, 3)],
            "端午节": [(6, 14), (6, 16)],    # 大致时间
            "中秋节": [(9, 15), (9, 17)],    # 大致时间
            "国庆节": [(10, 1), (10, 7)]
        }
        
        self.shopping_festivals = {
            "双十一": [(11, 11), (11, 11)],
            "双十二": [(12, 12), (12, 12)],
            "618": [(6, 18), (6, 18)],
            "年货节": [(1, 10), (1, 25)]
        }
        
        # 季节性基准
        self.seasonal_benchmarks = {
            ProductType.COMPETITOR: {
                "electronics": {"spring": 1.0, "summer": 0.9, "autumn": 1.2, "winter": 1.3},
                "clothing": {"spring": 1.1, "summer": 0.8, "autumn": 1.3, "winter": 1.2},
                "default": {"spring": 1.0, "summer": 0.95, "autumn": 1.1, "winter": 1.15}
            },
            ProductType.SUPPLIER: {
                "default": {"spring": 1.05, "summer": 1.0, "autumn": 1.1, "winter": 0.9}
            }
        }
    
    async def analyze_comprehensive_seasonality(self, product: Product,
                                             sales_data: List[Tuple[datetime, int]],
                                             category: str = "default") -> Dict[str, Any]:
        """
        综合季节性分析
        
        Args:
            product: 商品对象
            sales_data: 销量数据 [(时间, 销量)]
            category: 商品类别
        
        Returns:
            Dict: 综合季节性分析结果
        """
        try:
            logger.info(f"开始综合季节性分析: {product.id}")
            
            if len(sales_data) < 30:  # 至少需要一个月数据
                return self._create_empty_seasonality_analysis(product)
            
            # 1. 基础季节性检测
            basic_seasonality = await self._detect_basic_seasonality(sales_data)
            
            # 2. 多周期季节性分析
            multi_period_analysis = await self._analyze_multi_period_seasonality(sales_data)
            
            # 3. 节假日影响分析
            holiday_impact = await self._analyze_holiday_impact(sales_data)
            
            # 4. 促销活动影响分析
            promotional_impact = await self._analyze_promotional_impact(sales_data)
            
            # 5. 季节性强度评估
            seasonality_strength = self._assess_seasonality_strength(sales_data)
            
            # 6. 季节性洞察生成
            insights = self._generate_seasonal_insights(
                product, basic_seasonality, multi_period_analysis, 
                holiday_impact, promotional_impact, category
            )
            
            # 7. 季节性预测
            forecast = await self._generate_seasonal_forecast(
                product, sales_data, basic_seasonality
            )
            
            # 8. 季节性策略建议
            strategies = self._generate_seasonal_strategies(
                product, basic_seasonality, insights, category
            )
            
            return {
                "product_id": product.id,
                "analysis_period": (sales_data[0][0], sales_data[-1][0]),
                "basic_seasonality": basic_seasonality,
                "multi_period_analysis": multi_period_analysis,
                "holiday_impact": holiday_impact,
                "promotional_impact": promotional_impact,
                "seasonality_strength": seasonality_strength,
                "insights": insights,
                "forecast": forecast,
                "strategies": strategies,
                "analysis_timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"综合季节性分析失败: {e}")
            return self._create_empty_seasonality_analysis(product)
    
    async def _detect_basic_seasonality(self, sales_data: List[Tuple[datetime, int]]) -> SalesSeasonality:
        """检测基础季节性"""
        timestamps = [item[0] for item in sales_data]
        sales_values = [float(item[1]) for item in sales_data]
        
        # 使用趋势计算器检测季节性
        seasonality_result = self.trend_calculator.detect_seasonality(sales_values)
        
        # 分析季节性模式
        peak_seasons = []
        low_seasons = []
        seasonal_factors = {}
        
        if seasonality_result.has_seasonality and seasonality_result.seasonal_pattern:
            # 识别峰值和低谷季节
            pattern = seasonality_result.seasonal_pattern
            avg_factor = statistics.mean(pattern)
            
            for i, factor in enumerate(pattern):
                if factor > avg_factor * 1.2:  # 高于平均20%
                    season_name = self._get_season_name(i, len(pattern))
                    peak_seasons.append(season_name)
                    seasonal_factors[season_name] = factor
                elif factor < avg_factor * 0.8:  # 低于平均20%
                    season_name = self._get_season_name(i, len(pattern))
                    low_seasons.append(season_name)
                    seasonal_factors[season_name] = factor
        
        # 生成季节性预测
        seasonal_forecast = []
        if seasonality_result.has_seasonality:
            base_date = timestamps[-1]
            for i in range(30):  # 预测30天
                forecast_date = base_date + timedelta(days=i+1)
                seasonal_index = i % len(seasonality_result.seasonal_pattern)
                seasonal_factor = seasonality_result.seasonal_pattern[seasonal_index]
                seasonal_forecast.append((forecast_date, seasonal_factor))
        
        return SalesSeasonality(
            product_id="",  # 将在调用处设置
            has_seasonality=seasonality_result.has_seasonality,
            seasonal_strength=seasonality_result.seasonal_strength,
            peak_seasons=peak_seasons,
            low_seasons=low_seasons,
            seasonal_factors=seasonal_factors,
            seasonal_forecast=seasonal_forecast
        )
    
    async def _analyze_multi_period_seasonality(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析多周期季节性"""
        analysis = {}
        
        # 周模式分析
        weekly_pattern = self._analyze_weekly_pattern(sales_data)
        analysis["weekly"] = weekly_pattern
        
        # 月模式分析
        monthly_pattern = self._analyze_monthly_pattern(sales_data)
        analysis["monthly"] = monthly_pattern
        
        # 季度模式分析
        quarterly_pattern = self._analyze_quarterly_pattern(sales_data)
        analysis["quarterly"] = quarterly_pattern
        
        return analysis
    
    def _analyze_weekly_pattern(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析周模式"""
        # 按星期几分组
        weekday_sales = {i: [] for i in range(7)}  # 0=周一, 6=周日
        
        for timestamp, sales in sales_data:
            weekday = timestamp.weekday()
            weekday_sales[weekday].append(sales)
        
        # 计算每天的平均销量
        weekday_averages = {}
        weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        
        for weekday, sales_list in weekday_sales.items():
            if sales_list:
                avg_sales = statistics.mean(sales_list)
                weekday_averages[weekday_names[weekday]] = avg_sales
        
        # 识别峰值和低谷
        if weekday_averages:
            max_day = max(weekday_averages, key=weekday_averages.get)
            min_day = min(weekday_averages, key=weekday_averages.get)
            
            # 计算周模式强度
            max_sales = weekday_averages[max_day]
            min_sales = weekday_averages[min_day]
            pattern_strength = (max_sales - min_sales) / max_sales if max_sales > 0 else 0
        else:
            max_day = min_day = "未知"
            pattern_strength = 0
        
        return {
            "has_pattern": pattern_strength > 0.2,
            "pattern_strength": pattern_strength,
            "peak_day": max_day,
            "low_day": min_day,
            "daily_averages": weekday_averages
        }
    
    def _analyze_monthly_pattern(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析月模式"""
        # 按月份分组
        monthly_sales = {i: [] for i in range(1, 13)}
        
        for timestamp, sales in sales_data:
            month = timestamp.month
            monthly_sales[month].append(sales)
        
        # 计算每月平均销量
        monthly_averages = {}
        month_names = [
            "1月", "2月", "3月", "4月", "5月", "6月",
            "7月", "8月", "9月", "10月", "11月", "12月"
        ]
        
        for month, sales_list in monthly_sales.items():
            if sales_list:
                avg_sales = statistics.mean(sales_list)
                monthly_averages[month_names[month-1]] = avg_sales
        
        # 识别峰值和低谷月份
        if monthly_averages:
            peak_month = max(monthly_averages, key=monthly_averages.get)
            low_month = min(monthly_averages, key=monthly_averages.get)
            
            # 计算月模式强度
            max_sales = monthly_averages[peak_month]
            min_sales = monthly_averages[low_month]
            pattern_strength = (max_sales - min_sales) / max_sales if max_sales > 0 else 0
        else:
            peak_month = low_month = "未知"
            pattern_strength = 0
        
        return {
            "has_pattern": pattern_strength > 0.3,
            "pattern_strength": pattern_strength,
            "peak_month": peak_month,
            "low_month": low_month,
            "monthly_averages": monthly_averages
        }
    
    def _analyze_quarterly_pattern(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析季度模式"""
        # 按季度分组
        quarterly_sales = {1: [], 2: [], 3: [], 4: []}
        
        for timestamp, sales in sales_data:
            quarter = (timestamp.month - 1) // 3 + 1
            quarterly_sales[quarter].append(sales)
        
        # 计算每季度平均销量
        quarterly_averages = {}
        quarter_names = ["Q1", "Q2", "Q3", "Q4"]
        
        for quarter, sales_list in quarterly_sales.items():
            if sales_list:
                avg_sales = statistics.mean(sales_list)
                quarterly_averages[quarter_names[quarter-1]] = avg_sales
        
        # 识别峰值和低谷季度
        if quarterly_averages:
            peak_quarter = max(quarterly_averages, key=quarterly_averages.get)
            low_quarter = min(quarterly_averages, key=quarterly_averages.get)
            
            # 计算季度模式强度
            max_sales = quarterly_averages[peak_quarter]
            min_sales = quarterly_averages[low_quarter]
            pattern_strength = (max_sales - min_sales) / max_sales if max_sales > 0 else 0
        else:
            peak_quarter = low_quarter = "未知"
            pattern_strength = 0
        
        return {
            "has_pattern": pattern_strength > 0.25,
            "pattern_strength": pattern_strength,
            "peak_quarter": peak_quarter,
            "low_quarter": low_quarter,
            "quarterly_averages": quarterly_averages
        }
    
    async def _analyze_holiday_impact(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析节假日影响"""
        holiday_impacts = {}
        
        # 分析每个节假日的影响
        for holiday_name, date_ranges in self.chinese_holidays.items():
            impact = self._calculate_holiday_impact(sales_data, date_ranges, holiday_name)
            if impact["has_impact"]:
                holiday_impacts[holiday_name] = impact
        
        return {
            "total_holidays_analyzed": len(self.chinese_holidays),
            "holidays_with_impact": len(holiday_impacts),
            "holiday_impacts": holiday_impacts
        }
    
    async def _analyze_promotional_impact(self, sales_data: List[Tuple[datetime, int]]) -> Dict[str, Any]:
        """分析促销活动影响"""
        promotional_impacts = {}
        
        # 分析每个购物节的影响
        for festival_name, date_ranges in self.shopping_festivals.items():
            impact = self._calculate_holiday_impact(sales_data, date_ranges, festival_name)
            if impact["has_impact"]:
                promotional_impacts[festival_name] = impact
        
        return {
            "total_festivals_analyzed": len(self.shopping_festivals),
            "festivals_with_impact": len(promotional_impacts),
            "promotional_impacts": promotional_impacts
        }
    
    def _calculate_holiday_impact(self, sales_data: List[Tuple[datetime, int]],
                                date_ranges: List[Tuple[int, int]], 
                                event_name: str) -> Dict[str, Any]:
        """计算节假日/促销活动影响"""
        holiday_sales = []
        normal_sales = []
        
        for timestamp, sales in sales_data:
            is_holiday = False
            for start_month, start_day in date_ranges:
                # 简化判断：只考虑月日，不考虑年份
                if timestamp.month == start_month and timestamp.day == start_day:
                    is_holiday = True
                    break
            
            if is_holiday:
                holiday_sales.append(sales)
            else:
                normal_sales.append(sales)
        
        if not holiday_sales or not normal_sales:
            return {"has_impact": False}
        
        # 计算影响
        holiday_avg = statistics.mean(holiday_sales)
        normal_avg = statistics.mean(normal_sales)
        
        if normal_avg > 0:
            impact_ratio = holiday_avg / normal_avg
            impact_percent = (impact_ratio - 1) * 100
        else:
            impact_ratio = 1.0
            impact_percent = 0
        
        # 判断是否有显著影响
        has_significant_impact = abs(impact_percent) > 20  # 20%以上变化
        
        return {
            "has_impact": has_significant_impact,
            "impact_ratio": impact_ratio,
            "impact_percent": impact_percent,
            "holiday_average_sales": holiday_avg,
            "normal_average_sales": normal_avg,
            "sample_size": len(holiday_sales)
        }
    
    def _assess_seasonality_strength(self, sales_data: List[Tuple[datetime, int]]) -> float:
        """评估季节性强度"""
        if len(sales_data) < 14:
            return 0.0
        
        sales_values = [float(item[1]) for item in sales_data]
        
        # 使用变异系数评估季节性强度
        if not sales_values:
            return 0.0
        
        mean_sales = statistics.mean(sales_values)
        if mean_sales == 0:
            return 0.0
        
        std_sales = statistics.stdev(sales_values) if len(sales_values) > 1 else 0
        coefficient_of_variation = std_sales / mean_sales
        
        # 将变异系数转换为0-1的季节性强度分数
        seasonality_strength = min(1.0, coefficient_of_variation)
        
        return seasonality_strength
    
    def _generate_seasonal_insights(self, product: Product, 
                                  basic_seasonality: SalesSeasonality,
                                  multi_period_analysis: Dict[str, Any],
                                  holiday_impact: Dict[str, Any],
                                  promotional_impact: Dict[str, Any],
                                  category: str) -> List[SeasonalInsight]:
        """生成季节性洞察"""
        insights = []
        
        # 基础季节性洞察
        if basic_seasonality.has_seasonality:
            insights.append(SeasonalInsight(
                insight_type="basic_seasonality",
                description=f"商品具有明显的季节性特征，强度为 {basic_seasonality.seasonal_strength:.2f}",
                impact_score=basic_seasonality.seasonal_strength,
                recommendations=[
                    "根据季节性调整库存策略",
                    "在峰值季节加大营销投入",
                    "在低谷季节控制成本"
                ],
                supporting_data={
                    "peak_seasons": basic_seasonality.peak_seasons,
                    "low_seasons": basic_seasonality.low_seasons
                }
            ))
        
        # 周模式洞察
        weekly = multi_period_analysis.get("weekly", {})
        if weekly.get("has_pattern", False):
            insights.append(SeasonalInsight(
                insight_type="weekly_pattern",
                description=f"存在明显的周模式，{weekly['peak_day']}销量最高，{weekly['low_day']}销量最低",
                impact_score=weekly["pattern_strength"],
                recommendations=[
                    f"在{weekly['peak_day']}增加库存准备",
                    f"在{weekly['low_day']}进行促销活动",
                    "优化周内营销节奏"
                ],
                supporting_data=weekly
            ))
        
        # 节假日影响洞察
        if holiday_impact["holidays_with_impact"] > 0:
            insights.append(SeasonalInsight(
                insight_type="holiday_impact",
                description=f"受到 {holiday_impact['holidays_with_impact']} 个节假日显著影响",
                impact_score=0.7,  # 节假日影响通常较大
                recommendations=[
                    "制定节假日专项营销策略",
                    "提前准备节假日库存",
                    "关注节假日前后的销量变化"
                ],
                supporting_data=holiday_impact
            ))
        
        # 促销活动影响洞察
        if promotional_impact["festivals_with_impact"] > 0:
            insights.append(SeasonalInsight(
                insight_type="promotional_impact",
                description=f"受到 {promotional_impact['festivals_with_impact']} 个购物节显著影响",
                impact_score=0.8,  # 促销影响通常很大
                recommendations=[
                    "积极参与购物节促销",
                    "优化购物节期间的价格策略",
                    "加强购物节前的预热营销"
                ],
                supporting_data=promotional_impact
            ))
        
        return insights
    
    async def _generate_seasonal_forecast(self, product: Product,
                                        sales_data: List[Tuple[datetime, int]],
                                        seasonality: SalesSeasonality) -> SeasonalForecast:
        """生成季节性预测"""
        if not seasonality.has_seasonality:
            return SeasonalForecast(
                period=(datetime.now(), datetime.now() + timedelta(days=30)),
                predicted_sales=[],
                seasonal_adjustment=[],
                confidence_intervals=[],
                key_events=[]
            )
        
        # 预测未来30天
        forecast_start = sales_data[-1][0] + timedelta(days=1)
        forecast_end = forecast_start + timedelta(days=30)
        
        predicted_sales = []
        seasonal_adjustments = []
        confidence_intervals = []
        
        # 计算基础销量（去除季节性）
        recent_sales = [item[1] for item in sales_data[-7:]]  # 最近一周
        base_sales = statistics.mean(recent_sales) if recent_sales else 0
        
        for i in range(30):
            forecast_date = forecast_start + timedelta(days=i)
            
            # 获取季节性因子
            if seasonality.seasonal_forecast:
                seasonal_factor = seasonality.seasonal_forecast[i % len(seasonality.seasonal_forecast)][1]
            else:
                seasonal_factor = 1.0
            
            # 预测销量
            predicted = int(base_sales * seasonal_factor)
            predicted_sales.append(predicted)
            seasonal_adjustments.append(seasonal_factor)
            
            # 置信区间（简化计算）
            confidence_lower = predicted * 0.8
            confidence_upper = predicted * 1.2
            confidence_intervals.append((confidence_lower, confidence_upper))
        
        # 识别预测期间的关键事件
        key_events = self._identify_key_events_in_period(forecast_start, forecast_end)
        
        return SeasonalForecast(
            period=(forecast_start, forecast_end),
            predicted_sales=predicted_sales,
            seasonal_adjustment=seasonal_adjustments,
            confidence_intervals=confidence_intervals,
            key_events=key_events
        )
    
    def _identify_key_events_in_period(self, start_date: datetime, 
                                     end_date: datetime) -> List[str]:
        """识别预测期间的关键事件"""
        key_events = []
        
        # 检查节假日
        for holiday_name, date_ranges in self.chinese_holidays.items():
            for month, day in date_ranges:
                event_date = datetime(start_date.year, month, day)
                if start_date <= event_date <= end_date:
                    key_events.append(f"{holiday_name} ({month}月{day}日)")
        
        # 检查购物节
        for festival_name, date_ranges in self.shopping_festivals.items():
            for month, day in date_ranges:
                event_date = datetime(start_date.year, month, day)
                if start_date <= event_date <= end_date:
                    key_events.append(f"{festival_name} ({month}月{day}日)")
        
        return key_events
    
    def _generate_seasonal_strategies(self, product: Product,
                                    seasonality: SalesSeasonality,
                                    insights: List[SeasonalInsight],
                                    category: str) -> List[SeasonalStrategy]:
        """生成季节性策略"""
        strategies = []
        
        if not seasonality.has_seasonality:
            return strategies
        
        # 为每个峰值季节生成策略
        for peak_season in seasonality.peak_seasons:
            strategy = SeasonalStrategy(
                season=peak_season,
                strategy_type="peak_season_optimization",
                recommendations=[
                    f"在{peak_season}前1-2周增加库存",
                    f"加大{peak_season}期间的营销投入",
                    f"优化{peak_season}的价格策略",
                    f"准备{peak_season}专项促销活动"
                ],
                expected_impact=0.15,  # 预期15%的销量提升
                implementation_timeline=f"{peak_season}前4周开始准备",
                success_metrics=[
                    "销量增长率",
                    "库存周转率",
                    "营销ROI",
                    "客户满意度"
                ]
            )
            strategies.append(strategy)
        
        # 为每个低谷季节生成策略
        for low_season in seasonality.low_seasons:
            strategy = SeasonalStrategy(
                season=low_season,
                strategy_type="low_season_activation",
                recommendations=[
                    f"在{low_season}推出特价促销",
                    f"开发{low_season}专属产品或服务",
                    f"加强{low_season}的用户留存活动",
                    f"利用{low_season}进行品牌建设"
                ],
                expected_impact=0.10,  # 预期10%的销量提升
                implementation_timeline=f"{low_season}开始前2周",
                success_metrics=[
                    "促销转化率",
                    "用户活跃度",
                    "品牌认知度",
                    "成本控制效果"
                ]
            )
            strategies.append(strategy)
        
        return strategies
    
    def _get_season_name(self, index: int, total_periods: int) -> str:
        """根据索引获取季节名称"""
        if total_periods == 7:  # 周模式
            weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            return weekdays[index % 7]
        elif total_periods == 12:  # 月模式
            months = ["1月", "2月", "3月", "4月", "5月", "6月", 
                     "7月", "8月", "9月", "10月", "11月", "12月"]
            return months[index % 12]
        elif total_periods == 4:  # 季度模式
            quarters = ["Q1", "Q2", "Q3", "Q4"]
            return quarters[index % 4]
        else:
            return f"周期{index + 1}"
    
    def _create_empty_seasonality_analysis(self, product: Product) -> Dict[str, Any]:
        """创建空的季节性分析结果"""
        return {
            "product_id": product.id,
            "analysis_period": (datetime.now(), datetime.now()),
            "basic_seasonality": SalesSeasonality(
                product_id=product.id,
                has_seasonality=False,
                seasonal_strength=0.0,
                peak_seasons=[],
                low_seasons=[],
                seasonal_factors={},
                seasonal_forecast=[]
            ),
            "multi_period_analysis": {},
            "holiday_impact": {"holidays_with_impact": 0, "holiday_impacts": {}},
            "promotional_impact": {"festivals_with_impact": 0, "promotional_impacts": {}},
            "seasonality_strength": 0.0,
            "insights": [SeasonalInsight(
                insight_type="insufficient_data",
                description="数据不足，无法进行季节性分析",
                impact_score=0.0,
                recommendations=["收集更多历史销量数据"],
                supporting_data={}
            )],
            "forecast": SeasonalForecast(
                period=(datetime.now(), datetime.now()),
                predicted_sales=[],
                seasonal_adjustment=[],
                confidence_intervals=[],
                key_events=[]
            ),
            "strategies": [],
            "analysis_timestamp": datetime.now()
        }
