"""
配置管理测试
"""

import os
import pytest
from unittest.mock import patch, mock_open
from app.core.config import Config<PERSON>anager, get_settings


class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_yaml_config_success(self):
        """测试成功加载YAML配置"""
        yaml_content = """
        app:
          name: "测试应用"
          version: "1.0.0"
        database:
          url: "postgresql://test:test@localhost:5432/test"
        """
        
        with patch("builtins.open", mock_open(read_data=yaml_content)):
            with patch("pathlib.Path.exists", return_value=True):
                config_manager = ConfigManager()
                config = config_manager.load_yaml_config()
                
                assert config["app"]["name"] == "测试应用"
                assert config["database"]["url"] == "postgresql://test:test@localhost:5432/test"
    
    def test_load_yaml_config_file_not_exists(self):
        """测试配置文件不存在的情况"""
        with patch("pathlib.Path.exists", return_value=False):
            config_manager = ConfigManager()
            config = config_manager.load_yaml_config()
            
            assert config == {}
    
    def test_get_app_settings_with_env_override(self):
        """测试环境变量覆盖配置"""
        # 清除配置缓存
        from app.core.config import config_manager
        config_manager._app_settings = None

        with patch.dict(os.environ, {
            "DATABASE_URL": "postgresql://env:env@localhost:5432/env_db",
            "DEBUG": "true",
            "APP_PORT": "9000"
        }):
            settings = get_settings()

            assert "postgresql://env:env@localhost:5432/env_db" in settings.database.url
            assert settings.debug == True
            assert settings.port == 9000
    
    def test_config_validation(self):
        """测试配置验证"""
        settings = get_settings()
        
        # 验证必需字段
        assert settings.name is not None
        assert settings.version is not None
        assert settings.database.url is not None
        assert settings.redis.url is not None
        
        # 验证数值范围
        assert settings.database.pool_size > 0
        assert settings.database.max_overflow >= 0
        assert settings.redis.max_connections > 0
        assert settings.security.access_token_expire_minutes > 0


class TestConfigIntegration:
    """配置集成测试"""
    
    def test_database_config_integration(self):
        """测试数据库配置集成"""
        settings = get_settings()
        db_config = settings.database
        
        assert db_config.pool_size >= 1
        assert db_config.max_overflow >= 0
        assert db_config.pool_recycle > 0
        assert isinstance(db_config.echo, bool)
        assert isinstance(db_config.pool_pre_ping, bool)
    
    def test_redis_config_integration(self):
        """测试Redis配置集成"""
        settings = get_settings()
        redis_config = settings.redis
        
        assert redis_config.max_connections > 0
        assert redis_config.socket_timeout > 0
        assert redis_config.socket_connect_timeout > 0
    
    def test_celery_config_integration(self):
        """测试Celery配置集成"""
        settings = get_settings()
        celery_config = settings.celery
        
        assert celery_config.task_serializer == "json"
        assert "json" in celery_config.accept_content
        assert celery_config.result_serializer == "json"
        assert celery_config.timezone == "UTC"
        assert celery_config.enable_utc == True
    
    def test_security_config_integration(self):
        """测试安全配置集成"""
        settings = get_settings()
        security_config = settings.security
        
        assert len(security_config.secret_key) > 0
        assert security_config.access_token_expire_minutes > 0
        assert security_config.algorithm in ["HS256", "HS384", "HS512"]
        assert security_config.password_min_length >= 6


@pytest.fixture
def mock_config_file():
    """模拟配置文件"""
    return """
    app:
      name: "电商商品监控系统"
      version: "1.0.0"
      debug: false
      host: "0.0.0.0"
      port: 8000
    
    database:
      url: "postgresql://postgres:password@localhost:5432/ecommerce_monitor"
      pool_size: 10
      max_overflow: 20
      echo: false
      pool_pre_ping: true
      pool_recycle: 3600
    
    redis:
      url: "redis://localhost:6379/0"
      max_connections: 10
      socket_timeout: 5
      socket_connect_timeout: 5
    
    celery:
      broker_url: "redis://localhost:6379/1"
      result_backend: "redis://localhost:6379/2"
      task_serializer: "json"
      accept_content: ["json"]
      result_serializer: "json"
      timezone: "UTC"
      enable_utc: true
    
    task_middleware:
      base_url: "http://localhost:3000"
      timeout: 30
      max_retries: 3
      retry_delay: 5
    
    security:
      secret_key: "test-secret-key"
      access_token_expire_minutes: 30
      algorithm: "HS256"
      password_min_length: 8
    
    logging:
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      file: "logs/app.log"
      max_size: "10MB"
      backup_count: 5
      console_output: true
    """


def test_full_config_loading(mock_config_file):
    """测试完整配置加载"""
    # 清除配置缓存
    from app.core.config import config_manager
    config_manager._app_settings = None

    with patch("builtins.open", mock_open(read_data=mock_config_file)):
        with patch("pathlib.Path.exists", return_value=True):
            # 清除环境变量影响
            with patch.dict(os.environ, {}, clear=True):
                settings = get_settings()

                # 验证应用配置
                assert settings.name == "电商商品监控系统"
                assert settings.version == "1.0.0"
                assert settings.debug == False
                assert settings.host == "0.0.0.0"
                assert settings.port == 8000
            
            # 验证数据库配置
            assert "postgresql://" in settings.database.url
            assert settings.database.pool_size == 10
            assert settings.database.max_overflow == 20
            
            # 验证Redis配置
            assert "redis://" in settings.redis.url
            assert settings.redis.max_connections == 10
            
            # 验证Celery配置
            assert "redis://" in settings.celery.broker_url
            assert settings.celery.task_serializer == "json"
            
            # 验证安全配置
            assert settings.security.secret_key == "test-secret-key"
            assert settings.security.algorithm == "HS256"
            
            # 验证日志配置
            assert settings.logging.level == "INFO"
            assert settings.logging.file == "logs/app.log"
