/**
 * 系统设置页面
 */

import React, { useEffect, useState } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Select,
  InputNumber,
  Space,
  message,
  Spin,
  Tabs,
  Divider
} from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { systemApi } from '../../services/systemApi';

const { Option } = Select;
const { TabPane } = Tabs;

const SystemSettingsPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<any>(null);

  useEffect(() => {
    loadSystemConfig();
  }, []);

  const loadSystemConfig = async () => {
    try {
      setLoading(true);
      const response = await systemApi.getSystemConfig();
      setConfig(response.data);
      form.setFieldsValue(response.data);
    } catch (error: any) {
      message.error(`获取系统配置失败：${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setSaving(true);
      await systemApi.updateSystemConfig(values);
      message.success('系统配置保存成功');
      setConfig(values);
    } catch (error: any) {
      message.error(`保存配置失败：${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>系统设置</h2>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadSystemConfig}
            loading={loading}
          >
            刷新配置
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Tabs defaultActiveKey="basic">
          <TabPane tab="基本设置" key="basic">
            <Card>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  system_name: 'Moniit商品监控系统',
                  system_description: '智能商品价格监控与分析系统',
                  enable_notifications: true,
                  default_language: 'zh-CN',
                  timezone: 'Asia/Shanghai',
                }}
              >
                <Form.Item
                  label="系统名称"
                  name="system_name"
                  rules={[{ required: true, message: '请输入系统名称' }]}
                >
                  <Input />
                </Form.Item>

                <Form.Item
                  label="系统描述"
                  name="system_description"
                >
                  <Input.TextArea rows={3} />
                </Form.Item>

                <Form.Item
                  label="默认语言"
                  name="default_language"
                >
                  <Select>
                    <Option value="zh-CN">简体中文</Option>
                    <Option value="en-US">English</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  label="时区"
                  name="timezone"
                >
                  <Select>
                    <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                    <Option value="UTC">UTC</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  label="启用通知"
                  name="enable_notifications"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Divider />

                <Form.Item
                  label="默认监控间隔（分钟）"
                  name="default_monitor_interval"
                >
                  <InputNumber min={1} max={1440} placeholder="60" />
                </Form.Item>

                <Form.Item
                  label="最大并发监控任务"
                  name="max_concurrent_tasks"
                >
                  <InputNumber min={1} max={100} placeholder="10" />
                </Form.Item>

                <Form.Item
                  label="请求超时时间（秒）"
                  name="request_timeout"
                >
                  <InputNumber min={1} max={300} placeholder="30" />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={saving}
                    >
                      保存设置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane tab="日志设置" key="logs">
            <Card>
              <Form layout="vertical">
                <Form.Item
                  label="日志级别"
                  name="log_level"
                  initialValue="INFO"
                >
                  <Select>
                    <Option value="DEBUG">DEBUG</Option>
                    <Option value="INFO">INFO</Option>
                    <Option value="WARNING">WARNING</Option>
                    <Option value="ERROR">ERROR</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  label="日志保留天数"
                  name="log_retention_days"
                  initialValue={30}
                >
                  <InputNumber min={1} max={365} />
                </Form.Item>

                <Form.Item
                  label="启用文件日志"
                  name="enable_file_logging"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  label="启用数据库日志"
                  name="enable_db_logging"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch />
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane tab="安全设置" key="security">
            <Card>
              <Form layout="vertical">
                <Form.Item
                  label="JWT过期时间（小时）"
                  name="jwt_expiration_hours"
                  initialValue={24}
                >
                  <InputNumber min={1} max={168} />
                </Form.Item>

                <Form.Item
                  label="密码最小长度"
                  name="password_min_length"
                  initialValue={8}
                >
                  <InputNumber min={6} max={32} />
                </Form.Item>

                <Form.Item
                  label="启用双因子认证"
                  name="enable_2fa"
                  valuePropName="checked"
                  initialValue={false}
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  label="登录失败锁定次数"
                  name="max_login_attempts"
                  initialValue={5}
                >
                  <InputNumber min={3} max={10} />
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default SystemSettingsPage;
