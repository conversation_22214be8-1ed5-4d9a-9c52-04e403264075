/**
 * API客户端基础配置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse } from '../types';
import { store } from '../store';
import { refreshTokenAsync, clearAuth } from '../store/slices/authSlice';
import { addNotification } from '../store/slices/uiSlice';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || '';
const API_TIMEOUT = 30000; // 30秒超时

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const state = store.getState();
    const token = state.auth.token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId();

    // 记录请求日志（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 记录响应日志（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // 记录错误日志
    console.error('[API Response Error]', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data,
    });

    // 处理401未授权错误
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // 尝试刷新token
        const state = store.getState();
        if (state.auth.refreshToken) {
          await store.dispatch(refreshTokenAsync());
          
          // 重新发送原始请求
          const newToken = store.getState().auth.token;
          if (newToken && originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return apiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        // 刷新token失败，清除认证状态
        store.dispatch(clearAuth());
        
        // 跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        
        return Promise.reject(refreshError);
      }
    }

    // 处理网络错误
    if (!error.response) {
      store.dispatch(addNotification({
        type: 'error',
        title: '网络错误',
        message: '请检查网络连接或稍后重试',
        duration: 5000,
      }));
      return Promise.reject(new Error('网络连接失败'));
    }

    // 处理服务器错误
    const errorMessage = getErrorMessage(error);
    
    // 显示错误通知（除了某些特定的错误）
    if (!shouldSuppressErrorNotification(error)) {
      store.dispatch(addNotification({
        type: 'error',
        title: '请求失败',
        message: errorMessage,
        duration: 5000,
      }));
    }

    return Promise.reject(error);
  }
);

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 获取错误消息
function getErrorMessage(error: AxiosError): string {
  if (error.response?.data) {
    const data = error.response.data as any;
    if (data.message) {
      return data.message;
    }
    if (data.detail) {
      return data.detail;
    }
    if (data.error) {
      return data.error;
    }
  }

  switch (error.response?.status) {
    case 400:
      return '请求参数错误';
    case 401:
      return '未授权，请重新登录';
    case 403:
      return '权限不足';
    case 404:
      return '请求的资源不存在';
    case 422:
      return '数据验证失败';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器内部错误';
    case 502:
      return '网关错误';
    case 503:
      return '服务暂时不可用';
    case 504:
      return '请求超时';
    default:
      return error.message || '未知错误';
  }
}

// 判断是否应该抑制错误通知
function shouldSuppressErrorNotification(error: AxiosError): boolean {
  // 登录失败不显示通用错误通知
  if (error.config?.url?.includes('/auth/login')) {
    return true;
  }

  // 健康检查失败不显示通知
  if (error.config?.url?.includes('/health')) {
    return true;
  }

  // 获取当前用户信息失败不显示通知（可能是token过期）
  if (error.config?.url?.includes('/auth/me')) {
    return true;
  }

  return false;
}

// API响应包装器
export async function apiRequest<T = any>(
  config: AxiosRequestConfig
): Promise<ApiResponse<T>> {
  try {
    const response = await apiClient(config);

    // 检查响应数据是否已经是ApiResponse格式
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      // 已经是ApiResponse格式，直接返回
      return response.data;
    } else {
      // 不是ApiResponse格式，包装成ApiResponse格式
      return {
        success: true,
        data: response.data,
        message: '操作成功'
      };
    }
  } catch (error) {
    throw error;
  }
}

// 便捷方法
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ method: 'GET', url, ...config }),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ method: 'POST', url, data, ...config }),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ method: 'PUT', url, data, ...config }),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ method: 'PATCH', url, data, ...config }),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ method: 'DELETE', url, ...config }),
};

// 文件上传
export async function uploadFile(
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<ApiResponse> {
  const formData = new FormData();
  formData.append('file', file);

  return apiRequest({
    method: 'POST',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
}

// 文件下载
export async function downloadFile(
  url: string,
  filename?: string,
  config?: AxiosRequestConfig
): Promise<void> {
  try {
    const response = await apiClient({
      method: 'GET',
      url,
      responseType: 'blob',
      ...config,
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    
    // 从响应头获取文件名或使用提供的文件名
    const contentDisposition = response.headers['content-disposition'];
    if (contentDisposition && !filename) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('文件下载失败:', error);
    throw error;
  }
}

// 取消请求的token管理
export const cancelTokenSource = () => axios.CancelToken.source();

export default apiClient;
