#!/usr/bin/env python3
"""
供货商表迁移脚本

添加缺失的字段到suppliers表
"""

import asyncio
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import os


async def migrate_suppliers_table():
    """迁移suppliers表，添加缺失的字段"""
    print("🔄 开始迁移suppliers表...")
    
    # 数据库连接配置
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://moniit:moniit123@localhost:5432/moniit")
    
    # 创建异步引擎
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 检查suppliers表是否存在
            check_table_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'suppliers'
            );
            """
            
            result = await conn.execute(text(check_table_query))
            table_exists = result.scalar()
            
            if not table_exists:
                print("  📋 suppliers表不存在，创建新表...")
                create_table_query = """
                CREATE TABLE suppliers (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(200) NOT NULL,
                    contact_person VARCHAR(100),
                    phone VARCHAR(50),
                    email VARCHAR(200),
                    address TEXT,
                    payment_terms VARCHAR(200),
                    delivery_time INTEGER,
                    min_order_quantity INTEGER,
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    rating DECIMAL(3, 2),
                    notes TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                -- 添加注释
                COMMENT ON TABLE suppliers IS '供货商表';
                COMMENT ON COLUMN suppliers.name IS '供货商名称';
                COMMENT ON COLUMN suppliers.contact_person IS '联系人';
                COMMENT ON COLUMN suppliers.phone IS '电话';
                COMMENT ON COLUMN suppliers.email IS '邮箱';
                COMMENT ON COLUMN suppliers.address IS '地址';
                COMMENT ON COLUMN suppliers.payment_terms IS '付款条件';
                COMMENT ON COLUMN suppliers.delivery_time IS '交货时间(天)';
                COMMENT ON COLUMN suppliers.min_order_quantity IS '最小订货量';
                COMMENT ON COLUMN suppliers.is_active IS '是否激活';
                COMMENT ON COLUMN suppliers.rating IS '供货商评分';
                COMMENT ON COLUMN suppliers.notes IS '备注';
                
                -- 创建索引
                CREATE INDEX idx_suppliers_name ON suppliers(name);
                CREATE INDEX idx_suppliers_is_active ON suppliers(is_active);
                CREATE INDEX idx_suppliers_rating ON suppliers(rating);
                """
                
                await conn.execute(text(create_table_query))
                print("  ✅ suppliers表创建成功")
                
            else:
                print("  📋 suppliers表已存在，检查字段...")
                
                # 检查缺失的字段
                missing_columns = []
                required_columns = [
                    ('payment_terms', 'VARCHAR(200)'),
                    ('delivery_time', 'INTEGER'),
                    ('min_order_quantity', 'INTEGER'),
                    ('rating', 'DECIMAL(3, 2)'),
                    ('notes', 'TEXT')
                ]
                
                for column_name, column_type in required_columns:
                    check_column_query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'public' 
                        AND table_name = 'suppliers' 
                        AND column_name = :column_name
                    );
                    """
                    
                    result = await conn.execute(text(check_column_query), {"column_name": column_name})
                    column_exists = result.scalar()
                    
                    if not column_exists:
                        missing_columns.append((column_name, column_type))
                
                # 添加缺失的字段
                if missing_columns:
                    print(f"  ➕ 发现{len(missing_columns)}个缺失字段，开始添加...")
                    
                    for column_name, column_type in missing_columns:
                        add_column_query = f"""
                        ALTER TABLE suppliers 
                        ADD COLUMN {column_name} {column_type};
                        """
                        
                        await conn.execute(text(add_column_query))
                        print(f"    ✅ 添加字段: {column_name} ({column_type})")
                    
                    # 添加字段注释
                    comment_queries = [
                        "COMMENT ON COLUMN suppliers.payment_terms IS '付款条件';",
                        "COMMENT ON COLUMN suppliers.delivery_time IS '交货时间(天)';",
                        "COMMENT ON COLUMN suppliers.min_order_quantity IS '最小订货量';",
                        "COMMENT ON COLUMN suppliers.rating IS '供货商评分';",
                        "COMMENT ON COLUMN suppliers.notes IS '备注';"
                    ]
                    
                    for comment_query in comment_queries:
                        try:
                            await conn.execute(text(comment_query))
                        except Exception as e:
                            print(f"    ⚠️ 添加注释失败: {str(e)}")
                    
                    print("  ✅ 所有缺失字段添加完成")
                else:
                    print("  ✅ 所有必需字段都已存在")
            
            # 检查并创建索引
            print("  📊 检查索引...")
            index_queries = [
                "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);",
                "CREATE INDEX IF NOT EXISTS idx_suppliers_is_active ON suppliers(is_active);",
                "CREATE INDEX IF NOT EXISTS idx_suppliers_rating ON suppliers(rating);"
            ]
            
            for index_query in index_queries:
                try:
                    await conn.execute(text(index_query))
                except Exception as e:
                    print(f"    ⚠️ 创建索引失败: {str(e)}")
            
            print("  ✅ 索引检查完成")
            
        print("🎉 suppliers表迁移完成！")
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        raise
    finally:
        await engine.dispose()


async def verify_table_structure():
    """验证表结构"""
    print("\n🔍 验证suppliers表结构...")
    
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://moniit:moniit123@localhost:5432/moniit")
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 查询表结构
            structure_query = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'suppliers'
            ORDER BY ordinal_position;
            """
            
            result = await conn.execute(text(structure_query))
            columns = result.fetchall()
            
            print("  📋 suppliers表结构:")
            for column in columns:
                nullable = "NULL" if column.is_nullable == "YES" else "NOT NULL"
                default = f" DEFAULT {column.column_default}" if column.column_default else ""
                print(f"    - {column.column_name}: {column.data_type} {nullable}{default}")
            
            print(f"  ✅ 表结构验证完成，共{len(columns)}个字段")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
    finally:
        await engine.dispose()


async def main():
    """主函数"""
    print("🚀 开始suppliers表迁移...")
    print("🐳 目标: Docker环境中的PostgreSQL数据库")
    print("=" * 50)
    
    await migrate_suppliers_table()
    await verify_table_structure()
    
    print("\n" + "=" * 50)
    print("✅ 迁移完成！现在可以重新测试供货商API了。")


if __name__ == "__main__":
    asyncio.run(main())
