"""
批量翻译优化系统和翻译配置监控测试
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from app.services.translation.translation_engine import (
    TranslationEngine, TranslationRequest, LanguageCode, TextType
)
from app.services.translation.provider_manager import ProviderManager
from app.services.translation.quality_assessor import QualityAssessor
from app.services.translation.cache_manager import CacheManager, CacheType

from app.services.translation.batch_processor import (
    BatchProcessor, BatchJob, BatchItem, BatchStatus, BatchPriority
)
from app.services.translation.queue_manager import (
    PriorityQueueManager, QueueItem, QueuePriority, QueueStatus
)
from app.services.translation.progress_tracker import (
    ProgressTracker, TaskStatus, ProgressEvent
)
from app.services.translation.template_manager import (
    TemplateManager, PlatformType, TemplateType
)
from app.services.translation.prompt_manager import (
    PromptManager, PromptType, RuleType, RuleAction
)
from app.services.translation.monitoring_system import (
    MonitoringSystem, MetricType, AlertLevel, TimeRange
)


class TestBatchProcessor:
    """批量处理器测试"""
    
    @pytest.fixture
    def translation_engine(self):
        """翻译引擎"""
        provider_manager = ProviderManager()
        quality_assessor = QualityAssessor()
        cache_manager = CacheManager(CacheType.MEMORY)
        return TranslationEngine(provider_manager, quality_assessor, cache_manager)
    
    @pytest.fixture
    def batch_processor(self, translation_engine):
        """批量处理器"""
        return BatchProcessor(translation_engine)
    
    def test_initialization(self, batch_processor):
        """测试初始化"""
        assert batch_processor.translation_engine is not None
        assert len(batch_processor.batch_jobs) == 0
        assert batch_processor.processor_config["max_concurrent_jobs"] == 3
        assert batch_processor.processor_stats["total_jobs"] == 0
    
    @pytest.mark.asyncio
    async def test_create_batch_job(self, batch_processor):
        """测试创建批量作业"""
        # 创建批量项目
        items = [
            BatchItem(
                item_id=f"item_{i}",
                text=f"Test product {i}",
                source_lang=LanguageCode.ENGLISH,
                target_lang=LanguageCode.CHINESE,
                text_type=TextType.PRODUCT_TITLE,
                priority=i % 3 + 1
            )
            for i in range(5)
        ]
        
        # 创建批量作业
        job_id = await batch_processor.create_batch_job(
            name="测试批量作业",
            items=items,
            priority=BatchPriority.HIGH
        )
        
        assert job_id is not None
        assert job_id in batch_processor.batch_jobs
        
        batch_job = batch_processor.batch_jobs[job_id]
        assert batch_job.name == "测试批量作业"
        assert batch_job.priority == BatchPriority.HIGH
        assert len(batch_job.items) == 5
        assert batch_job.total_items == 5
        assert batch_job.status == BatchStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_start_batch_job(self, batch_processor):
        """测试启动批量作业"""
        # 创建批量作业
        items = [
            BatchItem(
                item_id="item_1",
                text="Test product",
                source_lang=LanguageCode.ENGLISH,
                target_lang=LanguageCode.CHINESE,
                text_type=TextType.PRODUCT_TITLE
            )
        ]
        
        job_id = await batch_processor.create_batch_job("测试作业", items)
        
        # 启动作业
        result = await batch_processor.start_batch_job(job_id)
        assert result is True
        
        batch_job = batch_processor.batch_jobs[job_id]
        assert batch_job.status == BatchStatus.PROCESSING
        assert batch_job.started_at is not None
        
        # 等待作业完成
        await asyncio.sleep(2)
        
        # 检查作业状态
        assert batch_job.status in [BatchStatus.COMPLETED, BatchStatus.PROCESSING]
    
    def test_get_processor_statistics(self, batch_processor):
        """测试获取处理器统计"""
        stats = batch_processor.get_processor_statistics()
        
        assert "total_jobs" in stats
        assert "completed_jobs" in stats
        assert "failed_jobs" in stats
        assert "active_jobs" in stats
        assert "success_rate" in stats
        assert "config" in stats


class TestPriorityQueueManager:
    """优先级队列管理器测试"""
    
    @pytest.fixture
    def translation_engine(self):
        """翻译引擎"""
        provider_manager = ProviderManager()
        quality_assessor = QualityAssessor()
        cache_manager = CacheManager(CacheType.MEMORY)
        return TranslationEngine(provider_manager, quality_assessor, cache_manager)
    
    @pytest.fixture
    def queue_manager(self, translation_engine):
        """队列管理器"""
        return PriorityQueueManager(translation_engine)
    
    def test_initialization(self, queue_manager):
        """测试初始化"""
        assert queue_manager.translation_engine is not None
        assert "default" in queue_manager.queues
        assert queue_manager.queue_status["default"] == QueueStatus.ACTIVE
    
    def test_create_queue(self, queue_manager):
        """测试创建队列"""
        result = queue_manager.create_queue("test_queue")
        assert result is True
        assert "test_queue" in queue_manager.queues
        assert queue_manager.queue_status["test_queue"] == QueueStatus.ACTIVE
    
    @pytest.mark.asyncio
    async def test_enqueue(self, queue_manager):
        """测试入队"""
        request = TranslationRequest(
            request_id="test_001",
            text="Test text",
            source_lang=LanguageCode.ENGLISH,
            target_lang=LanguageCode.CHINESE,
            text_type=TextType.GENERAL_TEXT
        )
        
        item_id = await queue_manager.enqueue(
            request=request,
            priority=QueuePriority.HIGH
        )
        
        assert item_id is not None
        assert len(queue_manager.queues["default"]) == 1
        
        stats = queue_manager.queue_stats["default"]
        assert stats.total_items == 1
        assert stats.pending_items == 1
    
    def test_pause_resume_queue(self, queue_manager):
        """测试暂停和恢复队列"""
        # 暂停队列
        result = queue_manager.pause_queue("default")
        assert result is True
        assert queue_manager.queue_status["default"] == QueueStatus.PAUSED
        
        # 恢复队列
        result = queue_manager.resume_queue("default")
        assert result is True
        assert queue_manager.queue_status["default"] == QueueStatus.ACTIVE
    
    def test_get_queue_info(self, queue_manager):
        """测试获取队列信息"""
        info = queue_manager.get_queue_info("default")
        
        assert info is not None
        assert info["name"] == "default"
        assert info["status"] == "active"
        assert "config" in info
        assert "stats" in info
        assert "workers" in info
    
    def test_get_all_queues_info(self, queue_manager):
        """测试获取所有队列信息"""
        info = queue_manager.get_all_queues_info()
        
        assert "queues" in info
        assert "global_stats" in info
        assert "config" in info
        assert "default" in info["queues"]


class TestProgressTracker:
    """进度跟踪器测试"""
    
    @pytest.fixture
    def progress_tracker(self):
        """进度跟踪器"""
        return ProgressTracker()
    
    def test_initialization(self, progress_tracker):
        """测试初始化"""
        assert len(progress_tracker.tasks) == 0
        assert progress_tracker.config["update_interval"] == 1.0
        assert progress_tracker.global_stats["total_tasks"] == 0
    
    def test_create_task(self, progress_tracker):
        """测试创建任务"""
        task_id = progress_tracker.create_task(
            name="测试任务",
            total_items=100,
            description="测试任务描述"
        )
        
        assert task_id is not None
        assert task_id in progress_tracker.tasks
        
        task_info = progress_tracker.tasks[task_id]
        assert task_info.name == "测试任务"
        assert task_info.progress.total == 100
        assert task_info.status == TaskStatus.PENDING
    
    def test_start_task(self, progress_tracker):
        """测试启动任务"""
        task_id = progress_tracker.create_task("测试任务", 100)
        
        result = progress_tracker.start_task(task_id)
        assert result is True
        
        task_info = progress_tracker.tasks[task_id]
        assert task_info.status == TaskStatus.RUNNING
        assert task_info.metrics.start_time is not None
    
    def test_update_progress(self, progress_tracker):
        """测试更新进度"""
        task_id = progress_tracker.create_task("测试任务", 100)
        progress_tracker.start_task(task_id)
        
        result = progress_tracker.update_progress(task_id, 50)
        assert result is True
        
        task_info = progress_tracker.tasks[task_id]
        assert task_info.progress.current == 50
        assert task_info.progress.percentage == 50.0
    
    def test_complete_task(self, progress_tracker):
        """测试完成任务"""
        task_id = progress_tracker.create_task("测试任务", 100)
        progress_tracker.start_task(task_id)
        
        result = progress_tracker.complete_task(task_id, success=True)
        assert result is True
        
        task_info = progress_tracker.tasks[task_id]
        assert task_info.status == TaskStatus.COMPLETED
        assert task_info.progress.percentage == 100.0
        assert task_info.metrics.end_time is not None
    
    def test_get_statistics(self, progress_tracker):
        """测试获取统计信息"""
        stats = progress_tracker.get_statistics()
        
        assert "global_stats" in stats
        assert "current_stats" in stats
        assert "config" in stats


class TestTemplateManager:
    """模板管理器测试"""
    
    @pytest.fixture
    def template_manager(self):
        """模板管理器"""
        return TemplateManager()
    
    def test_initialization(self, template_manager):
        """测试初始化"""
        assert len(template_manager.templates) >= 2  # 至少有默认模板
        assert template_manager.config["auto_save"] is True
        assert template_manager.stats["total_templates"] >= 2
    
    def test_create_template(self, template_manager):
        """测试创建模板"""
        template_id = template_manager.create_template(
            name="测试模板",
            platform=PlatformType.AMAZON,
            template_type=TemplateType.PRODUCT_TITLE,
            source_lang="en",
            target_lang="zh",
            system_prompt="测试系统提示词",
            description="测试模板描述"
        )
        
        assert template_id is not None
        assert template_id in template_manager.templates
        
        template = template_manager.templates[template_id]
        assert template.name == "测试模板"
        assert template.platform == PlatformType.AMAZON
        assert template.template_type == TemplateType.PRODUCT_TITLE
    
    def test_find_templates(self, template_manager):
        """测试查找模板"""
        templates = template_manager.find_templates(
            platform=PlatformType.AMAZON,
            template_type=TemplateType.PRODUCT_TITLE,
            source_lang="en",
            target_lang="zh"
        )
        
        assert len(templates) >= 1
        for template in templates:
            assert template.platform == PlatformType.AMAZON
            assert template.template_type == TemplateType.PRODUCT_TITLE
            assert template.source_lang == "en"
            assert template.target_lang == "zh"
    
    def test_get_best_template(self, template_manager):
        """测试获取最佳模板"""
        template = template_manager.get_best_template(
            platform=PlatformType.AMAZON,
            template_type=TemplateType.PRODUCT_TITLE,
            source_lang="en",
            target_lang="zh"
        )
        
        assert template is not None
        assert template.template_type == TemplateType.PRODUCT_TITLE
    
    def test_apply_template(self, template_manager):
        """测试应用模板"""
        template = template_manager.get_best_template(
            platform=PlatformType.AMAZON,
            template_type=TemplateType.PRODUCT_TITLE,
            source_lang="en",
            target_lang="zh"
        )
        
        result = template_manager.apply_template(template, "Test product title")
        
        assert "template_id" in result
        assert "original_text" in result
        assert "processed_text" in result
        assert "system_prompt" in result
        assert result["original_text"] == "Test product title"
    
    def test_get_statistics(self, template_manager):
        """测试获取统计信息"""
        stats = template_manager.get_statistics()
        
        assert "basic_stats" in stats
        assert "platform_distribution" in stats
        assert "type_distribution" in stats
        assert "language_pairs" in stats


class TestPromptManager:
    """提示词管理器测试"""
    
    @pytest.fixture
    def prompt_manager(self):
        """提示词管理器"""
        return PromptManager()
    
    def test_initialization(self, prompt_manager):
        """测试初始化"""
        assert len(prompt_manager.prompts) >= 2  # 至少有默认提示词
        assert len(prompt_manager.rules) >= 1   # 至少有默认规则
        assert len(prompt_manager.terminology) >= 1  # 至少有默认术语
    
    def test_create_prompt(self, prompt_manager):
        """测试创建提示词"""
        prompt_id = prompt_manager.create_prompt(
            name="测试提示词",
            prompt_type=PromptType.SYSTEM,
            content="你是一个专业的翻译专家，请翻译以下内容：{text}",
            description="测试提示词描述"
        )
        
        assert prompt_id is not None
        assert prompt_id in prompt_manager.prompts
        
        prompt = prompt_manager.prompts[prompt_id]
        assert prompt.name == "测试提示词"
        assert prompt.prompt_type == PromptType.SYSTEM
        assert "text" in prompt.variables
    
    def test_render_prompt(self, prompt_manager):
        """测试渲染提示词"""
        prompt_id = prompt_manager.create_prompt(
            name="测试模板",
            prompt_type=PromptType.USER,
            content="请翻译：{text}，从{source_lang}到{target_lang}"
        )
        
        rendered = prompt_manager.render_prompt(prompt_id, {
            "text": "Hello world",
            "source_lang": "英文",
            "target_lang": "中文"
        })
        
        assert rendered == "请翻译：Hello world，从英文到中文"
    
    def test_create_rule(self, prompt_manager):
        """测试创建规则"""
        rule_id = prompt_manager.create_rule(
            name="测试规则",
            rule_type=RuleType.PREPROCESSING,
            action=RuleAction.REPLACE,
            pattern="test",
            replacement="测试",
            description="测试规则描述"
        )
        
        assert rule_id is not None
        assert rule_id in prompt_manager.rules
        
        rule = prompt_manager.rules[rule_id]
        assert rule.name == "测试规则"
        assert rule.rule_type == RuleType.PREPROCESSING
        assert rule.action == RuleAction.REPLACE
    
    def test_apply_rules(self, prompt_manager):
        """测试应用规则"""
        # 创建测试规则
        prompt_manager.create_rule(
            name="替换规则",
            rule_type=RuleType.PREPROCESSING,
            action=RuleAction.REPLACE,
            pattern="hello",
            replacement="你好"
        )
        
        processed_text, applied_rules = prompt_manager.apply_rules(
            "hello world", RuleType.PREPROCESSING
        )
        
        assert "你好" in processed_text
        assert len(applied_rules) > 0
    
    def test_add_terminology(self, prompt_manager):
        """测试添加术语"""
        term_id = prompt_manager.add_terminology(
            source_term="computer",
            target_term="计算机",
            source_lang="en",
            target_lang="zh",
            domain="technology"
        )
        
        assert term_id is not None
        assert term_id in prompt_manager.terminology
        
        term = prompt_manager.terminology[term_id]
        assert term.source_term == "computer"
        assert term.target_term == "计算机"
    
    def test_apply_terminology(self, prompt_manager):
        """测试应用术语"""
        # 添加测试术语
        prompt_manager.add_terminology(
            source_term="laptop",
            target_term="笔记本电脑",
            source_lang="en",
            target_lang="zh"
        )
        
        processed_text, applied_terms = prompt_manager.apply_terminology(
            "I have a laptop computer", "en", "zh"
        )
        
        assert "笔记本电脑" in processed_text
        assert len(applied_terms) > 0
    
    def test_get_statistics(self, prompt_manager):
        """测试获取统计信息"""
        stats = prompt_manager.get_statistics()
        
        assert "basic_stats" in stats
        assert "prompt_types" in stats
        assert "rule_types" in stats
        assert "terminology_domains" in stats


class TestMonitoringSystem:
    """监控系统测试"""
    
    @pytest.fixture
    def monitoring_system(self):
        """监控系统"""
        return MonitoringSystem()
    
    def test_initialization(self, monitoring_system):
        """测试初始化"""
        assert len(monitoring_system.metrics) == 0
        assert len(monitoring_system.alerts) == 0
        assert monitoring_system.config["data_retention_days"] == 90
    
    def test_record_metric(self, monitoring_system):
        """测试记录指标"""
        monitoring_system.record_metric(
            metric_type=MetricType.VOLUME,
            name="translations_count",
            value=10,
            unit="count",
            tags={"provider": "openai"}
        )
        
        assert len(monitoring_system.metrics) == 1
        
        metric = monitoring_system.metrics[0]
        assert metric.metric_type == MetricType.VOLUME
        assert metric.name == "translations_count"
        assert metric.value == 10
        assert metric.tags["provider"] == "openai"
    
    def test_get_metrics(self, monitoring_system):
        """测试获取指标"""
        # 记录测试指标
        monitoring_system.record_metric(MetricType.COST, "translation_cost", 5.0)
        monitoring_system.record_metric(MetricType.QUALITY, "quality_score", 8.5)
        
        # 获取所有指标
        all_metrics = monitoring_system.get_metrics()
        assert len(all_metrics) == 2
        
        # 按类型过滤
        cost_metrics = monitoring_system.get_metrics(metric_type=MetricType.COST)
        assert len(cost_metrics) == 1
        assert cost_metrics[0].name == "translation_cost"
    
    def test_get_aggregated_metrics(self, monitoring_system):
        """测试获取聚合指标"""
        # 记录多个指标
        for i in range(5):
            monitoring_system.record_metric(MetricType.VOLUME, "translations_count", i + 1)
        
        # 获取聚合结果
        result = monitoring_system.get_aggregated_metrics(
            MetricType.VOLUME, "translations_count", TimeRange.DAY, "sum"
        )
        
        assert result["value"] == 15  # 1+2+3+4+5
        assert result["count"] == 5
    
    def test_create_alert(self, monitoring_system):
        """测试创建告警"""
        alert_id = monitoring_system.create_alert(
            level=AlertLevel.WARNING,
            title="测试告警",
            message="这是一个测试告警",
            metric_type=MetricType.COST,
            threshold_value=10.0,
            current_value=15.0
        )
        
        assert alert_id is not None
        assert alert_id in monitoring_system.alerts
        
        alert = monitoring_system.alerts[alert_id]
        assert alert.level == AlertLevel.WARNING
        assert alert.title == "测试告警"
        assert not alert.resolved
    
    def test_resolve_alert(self, monitoring_system):
        """测试解决告警"""
        alert_id = monitoring_system.create_alert(
            AlertLevel.INFO, "测试", "测试", MetricType.VOLUME, 1.0, 2.0
        )
        
        result = monitoring_system.resolve_alert(alert_id)
        assert result is True
        
        alert = monitoring_system.alerts[alert_id]
        assert alert.resolved is True
        assert alert.resolved_at is not None
    
    def test_generate_report(self, monitoring_system):
        """测试生成报告"""
        # 记录一些测试数据
        monitoring_system.record_metric(MetricType.VOLUME, "translations_count", 100)
        monitoring_system.record_metric(MetricType.COST, "translation_cost", 25.0)
        monitoring_system.record_metric(MetricType.QUALITY, "quality_score", 8.5)
        
        report_id = monitoring_system.generate_report("daily", TimeRange.DAY)
        
        assert report_id is not None
        assert report_id in monitoring_system.reports
        
        report = monitoring_system.reports[report_id]
        assert report.report_type == "daily"
        assert report.time_range == TimeRange.DAY
        assert "summary" in report.data
        assert "volume_stats" in report.data
    
    def test_get_dashboard_data(self, monitoring_system):
        """测试获取仪表板数据"""
        # 记录一些测试数据
        monitoring_system.record_metric(MetricType.VOLUME, "translations_count", 50)
        monitoring_system.record_metric(MetricType.COST, "translation_cost", 12.5)
        
        dashboard_data = monitoring_system.get_dashboard_data()
        
        assert "overview" in dashboard_data
        assert "hourly_trends" in dashboard_data
        assert "recent_alerts" in dashboard_data
        assert "system_health" in dashboard_data
