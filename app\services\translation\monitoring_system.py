"""
翻译统计和监控系统

实现翻译统计和成本监控，翻译量统计、成本分析、质量报告
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import statistics
import os

from app.core.logging import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """指标类型"""
    VOLUME = "volume"           # 翻译量
    COST = "cost"              # 成本
    QUALITY = "quality"         # 质量
    PERFORMANCE = "performance" # 性能
    ERROR = "error"            # 错误


class TimeRange(Enum):
    """时间范围"""
    HOUR = "hour"              # 小时
    DAY = "day"                # 天
    WEEK = "week"              # 周
    MONTH = "month"            # 月
    YEAR = "year"              # 年


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"              # 信息
    WARNING = "warning"        # 警告
    ERROR = "error"            # 错误
    CRITICAL = "critical"      # 严重


@dataclass
class MetricData:
    """指标数据"""
    metric_id: str
    metric_type: MetricType
    name: str
    value: float
    unit: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Alert:
    """告警"""
    alert_id: str
    level: AlertLevel
    title: str
    message: str
    metric_type: MetricType
    threshold_value: float
    current_value: float
    triggered_at: datetime = field(default_factory=datetime.now)
    resolved_at: Optional[datetime] = None
    resolved: bool = False
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class Report:
    """报告"""
    report_id: str
    title: str
    report_type: str
    time_range: TimeRange
    start_time: datetime
    end_time: datetime
    data: Dict[str, Any] = field(default_factory=dict)
    generated_at: datetime = field(default_factory=datetime.now)
    generated_by: str = "system"


class MonitoringSystem:
    """翻译统计和监控系统"""
    
    def __init__(self, data_dir: str = "monitoring_data"):
        self.data_dir = data_dir
        
        # 数据存储
        self.metrics: List[MetricData] = []
        self.alerts: Dict[str, Alert] = {}
        self.reports: Dict[str, Report] = {}
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "metrics"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "alerts"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "reports"), exist_ok=True)
        
        # 监控配置
        self.config = {
            "data_retention_days": 90,      # 数据保留天数
            "metric_collection_interval": 60,  # 指标收集间隔（秒）
            "alert_check_interval": 300,    # 告警检查间隔（秒）
            "auto_report_enabled": True,    # 自动报告
            "daily_report_time": "09:00",   # 日报时间
            "weekly_report_day": 1,         # 周报日期（周一）
            "monthly_report_day": 1,        # 月报日期
            "max_metrics_in_memory": 10000, # 内存中最大指标数
            "enable_real_time_alerts": True, # 实时告警
            "alert_cooldown_minutes": 30    # 告警冷却时间
        }
        
        # 告警阈值
        self.alert_thresholds = {
            "cost_per_hour": 10.0,          # 每小时成本阈值
            "cost_per_day": 100.0,          # 每日成本阈值
            "error_rate": 5.0,              # 错误率阈值（%）
            "quality_score_min": 6.0,       # 最低质量评分
            "processing_time_max": 30.0,    # 最大处理时间（秒）
            "queue_length_max": 1000        # 最大队列长度
        }
        
        # 统计缓存
        self.stats_cache = {
            "last_update": None,
            "cache_duration": 300,  # 缓存5分钟
            "cached_stats": {}
        }
        
        # 后台任务
        self.collection_task: Optional[asyncio.Task] = None
        self.alert_task: Optional[asyncio.Task] = None
        self.report_task: Optional[asyncio.Task] = None
        
        # 启动监控
        self._start_monitoring()
    
    def record_metric(self, metric_type: MetricType, name: str, value: float,
                     unit: str = "", tags: Optional[Dict[str, str]] = None,
                     metadata: Optional[Dict[str, Any]] = None):
        """
        记录指标
        
        Args:
            metric_type: 指标类型
            name: 指标名称
            value: 指标值
            unit: 单位
            tags: 标签
            metadata: 元数据
        """
        try:
            metric_id = f"{metric_type.value}_{name}_{datetime.now().timestamp()}"
            
            metric = MetricData(
                metric_id=metric_id,
                metric_type=metric_type,
                name=name,
                value=value,
                unit=unit,
                tags=tags or {},
                metadata=metadata or {}
            )
            
            # 添加到内存
            self.metrics.append(metric)
            
            # 检查内存限制
            if len(self.metrics) > self.config["max_metrics_in_memory"]:
                # 移除最旧的指标
                self.metrics = self.metrics[-self.config["max_metrics_in_memory"]:]
            
            # 实时告警检查
            if self.config["enable_real_time_alerts"]:
                self._check_metric_alerts(metric)
            
            # 保存到磁盘（异步）
            try:
                asyncio.create_task(self._save_metric_to_disk(metric))
            except RuntimeError:
                # 没有运行的事件循环，跳过异步保存
                pass
            
        except Exception as e:
            logger.error(f"记录指标失败: {e}")
    
    def get_metrics(self, metric_type: Optional[MetricType] = None,
                   name: Optional[str] = None,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   tags: Optional[Dict[str, str]] = None) -> List[MetricData]:
        """
        获取指标数据
        
        Args:
            metric_type: 指标类型
            name: 指标名称
            start_time: 开始时间
            end_time: 结束时间
            tags: 标签过滤
        
        Returns:
            List[MetricData]: 匹配的指标列表
        """
        try:
            filtered_metrics = self.metrics.copy()
            
            # 过滤条件
            if metric_type:
                filtered_metrics = [m for m in filtered_metrics if m.metric_type == metric_type]
            
            if name:
                filtered_metrics = [m for m in filtered_metrics if m.name == name]
            
            if start_time:
                filtered_metrics = [m for m in filtered_metrics if m.timestamp >= start_time]
            
            if end_time:
                filtered_metrics = [m for m in filtered_metrics if m.timestamp <= end_time]
            
            if tags:
                filtered_metrics = [
                    m for m in filtered_metrics
                    if all(m.tags.get(k) == v for k, v in tags.items())
                ]
            
            # 按时间排序
            filtered_metrics.sort(key=lambda m: m.timestamp)
            
            return filtered_metrics
            
        except Exception as e:
            logger.error(f"获取指标数据失败: {e}")
            return []
    
    def get_aggregated_metrics(self, metric_type: MetricType, name: str,
                              time_range: TimeRange,
                              aggregation: str = "sum") -> Dict[str, float]:
        """
        获取聚合指标
        
        Args:
            metric_type: 指标类型
            name: 指标名称
            time_range: 时间范围
            aggregation: 聚合方式（sum, avg, min, max, count）
        
        Returns:
            Dict[str, float]: 聚合结果
        """
        try:
            # 计算时间范围
            end_time = datetime.now()
            if time_range == TimeRange.HOUR:
                start_time = end_time - timedelta(hours=1)
            elif time_range == TimeRange.DAY:
                start_time = end_time - timedelta(days=1)
            elif time_range == TimeRange.WEEK:
                start_time = end_time - timedelta(weeks=1)
            elif time_range == TimeRange.MONTH:
                start_time = end_time - timedelta(days=30)
            elif time_range == TimeRange.YEAR:
                start_time = end_time - timedelta(days=365)
            else:
                start_time = end_time - timedelta(days=1)
            
            # 获取指标数据
            metrics = self.get_metrics(
                metric_type=metric_type,
                name=name,
                start_time=start_time,
                end_time=end_time
            )
            
            if not metrics:
                return {"value": 0.0, "count": 0}
            
            values = [m.value for m in metrics]
            
            # 聚合计算
            result = {"count": len(values)}
            
            if aggregation == "sum":
                result["value"] = sum(values)
            elif aggregation == "avg":
                result["value"] = statistics.mean(values)
            elif aggregation == "min":
                result["value"] = min(values)
            elif aggregation == "max":
                result["value"] = max(values)
            elif aggregation == "count":
                result["value"] = len(values)
            else:
                result["value"] = sum(values)
            
            return result
            
        except Exception as e:
            logger.error(f"获取聚合指标失败: {e}")
            return {"value": 0.0, "count": 0}
    
    def create_alert(self, level: AlertLevel, title: str, message: str,
                    metric_type: MetricType, threshold_value: float,
                    current_value: float, tags: Optional[Dict[str, str]] = None) -> str:
        """
        创建告警
        
        Args:
            level: 告警级别
            title: 告警标题
            message: 告警消息
            metric_type: 指标类型
            threshold_value: 阈值
            current_value: 当前值
            tags: 标签
        
        Returns:
            str: 告警ID
        """
        try:
            alert_id = f"alert_{datetime.now().timestamp()}"
            
            alert = Alert(
                alert_id=alert_id,
                level=level,
                title=title,
                message=message,
                metric_type=metric_type,
                threshold_value=threshold_value,
                current_value=current_value,
                tags=tags or {}
            )
            
            # 存储告警
            self.alerts[alert_id] = alert
            
            # 保存到磁盘
            try:
                asyncio.create_task(self._save_alert_to_disk(alert))
            except RuntimeError:
                # 没有运行的事件循环，跳过异步保存
                pass
            
            logger.warning(f"创建告警: {level.value} - {title}")
            return alert_id
            
        except Exception as e:
            logger.error(f"创建告警失败: {e}")
            return ""
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        try:
            if alert_id not in self.alerts:
                return False
            
            alert = self.alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            # 保存到磁盘
            try:
                asyncio.create_task(self._save_alert_to_disk(alert))
            except RuntimeError:
                # 没有运行的事件循环，跳过异步保存
                pass
            
            logger.info(f"解决告警: {alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"解决告警失败: {alert_id}, {e}")
            return False
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        try:
            active_alerts = [alert for alert in self.alerts.values() if not alert.resolved]
            active_alerts.sort(key=lambda a: a.triggered_at, reverse=True)
            return active_alerts
        except Exception as e:
            logger.error(f"获取活跃告警失败: {e}")
            return []
    
    def generate_report(self, report_type: str, time_range: TimeRange,
                       title: Optional[str] = None) -> str:
        """
        生成报告
        
        Args:
            report_type: 报告类型
            time_range: 时间范围
            title: 报告标题
        
        Returns:
            str: 报告ID
        """
        try:
            report_id = f"report_{report_type}_{datetime.now().timestamp()}"
            
            # 计算时间范围
            end_time = datetime.now()
            if time_range == TimeRange.DAY:
                start_time = end_time - timedelta(days=1)
                default_title = "日报"
            elif time_range == TimeRange.WEEK:
                start_time = end_time - timedelta(weeks=1)
                default_title = "周报"
            elif time_range == TimeRange.MONTH:
                start_time = end_time - timedelta(days=30)
                default_title = "月报"
            else:
                start_time = end_time - timedelta(days=1)
                default_title = "报告"
            
            # 生成报告数据
            report_data = self._generate_report_data(report_type, start_time, end_time)
            
            # 创建报告
            report = Report(
                report_id=report_id,
                title=title or f"翻译服务{default_title}",
                report_type=report_type,
                time_range=time_range,
                start_time=start_time,
                end_time=end_time,
                data=report_data
            )
            
            # 存储报告
            self.reports[report_id] = report
            
            # 保存到磁盘
            try:
                asyncio.create_task(self._save_report_to_disk(report))
            except RuntimeError:
                # 没有运行的事件循环，跳过异步保存
                pass
            
            logger.info(f"生成报告: {report_id} - {report.title}")
            return report_id
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return ""
    
    def _generate_report_data(self, report_type: str, start_time: datetime, 
                             end_time: datetime) -> Dict[str, Any]:
        """生成报告数据"""
        try:
            data = {
                "summary": {},
                "volume_stats": {},
                "cost_stats": {},
                "quality_stats": {},
                "performance_stats": {},
                "error_stats": {},
                "trends": {},
                "recommendations": []
            }
            
            # 翻译量统计
            volume_metrics = self.get_metrics(
                metric_type=MetricType.VOLUME,
                start_time=start_time,
                end_time=end_time
            )
            
            if volume_metrics:
                total_translations = sum(m.value for m in volume_metrics if m.name == "translations_count")
                total_characters = sum(m.value for m in volume_metrics if m.name == "characters_translated")
                
                data["volume_stats"] = {
                    "total_translations": total_translations,
                    "total_characters": total_characters,
                    "avg_characters_per_translation": total_characters / max(total_translations, 1)
                }
            
            # 成本统计
            cost_metrics = self.get_metrics(
                metric_type=MetricType.COST,
                start_time=start_time,
                end_time=end_time
            )
            
            if cost_metrics:
                total_cost = sum(m.value for m in cost_metrics if m.name == "translation_cost")
                
                data["cost_stats"] = {
                    "total_cost": total_cost,
                    "avg_cost_per_translation": total_cost / max(data["volume_stats"].get("total_translations", 1), 1),
                    "cost_per_character": total_cost / max(data["volume_stats"].get("total_characters", 1), 1)
                }
            
            # 质量统计
            quality_metrics = self.get_metrics(
                metric_type=MetricType.QUALITY,
                start_time=start_time,
                end_time=end_time
            )
            
            if quality_metrics:
                quality_scores = [m.value for m in quality_metrics if m.name == "quality_score"]
                if quality_scores:
                    data["quality_stats"] = {
                        "avg_quality_score": statistics.mean(quality_scores),
                        "min_quality_score": min(quality_scores),
                        "max_quality_score": max(quality_scores),
                        "quality_score_std": statistics.stdev(quality_scores) if len(quality_scores) > 1 else 0
                    }
            
            # 性能统计
            performance_metrics = self.get_metrics(
                metric_type=MetricType.PERFORMANCE,
                start_time=start_time,
                end_time=end_time
            )
            
            if performance_metrics:
                processing_times = [m.value for m in performance_metrics if m.name == "processing_time"]
                if processing_times:
                    data["performance_stats"] = {
                        "avg_processing_time": statistics.mean(processing_times),
                        "min_processing_time": min(processing_times),
                        "max_processing_time": max(processing_times),
                        "processing_time_std": statistics.stdev(processing_times) if len(processing_times) > 1 else 0
                    }
            
            # 错误统计
            error_metrics = self.get_metrics(
                metric_type=MetricType.ERROR,
                start_time=start_time,
                end_time=end_time
            )
            
            if error_metrics:
                total_errors = sum(m.value for m in error_metrics if m.name == "error_count")
                error_rate = (total_errors / max(data["volume_stats"].get("total_translations", 1), 1)) * 100
                
                data["error_stats"] = {
                    "total_errors": total_errors,
                    "error_rate": error_rate
                }
            
            # 汇总信息
            data["summary"] = {
                "time_range": f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                "total_translations": data["volume_stats"].get("total_translations", 0),
                "total_cost": data["cost_stats"].get("total_cost", 0),
                "avg_quality_score": data["quality_stats"].get("avg_quality_score", 0),
                "avg_processing_time": data["performance_stats"].get("avg_processing_time", 0),
                "error_rate": data["error_stats"].get("error_rate", 0)
            }
            
            # 生成建议
            data["recommendations"] = self._generate_recommendations(data)
            
            return data
            
        except Exception as e:
            logger.error(f"生成报告数据失败: {e}")
            return {}
    
    def _generate_recommendations(self, report_data: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        try:
            # 成本建议
            cost_per_char = report_data.get("cost_stats", {}).get("cost_per_character", 0)
            if cost_per_char > 0.001:
                recommendations.append("翻译成本较高，建议优化提供商选择或增加缓存使用")
            
            # 质量建议
            avg_quality = report_data.get("quality_stats", {}).get("avg_quality_score", 10)
            if avg_quality < 7.0:
                recommendations.append("翻译质量偏低，建议调整翻译模板或提示词")
            
            # 性能建议
            avg_time = report_data.get("performance_stats", {}).get("avg_processing_time", 0)
            if avg_time > 10.0:
                recommendations.append("处理时间较长，建议增加并发数或优化翻译流程")
            
            # 错误率建议
            error_rate = report_data.get("error_stats", {}).get("error_rate", 0)
            if error_rate > 5.0:
                recommendations.append("错误率较高，建议检查提供商配置和网络连接")
            
            if not recommendations:
                recommendations.append("系统运行正常，各项指标均在合理范围内")
            
        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            recommendations.append("建议生成失败，请检查系统状态")
        
        return recommendations
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        try:
            # 检查缓存
            if (self.stats_cache["last_update"] and 
                datetime.now() - self.stats_cache["last_update"] < timedelta(seconds=self.stats_cache["cache_duration"])):
                return self.stats_cache["cached_stats"]
            
            # 生成仪表板数据
            dashboard_data = {
                "overview": {
                    "total_translations_today": self.get_aggregated_metrics(
                        MetricType.VOLUME, "translations_count", TimeRange.DAY, "sum"
                    )["value"],
                    "total_cost_today": self.get_aggregated_metrics(
                        MetricType.COST, "translation_cost", TimeRange.DAY, "sum"
                    )["value"],
                    "avg_quality_today": self.get_aggregated_metrics(
                        MetricType.QUALITY, "quality_score", TimeRange.DAY, "avg"
                    )["value"],
                    "active_alerts": len(self.get_active_alerts())
                },
                "hourly_trends": {
                    "translations": self._get_hourly_trend(MetricType.VOLUME, "translations_count"),
                    "cost": self._get_hourly_trend(MetricType.COST, "translation_cost"),
                    "quality": self._get_hourly_trend(MetricType.QUALITY, "quality_score")
                },
                "recent_alerts": self.get_active_alerts()[:5],
                "system_health": {
                    "error_rate": self.get_aggregated_metrics(
                        MetricType.ERROR, "error_count", TimeRange.HOUR, "sum"
                    )["value"],
                    "avg_processing_time": self.get_aggregated_metrics(
                        MetricType.PERFORMANCE, "processing_time", TimeRange.HOUR, "avg"
                    )["value"]
                }
            }
            
            # 更新缓存
            self.stats_cache["last_update"] = datetime.now()
            self.stats_cache["cached_stats"] = dashboard_data
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"获取仪表板数据失败: {e}")
            return {}
    
    def _get_hourly_trend(self, metric_type: MetricType, name: str) -> List[Dict[str, Any]]:
        """获取小时趋势数据"""
        try:
            trend_data = []
            now = datetime.now()
            
            for i in range(24):
                hour_start = now - timedelta(hours=i+1)
                hour_end = now - timedelta(hours=i)
                
                metrics = self.get_metrics(
                    metric_type=metric_type,
                    name=name,
                    start_time=hour_start,
                    end_time=hour_end
                )
                
                if metric_type == MetricType.QUALITY:
                    # 质量指标使用平均值
                    value = statistics.mean([m.value for m in metrics]) if metrics else 0
                else:
                    # 其他指标使用总和
                    value = sum(m.value for m in metrics)
                
                trend_data.append({
                    "hour": hour_start.strftime("%H:00"),
                    "value": value
                })
            
            return list(reversed(trend_data))
            
        except Exception as e:
            logger.error(f"获取小时趋势数据失败: {e}")
            return []
    
    def _check_metric_alerts(self, metric: MetricData):
        """检查指标告警"""
        try:
            # 成本告警
            if metric.metric_type == MetricType.COST and metric.name == "translation_cost":
                hourly_cost = self.get_aggregated_metrics(
                    MetricType.COST, "translation_cost", TimeRange.HOUR, "sum"
                )["value"]
                
                if hourly_cost > self.alert_thresholds["cost_per_hour"]:
                    self.create_alert(
                        AlertLevel.WARNING,
                        "小时成本超标",
                        f"当前小时翻译成本 ${hourly_cost:.2f} 超过阈值 ${self.alert_thresholds['cost_per_hour']:.2f}",
                        MetricType.COST,
                        self.alert_thresholds["cost_per_hour"],
                        hourly_cost
                    )
            
            # 质量告警
            elif metric.metric_type == MetricType.QUALITY and metric.name == "quality_score":
                if metric.value < self.alert_thresholds["quality_score_min"]:
                    self.create_alert(
                        AlertLevel.WARNING,
                        "翻译质量偏低",
                        f"翻译质量评分 {metric.value:.2f} 低于阈值 {self.alert_thresholds['quality_score_min']:.2f}",
                        MetricType.QUALITY,
                        self.alert_thresholds["quality_score_min"],
                        metric.value
                    )
            
            # 性能告警
            elif metric.metric_type == MetricType.PERFORMANCE and metric.name == "processing_time":
                if metric.value > self.alert_thresholds["processing_time_max"]:
                    self.create_alert(
                        AlertLevel.WARNING,
                        "处理时间过长",
                        f"翻译处理时间 {metric.value:.2f}秒 超过阈值 {self.alert_thresholds['processing_time_max']:.2f}秒",
                        MetricType.PERFORMANCE,
                        self.alert_thresholds["processing_time_max"],
                        metric.value
                    )
            
        except Exception as e:
            logger.error(f"检查指标告警失败: {e}")
    
    def _start_monitoring(self):
        """启动监控"""
        try:
            self.collection_task = asyncio.create_task(self._metric_collection_loop())
            self.alert_task = asyncio.create_task(self._alert_check_loop())
            if self.config["auto_report_enabled"]:
                self.report_task = asyncio.create_task(self._auto_report_loop())
        except RuntimeError:
            # 没有运行的事件循环，跳过启动监控
            logger.warning("没有运行的事件循环，跳过启动监控")
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
    
    async def _metric_collection_loop(self):
        """指标收集循环"""
        while True:
            try:
                await asyncio.sleep(self.config["metric_collection_interval"])
                # 这里可以添加定期收集的指标
                await self._collect_system_metrics()
            except Exception as e:
                logger.error(f"指标收集循环异常: {e}")
    
    async def _alert_check_loop(self):
        """告警检查循环"""
        while True:
            try:
                await asyncio.sleep(self.config["alert_check_interval"])
                await self._check_system_alerts()
            except Exception as e:
                logger.error(f"告警检查循环异常: {e}")
    
    async def _auto_report_loop(self):
        """自动报告循环"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时检查一次
                await self._check_auto_reports()
            except Exception as e:
                logger.error(f"自动报告循环异常: {e}")
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 这里可以添加系统级别的指标收集
            # 例如：内存使用、CPU使用、队列长度等
            pass
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    async def _check_system_alerts(self):
        """检查系统告警"""
        try:
            # 检查日成本
            daily_cost = self.get_aggregated_metrics(
                MetricType.COST, "translation_cost", TimeRange.DAY, "sum"
            )["value"]
            
            if daily_cost > self.alert_thresholds["cost_per_day"]:
                self.create_alert(
                    AlertLevel.ERROR,
                    "日成本超标",
                    f"当日翻译成本 ${daily_cost:.2f} 超过阈值 ${self.alert_thresholds['cost_per_day']:.2f}",
                    MetricType.COST,
                    self.alert_thresholds["cost_per_day"],
                    daily_cost
                )
            
        except Exception as e:
            logger.error(f"检查系统告警失败: {e}")
    
    async def _check_auto_reports(self):
        """检查自动报告"""
        try:
            now = datetime.now()
            
            # 检查是否需要生成日报
            if now.strftime("%H:%M") == self.config["daily_report_time"]:
                self.generate_report("daily", TimeRange.DAY)
            
            # 检查是否需要生成周报
            if (now.weekday() == self.config["weekly_report_day"] and 
                now.strftime("%H:%M") == self.config["daily_report_time"]):
                self.generate_report("weekly", TimeRange.WEEK)
            
            # 检查是否需要生成月报
            if (now.day == self.config["monthly_report_day"] and 
                now.strftime("%H:%M") == self.config["daily_report_time"]):
                self.generate_report("monthly", TimeRange.MONTH)
            
        except Exception as e:
            logger.error(f"检查自动报告失败: {e}")
    
    async def _save_metric_to_disk(self, metric: MetricData):
        """保存指标到磁盘"""
        try:
            date_str = metric.timestamp.strftime("%Y-%m-%d")
            file_path = os.path.join(self.data_dir, "metrics", f"metrics_{date_str}.json")
            
            # 读取现有数据
            metrics_data = []
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    metrics_data = json.load(f)
            
            # 添加新指标
            metric_dict = asdict(metric)
            metric_dict['metric_type'] = metric.metric_type.value
            metric_dict['timestamp'] = metric.timestamp.isoformat()
            metrics_data.append(metric_dict)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存指标到磁盘失败: {e}")
    
    async def _save_alert_to_disk(self, alert: Alert):
        """保存告警到磁盘"""
        try:
            file_path = os.path.join(self.data_dir, "alerts", f"{alert.alert_id}.json")
            
            alert_dict = asdict(alert)
            alert_dict['level'] = alert.level.value
            alert_dict['metric_type'] = alert.metric_type.value
            alert_dict['triggered_at'] = alert.triggered_at.isoformat()
            if alert.resolved_at:
                alert_dict['resolved_at'] = alert.resolved_at.isoformat()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(alert_dict, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存告警到磁盘失败: {e}")
    
    async def _save_report_to_disk(self, report: Report):
        """保存报告到磁盘"""
        try:
            file_path = os.path.join(self.data_dir, "reports", f"{report.report_id}.json")
            
            report_dict = asdict(report)
            report_dict['time_range'] = report.time_range.value
            report_dict['start_time'] = report.start_time.isoformat()
            report_dict['end_time'] = report.end_time.isoformat()
            report_dict['generated_at'] = report.generated_at.isoformat()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存报告到磁盘失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.collection_task:
            self.collection_task.cancel()
            self.collection_task = None
        
        if self.alert_task:
            self.alert_task.cancel()
            self.alert_task = None
        
        if self.report_task:
            self.report_task.cancel()
            self.report_task = None
