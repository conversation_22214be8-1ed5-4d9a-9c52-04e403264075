# 电商商品监控系统简化设计文档

## 概述

电商商品监控系统是一个基于现有task-middleware和webui基础设施的专业化监控解决方案。本简化版本采用单机部署架构，去除了集群、CI/CD等复杂组件，专注于核心功能实现，降低部署和运维复杂度。

### 核心特性

- **完整商品信息监控**：通过TaskMiddleware API获取价格、销量、库存、好评率等全维度商品数据
- **时序数据存储**：将所有商品信息存储到TimescaleDB，建立完整的历史数据基础
- **多维趋势分析**：价格趋势、销量趋势、库存变化、好评率变化的综合分析
- **利差计算引擎**：基于价格历史数据和供货商成本实时计算利润空间
- **智能预警系统**：价格波动、销量异常、库存不足、好评率下降等多维度预警
- **成本管理**：支持多供货商成本对比和成本变化追踪
- **简化架构**：单机部署，避免分布式复杂性
- **多平台支持**：支持主要国际电商平台的商品监控
- **智能翻译**：集成LLM API实现多语言商品信息翻译

## 简化架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "电商监控系统 (简化单机架构)"
        subgraph "前端层"
            A[WebUI - React/TypeScript]
        end
        
        subgraph "应用层"
            B[FastAPI应用服务<br/>- 统一API网关<br/>- 业务逻辑处理<br/>- 认证和权限]
        end
        
        subgraph "服务层"
            C[商品监控模块<br/>- 商品管理<br/>- 监控任务调度<br/>- TaskMiddleware集成<br/>- 平台配置]
            D[数据分析模块<br/>- 价格趋势分析<br/>- 销量趋势分析<br/>- 库存变化监控<br/>- 好评率分析]
            E[利差计算模块<br/>- 成本管理<br/>- 利润分析<br/>- 供货商对比]
            F[预警报表模块<br/>- 多维度智能预警<br/>- 综合趋势报表<br/>- 利润分析报告]
            G[翻译服务模块<br/>- LLM集成<br/>- 批量处理<br/>- 质量评估]
        end
        
        subgraph "存储层"
            H[PostgreSQL<br/>- 业务数据<br/>- 成本数据<br/>- 时序数据扩展]
            I[Redis<br/>- 缓存<br/>- 会话存储<br/>- 任务队列]
            J[本地文件存储<br/>- 日志文件<br/>- 导入导出文件<br/>- 备份文件]
        end
    end
    
    subgraph "外部服务"
        K[Task Middleware API]
        L[LLM API Services]
        M[电商平台]
        N[邮件服务]
        O[供货商API/数据源]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G

    C --> H
    C --> I
    D --> H
    D --> I
    E --> H
    E --> I
    F --> H
    F --> I
    G --> I

    C -.->|HTTP API| K
    E -.->|成本数据| O
    F -.->|SMTP| N
    G -.->|HTTP API| L

    K --> M
```

### 架构简化说明

#### 1. 单机部署架构
- **应用服务**：单一FastAPI应用，包含所有业务逻辑
- **模块化设计**：功能模块化但运行在同一进程中
- **统一配置**：使用配置文件和环境变量管理
- **简化通信**：模块间直接方法调用，无网络开销

#### 2. 简化的技术栈
- **数据库**：PostgreSQL单实例 + TimescaleDB扩展
- **缓存**：Redis单实例
- **部署**：Docker Compose
- **监控**：简化的日志监控和健康检查
- **配置**：文件配置 + 数据库配置表

## 简化技术栈

- **前端**: React 18, TypeScript, Ant Design, ECharts
- **后端**: Python 3.11, FastAPI, Pydantic, SQLAlchemy
- **数据库**: PostgreSQL 15 + TimescaleDB扩展
- **缓存**: Redis 7.0 单实例
- **任务队列**: Celery + Redis
- **部署**: Docker + Docker Compose
- **监控**: 内置健康检查 + 日志文件
- **配置**: YAML配置文件 + 环境变量

## 核心模块设计

### 1. 统一应用服务

```python
class EcommerceMonitoringApp:
    """统一的电商监控应用服务"""
    
    def __init__(self):
        self.config = self._load_config()
        self.db = self._init_database()
        self.cache = self._init_cache()
        self.task_queue = self._init_task_queue()
        
        # 初始化业务模块
        self.product_monitor = ProductMonitorModule(self.db, self.cache)
        self.sales_analytics = SalesAnalyticsModule(self.db, self.cache)
        self.profit_calculator = ProfitCalculatorModule(self.db, self.cache)
        self.alert_report = AlertReportModule(self.db, self.cache, self.config)
        self.translation = TranslationModule(self.cache, self.config)
        
        # 初始化FastAPI应用
        self.app = self._create_fastapi_app()
    
    def _load_config(self) -> AppConfig:
        """加载应用配置"""
        config = AppConfig()
        
        # 从YAML文件加载基础配置
        with open('config/app.yaml', 'r') as f:
            yaml_config = yaml.safe_load(f)
            config.update(yaml_config)
        
        # 从环境变量覆盖配置
        config.database_url = os.getenv('DATABASE_URL', config.database_url)
        config.redis_url = os.getenv('REDIS_URL', config.redis_url)
        
        return config
    
    def _init_database(self) -> Database:
        """初始化数据库连接"""
        engine = create_engine(
            self.config.database_url,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True
        )
        return Database(engine)
    
    def _init_cache(self) -> Redis:
        """初始化Redis缓存"""
        return redis.from_url(
            self.config.redis_url,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )
    
    def _create_fastapi_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title="电商商品监控系统",
            description="简化版电商商品监控API",
            version="1.0.0"
        )
        
        # 添加中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 注册路由
        app.include_router(self.product_monitor.router, prefix="/api/v1/products")
        app.include_router(self.sales_analytics.router, prefix="/api/v1/sales")
        app.include_router(self.profit_calculator.router, prefix="/api/v1/profit")
        app.include_router(self.alert_report.router, prefix="/api/v1/alerts")
        app.include_router(self.translation.router, prefix="/api/v1/translation")
        
        # 健康检查端点
        @app.get("/health")
        async def health_check():
            return await self._check_system_health()
        
        return app
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """系统健康检查"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {}
        }
        
        # 检查数据库连接
        try:
            await self.db.execute("SELECT 1")
            health_status["services"]["database"] = "healthy"
        except Exception as e:
            health_status["services"]["database"] = f"unhealthy: {str(e)}"
            health_status["status"] = "unhealthy"
        
        # 检查Redis连接
        try:
            await self.cache.ping()
            health_status["services"]["redis"] = "healthy"
        except Exception as e:
            health_status["services"]["redis"] = f"unhealthy: {str(e)}"
            health_status["status"] = "unhealthy"
        
        return health_status
```

### 2. 数据分析模块

```python
class DataAnalyticsModule:
    """数据分析模块 - 多维度商品信息趋势分析"""

    def __init__(self, db: Database, cache: Redis):
        self.db = db
        self.cache = cache
        self.router = APIRouter()
        self._setup_routes()

    def _setup_routes(self):
        """设置API路由"""

        @self.router.get("/price/trends/{product_id}")
        async def get_price_trend(
            product_id: str,
            days: int = 30,
            interval: str = "1d"
        ):
            return await self.get_price_trend(product_id, days, interval)

        @self.router.get("/sales/trends/{product_id}")
        async def get_sales_trend(
            product_id: str,
            days: int = 30,
            interval: str = "1d"
        ):
            return await self.get_sales_trend(product_id, days, interval)

        @self.router.get("/inventory/trends/{product_id}")
        async def get_inventory_trend(
            product_id: str,
            days: int = 30
        ):
            return await self.get_inventory_trend(product_id, days)

        @self.router.get("/rating/trends/{product_id}")
        async def get_rating_trend(
            product_id: str,
            days: int = 30
        ):
            return await self.get_rating_trend(product_id, days)

        @self.router.get("/comprehensive/{product_id}")
        async def get_comprehensive_analysis(
            product_id: str,
            days: int = 30
        ):
            return await self.get_comprehensive_analysis(product_id, days)

        @self.router.post("/forecast")
        async def forecast_sales(request: SalesForecastRequest):
            return await self.forecast_sales(request)

        @self.router.get("/performance/compare")
        async def compare_products_performance(
            product_ids: List[str],
            days: int = 30
        ):
            return await self.compare_products_performance(product_ids, days)

    async def get_price_trend(self, product_id: str, days: int, interval: str) -> PriceTrend:
        """获取价格趋势分析"""
        cache_key = f"price_trend:{product_id}:{days}:{interval}"

        # 尝试从缓存获取
        cached_trend = await self.cache.get(cache_key)
        if cached_trend:
            return PriceTrend.parse_raw(cached_trend)

        # 从数据库查询价格数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        query = """
        WITH price_data AS (
            SELECT
                time_bucket($1::interval, time) as bucket,
                product_id,
                avg(price) as avg_price,
                max(price) as max_price,
                min(price) as min_price,
                count(*) as data_points,
                -- 计算价格变化率
                LAG(avg(price)) OVER (ORDER BY time_bucket($1::interval, time)) as prev_price
            FROM product_history
            WHERE product_id = $2
                AND time >= $3
                AND time <= $4
                AND price IS NOT NULL
            GROUP BY bucket, product_id
            ORDER BY bucket
        )
        SELECT
            bucket,
            avg_price,
            max_price,
            min_price,
            data_points,
            CASE
                WHEN prev_price > 0 THEN ((avg_price - prev_price) / prev_price * 100)
                ELSE 0
            END as price_change_rate
        FROM price_data
        """

        results = await self.db.fetch_all(query, interval, product_id, start_time, end_time)

        # 构建价格趋势数据
        trend_points = []
        for row in results:
            trend_points.append({
                "timestamp": row["bucket"],
                "avg_price": float(row["avg_price"] or 0),
                "max_price": float(row["max_price"] or 0),
                "min_price": float(row["min_price"] or 0),
                "price_change_rate": float(row["price_change_rate"] or 0),
                "data_points": row["data_points"]
            })

        # 计算价格统计
        price_stats = self._calculate_price_statistics(trend_points)

        trend = PriceTrend(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            trend_points=trend_points,
            statistics=price_stats
        )

        # 缓存结果
        await self.cache.setex(cache_key, 1800, trend.json())  # 30分钟缓存

        return trend

    async def get_sales_trend(self, product_id: str, days: int, interval: str) -> SalesTrend:
        """获取销量趋势分析"""
        cache_key = f"sales_trend:{product_id}:{days}:{interval}"

        # 尝试从缓存获取
        cached_trend = await self.cache.get(cache_key)
        if cached_trend:
            return SalesTrend.parse_raw(cached_trend)

        # 从数据库查询销量数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        query = """
        WITH sales_data AS (
            SELECT
                time_bucket($1::interval, time) as bucket,
                product_id,
                avg(sales_count) as avg_sales,
                max(sales_count) as max_sales,
                min(sales_count) as min_sales,
                avg(stock_quantity) as avg_stock,
                count(*) as data_points,
                -- 计算销量变化率
                LAG(avg(sales_count)) OVER (ORDER BY time_bucket($1::interval, time)) as prev_sales
            FROM product_history
            WHERE product_id = $2
                AND time >= $3
                AND time <= $4
                AND sales_count IS NOT NULL
            GROUP BY bucket, product_id
            ORDER BY bucket
        )
        SELECT
            bucket,
            avg_sales,
            max_sales,
            min_sales,
            avg_stock,
            data_points,
            CASE
                WHEN prev_sales > 0 THEN ((avg_sales - prev_sales) / prev_sales * 100)
                ELSE 0
            END as growth_rate
        FROM sales_data
        """

        results = await self.db.fetch_all(query, interval, product_id, start_time, end_time)

        # 构建趋势数据
        trend_points = []
        total_sales = 0
        for row in results:
            trend_points.append({
                "timestamp": row["bucket"],
                "avg_sales": float(row["avg_sales"] or 0),
                "max_sales": row["max_sales"] or 0,
                "min_sales": row["min_sales"] or 0,
                "avg_stock": float(row["avg_stock"] or 0),
                "growth_rate": float(row["growth_rate"] or 0),
                "data_points": row["data_points"]
            })
            total_sales += float(row["avg_sales"] or 0)

        # 计算趋势统计
        trend_stats = self._calculate_sales_statistics(trend_points)

        trend = SalesTrend(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            trend_points=trend_points,
            statistics=trend_stats,
            total_sales=total_sales,
            average_daily_sales=total_sales / max(days, 1)
        )

        # 缓存结果
        await self.cache.setex(cache_key, 1800, trend.json())  # 30分钟缓存

        return trend

    async def get_inventory_trend(self, product_id: str, days: int) -> InventoryTrend:
        """获取库存趋势分析"""
        cache_key = f"inventory_trend:{product_id}:{days}"

        cached_trend = await self.cache.get(cache_key)
        if cached_trend:
            return InventoryTrend.parse_raw(cached_trend)

        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        query = """
        SELECT
            time_bucket('1d', time) as date,
            avg(stock_quantity) as avg_stock,
            min(stock_quantity) as min_stock,
            max(stock_quantity) as max_stock,
            count(*) as data_points
        FROM product_history
        WHERE product_id = $1
            AND time >= $2
            AND time <= $3
            AND stock_quantity IS NOT NULL
        GROUP BY date
        ORDER BY date
        """

        results = await self.db.fetch_all(query, product_id, start_time, end_time)

        trend_points = []
        for row in results:
            trend_points.append({
                "date": row["date"],
                "avg_stock": float(row["avg_stock"] or 0),
                "min_stock": row["min_stock"] or 0,
                "max_stock": row["max_stock"] or 0,
                "data_points": row["data_points"]
            })

        # 检测缺货风险
        current_stock = trend_points[-1]["avg_stock"] if trend_points else 0
        stock_trend = self._calculate_stock_trend(trend_points)

        trend = InventoryTrend(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            trend_points=trend_points,
            current_stock=current_stock,
            stock_trend=stock_trend,
            low_stock_risk=current_stock < 10  # 简单的缺货风险判断
        )

        await self.cache.setex(cache_key, 1800, trend.json())
        return trend

    async def get_rating_trend(self, product_id: str, days: int) -> RatingTrend:
        """获取好评率趋势分析"""
        cache_key = f"rating_trend:{product_id}:{days}"

        cached_trend = await self.cache.get(cache_key)
        if cached_trend:
            return RatingTrend.parse_raw(cached_trend)

        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        query = """
        SELECT
            time_bucket('1d', time) as date,
            avg(rating) as avg_rating,
            avg(review_count) as avg_reviews,
            count(*) as data_points
        FROM product_history
        WHERE product_id = $1
            AND time >= $2
            AND time <= $3
            AND rating IS NOT NULL
        GROUP BY date
        ORDER BY date
        """

        results = await self.db.fetch_all(query, product_id, start_time, end_time)

        trend_points = []
        for row in results:
            trend_points.append({
                "date": row["date"],
                "avg_rating": float(row["avg_rating"] or 0),
                "avg_reviews": float(row["avg_reviews"] or 0),
                "data_points": row["data_points"]
            })

        # 计算好评率统计
        rating_stats = self._calculate_rating_statistics(trend_points)

        trend = RatingTrend(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            trend_points=trend_points,
            statistics=rating_stats
        )

        await self.cache.setex(cache_key, 1800, trend.json())
        return trend

    async def get_comprehensive_analysis(self, product_id: str, days: int) -> ComprehensiveAnalysis:
        """获取综合分析 - 整合价格、销量、库存、好评率"""
        # 并行获取各维度数据
        price_trend_task = self.get_price_trend(product_id, days, "1d")
        sales_trend_task = self.get_sales_trend(product_id, days, "1d")
        inventory_trend_task = self.get_inventory_trend(product_id, days)
        rating_trend_task = self.get_rating_trend(product_id, days)

        price_trend, sales_trend, inventory_trend, rating_trend = await asyncio.gather(
            price_trend_task, sales_trend_task, inventory_trend_task, rating_trend_task
        )

        # 综合分析
        analysis = ComprehensiveAnalysis(
            product_id=product_id,
            time_range={"start": datetime.utcnow() - timedelta(days=days), "end": datetime.utcnow()},
            price_analysis=price_trend,
            sales_analysis=sales_trend,
            inventory_analysis=inventory_trend,
            rating_analysis=rating_trend,
            overall_score=self._calculate_overall_score(price_trend, sales_trend, inventory_trend, rating_trend),
            recommendations=self._generate_recommendations(price_trend, sales_trend, inventory_trend, rating_trend)
        )

        return analysis

    def _calculate_overall_score(self, price_trend, sales_trend, inventory_trend, rating_trend) -> float:
        """计算综合评分"""
        scores = []

        # 价格稳定性评分 (价格波动越小越好)
        if price_trend.statistics.get("volatility", 0) < 0.1:
            scores.append(8.0)
        elif price_trend.statistics.get("volatility", 0) < 0.2:
            scores.append(6.0)
        else:
            scores.append(4.0)

        # 销量趋势评分 (销量增长越好越高分)
        sales_growth = sales_trend.statistics.get("avg_growth_rate", 0)
        if sales_growth > 10:
            scores.append(9.0)
        elif sales_growth > 0:
            scores.append(7.0)
        else:
            scores.append(5.0)

        # 库存健康评分
        if not inventory_trend.low_stock_risk:
            scores.append(8.0)
        else:
            scores.append(4.0)

        # 好评率评分
        avg_rating = rating_trend.statistics.get("avg_rating", 0)
        if avg_rating >= 4.5:
            scores.append(9.0)
        elif avg_rating >= 4.0:
            scores.append(7.0)
        else:
            scores.append(5.0)

        return sum(scores) / len(scores)

    async def forecast_sales(self, request: SalesForecastRequest) -> SalesForecast:
        """销量预测"""
        # 获取历史销量数据
        historical_data = await self.get_sales_trend(
            request.product_id,
            request.historical_days,
            "1d"
        )

        # 简单线性回归预测（可以后续升级为更复杂的ML模型）
        sales_values = [point["avg_sales"] for point in historical_data.trend_points]

        if len(sales_values) < 7:  # 至少需要7天数据
            raise ValueError("需要至少7天的历史数据进行预测")

        # 计算趋势斜率
        x_values = list(range(len(sales_values)))
        slope, intercept = self._calculate_linear_regression(x_values, sales_values)

        # 预测未来销量
        forecast_points = []
        for i in range(request.forecast_days):
            future_x = len(sales_values) + i
            predicted_sales = max(0, slope * future_x + intercept)  # 销量不能为负

            forecast_points.append({
                "date": (datetime.utcnow() + timedelta(days=i+1)).date(),
                "predicted_sales": round(predicted_sales, 2),
                "confidence": self._calculate_confidence(i, len(sales_values))
            })

        return SalesForecast(
            product_id=request.product_id,
            forecast_points=forecast_points,
            model_accuracy=self._calculate_model_accuracy(sales_values, slope, intercept),
            trend_direction="上升" if slope > 0 else "下降" if slope < 0 else "平稳"
        )

    def _calculate_sales_statistics(self, trend_points: List[Dict]) -> Dict[str, Any]:
        """计算销量统计指标"""
        if not trend_points:
            return {}

        sales_values = [point["avg_sales"] for point in trend_points]
        growth_rates = [point["growth_rate"] for point in trend_points if point["growth_rate"] != 0]

        return {
            "max_sales": max(sales_values),
            "min_sales": min(sales_values),
            "avg_sales": sum(sales_values) / len(sales_values),
            "sales_volatility": self._calculate_volatility(sales_values),
            "avg_growth_rate": sum(growth_rates) / len(growth_rates) if growth_rates else 0,
            "trend_strength": self._calculate_trend_strength(sales_values)
        }

    def _calculate_linear_regression(self, x_values: List[float], y_values: List[float]) -> Tuple[float, float]:
        """计算线性回归的斜率和截距"""
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n

        return slope, intercept
```

### 3. 利差计算模块

```python
class ProfitCalculatorModule:
    """利差计算模块 - 专注于成本管理和利润分析"""

    def __init__(self, db: Database, cache: Redis):
        self.db = db
        self.cache = cache
        self.router = APIRouter()
        self._setup_routes()

    def _setup_routes(self):
        """设置API路由"""

        @self.router.post("/suppliers")
        async def add_supplier(supplier: SupplierCreate):
            return await self.add_supplier(supplier)

        @self.router.post("/costs")
        async def add_product_cost(cost: ProductCostCreate):
            return await self.add_product_cost(cost)

        @self.router.get("/profit/{product_id}")
        async def calculate_profit_margin(
            product_id: str,
            days: int = 30
        ):
            return await self.calculate_profit_margin(product_id, days)

        @self.router.get("/comparison/{product_id}")
        async def compare_suppliers(
            product_id: str,
            supplier_ids: List[str] = None
        ):
            return await self.compare_suppliers(product_id, supplier_ids)

        @self.router.get("/opportunities")
        async def find_profit_opportunities(
            min_margin: float = 0.2,
            min_sales: int = 10
        ):
            return await self.find_profit_opportunities(min_margin, min_sales)

    async def add_supplier(self, supplier_data: SupplierCreate) -> Supplier:
        """添加供货商"""
        supplier = Supplier(
            name=supplier_data.name,
            contact_info=supplier_data.contact_info,
            payment_terms=supplier_data.payment_terms,
            shipping_cost=supplier_data.shipping_cost,
            minimum_order=supplier_data.minimum_order,
            lead_time_days=supplier_data.lead_time_days,
            quality_rating=supplier_data.quality_rating,
            is_active=True,
            created_at=datetime.utcnow()
        )

        await self.db.save(supplier)

        # 清除供货商缓存
        await self.cache.delete("active_suppliers")

        return supplier

    async def add_product_cost(self, cost_data: ProductCostCreate) -> ProductCost:
        """添加商品成本记录"""
        cost = ProductCost(
            product_id=cost_data.product_id,
            supplier_id=cost_data.supplier_id,
            unit_cost=cost_data.unit_cost,
            currency=cost_data.currency,
            minimum_quantity=cost_data.minimum_quantity,
            shipping_cost=cost_data.shipping_cost,
            other_fees=cost_data.other_fees,
            valid_from=cost_data.valid_from,
            valid_until=cost_data.valid_until,
            created_at=datetime.utcnow()
        )

        await self.db.save(cost)

        # 清除相关缓存
        await self.cache.delete(f"product_costs:{cost_data.product_id}")

        return cost

    async def calculate_profit_margin(self, product_id: str, days: int) -> ProfitAnalysis:
        """计算利润分析"""
        cache_key = f"profit_analysis:{product_id}:{days}"

        # 尝试从缓存获取
        cached_analysis = await self.cache.get(cache_key)
        if cached_analysis:
            return ProfitAnalysis.parse_raw(cached_analysis)

        # 获取商品价格趋势
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        price_query = """
        SELECT
            time_bucket('1d', time) as date,
            avg(price) as avg_price,
            avg(sales_count) as avg_sales
        FROM product_history
        WHERE product_id = $1
            AND time >= $2
            AND time <= $3
            AND price IS NOT NULL
        GROUP BY date
        ORDER BY date
        """

        price_data = await self.db.fetch_all(price_query, product_id, start_time, end_time)

        # 获取当前有效的成本数据
        cost_query = """
        SELECT
            pc.*,
            s.name as supplier_name,
            s.shipping_cost as supplier_shipping_cost
        FROM product_costs pc
        JOIN suppliers s ON pc.supplier_id = s.id
        WHERE pc.product_id = $1
            AND pc.valid_from <= NOW()
            AND (pc.valid_until IS NULL OR pc.valid_until >= NOW())
            AND s.is_active = true
        ORDER BY pc.unit_cost ASC
        """

        cost_data = await self.db.fetch_all(cost_query, product_id)

        if not cost_data:
            raise ValueError(f"未找到商品 {product_id} 的成本数据")

        # 计算每日利润分析
        daily_analysis = []
        total_profit = 0
        total_revenue = 0

        for price_row in price_data:
            date = price_row["date"]
            avg_price = float(price_row["avg_price"] or 0)
            avg_sales = float(price_row["avg_sales"] or 0)

            # 选择最优供货商（成本最低）
            best_supplier = cost_data[0]
            total_cost = (
                float(best_supplier["unit_cost"]) +
                float(best_supplier["shipping_cost"] or 0) +
                float(best_supplier["other_fees"] or 0) +
                float(best_supplier["supplier_shipping_cost"] or 0)
            )

            unit_profit = avg_price - total_cost
            daily_profit = unit_profit * avg_sales
            daily_revenue = avg_price * avg_sales
            profit_margin = (unit_profit / avg_price * 100) if avg_price > 0 else 0

            daily_analysis.append({
                "date": date,
                "avg_price": avg_price,
                "avg_sales": avg_sales,
                "unit_cost": total_cost,
                "unit_profit": unit_profit,
                "daily_profit": daily_profit,
                "daily_revenue": daily_revenue,
                "profit_margin": profit_margin,
                "best_supplier": best_supplier["supplier_name"]
            })

            total_profit += daily_profit
            total_revenue += daily_revenue

        # 计算整体统计
        avg_profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

        analysis = ProfitAnalysis(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            daily_analysis=daily_analysis,
            total_profit=total_profit,
            total_revenue=total_revenue,
            avg_profit_margin=avg_profit_margin,
            best_supplier_info=cost_data[0],
            supplier_comparison=[
                {
                    "supplier_name": supplier["supplier_name"],
                    "unit_cost": float(supplier["unit_cost"]) +
                                float(supplier["shipping_cost"] or 0) +
                                float(supplier["other_fees"] or 0),
                    "potential_margin": ((sum(row["avg_price"] for row in price_data) / len(price_data)) -
                                       (float(supplier["unit_cost"]) + float(supplier["shipping_cost"] or 0))) /
                                       (sum(row["avg_price"] for row in price_data) / len(price_data)) * 100
                }
                for supplier in cost_data[:5]  # 显示前5个供货商
            ]
        )

        # 缓存结果
        await self.cache.setex(cache_key, 1800, analysis.json())  # 30分钟缓存

        return analysis

    async def find_profit_opportunities(self, min_margin: float, min_sales: int) -> List[ProfitOpportunity]:
        """寻找利润机会"""
        # 查询有潜力的商品
        query = """
        WITH recent_performance AS (
            SELECT
                ph.product_id,
                p.name as product_name,
                p.platform,
                avg(ph.price) as avg_price,
                avg(ph.sales_count) as avg_sales,
                count(*) as data_points
            FROM product_history ph
            JOIN products p ON ph.product_id = p.id
            WHERE ph.time >= NOW() - INTERVAL '30 days'
                AND ph.price IS NOT NULL
                AND ph.sales_count IS NOT NULL
                AND ph.sales_count >= $2
            GROUP BY ph.product_id, p.name, p.platform
            HAVING avg(ph.sales_count) >= $2
        ),
        cost_analysis AS (
            SELECT
                pc.product_id,
                MIN(pc.unit_cost + COALESCE(pc.shipping_cost, 0) + COALESCE(pc.other_fees, 0)) as min_cost
            FROM product_costs pc
            JOIN suppliers s ON pc.supplier_id = s.id
            WHERE pc.valid_from <= NOW()
                AND (pc.valid_until IS NULL OR pc.valid_until >= NOW())
                AND s.is_active = true
            GROUP BY pc.product_id
        )
        SELECT
            rp.*,
            ca.min_cost,
            (rp.avg_price - ca.min_cost) as unit_profit,
            ((rp.avg_price - ca.min_cost) / rp.avg_price * 100) as profit_margin
        FROM recent_performance rp
        JOIN cost_analysis ca ON rp.product_id = ca.product_id
        WHERE ((rp.avg_price - ca.min_cost) / rp.avg_price) >= $1
        ORDER BY profit_margin DESC, avg_sales DESC
        LIMIT 50
        """

        results = await self.db.fetch_all(query, min_margin, min_sales)

        opportunities = []
        for row in results:
            opportunities.append(ProfitOpportunity(
                product_id=row["product_id"],
                product_name=row["product_name"],
                platform=row["platform"],
                avg_price=float(row["avg_price"]),
                avg_sales=float(row["avg_sales"]),
                min_cost=float(row["min_cost"]),
                unit_profit=float(row["unit_profit"]),
                profit_margin=float(row["profit_margin"]),
                estimated_monthly_profit=float(row["unit_profit"]) * float(row["avg_sales"]) * 30
            ))

        return opportunities
```

### 4. 商品监控模块

```python
class ProductMonitorModule:
    """商品监控模块 - 完整的商品信息监控流程管理"""
    
    def __init__(self, db: Database, cache: Redis):
        self.db = db
        self.cache = cache
        self.crawler_client = CrawlerApiClient()
        self.router = APIRouter()
        self._setup_routes()
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.router.post("/", response_model=Product)
        async def create_product(product: ProductCreate):
            return await self.create_product(product)
        
        @self.router.get("/", response_model=List[Product])
        async def get_products(
            skip: int = 0, 
            limit: int = 100,
            platform: Optional[str] = None,
            category: Optional[str] = None
        ):
            return await self.get_products(skip, limit, platform, category)
        
        @self.router.post("/import")
        async def import_products(file: UploadFile):
            return await self.import_products_from_excel(file)
        
        @self.router.post("/monitor/batch")
        async def start_batch_monitoring(request: BatchMonitoringRequest):
            return await self.start_batch_monitoring(request)
    
    async def create_product(self, product_data: ProductCreate) -> Product:
        """创建商品"""
        # 验证URL和平台
        platform = self._detect_platform(product_data.url)
        
        # 获取平台配置
        platform_config = await self._get_platform_config(platform)
        
        # 创建商品记录
        product = Product(
            url=product_data.url,
            name=product_data.name,
            platform=platform,
            category=product_data.category,
            monitoring_frequency=product_data.monitoring_frequency or 24,
            status=ProductStatus.ACTIVE,
            created_at=datetime.utcnow()
        )
        
        # 保存到数据库
        await self.db.save(product)
        
        # 清除相关缓存
        await self.cache.delete(f"products:platform:{platform}")
        
        return product
    
    async def import_products_from_excel(self, file: UploadFile) -> ImportResult:
        """从Excel导入商品"""
        try:
            # 读取Excel文件
            content = await file.read()
            df = pd.read_excel(BytesIO(content))
            
            # 验证必需列
            required_columns = ['url', 'name']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需列: {missing_columns}")
            
            # 批量处理商品
            results = []
            for _, row in df.iterrows():
                try:
                    product_data = ProductCreate(
                        url=row['url'],
                        name=row['name'],
                        category=row.get('category', '未分类'),
                        monitoring_frequency=row.get('monitoring_frequency', 24)
                    )
                    product = await self.create_product(product_data)
                    results.append({"status": "success", "product": product})
                except Exception as e:
                    results.append({"status": "error", "error": str(e), "row": row.to_dict()})
            
            success_count = len([r for r in results if r["status"] == "success"])
            error_count = len([r for r in results if r["status"] == "error"])
            
            return ImportResult(
                total_count=len(results),
                success_count=success_count,
                error_count=error_count,
                results=results
            )
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")
    
    async def start_batch_monitoring(self, request: BatchMonitoringRequest) -> BatchMonitoringResult:
        """启动批量监控 - 完整商品信息采集"""
        products = await self.db.get_products_by_ids(request.product_ids)

        # 按平台分组
        platform_groups = {}
        for product in products:
            if product.platform not in platform_groups:
                platform_groups[product.platform] = []
            platform_groups[product.platform].append(product)

        # 为每个平台创建监控任务
        task_results = []
        for platform, platform_products in platform_groups.items():
            platform_config = await self._get_platform_config(platform)

            # 创建爬取任务 - 获取完整商品信息
            urls = [p.url for p in platform_products]
            crawl_task = await self.crawler_client.submit_batch_crawl(
                urls=urls,
                config=platform_config,
                priority=request.priority,
                # 指定需要采集的商品信息字段
                fields=[
                    "title", "price", "currency", "stock_quantity",
                    "sales_count", "rating", "review_count",
                    "description", "images", "seller_info"
                ]
            )

            task_results.append({
                "platform": platform,
                "task_id": crawl_task.task_id,
                "product_count": len(platform_products),
                "expected_fields": ["price", "sales_count", "stock_quantity", "rating"]
            })

        return BatchMonitoringResult(
            batch_id=str(uuid.uuid4()),
            total_products=len(products),
            platform_tasks=task_results,
            status="submitted"
        )

    async def process_crawl_result(self, crawl_result: CrawlResult) -> ProcessResult:
        """处理爬取结果 - 存储到时序数据库"""
        processed_count = 0
        failed_count = 0

        for item in crawl_result.items:
            try:
                # 解析商品信息
                product_data = self._parse_product_data(item)

                # 存储到时序数据库
                history_record = ProductHistory(
                    time=datetime.utcnow(),
                    product_id=product_data.product_id,
                    platform=product_data.platform,
                    title=product_data.title,
                    title_translated=None,  # 后续翻译
                    price=product_data.price,
                    currency=product_data.currency,
                    stock_quantity=product_data.stock_quantity,
                    sales_count=product_data.sales_count,
                    rating=product_data.rating,
                    review_count=product_data.review_count,
                    change_type=self._detect_change_type(product_data),
                    raw_data=product_data.raw_data
                )

                await self.db.save(history_record)
                processed_count += 1

                # 触发实时分析
                await self._trigger_real_time_analysis(product_data)

            except Exception as e:
                logger.error(f"处理商品数据失败: {e}")
                failed_count += 1

        return ProcessResult(
            total_items=len(crawl_result.items),
            processed_count=processed_count,
            failed_count=failed_count,
            timestamp=datetime.utcnow()
        )

    def _parse_product_data(self, raw_item: Dict[str, Any]) -> ProductData:
        """解析原始商品数据"""
        return ProductData(
            product_id=raw_item.get("product_id"),
            platform=raw_item.get("platform"),
            title=raw_item.get("title"),
            price=self._parse_price(raw_item.get("price")),
            currency=raw_item.get("currency", "USD"),
            stock_quantity=self._parse_int(raw_item.get("stock_quantity")),
            sales_count=self._parse_int(raw_item.get("sales_count")),
            rating=self._parse_float(raw_item.get("rating")),
            review_count=self._parse_int(raw_item.get("review_count")),
            raw_data=raw_item
        )

    def _detect_change_type(self, current_data: ProductData) -> str:
        """检测数据变化类型"""
        # 获取上一次的数据进行对比
        # 这里简化处理，实际应该从数据库获取最近一次记录
        if current_data.price:
            return "price_update"
        elif current_data.sales_count:
            return "sales_update"
        elif current_data.stock_quantity is not None:
            return "stock_update"
        else:
            return "general_update"

    async def _trigger_real_time_analysis(self, product_data: ProductData):
        """触发实时分析"""
        # 触发价格变化分析
        if product_data.price:
            await self._check_price_alerts(product_data)

        # 触发销量变化分析
        if product_data.sales_count:
            await self._check_sales_alerts(product_data)

        # 触发库存变化分析
        if product_data.stock_quantity is not None:
            await self._check_stock_alerts(product_data)

        # 触发好评率变化分析
        if product_data.rating:
            await self._check_rating_alerts(product_data)
    
    async def _get_platform_config(self, platform: str) -> PlatformConfig:
        """获取平台配置（带缓存）"""
        cache_key = f"platform_config:{platform}"
        
        # 尝试从缓存获取
        cached_config = await self.cache.get(cache_key)
        if cached_config:
            return PlatformConfig.parse_raw(cached_config)
        
        # 从数据库获取
        config = await self.db.get_platform_config(platform)
        if not config:
            # 使用默认配置
            config = self._get_default_platform_config(platform)
        
        # 缓存配置
        await self.cache.setex(
            cache_key, 
            3600,  # 1小时过期
            config.json()
        )
        
        return config
    
    def _detect_platform(self, url: str) -> str:
        """检测URL对应的平台"""
        if "1688.com" in url:
            return "1688"
        elif "amazon." in url:
            return "amazon"
        elif "mercadolibre." in url:
            return "mercadolibre"
        elif "ebay." in url:
            return "ebay"
        elif "aliexpress." in url:
            return "aliexpress"
        else:
            return "unknown"
```

### 3. 数据分析模块

```python
class DataAnalyticsModule:
    """数据分析模块 - 集成趋势分析、预警处理、报表生成"""
    
    def __init__(self, db: Database, cache: Redis):
        self.db = db
        self.cache = cache
        self.router = APIRouter()
        self._setup_routes()
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.router.get("/trends/{product_id}")
        async def get_price_trend(
            product_id: str,
            days: int = 30,
            interval: str = "1h"
        ):
            return await self.get_price_trend(product_id, days, interval)
        
        @self.router.get("/market-analysis")
        async def get_market_analysis(
            category: str,
            days: int = 7
        ):
            return await self.get_market_analysis(category, days)
        
        @self.router.post("/alerts/rules")
        async def create_alert_rule(rule: AlertRuleCreate):
            return await self.create_alert_rule(rule)
        
        @self.router.get("/reports/export")
        async def export_report(
            report_type: str,
            start_date: date,
            end_date: date,
            format: str = "excel"
        ):
            return await self.export_report(report_type, start_date, end_date, format)
    
    async def get_price_trend(self, product_id: str, days: int, interval: str) -> PriceTrend:
        """获取价格趋势分析"""
        cache_key = f"price_trend:{product_id}:{days}:{interval}"
        
        # 尝试从缓存获取
        cached_trend = await self.cache.get(cache_key)
        if cached_trend:
            return PriceTrend.parse_raw(cached_trend)
        
        # 从数据库查询
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        # 使用TimescaleDB的time_bucket函数进行时间聚合
        query = """
        SELECT 
            time_bucket($1::interval, time) as bucket,
            avg(price) as avg_price,
            min(price) as min_price,
            max(price) as max_price,
            count(*) as data_points
        FROM product_history 
        WHERE product_id = $2 
            AND time >= $3 
            AND time <= $4
            AND price IS NOT NULL
        GROUP BY bucket
        ORDER BY bucket
        """
        
        results = await self.db.fetch_all(
            query, 
            interval, 
            product_id, 
            start_time, 
            end_time
        )
        
        # 构建趋势数据
        trend_data = []
        for row in results:
            trend_data.append({
                "timestamp": row["bucket"],
                "avg_price": float(row["avg_price"]),
                "min_price": float(row["min_price"]),
                "max_price": float(row["max_price"]),
                "data_points": row["data_points"]
            })
        
        # 计算趋势指标
        trend = PriceTrend(
            product_id=product_id,
            time_range={"start": start_time, "end": end_time},
            data_points=trend_data,
            statistics=self._calculate_trend_statistics(trend_data)
        )
        
        # 缓存结果
        await self.cache.setex(cache_key, 1800, trend.json())  # 30分钟缓存
        
        return trend
    
    async def create_alert_rule(self, rule_data: AlertRuleCreate) -> AlertRule:
        """创建预警规则"""
        rule = AlertRule(
            name=rule_data.name,
            description=rule_data.description,
            condition=rule_data.condition,
            threshold=rule_data.threshold,
            notification_channels=rule_data.notification_channels,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        await self.db.save(rule)
        
        # 清除预警规则缓存
        await self.cache.delete("active_alert_rules")
        
        return rule
    
    async def check_alert_conditions(self, product_data: ProductData) -> List[Alert]:
        """检查预警条件"""
        # 获取活跃的预警规则
        rules = await self._get_active_alert_rules()
        
        alerts = []
        for rule in rules:
            if await self._evaluate_alert_condition(rule, product_data):
                alert = Alert(
                    rule_id=rule.id,
                    product_id=product_data.product_id,
                    alert_type=rule.alert_type,
                    message=self._generate_alert_message(rule, product_data),
                    severity=rule.severity,
                    triggered_at=datetime.utcnow()
                )
                alerts.append(alert)
        
        return alerts
    
    async def _get_active_alert_rules(self) -> List[AlertRule]:
        """获取活跃的预警规则（带缓存）"""
        cache_key = "active_alert_rules"
        
        cached_rules = await self.cache.get(cache_key)
        if cached_rules:
            return [AlertRule.parse_raw(rule) for rule in json.loads(cached_rules)]
        
        rules = await self.db.get_active_alert_rules()
        
        # 缓存10分钟
        await self.cache.setex(
            cache_key, 
            600, 
            json.dumps([rule.json() for rule in rules])
        )
        
        return rules
```

### 4. 简化配置管理

```python
class ConfigManager:
    """简化的配置管理器"""
    
    def __init__(self, config_file: str = "config/app.yaml"):
        self.config_file = config_file
        self.config_cache = {}
        self.last_reload = None
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 环境变量覆盖
            config = self._apply_env_overrides(config)
            
            self.config_cache = config
            self.last_reload = datetime.utcnow()
            
            return config
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            return self.config_cache or {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        if not self.config_cache or self._should_reload():
            self.load_config()
        
        keys = key.split('.')
        value = self.config_cache
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def _should_reload(self) -> bool:
        """检查是否需要重新加载配置"""
        if not self.last_reload:
            return True
        
        # 每5分钟检查一次配置文件是否更新
        if datetime.utcnow() - self.last_reload > timedelta(minutes=5):
            try:
                file_mtime = datetime.fromtimestamp(os.path.getmtime(self.config_file))
                return file_mtime > self.last_reload
            except:
                return False
        
        return False
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        env_mappings = {
            'DATABASE_URL': 'database.url',
            'REDIS_URL': 'redis.url',
            'SECRET_KEY': 'security.secret_key',
            'OPENAI_API_KEY': 'translation.openai.api_key',
            'SMTP_HOST': 'notification.email.smtp_host',
            'SMTP_PORT': 'notification.email.smtp_port',
            'SMTP_USER': 'notification.email.smtp_user',
            'SMTP_PASSWORD': 'notification.email.smtp_password'
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                self._set_nested_value(config, config_path, env_value)
        
        return config
    
    def _set_nested_value(self, config: Dict[str, Any], path: str, value: Any):
        """设置嵌套配置值"""
        keys = path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value

# 配置文件示例 (config/app.yaml)
"""
# 应用基础配置
app:
  name: "电商商品监控系统"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000

# 数据库配置
database:
  url: "postgresql://user:password@localhost:5432/ecommerce_monitor"
  pool_size: 10
  max_overflow: 20
  echo: false

# Redis配置
redis:
  url: "redis://localhost:6379/0"
  max_connections: 10

# 任务队列配置
celery:
  broker_url: "redis://localhost:6379/1"
  result_backend: "redis://localhost:6379/2"

# 翻译服务配置
translation:
  default_provider: "openai"
  openai:
    api_key: ""
    base_url: "https://api.openai.com/v1"
    model: "gpt-3.5-turbo"
  claude:
    api_key: ""
    base_url: "https://api.anthropic.com"
    model: "claude-3-haiku-20240307"

# 监控配置
monitoring:
  default_frequency: 24  # 小时
  max_concurrent_tasks: 10
  retry_attempts: 3
  timeout: 30

# 通知配置
notification:
  email:
    enabled: true
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    smtp_user: ""
    smtp_password: ""
    from_address: "<EMAIL>"

# 安全配置
security:
  secret_key: "your-secret-key-here"
  access_token_expire_minutes: 30
  algorithm: "HS256"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
"""
```

### 5. 简化部署配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/ecommerce_monitor
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery工作进程
  worker:
    build: .
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=**************************************/ecommerce_monitor
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=ecommerce_monitor
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 简化数据存储设计

### 1. PostgreSQL + TimescaleDB扩展

```sql
-- 数据库初始化脚本 (database/init.sql)

-- 启用TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 商品基础表
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    platform TEXT NOT NULL,
    category TEXT DEFAULT '未分类',
    monitoring_frequency INTEGER DEFAULT 24,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 商品历史数据表（时序数据）
CREATE TABLE product_history (
    time TIMESTAMPTZ NOT NULL,
    product_id UUID NOT NULL REFERENCES products(id),
    platform TEXT NOT NULL,
    title TEXT,
    title_translated TEXT,
    price DECIMAL(12,4),
    currency TEXT,
    stock_quantity INTEGER,
    sales_count INTEGER,
    rating DECIMAL(3,2),
    review_count INTEGER,
    change_type TEXT,
    raw_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建时序超表（按天分区）
SELECT create_hypertable('product_history', 'time', chunk_time_interval => INTERVAL '1 day');

-- 创建索引 - 优化所有核心商品信息查询
CREATE INDEX idx_products_platform ON products(platform);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_status ON products(status);

-- 商品历史数据核心索引
CREATE INDEX idx_product_history_product_time ON product_history(product_id, time DESC);
CREATE INDEX idx_product_history_platform_time ON product_history(platform, time DESC);

-- 价格相关索引
CREATE INDEX idx_product_history_price ON product_history(product_id, time DESC) WHERE price IS NOT NULL;
CREATE INDEX idx_product_history_price_change ON product_history(product_id, time DESC) WHERE change_type = 'price_change';

-- 销量相关索引
CREATE INDEX idx_product_history_sales ON product_history(product_id, time DESC) WHERE sales_count IS NOT NULL;
CREATE INDEX idx_product_history_sales_change ON product_history(product_id, time DESC) WHERE change_type = 'sales_update';

-- 库存相关索引
CREATE INDEX idx_product_history_stock ON product_history(product_id, time DESC) WHERE stock_quantity IS NOT NULL;
CREATE INDEX idx_product_history_stock_change ON product_history(product_id, time DESC) WHERE change_type = 'stock_update';

-- 好评率相关索引
CREATE INDEX idx_product_history_rating ON product_history(product_id, time DESC) WHERE rating IS NOT NULL;
CREATE INDEX idx_product_history_reviews ON product_history(product_id, time DESC) WHERE review_count IS NOT NULL;

-- 供货商和成本相关索引
CREATE INDEX idx_suppliers_active ON suppliers(is_active, name);
CREATE INDEX idx_product_costs_product ON product_costs(product_id, valid_from DESC);
CREATE INDEX idx_product_costs_supplier ON product_costs(supplier_id, valid_from DESC);
CREATE INDEX idx_product_costs_valid ON product_costs(product_id, valid_from, valid_until);

-- 复合查询优化索引
CREATE INDEX idx_product_history_comprehensive ON product_history(product_id, time DESC, price, sales_count, stock_quantity, rating);

-- 平台配置表
CREATE TABLE platform_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    platform TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 预警规则表
CREATE TABLE alert_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    condition JSONB NOT NULL,
    notification_channels JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 预警历史表
CREATE TABLE alert_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID REFERENCES alert_rules(id),
    product_id UUID REFERENCES products(id),
    alert_type TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT NOT NULL,
    status TEXT DEFAULT 'new',
    triggered_at TIMESTAMPTZ DEFAULT NOW(),
    acknowledged_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ
);

-- 供货商表
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    contact_info JSONB,
    payment_terms TEXT,
    shipping_cost DECIMAL(10,4) DEFAULT 0,
    minimum_order INTEGER DEFAULT 1,
    lead_time_days INTEGER DEFAULT 7,
    quality_rating DECIMAL(3,2) DEFAULT 5.0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 商品成本表
CREATE TABLE product_costs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id),
    supplier_id UUID NOT NULL REFERENCES suppliers(id),
    unit_cost DECIMAL(12,4) NOT NULL,
    currency TEXT DEFAULT 'USD',
    minimum_quantity INTEGER DEFAULT 1,
    shipping_cost DECIMAL(10,4) DEFAULT 0,
    other_fees DECIMAL(10,4) DEFAULT 0,
    valid_from TIMESTAMPTZ DEFAULT NOW(),
    valid_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 用户表（简化版）
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ
);

-- 插入默认平台配置
INSERT INTO platform_configs (platform, name, config) VALUES
('1688', '1688.com', '{
    "selectors": {
        "title": ".d-title",
        "price": ".price-now",
        "stock": ".amount-text",
        "sales": ".sale-text"
    },
    "headers": {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    },
    "rate_limit": {
        "requests_per_minute": 30,
        "delay_between_requests": 2
    }
}'),
('amazon', 'Amazon', '{
    "selectors": {
        "title": "#productTitle",
        "price": ".a-price-whole",
        "stock": "#availability span",
        "rating": ".a-icon-alt"
    },
    "headers": {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    },
    "rate_limit": {
        "requests_per_minute": 20,
        "delay_between_requests": 3
    }
}');

-- 创建默认管理员用户（密码：admin123）
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxG', 'admin');
```

### 2. 简化缓存策略

```python
class SimpleCacheManager:
    """简化的缓存管理器"""

    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.local_cache = {}
        self.cache_stats = {"hits": 0, "misses": 0}

    async def get(self, key: str, use_local: bool = True) -> Any:
        """获取缓存值"""
        # L1: 本地缓存（内存）
        if use_local and key in self.local_cache:
            entry = self.local_cache[key]
            if entry["expires"] > time.time():
                self.cache_stats["hits"] += 1
                return entry["value"]
            else:
                del self.local_cache[key]

        # L2: Redis缓存
        try:
            value = await self.redis.get(key)
            if value:
                self.cache_stats["hits"] += 1
                # 更新本地缓存
                if use_local:
                    self.local_cache[key] = {
                        "value": json.loads(value),
                        "expires": time.time() + 300  # 5分钟本地缓存
                    }
                return json.loads(value)
        except Exception as e:
            logger.warning(f"Redis缓存获取失败: {e}")

        self.cache_stats["misses"] += 1
        return None

    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        try:
            # 设置Redis缓存
            await self.redis.setex(key, ttl, json.dumps(value, default=str))

            # 更新本地缓存
            self.local_cache[key] = {
                "value": value,
                "expires": time.time() + min(ttl, 300)  # 本地缓存最多5分钟
            }

            return True
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            # 删除Redis缓存
            await self.redis.delete(key)

            # 删除本地缓存
            if key in self.local_cache:
                del self.local_cache[key]

            return True
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0

        return {
            "hits": self.cache_stats["hits"],
            "misses": self.cache_stats["misses"],
            "hit_rate": round(hit_rate, 3),
            "local_cache_size": len(self.local_cache)
        }
```

## 简化监控和日志

### 1. 内置健康检查

```python
class HealthChecker:
    """简化的健康检查器"""

    def __init__(self, db: Database, cache: Redis):
        self.db = db
        self.cache = cache
        self.last_check = {}

    async def check_all(self) -> Dict[str, Any]:
        """检查所有组件健康状态"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {}
        }

        # 检查数据库
        db_health = await self._check_database()
        health_status["checks"]["database"] = db_health

        # 检查Redis
        redis_health = await self._check_redis()
        health_status["checks"]["redis"] = redis_health

        # 检查磁盘空间
        disk_health = self._check_disk_space()
        health_status["checks"]["disk"] = disk_health

        # 检查内存使用
        memory_health = self._check_memory()
        health_status["checks"]["memory"] = memory_health

        # 判断整体状态
        if any(check["status"] != "healthy" for check in health_status["checks"].values()):
            health_status["status"] = "unhealthy"

        return health_status

    async def _check_database(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            start_time = time.time()
            await self.db.execute("SELECT 1")
            response_time = time.time() - start_time

            return {
                "status": "healthy",
                "response_time": round(response_time * 1000, 2),  # ms
                "message": "Database connection OK"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "message": "Database connection failed"
            }

    async def _check_redis(self) -> Dict[str, Any]:
        """检查Redis连接"""
        try:
            start_time = time.time()
            await self.cache.ping()
            response_time = time.time() - start_time

            # 获取Redis信息
            info = await self.cache.info()
            memory_usage = info.get('used_memory_human', 'unknown')

            return {
                "status": "healthy",
                "response_time": round(response_time * 1000, 2),  # ms
                "memory_usage": memory_usage,
                "message": "Redis connection OK"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "message": "Redis connection failed"
            }

    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("/")

            free_percent = (free / total) * 100
            used_percent = (used / total) * 100

            status = "healthy"
            if free_percent < 10:  # 少于10%空闲空间
                status = "critical"
            elif free_percent < 20:  # 少于20%空闲空间
                status = "warning"

            return {
                "status": status,
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "used_percent": round(used_percent, 1),
                "free_percent": round(free_percent, 1),
                "message": f"Disk usage: {used_percent:.1f}%"
            }
        except Exception as e:
            return {
                "status": "unknown",
                "error": str(e),
                "message": "Unable to check disk space"
            }

    def _check_memory(self) -> Dict[str, Any]:
        """检查内存使用"""
        try:
            import psutil
            memory = psutil.virtual_memory()

            status = "healthy"
            if memory.percent > 90:
                status = "critical"
            elif memory.percent > 80:
                status = "warning"

            return {
                "status": status,
                "total_gb": round(memory.total / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent,
                "message": f"Memory usage: {memory.percent}%"
            }
        except Exception as e:
            return {
                "status": "unknown",
                "error": str(e),
                "message": "Unable to check memory usage"
            }
```

### 2. 简化日志管理

```python
import logging
import logging.handlers
from pathlib import Path

class SimpleLogManager:
    """简化的日志管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.loggers = {}
        self._setup_logging()

    def _setup_logging(self):
        """设置日志配置"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 基础配置
        log_level = getattr(logging, self.config.get("level", "INFO"))
        log_format = self.config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")

        # 配置根日志器
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                # 控制台输出
                logging.StreamHandler(),
                # 文件输出（带轮转）
                logging.handlers.RotatingFileHandler(
                    filename=self.config.get("file", "logs/app.log"),
                    maxBytes=self._parse_size(self.config.get("max_size", "10MB")),
                    backupCount=self.config.get("backup_count", 5),
                    encoding='utf-8'
                )
            ]
        )

        # 设置第三方库日志级别
        logging.getLogger("uvicorn").setLevel(logging.WARNING)
        logging.getLogger("sqlalchemy").setLevel(logging.WARNING)

    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]

    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串（如 '10MB'）"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

# 使用示例
logger = logging.getLogger(__name__)
```

## 简化部署和运维

### 1. 一键部署脚本

```bash
#!/bin/bash
# deploy.sh - 一键部署脚本

set -e

echo "🚀 开始部署电商监控系统..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p logs uploads config database nginx

# 检查配置文件
if [ ! -f "config/app.yaml" ]; then
    echo "📝 创建默认配置文件..."
    cp config/app.yaml.example config/app.yaml
    echo "⚠️  请编辑 config/app.yaml 文件，配置数据库和其他服务参数"
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境变量文件..."
    cat > .env << EOF
# 数据库配置
POSTGRES_DB=ecommerce_monitor
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here

# 应用配置
SECRET_KEY=your_secret_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
EOF
    echo "⚠️  请编辑 .env 文件，设置安全的密码和API密钥"
fi

# 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 访问地址: http://localhost"
    echo "📊 API文档: http://localhost:8000/docs"
    echo "💾 数据库: localhost:5432"
else
    echo "❌ 服务启动失败，请检查日志："
    docker-compose logs app
    exit 1
fi

echo "🎉 部署完成！"
```

### 2. 备份和恢复脚本

```bash
#!/bin/bash
# backup.sh - 数据备份脚本

set -e

BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_${DATE}.tar.gz"

echo "📦 开始备份数据..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
echo "💾 备份数据库..."
docker-compose exec -T db pg_dump -U postgres ecommerce_monitor > $BACKUP_DIR/database_${DATE}.sql

# 备份配置文件
echo "⚙️ 备份配置文件..."
cp -r config $BACKUP_DIR/config_${DATE}

# 备份上传文件
echo "📁 备份上传文件..."
if [ -d "uploads" ]; then
    cp -r uploads $BACKUP_DIR/uploads_${DATE}
fi

# 创建压缩包
echo "🗜️ 创建压缩包..."
cd $BACKUP_DIR
tar -czf $BACKUP_FILE database_${DATE}.sql config_${DATE} uploads_${DATE} 2>/dev/null || true
rm -rf database_${DATE}.sql config_${DATE} uploads_${DATE}
cd ..

echo "✅ 备份完成: $BACKUP_DIR/$BACKUP_FILE"

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

echo "🧹 清理完成"
```

### 3. 监控脚本

```bash
#!/bin/bash
# monitor.sh - 简单监控脚本

check_service() {
    local service_name=$1
    local url=$2

    if curl -f -s $url > /dev/null; then
        echo "✅ $service_name: 正常"
        return 0
    else
        echo "❌ $service_name: 异常"
        return 1
    fi
}

check_docker_service() {
    local service_name=$1

    if docker-compose ps $service_name | grep -q "Up"; then
        echo "✅ Docker服务 $service_name: 运行中"
        return 0
    else
        echo "❌ Docker服务 $service_name: 停止"
        return 1
    fi
}

echo "🔍 系统监控检查 - $(date)"
echo "================================"

# 检查Docker服务
check_docker_service "app"
check_docker_service "db"
check_docker_service "redis"
check_docker_service "nginx"

echo ""

# 检查HTTP服务
check_service "应用健康检查" "http://localhost:8000/health"
check_service "前端页面" "http://localhost/"

echo ""

# 检查磁盘空间
echo "💾 磁盘使用情况:"
df -h / | tail -1 | awk '{print "   使用: " $3 "/" $2 " (" $5 ")"}'

# 检查内存使用
echo "🧠 内存使用情况:"
free -h | grep "Mem:" | awk '{print "   使用: " $3 "/" $2}'

echo ""
echo "监控检查完成"
```

这个简化版本去除了所有集群组件、CI/CD流水线、Kubernetes等复杂技术，采用单机部署架构，使用Docker Compose进行容器编排，大大降低了部署和运维复杂度，同时保持了系统的核心功能和良好的性能。
