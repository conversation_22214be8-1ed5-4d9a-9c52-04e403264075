/**
 * 用户反馈按钮组件
 */

import React, { useState } from 'react';
import { 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Rate, 
  Upload, 
  message,
  FloatButton
} from 'antd';
import { 
  MessageOutlined, 
  UploadOutlined, 
  SendOutlined,
  BugOutlined,
  BulbOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';

const { TextArea } = Input;
const { Option } = Select;

interface FeedbackData {
  type: 'bug' | 'feature' | 'question' | 'other';
  title: string;
  description: string;
  rating?: number;
  contact?: string;
  attachments?: File[];
}

const FeedbackButton: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);

  const feedbackTypes = [
    { value: 'bug', label: '问题反馈', icon: <BugOutlined /> },
    { value: 'feature', label: '功能建议', icon: <BulbOutlined /> },
    { value: 'question', label: '使用咨询', icon: <QuestionCircleOutlined /> },
    { value: 'other', label: '其他', icon: <MessageOutlined /> },
  ];

  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      const feedbackData: FeedbackData = {
        ...values,
        attachments,
      };

      // 这里应该调用反馈API
      console.log('Feedback data:', feedbackData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('反馈提交成功，感谢您的建议！');
      setModalVisible(false);
      form.resetFields();
      setAttachments([]);
    } catch (error) {
      message.error('反馈提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      const isValidType = file.type.startsWith('image/') || 
                         file.type === 'application/pdf' ||
                         file.type.startsWith('text/');
      
      if (!isValidType) {
        message.error('只支持图片、PDF和文本文件');
        return false;
      }

      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('文件大小不能超过5MB');
        return false;
      }

      setAttachments(prev => [...prev, file]);
      return false;
    },
    onRemove: (file) => {
      setAttachments(prev => prev.filter(f => f.name !== file.name));
    },
    fileList: attachments.map(file => ({
      uid: file.name,
      name: file.name,
      status: 'done' as const,
    })),
  };

  return (
    <>
      <FloatButton
        icon={<MessageOutlined />}
        tooltip="意见反馈"
        onClick={() => setModalVisible(true)}
        style={{ right: 24, bottom: 24 }}
      />

      <Modal
        title="意见反馈"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            type: 'bug',
            rating: 5,
          }}
        >
          <Form.Item
            label="反馈类型"
            name="type"
            rules={[{ required: true, message: '请选择反馈类型' }]}
          >
            <Select>
              {feedbackTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    {type.icon}
                    {type.label}
                  </span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="标题"
            name="title"
            rules={[
              { required: true, message: '请输入反馈标题' },
              { max: 100, message: '标题不能超过100个字符' }
            ]}
          >
            <Input placeholder="请简要描述您的问题或建议" />
          </Form.Item>

          <Form.Item
            label="详细描述"
            name="description"
            rules={[
              { required: true, message: '请输入详细描述' },
              { min: 10, message: '描述至少10个字符' },
              { max: 1000, message: '描述不能超过1000个字符' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请详细描述您遇到的问题、期望的功能或其他建议..."
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item
            label="满意度评分"
            name="rating"
          >
            <Rate />
          </Form.Item>

          <Form.Item
            label="联系方式（可选）"
            name="contact"
          >
            <Input placeholder="邮箱或手机号，方便我们与您联系" />
          </Form.Item>

          <Form.Item
            label="附件（可选）"
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>
                上传文件
              </Button>
            </Upload>
            <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
              支持图片、PDF、文本文件，单个文件不超过5MB
            </div>
          </Form.Item>

          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SendOutlined />}
                loading={submitting}
              >
                提交反馈
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default FeedbackButton;
