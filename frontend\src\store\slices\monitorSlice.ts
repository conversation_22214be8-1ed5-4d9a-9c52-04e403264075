/**
 * 监控任务状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { MonitorTask, MonitorTaskForm, MonitorSearchParams, TaskStatus, PaginatedResponse } from '../../types';
import { monitorApi } from '../../services/monitorApi';

// 状态类型
interface MonitorState {
  tasks: MonitorTask[];
  currentTask: MonitorTask | null;
  total: number;
  page: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
  searchParams: MonitorSearchParams;
  selectedTasks: string[];
  runningTasks: string[];
}

// 初始状态
const initialState: MonitorState = {
  tasks: [],
  currentTask: null,
  total: 0,
  page: 1,
  pageSize: 20,
  isLoading: false,
  error: null,
  searchParams: {},
  selectedTasks: [],
  runningTasks: [],
};

// 异步actions
export const fetchMonitorTasksAsync = createAsyncThunk(
  'monitor/fetchTasks',
  async (params: { page?: number; page_size?: number; search?: MonitorSearchParams }, { rejectWithValue }) => {
    try {
      const response = await monitorApi.getTasks(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取监控任务失败');
    }
  }
);

export const fetchTaskByIdAsync = createAsyncThunk(
  'monitor/fetchTaskById',
  async (taskId: string, { rejectWithValue }) => {
    try {
      const response = await monitorApi.getTaskById(taskId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取任务详情失败');
    }
  }
);

export const createTaskAsync = createAsyncThunk(
  'monitor/createTask',
  async (taskData: MonitorTaskForm, { rejectWithValue }) => {
    try {
      const response = await monitorApi.createTask(taskData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建监控任务失败');
    }
  }
);

export const updateTaskAsync = createAsyncThunk(
  'monitor/updateTask',
  async ({ taskId, taskData }: { taskId: string; taskData: Partial<MonitorTaskForm> }, { rejectWithValue }) => {
    try {
      const response = await monitorApi.updateTask(taskId, taskData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新监控任务失败');
    }
  }
);

export const deleteTaskAsync = createAsyncThunk(
  'monitor/deleteTask',
  async (taskId: string, { rejectWithValue }) => {
    try {
      await monitorApi.deleteTask(taskId);
      return taskId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除监控任务失败');
    }
  }
);

export const runTaskAsync = createAsyncThunk(
  'monitor/runTask',
  async (taskId: string, { rejectWithValue }) => {
    try {
      const response = await monitorApi.runTask(taskId);
      return { taskId, result: response.data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '执行监控任务失败');
    }
  }
);

export const pauseTaskAsync = createAsyncThunk(
  'monitor/pauseTask',
  async (taskId: string, { rejectWithValue }) => {
    try {
      const response = await monitorApi.pauseTask(taskId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '暂停监控任务失败');
    }
  }
);

export const resumeTaskAsync = createAsyncThunk(
  'monitor/resumeTask',
  async (taskId: string, { rejectWithValue }) => {
    try {
      const response = await monitorApi.resumeTask(taskId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '恢复监控任务失败');
    }
  }
);

export const batchOperationAsync = createAsyncThunk(
  'monitor/batchOperation',
  async ({ taskIds, operation }: { taskIds: string[]; operation: 'pause' | 'resume' | 'delete' }, { rejectWithValue }) => {
    try {
      const response = await monitorApi.batchOperation({ task_ids: taskIds, operation });
      return { taskIds, operation, result: response.data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '批量操作失败');
    }
  }
);

// Slice
const monitorSlice = createSlice({
  name: 'monitor',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentTask: (state, action: PayloadAction<MonitorTask | null>) => {
      state.currentTask = action.payload;
    },
    setSearchParams: (state, action: PayloadAction<MonitorSearchParams>) => {
      state.searchParams = action.payload;
      state.page = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.page = 1;
    },
    setSelectedTasks: (state, action: PayloadAction<string[]>) => {
      state.selectedTasks = action.payload;
    },
    toggleTaskSelection: (state, action: PayloadAction<string>) => {
      const taskId = action.payload;
      const index = state.selectedTasks.indexOf(taskId);
      if (index > -1) {
        state.selectedTasks.splice(index, 1);
      } else {
        state.selectedTasks.push(taskId);
      }
    },
    selectAllTasks: (state) => {
      state.selectedTasks = state.tasks.map(t => t.task_id);
    },
    clearSelection: (state) => {
      state.selectedTasks = [];
    },
    updateTaskStatus: (state, action: PayloadAction<{ taskId: string; status: TaskStatus }>) => {
      const { taskId, status } = action.payload;
      const task = state.tasks.find(t => t.task_id === taskId);
      if (task) {
        task.status = status;
      }
      if (state.currentTask?.task_id === taskId) {
        state.currentTask.status = status;
      }
    },
    addRunningTask: (state, action: PayloadAction<string>) => {
      if (!state.runningTasks.includes(action.payload)) {
        state.runningTasks.push(action.payload);
      }
    },
    removeRunningTask: (state, action: PayloadAction<string>) => {
      state.runningTasks = state.runningTasks.filter(id => id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // 获取监控任务列表
    builder
      .addCase(fetchMonitorTasksAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMonitorTasksAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload as PaginatedResponse<MonitorTask>;
        state.tasks = data.items;
        state.total = data.total;
        state.page = data.page;
        state.pageSize = data.page_size;
        state.error = null;
      })
      .addCase(fetchMonitorTasksAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取任务详情
    builder
      .addCase(fetchTaskByIdAsync.fulfilled, (state, action) => {
        state.currentTask = action.payload;
      });

    // 创建任务
    builder
      .addCase(createTaskAsync.fulfilled, (state, action) => {
        state.tasks.unshift(action.payload);
        state.total += 1;
      });

    // 更新任务
    builder
      .addCase(updateTaskAsync.fulfilled, (state, action) => {
        const updatedTask = action.payload;
        const index = state.tasks.findIndex(t => t.task_id === updatedTask.task_id);
        if (index > -1) {
          state.tasks[index] = updatedTask;
        }
        if (state.currentTask?.task_id === updatedTask.task_id) {
          state.currentTask = updatedTask;
        }
      });

    // 删除任务
    builder
      .addCase(deleteTaskAsync.fulfilled, (state, action) => {
        const taskId = action.payload;
        state.tasks = state.tasks.filter(t => t.task_id !== taskId);
        state.total -= 1;
        if (state.currentTask?.task_id === taskId) {
          state.currentTask = null;
        }
        state.selectedTasks = state.selectedTasks.filter(id => id !== taskId);
      });

    // 执行任务
    builder
      .addCase(runTaskAsync.pending, (state, action) => {
        const taskId = action.meta.arg;
        if (!state.runningTasks.includes(taskId)) {
          state.runningTasks.push(taskId);
        }
        // 更新任务状态为运行中
        const task = state.tasks.find(t => t.task_id === taskId);
        if (task) {
          task.status = TaskStatus.RUNNING;
        }
      })
      .addCase(runTaskAsync.fulfilled, (state, action) => {
        const { taskId } = action.payload;
        state.runningTasks = state.runningTasks.filter(id => id !== taskId);
        // 更新任务状态
        const task = state.tasks.find(t => t.task_id === taskId);
        if (task) {
          task.status = TaskStatus.SUCCESS;
          task.last_run = new Date().toISOString();
        }
      })
      .addCase(runTaskAsync.rejected, (state, action) => {
        const taskId = action.meta.arg;
        state.runningTasks = state.runningTasks.filter(id => id !== taskId);
        // 更新任务状态为失败
        const task = state.tasks.find(t => t.task_id === taskId);
        if (task) {
          task.status = TaskStatus.FAILED;
        }
      });

    // 暂停任务
    builder
      .addCase(pauseTaskAsync.fulfilled, (state, action) => {
        const updatedTask = action.payload;
        const index = state.tasks.findIndex(t => t.task_id === updatedTask.task_id);
        if (index > -1) {
          state.tasks[index] = updatedTask;
        }
      });

    // 恢复任务
    builder
      .addCase(resumeTaskAsync.fulfilled, (state, action) => {
        const updatedTask = action.payload;
        const index = state.tasks.findIndex(t => t.task_id === updatedTask.task_id);
        if (index > -1) {
          state.tasks[index] = updatedTask;
        }
      });

    // 批量操作
    builder
      .addCase(batchOperationAsync.fulfilled, (state, action) => {
        const { taskIds, operation } = action.payload;
        if (operation === 'delete') {
          state.tasks = state.tasks.filter(t => !taskIds.includes(t.task_id));
          state.total -= taskIds.length;
          state.selectedTasks = [];
        }
      });
  },
});

// 导出actions
export const {
  clearError,
  setCurrentTask,
  setSearchParams,
  setPage,
  setPageSize,
  setSelectedTasks,
  toggleTaskSelection,
  selectAllTasks,
  clearSelection,
  updateTaskStatus,
  addRunningTask,
  removeRunningTask,
} = monitorSlice.actions;

// 选择器
export const selectMonitorTasks = (state: { monitor: MonitorState }) => state.monitor.tasks;
export const selectCurrentTask = (state: { monitor: MonitorState }) => state.monitor.currentTask;
export const selectMonitorLoading = (state: { monitor: MonitorState }) => state.monitor.isLoading;
export const selectMonitorError = (state: { monitor: MonitorState }) => state.monitor.error;
export const selectMonitorPagination = (state: { monitor: MonitorState }) => ({
  total: state.monitor.total,
  page: state.monitor.page,
  pageSize: state.monitor.pageSize,
});
export const selectMonitorSearchParams = (state: { monitor: MonitorState }) => state.monitor.searchParams;
export const selectSelectedTasks = (state: { monitor: MonitorState }) => state.monitor.selectedTasks;
export const selectRunningTasks = (state: { monitor: MonitorState }) => state.monitor.runningTasks;

export default monitorSlice.reducer;
