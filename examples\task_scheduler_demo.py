"""
任务调度引擎演示

展示Celery任务调度、监控和管理功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.task_scheduler_service import (
    TaskSchedulerService, ScheduledTask, TaskBatch, TaskStatus
)


def demo_task_scheduler_service():
    """演示任务调度服务"""
    print("=== 任务调度服务演示 ===")
    
    # 创建任务调度服务
    scheduler = TaskSchedulerService()
    
    print(f"初始状态:")
    print(f"  - 活跃任务数: {len(scheduler.active_tasks)}")
    print(f"  - 任务批次数: {len(scheduler.task_batches)}")
    
    # 模拟创建任务
    print(f"\n1. 创建测试任务...")
    
    tasks_data = [
        ("crawl_1688_competitor", TaskStatus.PENDING),
        ("crawl_taobao_supplier", TaskStatus.STARTED),
        ("crawl_jd_other", TaskStatus.SUCCESS),
        ("analysis_price_trend", TaskStatus.FAILURE),
        ("notification_alert", TaskStatus.RETRY)
    ]
    
    for i, (task_name, status) in enumerate(tasks_data):
        task_id = f"task_{i+1:03d}"
        task = ScheduledTask(
            task_id=task_id,
            task_name=task_name,
            status=status,
            created_at=datetime.now()
        )
        
        # 模拟任务时间
        if status in [TaskStatus.STARTED, TaskStatus.SUCCESS, TaskStatus.FAILURE]:
            task.started_at = datetime.now()
        
        if status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]:
            task.completed_at = datetime.now()
            
        if status == TaskStatus.SUCCESS:
            task.result = {"success": True, "data": f"Result for {task_name}"}
        elif status == TaskStatus.FAILURE:
            task.error = f"Error in {task_name}: Connection timeout"
        elif status == TaskStatus.RETRY:
            task.retry_count = 2
        
        scheduler.active_tasks[task_id] = task
        print(f"  - 创建任务: {task_id} ({task_name}) - {status.value}")
    
    print(f"\n任务创建完成:")
    print(f"  - 活跃任务数: {len(scheduler.active_tasks)}")
    
    # 2. 创建任务批次
    print(f"\n2. 创建任务批次...")
    
    batch_data = [
        ("crawl_batch_1", ["task_001", "task_002"], "crawl_batch"),
        ("analysis_batch_1", ["task_003", "task_004"], "analysis_batch"),
        ("notification_batch_1", ["task_005"], "notification_batch")
    ]
    
    for batch_id, task_ids, batch_type in batch_data:
        batch = TaskBatch(
            batch_id=batch_id,
            task_ids=task_ids,
            batch_type=batch_type,
            created_at=datetime.now(),
            status=TaskStatus.STARTED
        )
        
        # 计算批次进度
        completed_tasks = sum(
            1 for tid in task_ids 
            if tid in scheduler.active_tasks and 
            scheduler.active_tasks[tid].status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]
        )
        batch.progress = completed_tasks / len(task_ids) if task_ids else 0
        
        # 收集结果
        batch.results = [
            scheduler.active_tasks[tid].result 
            for tid in task_ids 
            if tid in scheduler.active_tasks and scheduler.active_tasks[tid].result
        ]
        
        scheduler.task_batches[batch_id] = batch
        print(f"  - 创建批次: {batch_id} ({batch_type}) - {len(task_ids)}个任务, 进度: {batch.progress:.1%}")
    
    print(f"\n批次创建完成:")
    print(f"  - 任务批次数: {len(scheduler.task_batches)}")
    
    return scheduler


def demo_task_status_monitoring(scheduler: TaskSchedulerService):
    """演示任务状态监控"""
    print("\n=== 任务状态监控演示 ===")
    
    # 1. 获取任务统计
    print(f"\n1. 任务统计信息:")
    
    status_counts = {}
    for task in scheduler.active_tasks.values():
        status = task.status.value
        status_counts[status] = status_counts.get(status, 0) + 1
    
    total_tasks = len(scheduler.active_tasks)
    print(f"  - 总任务数: {total_tasks}")
    
    for status, count in status_counts.items():
        percentage = (count / total_tasks) * 100 if total_tasks > 0 else 0
        print(f"  - {status}: {count} ({percentage:.1f}%)")
    
    # 2. 活跃任务列表
    print(f"\n2. 活跃任务详情:")
    
    active_statuses = [TaskStatus.PENDING, TaskStatus.STARTED, TaskStatus.RETRY]
    active_tasks = [
        task for task in scheduler.active_tasks.values() 
        if task.status in active_statuses
    ]
    
    print(f"  活跃任务数: {len(active_tasks)}")
    for task in active_tasks:
        print(f"  - {task.task_id}: {task.task_name} ({task.status.value})")
        if task.retry_count > 0:
            print(f"    重试次数: {task.retry_count}")
    
    # 3. 完成任务列表
    print(f"\n3. 完成任务详情:")
    
    completed_statuses = [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.REVOKED]
    completed_tasks = [
        task for task in scheduler.active_tasks.values() 
        if task.status in completed_statuses
    ]
    
    print(f"  完成任务数: {len(completed_tasks)}")
    for task in completed_tasks:
        print(f"  - {task.task_id}: {task.task_name} ({task.status.value})")
        if task.status == TaskStatus.SUCCESS and task.result:
            print(f"    结果: {task.result.get('success', 'N/A')}")
        elif task.status == TaskStatus.FAILURE and task.error:
            print(f"    错误: {task.error}")
    
    # 4. 批次状态监控
    print(f"\n4. 批次状态监控:")
    
    for batch_id, batch in scheduler.task_batches.items():
        print(f"  - 批次: {batch_id}")
        print(f"    类型: {batch.batch_type}")
        print(f"    状态: {batch.status.value}")
        print(f"    进度: {batch.progress:.1%}")
        print(f"    任务数: {len(batch.task_ids)}")
        print(f"    结果数: {len(batch.results) if batch.results else 0}")


def demo_task_management_operations(scheduler: TaskSchedulerService):
    """演示任务管理操作"""
    print("\n=== 任务管理操作演示 ===")
    
    # 1. 任务状态查询
    print(f"\n1. 任务状态查询:")
    
    test_task_ids = ["task_001", "task_003", "task_005", "nonexistent"]
    
    for task_id in test_task_ids:
        if task_id in scheduler.active_tasks:
            task = scheduler.active_tasks[task_id]
            print(f"  - {task_id}: 存在")
            print(f"    名称: {task.task_name}")
            print(f"    状态: {task.status.value}")
            print(f"    创建时间: {task.created_at.strftime('%H:%M:%S')}")
            
            if task.started_at:
                print(f"    开始时间: {task.started_at.strftime('%H:%M:%S')}")
            if task.completed_at:
                print(f"    完成时间: {task.completed_at.strftime('%H:%M:%S')}")
        else:
            print(f"  - {task_id}: 不存在")
    
    # 2. 批次状态查询
    print(f"\n2. 批次状态查询:")
    
    test_batch_ids = ["crawl_batch_1", "analysis_batch_1", "nonexistent_batch"]
    
    for batch_id in test_batch_ids:
        if batch_id in scheduler.task_batches:
            batch = scheduler.task_batches[batch_id]
            print(f"  - {batch_id}: 存在")
            print(f"    类型: {batch.batch_type}")
            print(f"    状态: {batch.status.value}")
            print(f"    进度: {batch.progress:.1%}")
            print(f"    包含任务: {', '.join(batch.task_ids)}")
        else:
            print(f"  - {batch_id}: 不存在")
    
    # 3. 模拟任务状态更新
    print(f"\n3. 模拟任务状态更新:")
    
    # 将一个待处理任务更新为开始状态
    pending_tasks = [
        task for task in scheduler.active_tasks.values() 
        if task.status == TaskStatus.PENDING
    ]
    
    if pending_tasks:
        task = pending_tasks[0]
        old_status = task.status.value
        task.status = TaskStatus.STARTED
        task.started_at = datetime.now()
        
        print(f"  - 任务 {task.task_id} 状态更新: {old_status} -> {task.status.value}")
        print(f"    开始时间: {task.started_at.strftime('%H:%M:%S')}")
    
    # 将一个重试任务更新为成功状态
    retry_tasks = [
        task for task in scheduler.active_tasks.values() 
        if task.status == TaskStatus.RETRY
    ]
    
    if retry_tasks:
        task = retry_tasks[0]
        old_status = task.status.value
        task.status = TaskStatus.SUCCESS
        task.completed_at = datetime.now()
        task.result = {"success": True, "retry_successful": True}
        
        print(f"  - 任务 {task.task_id} 重试成功: {old_status} -> {task.status.value}")
        print(f"    完成时间: {task.completed_at.strftime('%H:%M:%S')}")
        print(f"    重试次数: {task.retry_count}")


def demo_performance_metrics(scheduler: TaskSchedulerService):
    """演示性能指标"""
    print("\n=== 性能指标演示 ===")
    
    # 1. 任务执行统计
    print(f"\n1. 任务执行统计:")
    
    total_tasks = len(scheduler.active_tasks)
    successful_tasks = sum(
        1 for task in scheduler.active_tasks.values() 
        if task.status == TaskStatus.SUCCESS
    )
    failed_tasks = sum(
        1 for task in scheduler.active_tasks.values() 
        if task.status == TaskStatus.FAILURE
    )
    retry_tasks = sum(
        1 for task in scheduler.active_tasks.values() 
        if task.status == TaskStatus.RETRY
    )
    
    success_rate = (successful_tasks / total_tasks) * 100 if total_tasks > 0 else 0
    failure_rate = (failed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
    
    print(f"  - 总任务数: {total_tasks}")
    print(f"  - 成功任务: {successful_tasks} ({success_rate:.1f}%)")
    print(f"  - 失败任务: {failed_tasks} ({failure_rate:.1f}%)")
    print(f"  - 重试任务: {retry_tasks}")
    
    # 2. 重试统计
    print(f"\n2. 重试统计:")
    
    total_retries = sum(task.retry_count for task in scheduler.active_tasks.values())
    tasks_with_retries = sum(
        1 for task in scheduler.active_tasks.values() 
        if task.retry_count > 0
    )
    
    avg_retries = total_retries / tasks_with_retries if tasks_with_retries > 0 else 0
    
    print(f"  - 总重试次数: {total_retries}")
    print(f"  - 有重试的任务: {tasks_with_retries}")
    print(f"  - 平均重试次数: {avg_retries:.1f}")
    
    # 3. 批次执行统计
    print(f"\n3. 批次执行统计:")
    
    total_batches = len(scheduler.task_batches)
    avg_progress = sum(batch.progress for batch in scheduler.task_batches.values()) / total_batches if total_batches > 0 else 0
    
    print(f"  - 总批次数: {total_batches}")
    print(f"  - 平均进度: {avg_progress:.1%}")
    
    for batch_id, batch in scheduler.task_batches.items():
        print(f"  - {batch_id}: {batch.progress:.1%} ({len(batch.task_ids)}个任务)")
    
    # 4. 任务类型分布
    print(f"\n4. 任务类型分布:")
    
    task_types = {}
    for task in scheduler.active_tasks.values():
        task_type = task.task_name.split('_')[0]  # 获取任务类型前缀
        task_types[task_type] = task_types.get(task_type, 0) + 1
    
    for task_type, count in sorted(task_types.items()):
        percentage = (count / total_tasks) * 100 if total_tasks > 0 else 0
        print(f"  - {task_type}: {count} ({percentage:.1f}%)")


def main():
    """主演示函数"""
    print("🚀 任务调度引擎演示")
    print("=" * 50)
    
    # 1. 创建和初始化任务调度服务
    scheduler = demo_task_scheduler_service()
    
    # 2. 任务状态监控
    demo_task_status_monitoring(scheduler)
    
    # 3. 任务管理操作
    demo_task_management_operations(scheduler)
    
    # 4. 性能指标
    demo_performance_metrics(scheduler)
    
    print("\n" + "=" * 50)
    print("✅ 任务调度引擎演示完成！")
    print("\n核心功能:")
    print("- 任务调度和状态管理")
    print("- 批次任务处理")
    print("- 任务监控和统计")
    print("- 重试机制和错误处理")
    print("- 性能指标和分析")
    print("- 多种任务类型支持")
    
    print(f"\n最终统计:")
    print(f"- 管理任务数: {len(scheduler.active_tasks)}")
    print(f"- 管理批次数: {len(scheduler.task_batches)}")
    
    # 显示系统状态摘要
    status_summary = {}
    for task in scheduler.active_tasks.values():
        status = task.status.value
        status_summary[status] = status_summary.get(status, 0) + 1
    
    print(f"- 任务状态分布: {dict(status_summary)}")


if __name__ == "__main__":
    main()
