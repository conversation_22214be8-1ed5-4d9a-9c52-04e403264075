#!/usr/bin/env python3
"""
测试数据生成脚本

在TimescaleDB中生成多个商品的时序模拟数据，用于真实的数据库集成测试
"""

import asyncio
import asyncpg
import math
import random
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any
import json
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import get_settings


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.conn = None
    
    async def connect(self):
        """连接数据库"""
        try:
            # 获取数据库URL，支持不同的配置结构
            if hasattr(self.settings, 'DATABASE_URL'):
                db_url = self.settings.DATABASE_URL
            elif hasattr(self.settings, 'database') and hasattr(self.settings.database, 'url'):
                db_url = self.settings.database.url
            else:
                # 使用默认的数据库URL
                db_url = "postgresql://moniit:password@localhost:5432/moniit"
                print(f"⚠️ 未找到数据库配置，使用默认URL: {db_url}")

            # 检查是否需要修改端口（如果用户指定了5433端口）
            if ":5433/" not in db_url and os.getenv("USE_TEST_DB_PORT") == "true":
                db_url = db_url.replace(":5432/", ":5433/")
                print(f"🔄 使用测试数据库端口5433")

            self.conn = await asyncpg.connect(db_url)
            print(f"✅ 成功连接到TimescaleDB: {db_url}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            print(f"💡 请确保TimescaleDB正在运行在5433端口")
            print(f"   docker-compose up timescaledb")
            raise
    
    async def close(self):
        """关闭数据库连接"""
        if self.conn:
            await self.conn.close()
    
    async def setup_tables(self):
        """创建测试表结构"""
        print("🔧 创建测试表结构...")
        
        # 创建商品表
        await self.conn.execute("""
            CREATE TABLE IF NOT EXISTS test_products (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(500) NOT NULL,
                url TEXT NOT NULL,
                platform VARCHAR(50) NOT NULL,
                category VARCHAR(100),
                brand VARCHAR(100),
                model VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # 创建价格历史表（时序表）
        await self.conn.execute("""
            CREATE TABLE IF NOT EXISTS test_price_history (
                id SERIAL,
                product_id VARCHAR(50) NOT NULL,
                price DECIMAL(12,2) NOT NULL,
                original_price DECIMAL(12,2),
                currency VARCHAR(10) DEFAULT 'CNY',
                availability VARCHAR(50),
                source_url TEXT,
                recorded_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT NOW(),
                FOREIGN KEY (product_id) REFERENCES test_products(id)
            )
        """)
        
        # 创建时序表的超表（TimescaleDB特性）
        try:
            await self.conn.execute("""
                SELECT create_hypertable('test_price_history', 'recorded_at', 
                                       if_not_exists => TRUE)
            """)
            print("✅ 创建TimescaleDB超表成功")
        except Exception as e:
            print(f"⚠️ 创建超表失败（可能已存在）: {e}")
        
        # 创建供货商表
        await self.conn.execute("""
            CREATE TABLE IF NOT EXISTS test_suppliers (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                contact_person VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(50),
                address TEXT,
                business_license VARCHAR(100),
                credit_rating VARCHAR(10),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # 创建供货商价格表
        await self.conn.execute("""
            CREATE TABLE IF NOT EXISTS test_supplier_prices (
                id SERIAL,
                supplier_id VARCHAR(50) NOT NULL,
                product_id VARCHAR(50) NOT NULL,
                price DECIMAL(12,2) NOT NULL,
                minimum_quantity INTEGER DEFAULT 1,
                lead_time_days INTEGER DEFAULT 7,
                recorded_at TIMESTAMP NOT NULL,
                FOREIGN KEY (supplier_id) REFERENCES test_suppliers(id),
                FOREIGN KEY (product_id) REFERENCES test_products(id)
            )
        """)
        
        print("✅ 表结构创建完成")
    
    async def generate_products(self) -> List[Dict]:
        """生成测试商品数据"""
        print("📱 生成商品数据...")
        
        products = [
            {
                "id": "test_iphone_15_pro",
                "name": "iPhone 15 Pro 钛金属 128GB",
                "url": "https://www.apple.com/cn/iphone-15-pro/",
                "platform": "apple",
                "category": "智能手机",
                "brand": "Apple",
                "model": "iPhone 15 Pro"
            },
            {
                "id": "test_macbook_pro_14",
                "name": "MacBook Pro 14英寸 M3芯片",
                "url": "https://www.apple.com/cn/macbook-pro-14-and-16/",
                "platform": "apple", 
                "category": "笔记本电脑",
                "brand": "Apple",
                "model": "MacBook Pro 14"
            },
            {
                "id": "test_airpods_pro_2",
                "name": "AirPods Pro 第二代",
                "url": "https://www.apple.com/cn/airpods-pro/",
                "platform": "apple",
                "category": "无线耳机",
                "brand": "Apple", 
                "model": "AirPods Pro 2"
            },
            {
                "id": "test_xiaomi_14_ultra",
                "name": "小米14 Ultra 16GB+512GB",
                "url": "https://www.mi.com/xiaomi-14-ultra",
                "platform": "xiaomi",
                "category": "智能手机",
                "brand": "小米",
                "model": "小米14 Ultra"
            },
            {
                "id": "test_huawei_mate60_pro",
                "name": "华为Mate60 Pro 12GB+256GB",
                "url": "https://consumer.huawei.com/cn/phones/mate60-pro/",
                "platform": "huawei",
                "category": "智能手机", 
                "brand": "华为",
                "model": "Mate60 Pro"
            }
        ]
        
        # 清理旧数据
        await self.conn.execute("DELETE FROM test_price_history")
        await self.conn.execute("DELETE FROM test_supplier_prices") 
        await self.conn.execute("DELETE FROM test_products")
        
        # 插入商品数据
        for product in products:
            await self.conn.execute("""
                INSERT INTO test_products (id, name, url, platform, category, brand, model)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """, product["id"], product["name"], product["url"], 
                product["platform"], product["category"], product["brand"], product["model"])
        
        print(f"✅ 生成了 {len(products)} 个商品")
        return products
    
    async def generate_price_history(self, products: List[Dict], days: int = 90):
        """生成价格历史数据"""
        print(f"💰 生成 {days} 天的价格历史数据...")
        
        base_time = datetime.now() - timedelta(days=days)
        total_records = 0
        
        # 定义每个商品的基础价格和特性
        product_configs = {
            "test_iphone_15_pro": {"base_price": 8999, "volatility": 0.05, "trend": 0.001},
            "test_macbook_pro_14": {"base_price": 14999, "volatility": 0.03, "trend": -0.0005},
            "test_airpods_pro_2": {"base_price": 1899, "volatility": 0.08, "trend": 0.002},
            "test_xiaomi_14_ultra": {"base_price": 6499, "volatility": 0.12, "trend": 0.003},
            "test_huawei_mate60_pro": {"base_price": 6999, "volatility": 0.10, "trend": 0.001}
        }
        
        for product in products:
            product_id = product["id"]
            config = product_configs.get(product_id, {"base_price": 5000, "volatility": 0.08, "trend": 0})
            
            base_price = config["base_price"]
            volatility = config["volatility"]
            trend = config["trend"]
            
            print(f"  生成 {product['name']} 的价格数据...")
            
            # 生成每6小时一个数据点
            for hour in range(0, days * 24, 6):
                timestamp = base_time + timedelta(hours=hour)
                
                # 1. 长期趋势
                trend_factor = hour * trend
                
                # 2. 季节性波动（周周期和月周期）
                weekly_cycle = math.sin(2 * math.pi * hour / (24 * 7)) * base_price * 0.02
                monthly_cycle = math.sin(2 * math.pi * hour / (24 * 30)) * base_price * 0.01
                
                # 3. 随机波动
                random.seed(hour + hash(product_id))  # 确保可重现
                random_factor = random.gauss(0, base_price * volatility)
                
                # 4. 特殊事件（促销、发布会等）
                event_factor = 0
                if hour % (24 * 14) < 24:  # 每两周一天促销
                    event_factor = -base_price * 0.1
                elif hour % (24 * 30) < 12:  # 每月半天新品发布涨价
                    event_factor = base_price * 0.05
                
                # 计算最终价格
                price_change = trend_factor + weekly_cycle + monthly_cycle + random_factor + event_factor
                final_price = base_price + price_change
                
                # 确保价格在合理范围内
                final_price = max(final_price, base_price * 0.6)  # 最低6折
                final_price = min(final_price, base_price * 1.5)  # 最高1.5倍
                
                # 生成原价（通常比现价高一些）
                original_price = final_price * random.uniform(1.0, 1.3)
                
                # 随机生成可用性状态
                availability_options = ["in_stock", "limited", "out_of_stock", "pre_order"]
                availability_weights = [0.7, 0.15, 0.1, 0.05]
                availability = random.choices(availability_options, weights=availability_weights)[0]
                
                # 插入价格记录
                await self.conn.execute("""
                    INSERT INTO test_price_history 
                    (product_id, price, original_price, currency, availability, source_url, recorded_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, product_id, round(final_price, 2), round(original_price, 2), 
                    "CNY", availability, product["url"], timestamp)
                
                total_records += 1
        
        print(f"✅ 生成了 {total_records} 条价格记录")
    
    async def generate_suppliers(self) -> List[Dict]:
        """生成供货商数据"""
        print("🏭 生成供货商数据...")
        
        suppliers = [
            {
                "id": "supplier_apple_official",
                "name": "Apple官方授权经销商",
                "contact_person": "张经理",
                "email": "<EMAIL>",
                "phone": "+86-************",
                "address": "北京市朝阳区苹果园大厦",
                "business_license": "91110000000000001A",
                "credit_rating": "AAA"
            },
            {
                "id": "supplier_tech_wholesale",
                "name": "科技数码批发商",
                "contact_person": "李总",
                "email": "<EMAIL>", 
                "phone": "+86-138-0000-0001",
                "address": "深圳市华强北电子市场",
                "business_license": "91440300000000002B",
                "credit_rating": "AA"
            },
            {
                "id": "supplier_xiaomi_partner",
                "name": "小米生态链合作伙伴",
                "contact_person": "王经理",
                "email": "<EMAIL>",
                "phone": "+86-010-8888-9999",
                "address": "北京市海淀区小米科技园",
                "business_license": "91110000000000003C", 
                "credit_rating": "AA"
            }
        ]
        
        # 清理旧数据
        await self.conn.execute("DELETE FROM test_suppliers")
        
        # 插入供货商数据
        for supplier in suppliers:
            await self.conn.execute("""
                INSERT INTO test_suppliers 
                (id, name, contact_person, email, phone, address, business_license, credit_rating)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """, supplier["id"], supplier["name"], supplier["contact_person"],
                supplier["email"], supplier["phone"], supplier["address"],
                supplier["business_license"], supplier["credit_rating"])
        
        print(f"✅ 生成了 {len(suppliers)} 个供货商")
        return suppliers
    
    async def generate_supplier_prices(self, products: List[Dict], suppliers: List[Dict]):
        """生成供货商价格数据"""
        print("💼 生成供货商价格数据...")
        
        total_records = 0
        base_time = datetime.now() - timedelta(days=30)
        
        for product in products:
            for supplier in suppliers:
                # 不是所有供货商都有所有商品
                if random.random() < 0.7:  # 70%概率有这个商品
                    
                    # 生成每周的供货商价格
                    for week in range(5):  # 5周数据
                        timestamp = base_time + timedelta(weeks=week)
                        
                        # 供货商价格通常比零售价低20-40%
                        retail_price = await self.conn.fetchval("""
                            SELECT AVG(price) FROM test_price_history 
                            WHERE product_id = $1 AND recorded_at <= $2
                            ORDER BY recorded_at DESC LIMIT 10
                        """, product["id"], timestamp)
                        
                        if retail_price:
                            supplier_discount = random.uniform(0.6, 0.8)  # 6-8折
                            supplier_price = float(retail_price) * supplier_discount
                            
                            # 最小起订量
                            min_qty = random.choice([1, 5, 10, 50, 100])
                            
                            # 交货周期
                            lead_time = random.randint(3, 21)
                            
                            await self.conn.execute("""
                                INSERT INTO test_supplier_prices
                                (supplier_id, product_id, price, minimum_quantity, lead_time_days, recorded_at)
                                VALUES ($1, $2, $3, $4, $5, $6)
                            """, supplier["id"], product["id"], round(supplier_price, 2),
                                min_qty, lead_time, timestamp)
                            
                            total_records += 1
        
        print(f"✅ 生成了 {total_records} 条供货商价格记录")
    
    async def create_indexes(self):
        """创建索引优化查询性能"""
        print("🔍 创建数据库索引...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_price_history_product_time ON test_price_history(product_id, recorded_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_time ON test_price_history(recorded_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_supplier_prices_supplier_product ON test_supplier_prices(supplier_id, product_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_platform ON test_products(platform)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON test_products(category)"
        ]
        
        for index_sql in indexes:
            try:
                await self.conn.execute(index_sql)
            except Exception as e:
                print(f"⚠️ 创建索引失败: {e}")
        
        print("✅ 索引创建完成")
    
    async def generate_statistics(self):
        """生成数据统计信息"""
        print("\n📊 数据统计:")
        
        # 商品统计
        product_count = await self.conn.fetchval("SELECT COUNT(*) FROM test_products")
        print(f"  商品数量: {product_count}")
        
        # 价格记录统计
        price_count = await self.conn.fetchval("SELECT COUNT(*) FROM test_price_history")
        print(f"  价格记录数量: {price_count}")
        
        # 供货商统计
        supplier_count = await self.conn.fetchval("SELECT COUNT(*) FROM test_suppliers")
        print(f"  供货商数量: {supplier_count}")
        
        # 供货商价格统计
        supplier_price_count = await self.conn.fetchval("SELECT COUNT(*) FROM test_supplier_prices")
        print(f"  供货商价格记录: {supplier_price_count}")
        
        # 时间范围统计
        time_range = await self.conn.fetchrow("""
            SELECT MIN(recorded_at) as earliest, MAX(recorded_at) as latest 
            FROM test_price_history
        """)
        if time_range:
            print(f"  数据时间范围: {time_range['earliest']} 到 {time_range['latest']}")
        
        # 每个商品的数据量
        product_stats = await self.conn.fetch("""
            SELECT p.name, COUNT(ph.id) as record_count
            FROM test_products p
            LEFT JOIN test_price_history ph ON p.id = ph.product_id
            GROUP BY p.id, p.name
            ORDER BY record_count DESC
        """)
        
        print("\n  各商品数据量:")
        for stat in product_stats:
            print(f"    {stat['name']}: {stat['record_count']} 条记录")


async def main():
    """主函数"""
    print("🚀 开始生成TimescaleDB测试数据")
    print("=" * 60)
    
    generator = TestDataGenerator()
    
    try:
        # 连接数据库
        await generator.connect()
        
        # 创建表结构
        await generator.setup_tables()
        
        # 生成商品数据
        products = await generator.generate_products()
        
        # 生成价格历史数据
        await generator.generate_price_history(products, days=90)
        
        # 生成供货商数据
        suppliers = await generator.generate_suppliers()
        
        # 生成供货商价格数据
        await generator.generate_supplier_prices(products, suppliers)
        
        # 创建索引
        await generator.create_indexes()
        
        # 生成统计信息
        await generator.generate_statistics()
        
        print("\n🎉 测试数据生成完成！")
        print("现在可以运行数据库集成测试了:")
        print("  python -m pytest tests/test_database_integration.py -v")
        
    except Exception as e:
        print(f"❌ 生成测试数据失败: {e}")
        raise
    finally:
        await generator.close()


if __name__ == "__main__":
    asyncio.run(main())
