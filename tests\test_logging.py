"""
日志系统测试
"""

import pytest
import logging
import tempfile
import os
from unittest.mock import patch, MagicMock
from pathlib import Path

from app.core.logging import setup_logging, get_logger, ColoredFormatter, StructuredFormatter
from app.core.config import LoggingSettings


class TestColoredFormatter:
    """彩色格式化器测试"""

    def test_colored_formatter_initialization(self):
        """测试彩色格式化器初始化"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        assert formatter is not None
        assert hasattr(formatter, 'COLORS')

    def test_colored_formatter_colors(self):
        """测试颜色代码"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")

        # 验证颜色代码存在
        assert 'DEBUG' in formatter.COLORS
        assert 'INFO' in formatter.COLORS
        assert 'WARNING' in formatter.COLORS
        assert 'ERROR' in formatter.COLORS
        assert 'CRITICAL' in formatter.COLORS
        assert 'RESET' in formatter.COLORS

    def test_colored_formatter_format(self):
        """测试格式化功能"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")

        # 创建日志记录
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )

        # 格式化
        formatted = formatter.format(record)

        # 验证包含颜色代码和消息
        assert "Test message" in formatted
        assert "\033[" in formatted  # 包含ANSI颜色代码


class TestStructuredFormatter:
    """结构化格式化器测试"""

    def test_structured_formatter_initialization(self):
        """测试结构化格式化器初始化"""
        formatter = StructuredFormatter()
        assert formatter is not None

    def test_structured_formatter_format(self):
        """测试结构化格式化"""
        formatter = StructuredFormatter()

        # 创建日志记录
        record = logging.LogRecord(
            name="test.module", level=logging.INFO, pathname="test.py", lineno=10,
            msg="Test message", args=(), exc_info=None
        )

        # 格式化
        formatted = formatter.format(record)

        # 验证JSON格式
        import json
        log_data = json.loads(formatted)

        assert log_data["level"] == "INFO"
        assert log_data["logger"] == "test.module"
        assert log_data["message"] == "Test message"
        assert log_data["module"] == "test"
        assert "timestamp" in log_data


class TestLoggingSetup:
    """日志设置测试"""

    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    def test_setup_logging_creates_directory(self, temp_log_dir):
        """测试设置日志时创建目录"""
        log_file = os.path.join(temp_log_dir, "subdir", "test.log")
        config = LoggingSettings(
            level="INFO",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file=log_file,
            max_size="1MB",
            backup_count=3,
            console_output=False
        )

        setup_logging(config)

        # 验证目录被创建
        assert os.path.exists(os.path.dirname(log_file))

    def test_setup_logging_file_handler(self, temp_log_dir):
        """测试文件处理器设置"""
        log_file = os.path.join(temp_log_dir, "test.log")
        config = LoggingSettings(
            level="INFO",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file=log_file,
            console_output=False
        )

        setup_logging(config)
        logger = get_logger("test_file")

        # 写入日志
        logger.info("Test log message")

        # 验证日志文件被创建
        assert os.path.exists(log_file)

        # 验证日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # 结构化格式是JSON，每行一个JSON对象
            import json
            found_test_message = False
            for line in lines:
                if line.strip():
                    log_data = json.loads(line.strip())
                    if log_data.get("message") == "Test log message":
                        assert log_data["logger"] == "test_file"
                        assert log_data["level"] == "INFO"
                        found_test_message = True
                        break
            assert found_test_message, "Test log message not found in log file"

    def test_setup_logging_console_handler(self, temp_log_dir):
        """测试控制台处理器设置"""
        # 使用空字符串而不是None
        config = LoggingSettings(
            level="INFO",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file="",  # 空字符串表示不使用文件
            console_output=True
        )

        setup_logging(config)

        # 检查根日志器是否有控制台处理器
        root_logger = logging.getLogger()
        console_handlers = [
            h for h in root_logger.handlers
            if isinstance(h, logging.StreamHandler)
        ]
        assert len(console_handlers) > 0

    def test_setup_logging_level_configuration(self, temp_log_dir):
        """测试不同日志级别配置"""
        levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

        for level in levels:
            log_file = os.path.join(temp_log_dir, f"test_{level.lower()}.log")
            config = LoggingSettings(
                level=level,
                format="%(levelname)s - %(message)s",
                file=log_file,
                console_output=False
            )

            setup_logging(config)
            root_logger = logging.getLogger()

            assert root_logger.level == getattr(logging, level)


class TestUtilityFunctions:
    """工具函数测试"""

    def test_parse_size_kb(self):
        """测试解析KB大小"""
        from app.core.logging import parse_size

        assert parse_size("1KB") == 1024
        assert parse_size("10KB") == 10 * 1024
        assert parse_size("1kb") == 1024  # 小写

    def test_parse_size_mb(self):
        """测试解析MB大小"""
        from app.core.logging import parse_size

        assert parse_size("1MB") == 1024 * 1024
        assert parse_size("5MB") == 5 * 1024 * 1024
        assert parse_size("1mb") == 1024 * 1024  # 小写

    def test_parse_size_gb(self):
        """测试解析GB大小"""
        from app.core.logging import parse_size

        assert parse_size("1GB") == 1024 * 1024 * 1024
        assert parse_size("2GB") == 2 * 1024 * 1024 * 1024

    def test_parse_size_bytes(self):
        """测试解析字节大小"""
        from app.core.logging import parse_size

        assert parse_size("1024") == 1024
        assert parse_size("2048") == 2048


class TestGetLogger:
    """获取日志器函数测试"""

    def test_get_logger_returns_logger(self):
        """测试获取日志器返回Logger实例"""
        logger = get_logger("test_module")

        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_module"

    def test_get_logger_same_name_returns_same_instance(self):
        """测试相同名称返回相同实例"""
        logger1 = get_logger("same_module")
        logger2 = get_logger("same_module")

        assert logger1 is logger2

    def test_get_logger_different_names_return_different_instances(self):
        """测试不同名称返回不同实例"""
        logger1 = get_logger("module1")
        logger2 = get_logger("module2")

        assert logger1 is not logger2
        assert logger1.name == "module1"
        assert logger2.name == "module2"


class TestLoggingIntegration:
    """日志系统集成测试"""

    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    def test_logging_workflow(self, temp_log_dir):
        """测试完整的日志工作流"""
        log_file = os.path.join(temp_log_dir, "workflow.log")

        # 1. 设置日志配置
        config = LoggingSettings(
            level="DEBUG",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file=log_file,
            console_output=False
        )

        # 2. 初始化日志系统
        setup_logging(config)

        # 3. 获取不同模块的日志器
        api_logger = get_logger("api")
        db_logger = get_logger("database")
        cache_logger = get_logger("cache")

        # 4. 写入不同级别的日志
        api_logger.debug("API调试信息")
        api_logger.info("API请求处理")
        api_logger.warning("API警告信息")

        db_logger.info("数据库连接成功")
        db_logger.error("数据库查询失败")

        cache_logger.info("缓存命中")
        cache_logger.warning("缓存过期")

        # 5. 验证日志文件内容
        assert os.path.exists(log_file)

        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()

            # 验证不同模块的日志（JSON格式）
            assert "api" in content
            assert "database" in content
            assert "cache" in content

            # 验证不同级别的日志
            assert "DEBUG" in content
            assert "INFO" in content
            assert "WARNING" in content
            assert "ERROR" in content

            # 验证具体消息
            assert "API调试信息" in content
            assert "数据库连接成功" in content
            assert "缓存命中" in content

    def test_logging_with_exceptions(self, temp_log_dir):
        """测试异常日志记录"""
        log_file = os.path.join(temp_log_dir, "exceptions.log")

        config = LoggingSettings(
            level="ERROR",
            format="%(name)s - %(levelname)s - %(message)s",
            file=log_file,
            console_output=False
        )

        setup_logging(config)
        logger = get_logger("exception_test")

        # 模拟异常
        try:
            raise ValueError("测试异常")
        except ValueError as e:
            logger.exception("捕获到异常: %s", str(e))

        # 验证异常日志
        assert os.path.exists(log_file)

        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "捕获到异常" in content
            assert "测试异常" in content
            assert "ValueError" in content
