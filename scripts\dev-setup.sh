#!/bin/bash
# 开发环境设置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    required_version="3.11"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)"; then
        log_warning "Python版本 $python_version 低于推荐版本 $required_version"
        log_warning "建议升级到Python 3.11+"
    else
        log_success "Python版本检查通过: $python_version"
    fi
}

# 创建虚拟环境
create_venv() {
    log_info "创建Python虚拟环境..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    log_success "虚拟环境准备完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    source venv/bin/activate
    pip install -r requirements.txt
    
    log_success "依赖安装完成"
}

# 创建开发配置
create_dev_config() {
    log_info "创建开发环境配置..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        
        # 设置开发环境默认值
        sed -i 's/DATABASE_URL=.*/DATABASE_URL=postgresql:\/\/postgres:dev_password@localhost:5433\/ecommerce_monitor_dev/' .env
        sed -i 's/REDIS_URL=.*/REDIS_URL=redis:\/\/localhost:6380\/0/' .env
        sed -i 's/DEBUG=.*/DEBUG=true/' .env
        sed -i 's/ENVIRONMENT=.*/ENVIRONMENT=development/' .env
        
        log_success "开发环境配置创建完成"
        log_warning "请编辑 .env 文件，配置API密钥等敏感信息"
    else
        log_info "配置文件已存在"
    fi
}

# 启动开发环境服务
start_dev_services() {
    log_info "启动开发环境服务..."
    
    # 启动数据库和Redis
    docker-compose -f docker-compose.dev.yml up -d
    
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        log_success "开发环境服务启动完成"
    else
        log_error "开发环境服务启动失败"
        docker-compose -f docker-compose.dev.yml logs
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    source venv/bin/activate
    
    # 等待数据库完全启动
    log_info "等待数据库就绪..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres -d ecommerce_monitor_dev; then
            log_success "数据库连接成功"
            break
        fi
        
        log_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库连接超时"
        exit 1
    fi
    
    # 运行数据库迁移
    if command -v alembic &> /dev/null; then
        log_info "运行数据库迁移..."
        alembic upgrade head
        log_success "数据库迁移完成"
    else
        log_warning "Alembic未安装，跳过数据库迁移"
    fi
}

# 安装开发工具
install_dev_tools() {
    log_info "安装开发工具..."
    
    source venv/bin/activate
    
    # 安装pre-commit hooks
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        log_success "Pre-commit hooks安装完成"
    fi
}

# 显示开发环境信息
show_dev_info() {
    echo ""
    echo "🎉 开发环境设置完成！"
    echo ""
    echo "=== 开发环境信息 ==="
    echo "🐍 Python虚拟环境: venv/"
    echo "💾 开发数据库: localhost:5433"
    echo "🔴 开发Redis: localhost:6380"
    echo "🌸 Flower监控: http://localhost:5556"
    echo ""
    echo "=== 启动开发服务器 ==="
    echo "1. 激活虚拟环境: source venv/bin/activate"
    echo "2. 启动应用: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    echo "3. 或使用: make dev"
    echo ""
    echo "=== 开发工具 ==="
    echo "运行测试: pytest"
    echo "代码格式化: black app/ && isort app/"
    echo "代码检查: flake8 app/"
    echo "查看覆盖率: pytest --cov=app --cov-report=html"
    echo ""
    echo "=== 数据库操作 ==="
    echo "连接数据库: make db-shell"
    echo "创建迁移: alembic revision --autogenerate -m '描述'"
    echo "应用迁移: alembic upgrade head"
    echo ""
}

# 主函数
main() {
    echo "电商监控系统开发环境设置 v1.0"
    echo "================================"
    
    check_python
    create_venv
    install_dependencies
    create_dev_config
    start_dev_services
    init_database
    install_dev_tools
    show_dev_info
    
    log_success "开发环境设置完成！"
}

# 错误处理
trap 'log_error "设置过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
