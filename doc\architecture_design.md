# Moniit 架构设计文档

## 📋 目录

1. [系统概述](#系统概述)
2. [整体架构](#整体架构)
3. [核心模块设计](#核心模块设计)
4. [数据架构](#数据架构)
5. [技术选型](#技术选型)
6. [扩展性设计](#扩展性设计)
7. [安全架构](#安全架构)
8. [性能设计](#性能设计)

## 🎯 系统概述

### 系统定位
Moniit是一个企业级商品价格监控系统，专注于为电商、零售、采购等行业提供智能化的价格监控、分析和决策支持服务。

### 核心价值
- **实时监控**: 7×24小时自动监控商品价格变化
- **智能分析**: AI驱动的价格趋势分析和预测
- **决策支持**: 基于数据的定价和采购决策建议
- **成本优化**: 全链路成本分析和利润优化
- **供应链管理**: 供货商评估和关系管理

### 设计原则
- **高可用性**: 系统可用性≥99.9%
- **高性能**: 支持百万级商品监控
- **可扩展性**: 支持水平扩展和垂直扩展
- **数据一致性**: 确保数据准确性和完整性
- **安全性**: 多层次安全防护
- **易维护性**: 模块化设计，便于维护和升级

## 🏗️ 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "用户层"
        WEB[Web界面]
        API[API接口]
        MOBILE[移动端]
    end
    
    subgraph "接入层"
        LB[负载均衡器]
        GATEWAY[API网关]
        CDN[CDN]
    end
    
    subgraph "应用层"
        WEBAPP[Web应用]
        APIAPP[API服务]
        ADMIN[管理后台]
    end
    
    subgraph "业务层"
        PRODUCT[商品管理]
        MONITOR[价格监控]
        ANALYSIS[数据分析]
        SUPPLIER[供货商管理]
        PROFIT[利润分析]
    end
    
    subgraph "服务层"
        CRAWLER[爬虫服务]
        SCHEDULER[任务调度]
        NOTIFICATION[通知服务]
        REPORT[报表服务]
    end
    
    subgraph "数据层"
        CACHE[(缓存层)]
        DB[(主数据库)]
        TSDB[(时序数据库)]
        SEARCH[(搜索引擎)]
    end
    
    subgraph "基础设施层"
        MONITOR_SYS[监控系统]
        LOG[日志系统]
        CONFIG[配置中心]
        SECURITY[安全中心]
    end
    
    WEB --> LB
    API --> GATEWAY
    MOBILE --> GATEWAY
    
    LB --> WEBAPP
    GATEWAY --> APIAPP
    
    WEBAPP --> PRODUCT
    APIAPP --> MONITOR
    ADMIN --> ANALYSIS
    
    PRODUCT --> CRAWLER
    MONITOR --> SCHEDULER
    ANALYSIS --> REPORT
    
    CRAWLER --> DB
    SCHEDULER --> CACHE
    NOTIFICATION --> TSDB
```

### 架构分层

**1. 用户层 (Presentation Layer)**
- **Web界面**: 基于Vue.js的响应式Web应用
- **API接口**: RESTful API和GraphQL接口
- **移动端**: 原生移动应用和PWA

**2. 接入层 (Gateway Layer)**
- **负载均衡器**: Nginx/HAProxy实现高可用
- **API网关**: Kong/Zuul实现统一接入
- **CDN**: 静态资源加速和缓存

**3. 应用层 (Application Layer)**
- **Web应用**: Django Web框架
- **API服务**: Django REST Framework
- **管理后台**: Django Admin定制

**4. 业务层 (Business Layer)**
- **商品管理**: 商品信息管理和分类
- **价格监控**: 价格采集和监控任务
- **数据分析**: 价格趋势和市场分析
- **供货商管理**: 供货商评估和关系管理
- **利润分析**: 成本核算和利润优化

**5. 服务层 (Service Layer)**
- **爬虫服务**: 分布式爬虫集群
- **任务调度**: Celery异步任务处理
- **通知服务**: 邮件、短信、推送通知
- **报表服务**: 数据报表生成和导出

**6. 数据层 (Data Layer)**
- **缓存层**: Redis集群
- **主数据库**: PostgreSQL主从集群
- **时序数据库**: TimescaleDB集群
- **搜索引擎**: Elasticsearch集群

**7. 基础设施层 (Infrastructure Layer)**
- **监控系统**: Prometheus + Grafana
- **日志系统**: ELK Stack
- **配置中心**: Consul/Etcd
- **安全中心**: 认证授权和安全防护

## 🧩 核心模块设计

### 1. 商品管理模块

**功能职责**:
- 商品信息的CRUD操作
- 商品分类和标签管理
- 商品状态生命周期管理
- 批量导入导出功能

**核心类设计**:
```python
class Product(models.Model):
    """商品模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=200)
    url = models.URLField()
    platform = models.CharField(max_length=50)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class ProductManager:
    """商品管理器"""
    def create_product(self, data: dict) -> Product:
        """创建商品"""
        pass
    
    def update_product(self, product_id: str, data: dict) -> Product:
        """更新商品"""
        pass
    
    def batch_import(self, file_path: str) -> List[Product]:
        """批量导入商品"""
        pass
```

### 2. 价格监控模块

**功能职责**:
- 价格数据采集和存储
- 监控任务调度和管理
- 价格变化检测和告警
- 数据质量控制

**核心类设计**:
```python
class PriceRecord(models.Model):
    """价格记录模型"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='CNY')
    recorded_at = models.DateTimeField(auto_now_add=True)
    source = models.CharField(max_length=100)

class MonitoringTask(models.Model):
    """监控任务模型"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    frequency = models.CharField(max_length=20)
    status = models.CharField(max_length=20)
    last_run = models.DateTimeField(null=True)
    next_run = models.DateTimeField()

class PriceMonitor:
    """价格监控器"""
    def start_monitoring(self, product_id: str, config: dict):
        """启动监控"""
        pass
    
    def stop_monitoring(self, product_id: str):
        """停止监控"""
        pass
    
    def collect_price(self, product: Product) -> PriceRecord:
        """采集价格"""
        pass
```

### 3. 数据分析模块

**功能职责**:
- 价格趋势分析和预测
- 市场对比分析
- 异常检测和告警
- 分析报告生成

**核心类设计**:
```python
class TrendAnalyzer:
    """趋势分析器"""
    def analyze_trend(self, product_id: str, period: str) -> TrendResult:
        """分析价格趋势"""
        pass
    
    def predict_price(self, product_id: str, days: int) -> List[PricePrediction]:
        """价格预测"""
        pass

class MarketAnalyzer:
    """市场分析器"""
    def compare_products(self, product_ids: List[str]) -> ComparisonResult:
        """商品对比分析"""
        pass
    
    def analyze_market(self, category: str) -> MarketAnalysis:
        """市场分析"""
        pass

class AnomalyDetector:
    """异常检测器"""
    def detect_anomalies(self, product_id: str) -> List[Anomaly]:
        """检测价格异常"""
        pass
```

## 🗄️ 数据架构

### 数据模型设计

**核心实体关系图**:
```mermaid
erDiagram
    Product ||--o{ PriceRecord : has
    Product ||--o{ MonitoringTask : monitors
    Product }o--|| Category : belongs_to
    Product }o--|| Platform : on
    
    Supplier ||--o{ ProductCost : supplies
    Product ||--o{ ProductCost : costs
    
    User ||--o{ Product : manages
    User ||--o{ Alert : receives
    
    Product {
        uuid id PK
        string name
        string url
        string platform
        uuid category_id FK
        string status
        datetime created_at
        datetime updated_at
    }
    
    PriceRecord {
        uuid id PK
        uuid product_id FK
        decimal price
        string currency
        datetime recorded_at
        string source
    }
    
    Category {
        uuid id PK
        string name
        string slug
        uuid parent_id FK
        int level
    }
```

### 数据分片策略

**时序数据分片**:
```sql
-- 按时间分片价格记录表
CREATE TABLE price_records (
    id UUID PRIMARY KEY,
    product_id UUID NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    recorded_at TIMESTAMPTZ NOT NULL,
    source VARCHAR(100)
) PARTITION BY RANGE (recorded_at);

-- 创建月度分片
CREATE TABLE price_records_2025_08 PARTITION OF price_records
FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
```

**数据归档策略**:
- **热数据**: 最近3个月，存储在SSD
- **温数据**: 3-12个月，存储在普通磁盘
- **冷数据**: 12个月以上，归档到对象存储

### 缓存策略

**多级缓存架构**:
```python
class CacheManager:
    """缓存管理器"""
    def __init__(self):
        self.l1_cache = LocalCache()      # 本地缓存
        self.l2_cache = RedisCache()      # Redis缓存
        self.l3_cache = DatabaseCache()   # 数据库缓存
    
    def get(self, key: str) -> Any:
        # L1 -> L2 -> L3 -> Database
        value = self.l1_cache.get(key)
        if value is None:
            value = self.l2_cache.get(key)
            if value is not None:
                self.l1_cache.set(key, value)
        return value
```

**缓存策略**:
- **商品信息**: 缓存30分钟
- **价格数据**: 缓存10分钟
- **分析结果**: 缓存1小时
- **用户会话**: 缓存24小时

## 🛠️ 技术选型

### 后端技术栈

**Web框架**: Django 4.2
- 成熟稳定的Python Web框架
- 丰富的生态系统和第三方库
- 强大的ORM和管理后台
- 良好的安全性和扩展性

**数据库**: PostgreSQL 15 + TimescaleDB 2.11
- PostgreSQL: 关系型数据的存储
- TimescaleDB: 时序数据的高效存储和查询
- 支持复杂查询和事务
- 优秀的性能和可靠性

**缓存**: Redis 7.0
- 高性能内存数据库
- 支持多种数据结构
- 持久化和集群支持
- 用于缓存和消息队列

**任务队列**: Celery 5.3
- 分布式任务队列
- 支持多种消息代理
- 灵活的任务调度
- 监控和管理工具

### 前端技术栈

**框架**: Vue.js 3.0
- 渐进式JavaScript框架
- 组件化开发
- 响应式数据绑定
- 丰富的生态系统

**UI库**: Element Plus
- 基于Vue 3的组件库
- 丰富的组件和主题
- 良好的文档和社区支持

**状态管理**: Pinia
- Vue 3官方推荐的状态管理库
- 类型安全和开发工具支持
- 模块化和可组合

### 基础设施

**容器化**: Docker + Docker Compose
- 应用容器化部署
- 环境一致性保证
- 便于扩展和管理

**监控**: Prometheus + Grafana
- 指标收集和存储
- 可视化监控面板
- 告警和通知

**日志**: ELK Stack
- Elasticsearch: 日志存储和搜索
- Logstash: 日志收集和处理
- Kibana: 日志可视化分析

## 📈 扩展性设计

### 水平扩展

**应用层扩展**:
```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  web:
    image: moniit:latest
    deploy:
      replicas: 3
    ports:
      - "8000-8002:8000"
  
  celery:
    image: moniit:latest
    command: celery -A moniit worker
    deploy:
      replicas: 5
```

**数据库扩展**:
- **读写分离**: 主库写入，从库读取
- **分库分表**: 按业务或时间分片
- **连接池**: 优化数据库连接管理

### 垂直扩展

**资源优化**:
- **CPU**: 多核并行处理
- **内存**: 增加缓存容量
- **存储**: SSD提升I/O性能
- **网络**: 带宽和延迟优化

### 微服务架构演进

**服务拆分策略**:
```mermaid
graph TB
    subgraph "用户服务"
        USER_API[用户API]
        USER_DB[(用户数据库)]
    end
    
    subgraph "商品服务"
        PRODUCT_API[商品API]
        PRODUCT_DB[(商品数据库)]
    end
    
    subgraph "监控服务"
        MONITOR_API[监控API]
        MONITOR_DB[(监控数据库)]
    end
    
    subgraph "分析服务"
        ANALYSIS_API[分析API]
        ANALYSIS_DB[(分析数据库)]
    end
    
    API_GATEWAY[API网关] --> USER_API
    API_GATEWAY --> PRODUCT_API
    API_GATEWAY --> MONITOR_API
    API_GATEWAY --> ANALYSIS_API
```

## 🔒 安全架构

### 认证授权

**多层认证**:
- **用户认证**: JWT Token + Session
- **API认证**: API Key + OAuth 2.0
- **服务认证**: mTLS双向认证

**权限控制**:
```python
class Permission:
    """权限控制"""
    PRODUCT_READ = 'product:read'
    PRODUCT_WRITE = 'product:write'
    MONITOR_MANAGE = 'monitor:manage'
    ADMIN_ACCESS = 'admin:access'

class RoleBasedAccessControl:
    """基于角色的访问控制"""
    def check_permission(self, user: User, permission: str) -> bool:
        return permission in user.get_permissions()
```

### 数据安全

**数据加密**:
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: AES-256数据库加密
- **字段加密**: 敏感字段单独加密

**数据脱敏**:
```python
class DataMasking:
    """数据脱敏"""
    def mask_email(self, email: str) -> str:
        """邮箱脱敏"""
        name, domain = email.split('@')
        return f"{name[:2]}***@{domain}"
    
    def mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        return f"{phone[:3]}****{phone[-4:]}"
```

### 安全防护

**防护措施**:
- **SQL注入防护**: ORM参数化查询
- **XSS防护**: 输入验证和输出编码
- **CSRF防护**: CSRF Token验证
- **DDoS防护**: 限流和黑名单

## ⚡ 性能设计

### 性能目标

**响应时间**:
- **页面加载**: <2秒
- **API响应**: <500ms
- **数据查询**: <1秒
- **报表生成**: <5秒

**并发能力**:
- **在线用户**: 10,000+
- **API QPS**: 5,000+
- **监控任务**: 100,000+
- **数据写入**: 10,000 TPS

### 性能优化策略

**数据库优化**:
```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_price_records_product_time 
ON price_records (product_id, recorded_at DESC);

-- 分区表优化
CREATE TABLE price_records_2025_08 PARTITION OF price_records
FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');

-- 查询优化
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM price_records 
WHERE product_id = $1 AND recorded_at >= $2;
```

**应用优化**:
```python
class QueryOptimizer:
    """查询优化器"""
    def optimize_product_query(self, filters: dict):
        """优化商品查询"""
        queryset = Product.objects.select_related('category', 'platform')
        if 'category' in filters:
            queryset = queryset.filter(category__slug=filters['category'])
        return queryset.prefetch_related('price_records')
```

**缓存优化**:
```python
@cache_result(timeout=300)
def get_price_trend(product_id: str, period: str):
    """获取价格趋势（带缓存）"""
    return TrendAnalyzer().analyze_trend(product_id, period)
```

---

## 📚 相关文档

- [API接口文档](api_documentation.md)
- [部署运维指南](deployment_guide.md)
- [用户操作手册](user_manual.md)
- [常见问题解答](faq.md)

---

*本文档最后更新时间: 2025-08-24*
