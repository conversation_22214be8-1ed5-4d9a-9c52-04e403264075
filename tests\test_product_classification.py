"""
商品分类和标签管理测试

测试分类体系管理、标签管理等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta

from app.models.product import Product, ProductType, ProductCategory, ProductTag, ProductPrice, ProductSpecs, ProductMetrics
from app.services.product_management.category_manager import CategoryManager, CategoryRule, CategoryType
from app.services.product_management.tag_manager import TagManager, TagRule, TagType


class TestCategoryManager:
    """分类管理器测试"""
    
    @pytest.fixture
    def category_manager(self):
        """创建分类管理器实例"""
        return CategoryManager()
    
    @pytest.fixture
    def sample_product(self):
        """示例商品"""
        return Product(
            url="https://detail.1688.com/offer/123456.html",
            title="苹果iPhone 15 Pro Max 手机壳 透明保护套",
            platform="1688",
            specs=ProductSpecs(brand="苹果"),
            price=ProductPrice(current_price=29.90)
        )
    
    def test_default_hierarchy_creation(self, category_manager):
        """测试默认分类层级创建"""
        assert len(category_manager.categories) > 0
        assert len(category_manager.hierarchy.root_categories) > 0
        
        # 检查根分类
        root_category_ids = [cat.id for cat in category_manager.hierarchy.root_categories]
        assert "cat_electronics" in root_category_ids
        assert "cat_fashion" in root_category_ids
        assert "cat_home" in root_category_ids
        
        # 检查层级结构
        assert "cat_electronics" in category_manager.hierarchy.category_tree
        electronics_children = category_manager.hierarchy.category_tree["cat_electronics"]
        assert "cat_mobile" in electronics_children
        assert "cat_computer" in electronics_children
        assert "cat_accessories" in electronics_children
    
    @pytest.mark.asyncio
    async def test_classify_product_by_title(self, category_manager, sample_product):
        """测试基于标题的商品分类"""
        categories = await category_manager.classify_product_advanced(sample_product)
        
        assert len(categories) > 0
        
        # 检查是否包含电子数码分类
        category_ids = [cat.id for cat in categories]
        assert "cat_electronics" in category_ids
        assert "cat_accessories" in category_ids  # 手机壳应该归类为数码配件
    
    @pytest.mark.asyncio
    async def test_create_category(self, category_manager):
        """测试创建分类"""
        new_category = ProductCategory(
            id="cat_test",
            name="测试分类",
            parent_id="cat_electronics",
            level=2,
            description="测试用分类"
        )
        
        success = await category_manager.create_category(new_category)
        
        assert success is True
        assert "cat_test" in category_manager.categories
        assert category_manager.categories["cat_test"].name == "测试分类"
        
        # 检查层级结构更新
        assert "cat_test" in category_manager.hierarchy.category_tree["cat_electronics"]
    
    @pytest.mark.asyncio
    async def test_update_category(self, category_manager):
        """测试更新分类"""
        # 先创建一个分类
        category = ProductCategory(
            id="cat_update_test",
            name="更新测试",
            level=1
        )
        await category_manager.create_category(category)
        
        # 更新分类
        updates = {
            "name": "更新后的名称",
            "description": "更新后的描述"
        }
        
        success = await category_manager.update_category("cat_update_test", updates)
        
        assert success is True
        assert category_manager.categories["cat_update_test"].name == "更新后的名称"
        assert category_manager.categories["cat_update_test"].description == "更新后的描述"
    
    @pytest.mark.asyncio
    async def test_delete_category(self, category_manager):
        """测试删除分类"""
        # 先创建一个分类
        category = ProductCategory(
            id="cat_delete_test",
            name="删除测试",
            level=1
        )
        await category_manager.create_category(category)
        
        # 删除分类
        success = await category_manager.delete_category("cat_delete_test")
        
        assert success is True
        assert "cat_delete_test" not in category_manager.categories
    
    def test_get_category_tree(self, category_manager):
        """测试获取分类树"""
        tree = category_manager.get_category_tree()
        
        assert "root" in tree
        assert "children" in tree["root"]
        assert len(tree["root"]["children"]) > 0
        
        # 检查树结构
        electronics_node = None
        for child in tree["root"]["children"]:
            if child["id"] == "cat_electronics":
                electronics_node = child
                break
        
        assert electronics_node is not None
        assert len(electronics_node["children"]) > 0
    
    def test_get_category_path(self, category_manager):
        """测试获取分类路径"""
        path = category_manager.get_category_path("cat_mobile")
        
        assert len(path) == 2
        assert path[0] == "电子数码"
        assert path[1] == "手机通讯"


class TestTagManager:
    """标签管理器测试"""
    
    @pytest.fixture
    def tag_manager(self):
        """创建标签管理器实例"""
        return TagManager()
    
    @pytest.fixture
    def high_price_product(self):
        """高价商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="Apple iPhone 15 Pro Max 1TB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            specs=ProductSpecs(brand="Apple"),
            price=ProductPrice(current_price=12999.00),
            data_quality_score=0.95,
            metrics=ProductMetrics(sales_count=15000)
        )
    
    @pytest.fixture
    def low_price_product(self):
        """低价商品"""
        return Product(
            url="https://detail.1688.com/offer/789012.html",
            title="手机壳批发 透明硅胶保护套",
            platform="1688",
            product_type=ProductType.SUPPLIER,
            price=ProductPrice(current_price=5.80),
            data_quality_score=0.3,
            metrics=ProductMetrics(stock_quantity=50)
        )
    
    def test_default_tags_creation(self, tag_manager):
        """测试默认标签创建"""
        assert len(tag_manager.tags) > 0
        assert len(tag_manager.tag_rules) > 0
        
        # 检查系统标签
        assert "tag_competitor" in tag_manager.tags
        assert "tag_supplier" in tag_manager.tags
        assert "tag_1688" in tag_manager.tags
        assert "tag_taobao" in tag_manager.tags
        
        # 检查价格标签
        assert "tag_price_low" in tag_manager.tags
        assert "tag_price_high" in tag_manager.tags
        assert "tag_price_premium" in tag_manager.tags
    
    @pytest.mark.asyncio
    async def test_auto_tag_high_price_product(self, tag_manager, high_price_product):
        """测试高价商品自动标记"""
        tags = await tag_manager.auto_tag_product(high_price_product)
        
        assert len(tags) > 0
        
        tag_names = [tag.name for tag in tags]
        
        # 应该包含的标签
        assert "竞品" in tag_names  # 商品类型
        assert "淘宝" in tag_names  # 平台
        assert "奢侈品" in tag_names  # 价格区间
        assert "优质" in tag_names  # 质量等级
        assert "热销" in tag_names  # 销量
        assert "苹果" in tag_names  # 品牌
    
    @pytest.mark.asyncio
    async def test_auto_tag_low_price_product(self, tag_manager, low_price_product):
        """测试低价商品自动标记"""
        tags = await tag_manager.auto_tag_product(low_price_product)
        
        assert len(tags) > 0
        
        tag_names = [tag.name for tag in tags]
        
        # 应该包含的标签
        assert "供货商" in tag_names  # 商品类型
        assert "1688" in tag_names  # 平台
        assert "低价位" in tag_names  # 价格区间
        assert "待完善" in tag_names  # 质量等级
        assert "库存紧张" in tag_names  # 库存状态
    
    @pytest.mark.asyncio
    async def test_create_tag(self, tag_manager):
        """测试创建标签"""
        new_tag = ProductTag(
            id="tag_test",
            name="测试标签",
            color="#ff0000",
            description="测试用标签"
        )
        
        success = await tag_manager.create_tag(new_tag)
        
        assert success is True
        assert "tag_test" in tag_manager.tags
        assert tag_manager.tags["tag_test"].name == "测试标签"
        assert tag_manager.tags["tag_test"].color == "#ff0000"
    
    @pytest.mark.asyncio
    async def test_update_tag(self, tag_manager):
        """测试更新标签"""
        # 先创建一个标签
        tag = ProductTag(
            id="tag_update_test",
            name="更新测试",
            color="#000000"
        )
        await tag_manager.create_tag(tag)
        
        # 更新标签
        updates = {
            "name": "更新后的标签",
            "color": "#ffffff",
            "description": "更新后的描述"
        }
        
        success = await tag_manager.update_tag("tag_update_test", updates)
        
        assert success is True
        assert tag_manager.tags["tag_update_test"].name == "更新后的标签"
        assert tag_manager.tags["tag_update_test"].color == "#ffffff"
        assert tag_manager.tags["tag_update_test"].description == "更新后的描述"
    
    @pytest.mark.asyncio
    async def test_delete_tag(self, tag_manager):
        """测试删除标签"""
        # 先创建一个标签
        tag = ProductTag(
            id="tag_delete_test",
            name="删除测试",
            color="#000000"
        )
        await tag_manager.create_tag(tag)
        
        # 删除标签
        success = await tag_manager.delete_tag("tag_delete_test")
        
        assert success is True
        assert "tag_delete_test" not in tag_manager.tags
    
    def test_get_all_tags(self, tag_manager):
        """测试获取所有标签"""
        tags = tag_manager.get_all_tags()
        
        assert len(tags) > 0
        assert all("id" in tag for tag in tags)
        assert all("name" in tag for tag in tags)
        assert all("color" in tag for tag in tags)
    
    def test_get_tags_by_type(self, tag_manager):
        """测试按类型获取标签"""
        system_tags = tag_manager.get_tags_by_type(TagType.SYSTEM)
        auto_tags = tag_manager.get_tags_by_type(TagType.AUTO)
        
        assert len(system_tags) > 0
        assert len(auto_tags) > 0
        
        # 检查系统标签
        system_tag_ids = [tag.id for tag in system_tags]
        assert "tag_competitor" in system_tag_ids
        assert "tag_supplier" in system_tag_ids
    
    def test_get_tag_rules(self, tag_manager):
        """测试获取标签规则"""
        rules = tag_manager.get_tag_rules()
        
        assert len(rules) > 0
        assert all("id" in rule for rule in rules)
        assert all("name" in rule for rule in rules)
        assert all("tag_id" in rule for rule in rules)
        assert all("conditions" in rule for rule in rules)
    
    @pytest.mark.asyncio
    async def test_batch_tag_products(self, tag_manager, high_price_product, low_price_product):
        """测试批量标记商品"""
        products = [high_price_product, low_price_product]
        
        results = await tag_manager.batch_tag_products(products)
        
        assert len(results) == 2
        assert high_price_product.id in results
        assert low_price_product.id in results
        
        # 检查标记结果
        high_price_tags = results[high_price_product.id]
        low_price_tags = results[low_price_product.id]
        
        assert len(high_price_tags) > 0
        assert len(low_price_tags) > 0
        
        # 高价商品应该有奢侈品标签
        high_price_tag_names = [tag.name for tag in high_price_tags]
        assert "奢侈品" in high_price_tag_names
        
        # 低价商品应该有低价位标签
        low_price_tag_names = [tag.name for tag in low_price_tags]
        assert "低价位" in low_price_tag_names


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
