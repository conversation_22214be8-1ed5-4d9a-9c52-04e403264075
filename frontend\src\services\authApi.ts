/**
 * 认证相关API
 */

import { api } from './api';
import { User, ApiResponse } from '../types';

// 登录请求参数
interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应
interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// 注册请求参数
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

// 修改密码请求参数
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}

// 重置密码请求参数
interface ResetPasswordRequest {
  email: string;
}

// 确认重置密码请求参数
interface ConfirmResetPasswordRequest {
  token: string;
  newPassword: string;
}

export const authApi = {
  // 用户登录
  login: (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/api/v1/auth/login', credentials);
  },

  // 用户注册
  register: (userData: RegisterRequest): Promise<ApiResponse<User>> => {
    return api.post('/api/v1/auth/register', userData);
  },

  // 用户登出
  logout: (): Promise<ApiResponse<null>> => {
    return api.post('/api/v1/auth/logout');
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<ApiResponse<{ access_token: string; refresh_token?: string }>> => {
    return api.post('/api/v1/auth/refresh', { refresh_token: refreshToken });
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<ApiResponse<User>> => {
    return api.get('/api/v1/auth/me');
  },

  // 更新用户信息
  updateProfile: (profileData: Partial<User>): Promise<ApiResponse<User>> => {
    return api.put('/auth/profile', profileData);
  },

  // 修改密码
  changePassword: (passwordData: ChangePasswordRequest): Promise<ApiResponse<null>> => {
    return api.post('/auth/change-password', passwordData);
  },

  // 请求重置密码
  resetPassword: (resetData: ResetPasswordRequest): Promise<ApiResponse<null>> => {
    return api.post('/auth/reset-password', resetData);
  },

  // 确认重置密码
  confirmResetPassword: (confirmData: ConfirmResetPasswordRequest): Promise<ApiResponse<null>> => {
    return api.post('/auth/confirm-reset-password', confirmData);
  },

  // 验证token有效性
  verifyToken: (): Promise<ApiResponse<{ valid: boolean; user: User }>> => {
    return api.get('/api/v1/auth/verify');
  },

  // 获取用户权限
  getUserPermissions: (): Promise<ApiResponse<string[]>> => {
    return api.get('/auth/permissions');
  },

  // 检查特定权限
  checkPermission: (permission: string): Promise<ApiResponse<{ has_permission: boolean }>> => {
    return api.get(`/auth/permissions/${permission}`);
  },

  // 获取用户会话列表
  getUserSessions: (): Promise<ApiResponse<any[]>> => {
    return api.get('/auth/sessions');
  },

  // 终止指定会话
  terminateSession: (sessionId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/auth/sessions/${sessionId}`);
  },

  // 终止所有其他会话
  terminateOtherSessions: (): Promise<ApiResponse<null>> => {
    return api.post('/auth/sessions/terminate-others');
  },
};
