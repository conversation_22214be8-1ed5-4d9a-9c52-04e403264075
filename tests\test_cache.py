"""
缓存管理测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.cache import SimpleCacheManager, LocalCache, CacheStats
from app.utils.cache_utils import <PERSON><PERSON><PERSON>ey<PERSON>uilder, CacheHelper


class TestLocalCache:
    """本地缓存测试"""
    
    def test_local_cache_set_get(self):
        """测试本地缓存设置和获取"""
        cache = LocalCache(max_size=100, default_ttl=300)
        
        # 设置缓存
        result = cache.set("test_key", "test_value", 60)
        assert result == True
        
        # 获取缓存
        value = cache.get("test_key")
        assert value == "test_value"
    
    def test_local_cache_expiry(self):
        """测试本地缓存过期"""
        cache = LocalCache(max_size=100, default_ttl=1)
        
        # 设置短期缓存
        cache.set("expire_key", "expire_value", 0.1)
        
        # 立即获取应该成功
        value = cache.get("expire_key")
        assert value == "expire_value"
        
        # 等待过期后获取应该失败
        import time
        time.sleep(0.2)
        value = cache.get("expire_key")
        assert value is None
    
    def test_local_cache_max_size(self):
        """测试本地缓存最大大小限制"""
        cache = LocalCache(max_size=2, default_ttl=300)
        
        # 添加缓存项
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.set("key3", "value3")  # 应该触发最旧项的删除
        
        # 检查缓存大小
        assert cache.size() <= 2
    
    def test_local_cache_delete(self):
        """测试本地缓存删除"""
        cache = LocalCache()
        
        cache.set("delete_key", "delete_value")
        assert cache.get("delete_key") == "delete_value"
        
        result = cache.delete("delete_key")
        assert result == True
        assert cache.get("delete_key") is None
    
    def test_local_cache_clear(self):
        """测试本地缓存清空"""
        cache = LocalCache()
        
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        assert cache.size() == 2
        
        cache.clear()
        assert cache.size() == 0


class TestCacheStats:
    """缓存统计测试"""
    
    def test_cache_stats_initialization(self):
        """测试缓存统计初始化"""
        stats = CacheStats()
        
        assert stats.hits == 0
        assert stats.misses == 0
        assert stats.sets == 0
        assert stats.deletes == 0
        assert stats.errors == 0
        assert stats.hit_rate == 0.0
    
    def test_cache_stats_hit_rate_calculation(self):
        """测试缓存命中率计算"""
        stats = CacheStats()
        
        # 模拟一些操作
        stats.hits = 7
        stats.misses = 3
        
        assert stats.hit_rate == 0.7
    
    def test_cache_stats_to_dict(self):
        """测试缓存统计转换为字典"""
        stats = CacheStats()
        stats.hits = 10
        stats.misses = 5
        stats.sets = 8
        stats.deletes = 2
        stats.errors = 1
        
        result = stats.to_dict()
        
        assert result["hits"] == 10
        assert result["misses"] == 5
        assert result["sets"] == 8
        assert result["deletes"] == 2
        assert result["errors"] == 1
        assert result["hit_rate"] == 0.667
        assert result["total_requests"] == 15


class TestCacheKeyBuilder:
    """缓存键构建器测试"""
    
    def test_product_trend_key(self):
        """测试商品趋势缓存键"""
        key = CacheKeyBuilder.product_trend("product123", "price", 30, "1d")
        assert key == "trend:price:product123:30:1d"
    
    def test_product_analysis_key(self):
        """测试商品分析缓存键"""
        key = CacheKeyBuilder.product_analysis("product123", "comprehensive", 30)
        assert key == "analysis:comprehensive:product123:30"
    
    def test_supplier_costs_key(self):
        """测试供货商成本缓存键"""
        key = CacheKeyBuilder.supplier_costs("product123")
        assert key == "costs:product:product123"
    
    def test_platform_config_key(self):
        """测试平台配置缓存键"""
        key = CacheKeyBuilder.platform_config("1688")
        assert key == "config:platform:1688"
    
    def test_translation_cache_key(self):
        """测试翻译缓存键"""
        key = CacheKeyBuilder.translation_cache("Hello World", "zh")
        assert key.startswith("translation:zh:")
        assert len(key.split(":")) == 3


@pytest.mark.asyncio
class TestSimpleCacheManager:
    """简化缓存管理器测试"""
    
    async def test_cache_manager_initialization(self):
        """测试缓存管理器初始化"""
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        
        assert cache_manager.redis_url == "redis://localhost:6379/0"
        assert cache_manager.local_cache is not None
        assert cache_manager.stats is not None
        assert cache_manager.redis_client is None
    
    @patch('redis.asyncio.from_url')
    async def test_cache_manager_connect(self, mock_redis):
        """测试缓存管理器连接"""
        mock_client = AsyncMock()
        mock_client.ping = AsyncMock()
        mock_redis.return_value = mock_client
        
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        await cache_manager.connect()
        
        assert cache_manager.redis_client == mock_client
        mock_client.ping.assert_called_once()
    
    @patch('redis.asyncio.from_url')
    async def test_cache_manager_get_local_hit(self, mock_redis):
        """测试缓存管理器本地缓存命中"""
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        
        # 设置本地缓存
        cache_manager.local_cache.set("test_key", "test_value")
        
        # 获取应该命中本地缓存
        result = await cache_manager.get("test_key")
        
        assert result == "test_value"
        assert cache_manager.stats.hits == 1
        assert cache_manager.stats.misses == 0
    
    @patch('redis.asyncio.from_url')
    async def test_cache_manager_get_redis_hit(self, mock_redis):
        """测试缓存管理器Redis缓存命中"""
        mock_client = AsyncMock()
        mock_client.ping = AsyncMock()
        mock_client.get = AsyncMock(return_value='{"data": "test_value"}')
        mock_redis.return_value = mock_client
        
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        await cache_manager.connect()
        
        # 获取应该命中Redis缓存
        result = await cache_manager.get("test_key")
        
        assert result == {"data": "test_value"}
        assert cache_manager.stats.hits == 1
        mock_client.get.assert_called_once_with("test_key")
    
    @patch('redis.asyncio.from_url')
    async def test_cache_manager_set(self, mock_redis):
        """测试缓存管理器设置缓存"""
        mock_client = AsyncMock()
        mock_client.ping = AsyncMock()
        mock_client.setex = AsyncMock()
        mock_redis.return_value = mock_client
        
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        await cache_manager.connect()
        
        # 设置缓存
        result = await cache_manager.set("test_key", {"data": "test_value"}, 3600)
        
        assert result == True
        assert cache_manager.stats.sets == 1
        mock_client.setex.assert_called_once()
    
    async def test_cache_manager_get_cache_key(self):
        """测试缓存管理器生成缓存键"""
        cache_manager = SimpleCacheManager("redis://localhost:6379/0")
        
        key = cache_manager.get_cache_key("prefix", "arg1", "arg2", 123)
        assert key == "prefix:arg1:arg2:123"


@pytest.mark.asyncio
class TestCacheHelper:
    """缓存助手测试"""
    
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_helper_get_or_set_cache_hit(self, mock_get_cache):
        """测试缓存助手获取或设置 - 缓存命中"""
        mock_cache = AsyncMock()
        mock_cache.get = AsyncMock(return_value="cached_value")
        mock_get_cache.return_value = mock_cache
        
        helper = CacheHelper()
        
        async def fetch_func():
            return "fresh_value"
        
        result = await helper.get_or_set("test_key", fetch_func, 3600)
        
        assert result == "cached_value"
        mock_cache.get.assert_called_once_with("test_key")
        mock_cache.set.assert_not_called()
    
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_helper_get_or_set_cache_miss(self, mock_get_cache):
        """测试缓存助手获取或设置 - 缓存未命中"""
        mock_cache = AsyncMock()
        mock_cache.get = AsyncMock(return_value=None)
        mock_cache.set = AsyncMock(return_value=True)
        mock_get_cache.return_value = mock_cache
        
        helper = CacheHelper()
        
        async def fetch_func():
            return "fresh_value"
        
        result = await helper.get_or_set("test_key", fetch_func, 3600)
        
        assert result == "fresh_value"
        mock_cache.get.assert_called_once_with("test_key")
        mock_cache.set.assert_called_once_with("test_key", "fresh_value", 3600, "default")
    
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_helper_invalidate_product_cache(self, mock_get_cache):
        """测试缓存助手清除商品缓存"""
        mock_cache = AsyncMock()
        mock_cache.clear_pattern = AsyncMock(return_value=5)
        mock_get_cache.return_value = mock_cache
        
        helper = CacheHelper()
        await helper.invalidate_product_cache("product123")
        
        # 应该调用多个模式清除
        assert mock_cache.clear_pattern.call_count >= 3
    
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_helper_check_rate_limit_allowed(self, mock_get_cache):
        """测试缓存助手检查限流 - 允许"""
        mock_cache = AsyncMock()
        mock_cache.get = AsyncMock(return_value=5)  # 当前计数
        mock_cache.set = AsyncMock(return_value=True)
        mock_get_cache.return_value = mock_cache
        
        helper = CacheHelper()
        result = await helper.check_rate_limit("api_key", "endpoint", 10, 3600)
        
        assert result["allowed"] == True
        assert result["current_count"] == 6
        assert result["remaining"] == 4
    
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_helper_check_rate_limit_exceeded(self, mock_get_cache):
        """测试缓存助手检查限流 - 超限"""
        mock_cache = AsyncMock()
        mock_cache.get = AsyncMock(return_value=10)  # 当前计数已达限制
        mock_get_cache.return_value = mock_cache
        
        helper = CacheHelper()
        result = await helper.check_rate_limit("api_key", "endpoint", 10, 3600)
        
        assert result["allowed"] == False
        assert result["current_count"] == 10
        assert result["limit"] == 10
