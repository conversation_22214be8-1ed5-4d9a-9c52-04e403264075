"""
中间件测试
"""

import pytest
import time
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import Request, Response, HTTPException
from fastapi.testclient import TestClient

from app.core.middleware import (
    CacheMiddleware, RateLimitMiddleware, PerformanceMiddleware,
    CacheCleanupMiddleware
)


class TestCacheMiddleware:
    """缓存中间件测试"""
    
    @pytest.fixture
    def cache_middleware(self):
        """创建缓存中间件实例"""
        return CacheMiddleware(
            app=MagicMock(),
            cache_paths=["/api/v1/products", "/api/v1/analytics"]
        )
    
    def test_cache_middleware_initialization(self, cache_middleware):
        """测试缓存中间件初始化"""
        assert cache_middleware.cache_paths == ["/api/v1/products", "/api/v1/analytics"]
    
    def test_should_cache_get_request(self, cache_middleware):
        """测试GET请求应该被缓存"""
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/products"
        
        result = cache_middleware._should_cache(request)
        assert result == True
    
    def test_should_not_cache_post_request(self, cache_middleware):
        """测试POST请求不应该被缓存"""
        request = MagicMock()
        request.method = "POST"
        request.url.path = "/api/v1/products"
        
        result = cache_middleware._should_cache(request)
        assert result == False
    
    def test_should_not_cache_non_matching_path(self, cache_middleware):
        """测试不匹配路径的请求不应该被缓存"""
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/users"
        
        result = cache_middleware._should_cache(request)
        assert result == False
    
    def test_generate_cache_key(self, cache_middleware):
        """测试生成缓存键"""
        request = MagicMock()
        request.url.path = "/api/v1/products"
        request.query_params = "limit=10&skip=0"
        request.state.user_id = "user123"

        cache_key = cache_middleware._generate_cache_key(request)

        assert cache_key.startswith("http_cache:user123:/api/v1/products:")
        # 验证查询参数的哈希值被包含在缓存键中
        query_hash = str(hash("limit=10&skip=0"))
        assert query_hash in cache_key
    
    def test_generate_cache_key_anonymous_user(self, cache_middleware):
        """测试匿名用户生成缓存键"""
        request = MagicMock()
        request.url.path = "/api/v1/products"
        request.query_params = ""
        request.state = MagicMock()
        # 模拟没有user_id属性
        del request.state.user_id
        
        cache_key = cache_middleware._generate_cache_key(request)
        
        assert cache_key.startswith("http_cache:anonymous:/api/v1/products:")
    
    @pytest.mark.asyncio
    @patch('app.core.middleware.get_cache_manager')
    async def test_dispatch_cache_hit(self, mock_get_cache_manager, cache_middleware):
        """测试缓存命中"""
        # 模拟请求
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/products"
        request.query_params = ""
        request.state.user_id = "user123"
        
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cached_response = {
            "content": "cached content",
            "status_code": 200,
            "headers": {"Content-Type": "application/json"},
            "media_type": "application/json"
        }
        mock_cache_manager.get.return_value = mock_cached_response
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟call_next
        call_next = AsyncMock()
        
        # 执行中间件
        response = await cache_middleware.dispatch(request, call_next)
        
        # 验证
        assert response.body == b"cached content"
        assert response.status_code == 200
        call_next.assert_not_called()  # 不应该调用下一个处理器
        mock_cache_manager.get.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.core.middleware.get_cache_manager')
    async def test_dispatch_cache_miss(self, mock_get_cache_manager, cache_middleware):
        """测试缓存未命中"""
        # 模拟请求
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/products"
        request.query_params = ""
        request.state.user_id = "user123"
        
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # 缓存未命中
        mock_cache_manager.set = AsyncMock()
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.body = b"response content"
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.media_type = "application/json"
        
        # 模拟call_next
        call_next = AsyncMock(return_value=mock_response)
        
        # 执行中间件
        response = await cache_middleware.dispatch(request, call_next)
        
        # 验证
        assert response == mock_response
        call_next.assert_called_once()
        mock_cache_manager.get.assert_called_once()
        mock_cache_manager.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_dispatch_no_cache_needed(self, cache_middleware):
        """测试不需要缓存的请求"""
        # 模拟POST请求
        request = MagicMock()
        request.method = "POST"
        request.url.path = "/api/v1/products"
        
        # 模拟响应
        mock_response = MagicMock()
        call_next = AsyncMock(return_value=mock_response)
        
        # 执行中间件
        response = await cache_middleware.dispatch(request, call_next)
        
        # 验证
        assert response == mock_response
        call_next.assert_called_once()


class TestRateLimitMiddleware:
    """限流中间件测试"""
    
    @pytest.fixture
    def rate_limit_middleware(self):
        """创建限流中间件实例"""
        return RateLimitMiddleware(
            app=MagicMock(),
            default_limit=100,
            window=3600
        )

    def test_rate_limit_middleware_initialization(self, rate_limit_middleware):
        """测试限流中间件初始化"""
        assert rate_limit_middleware.default_limit == 100
        assert rate_limit_middleware.window == 3600
    
    def test_get_client_id_with_api_key(self, rate_limit_middleware):
        """测试通过API密钥获取客户端ID"""
        request = MagicMock()
        request.headers = {"X-API-Key": "test-api-key"}
        
        client_id = rate_limit_middleware._get_client_id(request)
        assert client_id == "api_key:test-api-key"
    
    def test_get_client_id_with_ip(self, rate_limit_middleware):
        """测试通过IP地址获取客户端ID"""
        request = MagicMock()
        request.headers = {}
        request.client.host = "***********"
        
        client_id = rate_limit_middleware._get_client_id(request)
        assert client_id == "ip:***********"
    
    def test_get_client_id_unknown(self, rate_limit_middleware):
        """测试未知客户端ID"""
        request = MagicMock()
        request.headers = {}
        # 模拟client对象
        request.client = MagicMock()
        request.client.host = "unknown"

        client_id = rate_limit_middleware._get_client_id(request)
        assert client_id == "ip:unknown"
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.cache_helper.check_rate_limit')
    async def test_dispatch_within_limit(self, mock_check_rate_limit, rate_limit_middleware):
        """测试在限制范围内的请求"""
        # 模拟限流检查结果
        mock_check_rate_limit.return_value = {
            "allowed": True,
            "current_count": 30,
            "limit": 100,
            "remaining": 70
        }

        # 创建请求
        request = MagicMock()
        request.url.path = "/api/v1/products"
        request.headers = {"X-API-Key": "test-key"}

        # 创建响应mock
        response_mock = MagicMock()
        response_mock.headers = {}

        async def mock_call_next(req):
            return response_mock

        # 执行dispatch
        response = await rate_limit_middleware.dispatch(request, mock_call_next)

        # 验证结果
        assert response == response_mock
        assert "X-RateLimit-Limit" in response.headers
        mock_check_rate_limit.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.cache_helper.check_rate_limit')
    async def test_dispatch_rate_limited(self, mock_check_rate_limit, rate_limit_middleware):
        """测试超出限制的请求"""
        # 模拟限流检查结果 - 超出限制
        mock_check_rate_limit.return_value = {
            "allowed": False,
            "current_count": 100,
            "limit": 100,
            "reset_time": datetime.utcnow() + timedelta(seconds=3600)
        }

        # 创建请求
        request = MagicMock()
        request.url.path = "/api/v1/products"
        request.headers = {"X-API-Key": "test-key"}

        async def mock_call_next(req):
            return MagicMock()

        # 执行dispatch，应该抛出HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await rate_limit_middleware.dispatch(request, mock_call_next)

        # 验证异常
        assert exc_info.value.status_code == 429
        mock_check_rate_limit.assert_called_once()
    



class TestPerformanceMiddleware:
    """性能监控中间件测试"""
    
    @pytest.fixture
    def performance_middleware(self):
        """创建性能监控中间件实例"""
        return PerformanceMiddleware(app=MagicMock())
    
    @pytest.mark.asyncio
    async def test_dispatch_normal_request(self, performance_middleware):
        """测试正常请求的性能监控"""
        # 模拟请求
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/products"
        
        # 模拟响应
        mock_response = MagicMock()
        mock_response.headers = {}
        
        # 模拟call_next（快速响应）
        async def fast_call_next(req):
            await asyncio.sleep(0.1)  # 模拟100ms处理时间
            return mock_response
        
        # 执行中间件
        response = await performance_middleware.dispatch(request, fast_call_next)
        
        # 验证
        assert response == mock_response
        assert "X-Process-Time" in response.headers
        process_time = float(response.headers["X-Process-Time"])
        assert 0.09 <= process_time <= 0.2  # 允许一些时间误差
    
    @pytest.mark.asyncio
    @patch('app.core.middleware.logger')
    async def test_dispatch_slow_request(self, mock_logger, performance_middleware):
        """测试慢请求的性能监控"""
        # 模拟请求
        request = MagicMock()
        request.method = "GET"
        request.url.path = "/api/v1/analytics"
        
        # 模拟响应
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        # 模拟call_next（慢响应）
        async def slow_call_next(req):
            await asyncio.sleep(1.1)  # 模拟1.1秒处理时间
            return mock_response
        
        # 执行中间件
        response = await performance_middleware.dispatch(request, slow_call_next)
        
        # 验证
        assert response == mock_response
        assert "X-Process-Time" in response.headers
        process_time = float(response.headers["X-Process-Time"])
        assert process_time >= 1.0
        
        # 验证慢请求日志
        mock_logger.warning.assert_called_once()
        warning_call = mock_logger.warning.call_args[0][0]
        assert "慢请求" in warning_call
        assert "/api/v1/analytics" in warning_call


class TestCacheCleanupMiddleware:
    """缓存清理中间件测试"""
    
    @pytest.fixture
    def cache_cleanup_middleware(self):
        """创建缓存清理中间件实例"""
        return CacheCleanupMiddleware(
            app=MagicMock(),
            cleanup_interval=300
        )
    
    def test_cache_cleanup_middleware_initialization(self, cache_cleanup_middleware):
        """测试缓存清理中间件初始化"""
        assert cache_cleanup_middleware.cleanup_interval == 300
        assert cache_cleanup_middleware.last_cleanup == 0
    
    def test_cleanup_interval_check(self, cache_cleanup_middleware):
        """测试清理间隔检查"""
        # 初始状态，last_cleanup为0，应该触发清理
        current_time = time.time()
        cache_cleanup_middleware.last_cleanup = current_time - 400  # 400秒前

        # 检查是否需要清理（间隔300秒）
        should_cleanup = (current_time - cache_cleanup_middleware.last_cleanup) > cache_cleanup_middleware.cleanup_interval
        assert should_cleanup == True

    def test_cleanup_interval_not_reached(self, cache_cleanup_middleware):
        """测试清理间隔未到达"""
        current_time = time.time()
        cache_cleanup_middleware.last_cleanup = current_time - 100  # 100秒前

        # 检查是否需要清理（间隔300秒）
        should_cleanup = (current_time - cache_cleanup_middleware.last_cleanup) > cache_cleanup_middleware.cleanup_interval
        assert should_cleanup == False
    
    @pytest.mark.asyncio
    @patch('app.core.middleware.get_cache_manager')
    async def test_dispatch_with_cleanup(self, mock_get_cache_manager, cache_cleanup_middleware):
        """测试触发清理的请求"""
        # 设置last_cleanup为很久以前，触发清理
        cache_cleanup_middleware.last_cleanup = 0

        # 模拟请求
        request = MagicMock()

        # 模拟缓存管理器
        mock_cache_manager = MagicMock()
        mock_cache_manager.cleanup_local_cache = MagicMock()
        mock_get_cache_manager.return_value = mock_cache_manager

        # 模拟响应
        mock_response = MagicMock()
        call_next = AsyncMock(return_value=mock_response)

        # 执行中间件
        response = await cache_cleanup_middleware.dispatch(request, call_next)

        # 验证
        assert response == mock_response
        call_next.assert_called_once()
        mock_cache_manager.cleanup_local_cache.assert_called_once()
        # 验证last_cleanup被更新
        assert cache_cleanup_middleware.last_cleanup > 0
    
    @pytest.mark.asyncio
    async def test_dispatch_without_cleanup(self, cache_cleanup_middleware):
        """测试不触发清理的请求"""
        # 设置last_cleanup为最近，不触发清理
        cache_cleanup_middleware.last_cleanup = time.time()

        # 模拟请求
        request = MagicMock()

        # 模拟响应
        mock_response = MagicMock()
        call_next = AsyncMock(return_value=mock_response)

        # 执行中间件
        response = await cache_cleanup_middleware.dispatch(request, call_next)

        # 验证
        assert response == mock_response
        call_next.assert_called_once()
