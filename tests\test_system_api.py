"""
系统管理API测试

测试系统管理API的所有功能
"""

import pytest
import asyncio
from httpx import AsyncClient
from uuid import uuid4
from datetime import datetime

from app.main import app


@pytest.fixture
async def client():
    """创建测试客户端"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac


class TestSystemInfoAPI:
    """系统信息API测试类"""
    
    async def test_get_system_info(self, client: AsyncClient):
        """测试获取系统信息"""
        response = await client.get("/api/v1/system/info")
        assert response.status_code == 200
        
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert "environment" in data
        assert "debug" in data
    
    async def test_get_system_health(self, client: AsyncClient):
        """测试系统健康检查"""
        response = await client.get("/api/v1/system/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data
        assert data["status"] in ["healthy", "unhealthy"]
    
    async def test_get_dashboard_stats(self, client: AsyncClient):
        """测试获取仪表板统计数据"""
        response = await client.get("/api/v1/system/dashboard/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_products" in data
        assert "active_monitors" in data
        assert "total_price_records" in data
        assert "system_health" in data
        assert "recent_alerts" in data
        assert "today_stats" in data
        assert "performance_metrics" in data
    
    async def test_detailed_health_check(self, client: AsyncClient):
        """测试详细健康检查"""
        response = await client.get("/api/v1/system/health/detailed")
        assert response.status_code == 200
        
        data = response.json()
        assert "overall" in data
        assert "timestamp" in data
        assert "services" in data
        assert "metrics" in data
        assert data["overall"] in ["healthy", "unhealthy", "degraded", "warning"]
    
    async def test_get_system_metrics(self, client: AsyncClient):
        """测试获取系统指标"""
        response = await client.get("/api/v1/system/metrics")
        assert response.status_code == 200
        
        data = response.json()
        assert "timestamp" in data
        assert "cpu" in data
        assert "memory" in data
        assert "network" in data
        assert "processes" in data


class TestSystemConfigAPI:
    """系统配置API测试类"""
    
    async def test_get_system_config(self, client: AsyncClient):
        """测试获取系统配置"""
        response = await client.get("/api/v1/system/config")
        assert response.status_code == 200
        
        data = response.json()
        assert "configs" in data
        assert "total" in data
        assert isinstance(data["configs"], dict)
        assert data["total"] >= 0
    
    async def test_get_system_config_with_category_filter(self, client: AsyncClient):
        """测试带分类筛选的配置查询"""
        response = await client.get("/api/v1/system/config?category=general")
        assert response.status_code == 200
        
        data = response.json()
        assert "configs" in data
        # 验证所有配置都属于指定分类
        for config_key, config_data in data["configs"].items():
            assert config_data.get("category") == "general"
    
    async def test_get_system_config_with_key_filter(self, client: AsyncClient):
        """测试带键筛选的配置查询"""
        response = await client.get("/api/v1/system/config?key=app")
        assert response.status_code == 200
        
        data = response.json()
        assert "configs" in data
        # 验证所有配置键都包含指定关键词
        for config_key in data["configs"].keys():
            assert "app" in config_key
    
    async def test_update_system_config(self, client: AsyncClient):
        """测试更新系统配置"""
        config_data = {
            "key": "test.config",
            "value": "test_value",
            "description": "测试配置",
            "category": "test"
        }
        
        response = await client.put("/api/v1/system/config/test.config", json=config_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["key"] == "test.config"
        assert data["value"] == "test_value"
        assert "配置更新成功" in data["message"]


class TestUserManagementAPI:
    """用户管理API测试类"""
    
    async def test_get_users(self, client: AsyncClient):
        """测试获取用户列表"""
        response = await client.get("/api/v1/system/users")
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["items"], list)
        assert data["total"] >= 0
    
    async def test_get_users_with_role_filter(self, client: AsyncClient):
        """测试带角色筛选的用户查询"""
        response = await client.get("/api/v1/system/users?role=admin")
        assert response.status_code == 200
        
        data = response.json()
        # 验证所有用户都是指定角色
        for user in data["items"]:
            assert user["role"] == "admin"
    
    async def test_get_users_with_active_filter(self, client: AsyncClient):
        """测试带激活状态筛选的用户查询"""
        response = await client.get("/api/v1/system/users?is_active=true")
        assert response.status_code == 200
        
        data = response.json()
        # 验证所有用户都是激活状态
        for user in data["items"]:
            assert user["is_active"] is True
    
    async def test_create_user_success(self, client: AsyncClient):
        """测试成功创建用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "测试用户",
            "role": "user",
            "is_active": True
        }
        
        response = await client.post("/api/v1/system/users", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert data["role"] == user_data["role"]
        assert "id" in data
        assert "created_at" in data
        
        return data["id"]  # 返回用户ID供其他测试使用
    
    async def test_create_user_duplicate_username(self, client: AsyncClient):
        """测试创建重复用户名的用户"""
        user_data = {
            "username": "admin",  # 已存在的用户名
            "email": "<EMAIL>",
            "role": "user",
            "is_active": True
        }
        
        response = await client.post("/api/v1/system/users", json=user_data)
        assert response.status_code == 400
        assert "用户名已存在" in response.json()["detail"]
    
    async def test_get_user_detail(self, client: AsyncClient):
        """测试获取用户详情"""
        # 先创建一个用户
        user_data = {
            "username": "detailuser",
            "email": "<EMAIL>",
            "role": "user",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/system/users", json=user_data)
        user_id = create_response.json()["id"]
        
        # 获取用户详情
        response = await client.get(f"/api/v1/system/users/{user_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == user_id
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
    
    async def test_get_user_not_found(self, client: AsyncClient):
        """测试获取不存在的用户"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/system/users/{fake_id}")
        assert response.status_code == 404
        assert "用户不存在" in response.json()["detail"]
    
    async def test_update_user(self, client: AsyncClient):
        """测试更新用户"""
        # 先创建一个用户
        user_data = {
            "username": "updateuser",
            "email": "<EMAIL>",
            "role": "user",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/system/users", json=user_data)
        user_id = create_response.json()["id"]
        
        # 更新用户
        update_data = {
            "email": "<EMAIL>",
            "full_name": "更新后的用户",
            "role": "admin"
        }
        
        response = await client.put(f"/api/v1/system/users/{user_id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == update_data["email"]
        assert data["full_name"] == update_data["full_name"]
        assert data["role"] == update_data["role"]
    
    async def test_delete_user(self, client: AsyncClient):
        """测试删除用户"""
        # 先创建一个用户
        user_data = {
            "username": "deleteuser",
            "email": "<EMAIL>",
            "role": "user",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/system/users", json=user_data)
        user_id = create_response.json()["id"]
        
        # 删除用户
        response = await client.delete(f"/api/v1/system/users/{user_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["user_id"] == user_id
        assert "删除成功" in data["message"]
        
        # 验证用户已删除
        get_response = await client.get(f"/api/v1/system/users/{user_id}")
        assert get_response.status_code == 404


class TestPermissionsAPI:
    """权限管理API测试类"""
    
    async def test_get_permissions(self, client: AsyncClient):
        """测试获取权限列表"""
        response = await client.get("/api/v1/system/permissions")
        assert response.status_code == 200
        
        data = response.json()
        assert "roles" in data
        assert "total" in data
        assert isinstance(data["roles"], dict)
        assert data["total"] > 0
        
        # 验证权限结构
        for role_name, role_data in data["roles"].items():
            assert "name" in role_data
            assert "permissions" in role_data
            assert isinstance(role_data["permissions"], list)


class TestOperationLogsAPI:
    """操作日志API测试类"""
    
    async def test_get_operation_logs(self, client: AsyncClient):
        """测试获取操作日志"""
        response = await client.get("/api/v1/system/logs/operations")
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["items"], list)
        assert data["total"] >= 0
    
    async def test_get_operation_logs_with_action_filter(self, client: AsyncClient):
        """测试带操作类型筛选的日志查询"""
        response = await client.get("/api/v1/system/logs/operations?action=create_user")
        assert response.status_code == 200
        
        data = response.json()
        # 验证所有日志都是指定操作类型
        for log in data["items"]:
            assert log["action"] == "create_user"
    
    async def test_get_operation_logs_with_pagination(self, client: AsyncClient):
        """测试分页查询操作日志"""
        response = await client.get("/api/v1/system/logs/operations?skip=0&limit=5")
        assert response.status_code == 200
        
        data = response.json()
        assert data["skip"] == 0
        assert data["limit"] == 5
        assert len(data["items"]) <= 5


class TestCacheAPI:
    """缓存管理API测试类"""
    
    async def test_get_cache_stats(self, client: AsyncClient):
        """测试获取缓存统计"""
        response = await client.get("/api/v1/system/cache/stats")
        assert response.status_code == 200
        
        # 由于缓存可能不可用，只验证响应格式
        data = response.json()
        assert isinstance(data, dict)
    
    async def test_clear_cache(self, client: AsyncClient):
        """测试清理缓存"""
        response = await client.post("/api/v1/system/cache/clear?pattern=test:*")
        assert response.status_code == 200
        
        # 由于缓存可能不可用，只验证响应格式
        data = response.json()
        assert isinstance(data, dict)


if __name__ == "__main__":
    pytest.main([__file__])
