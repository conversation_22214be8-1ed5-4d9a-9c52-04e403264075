"""
平台翻译模板管理器

实现平台专用翻译模板配置，针对不同平台的专业翻译模板
"""

import json
import uuid
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
import os

from app.core.logging import get_logger

logger = get_logger(__name__)


class PlatformType(Enum):
    """平台类型"""
    AMAZON = "amazon"           # 亚马逊
    EBAY = "ebay"              # eBay
    SHOPIFY = "shopify"        # Shopify
    ALIBABA = "alibaba"        # 阿里巴巴
    TAOBAO = "taobao"          # 淘宝
    TMALL = "tmall"            # 天猫
    JD = "jd"                  # 京东
    PINDUODUO = "pinduoduo"    # 拼多多
    LAZADA = "lazada"          # Lazada
    SHOPEE = "shopee"          # Shopee
    GENERIC = "generic"        # 通用平台


class TemplateType(Enum):
    """模板类型"""
    PRODUCT_TITLE = "product_title"           # 商品标题
    PRODUCT_DESCRIPTION = "product_description"  # 商品描述
    CATEGORY_NAME = "category_name"           # 分类名称
    BRAND_NAME = "brand_name"                 # 品牌名称
    SPECIFICATION = "specification"           # 规格说明
    MARKETING_COPY = "marketing_copy"         # 营销文案
    TECHNICAL_DOC = "technical_doc"           # 技术文档
    CUSTOMER_SERVICE = "customer_service"     # 客服用语


@dataclass
class TranslationRule:
    """翻译规则"""
    rule_id: str
    name: str
    description: str
    pattern: str = ""                    # 匹配模式
    replacement: str = ""                # 替换内容
    priority: int = 5                    # 优先级（1-10）
    enabled: bool = True                 # 是否启用
    case_sensitive: bool = False         # 是否区分大小写
    use_regex: bool = False             # 是否使用正则表达式
    apply_before_translation: bool = True  # 是否在翻译前应用
    apply_after_translation: bool = False  # 是否在翻译后应用


@dataclass
class PlatformConstraints:
    """平台约束"""
    max_title_length: Optional[int] = None      # 标题最大长度
    max_description_length: Optional[int] = None  # 描述最大长度
    forbidden_words: List[str] = field(default_factory=list)  # 禁用词汇
    required_keywords: List[str] = field(default_factory=list)  # 必需关键词
    allowed_html_tags: List[str] = field(default_factory=list)  # 允许的HTML标签
    character_restrictions: Dict[str, str] = field(default_factory=dict)  # 字符限制
    format_requirements: Dict[str, str] = field(default_factory=dict)  # 格式要求


@dataclass
class TranslationTemplate:
    """翻译模板"""
    template_id: str
    name: str
    platform: PlatformType
    template_type: TemplateType
    source_lang: str
    target_lang: str
    
    # 模板内容
    system_prompt: str = ""              # 系统提示词
    user_prompt_template: str = ""       # 用户提示词模板
    example_input: str = ""              # 示例输入
    example_output: str = ""             # 示例输出
    
    # 翻译规则
    pre_processing_rules: List[TranslationRule] = field(default_factory=list)
    post_processing_rules: List[TranslationRule] = field(default_factory=list)
    
    # 平台约束
    constraints: PlatformConstraints = field(default_factory=PlatformConstraints)
    
    # 质量要求
    min_quality_score: float = 7.0       # 最低质量评分
    quality_checks: List[str] = field(default_factory=list)  # 质量检查项
    
    # 元数据
    description: str = ""
    tags: List[str] = field(default_factory=list)
    version: str = "1.0"
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    usage_count: int = 0


class TemplateManager:
    """平台翻译模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = templates_dir
        self.templates: Dict[str, TranslationTemplate] = {}
        
        # 确保模板目录存在
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # 配置
        self.config = {
            "auto_save": True,              # 自动保存
            "backup_enabled": True,         # 启用备份
            "max_templates": 1000,          # 最大模板数
            "template_validation": True,    # 模板验证
            "version_control": True         # 版本控制
        }
        
        # 统计信息
        self.stats = {
            "total_templates": 0,
            "active_templates": 0,
            "total_usage": 0,
            "platforms_count": 0,
            "template_types_count": 0
        }
        
        # 加载默认模板
        self._load_default_templates()
        
        # 加载已保存的模板
        self._load_templates_from_disk()
    
    def create_template(self, name: str, platform: PlatformType, 
                       template_type: TemplateType, source_lang: str, 
                       target_lang: str, **kwargs) -> str:
        """
        创建翻译模板
        
        Args:
            name: 模板名称
            platform: 平台类型
            template_type: 模板类型
            source_lang: 源语言
            target_lang: 目标语言
            **kwargs: 其他参数
        
        Returns:
            str: 模板ID
        """
        try:
            template_id = str(uuid.uuid4())
            
            # 检查模板数量限制
            if len(self.templates) >= self.config["max_templates"]:
                raise ValueError(f"模板数量已达上限: {self.config['max_templates']}")
            
            # 创建模板
            template = TranslationTemplate(
                template_id=template_id,
                name=name,
                platform=platform,
                template_type=template_type,
                source_lang=source_lang,
                target_lang=target_lang,
                **kwargs
            )
            
            # 验证模板
            if self.config["template_validation"]:
                self._validate_template(template)
            
            # 存储模板
            self.templates[template_id] = template
            
            # 更新统计
            self._update_stats()
            
            # 自动保存
            if self.config["auto_save"]:
                self._save_template_to_disk(template)
            
            logger.info(f"创建翻译模板: {template_id}, 名称: {name}, 平台: {platform.value}")
            return template_id
            
        except Exception as e:
            logger.error(f"创建翻译模板失败: {e}")
            raise
    
    def update_template(self, template_id: str, **kwargs) -> bool:
        """
        更新翻译模板
        
        Args:
            template_id: 模板ID
            **kwargs: 更新参数
        
        Returns:
            bool: 是否更新成功
        """
        try:
            if template_id not in self.templates:
                logger.error(f"模板不存在: {template_id}")
                return False
            
            template = self.templates[template_id]
            
            # 备份原模板
            if self.config["backup_enabled"]:
                self._backup_template(template)
            
            # 更新模板属性
            for key, value in kwargs.items():
                if hasattr(template, key):
                    setattr(template, key, value)
            
            template.updated_at = datetime.now()
            
            # 验证更新后的模板
            if self.config["template_validation"]:
                self._validate_template(template)
            
            # 自动保存
            if self.config["auto_save"]:
                self._save_template_to_disk(template)
            
            logger.info(f"更新翻译模板: {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新翻译模板失败: {template_id}, {e}")
            return False
    
    def delete_template(self, template_id: str) -> bool:
        """
        删除翻译模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            bool: 是否删除成功
        """
        try:
            if template_id not in self.templates:
                logger.error(f"模板不存在: {template_id}")
                return False
            
            template = self.templates[template_id]
            
            # 备份模板
            if self.config["backup_enabled"]:
                self._backup_template(template)
            
            # 删除模板
            del self.templates[template_id]
            
            # 删除磁盘文件
            template_file = os.path.join(self.templates_dir, f"{template_id}.json")
            if os.path.exists(template_file):
                os.remove(template_file)
            
            # 更新统计
            self._update_stats()
            
            logger.info(f"删除翻译模板: {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除翻译模板失败: {template_id}, {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[TranslationTemplate]:
        """获取翻译模板"""
        template = self.templates.get(template_id)
        if template:
            template.usage_count += 1
            self.stats["total_usage"] += 1
        return template
    
    def find_templates(self, platform: Optional[PlatformType] = None,
                      template_type: Optional[TemplateType] = None,
                      source_lang: Optional[str] = None,
                      target_lang: Optional[str] = None,
                      enabled_only: bool = True) -> List[TranslationTemplate]:
        """
        查找翻译模板
        
        Args:
            platform: 平台类型
            template_type: 模板类型
            source_lang: 源语言
            target_lang: 目标语言
            enabled_only: 只返回启用的模板
        
        Returns:
            List[TranslationTemplate]: 匹配的模板列表
        """
        try:
            templates = list(self.templates.values())
            
            # 过滤条件
            if platform:
                templates = [t for t in templates if t.platform == platform]
            
            if template_type:
                templates = [t for t in templates if t.template_type == template_type]
            
            if source_lang:
                templates = [t for t in templates if t.source_lang == source_lang]
            
            if target_lang:
                templates = [t for t in templates if t.target_lang == target_lang]
            
            if enabled_only:
                templates = [t for t in templates if t.enabled]
            
            # 按使用次数和质量评分排序
            templates.sort(key=lambda t: (t.usage_count, t.min_quality_score), reverse=True)
            
            return templates
            
        except Exception as e:
            logger.error(f"查找翻译模板失败: {e}")
            return []
    
    def get_best_template(self, platform: PlatformType, template_type: TemplateType,
                         source_lang: str, target_lang: str) -> Optional[TranslationTemplate]:
        """
        获取最佳翻译模板
        
        Args:
            platform: 平台类型
            template_type: 模板类型
            source_lang: 源语言
            target_lang: 目标语言
        
        Returns:
            Optional[TranslationTemplate]: 最佳模板
        """
        try:
            # 精确匹配
            templates = self.find_templates(
                platform=platform,
                template_type=template_type,
                source_lang=source_lang,
                target_lang=target_lang
            )
            
            if templates:
                return templates[0]
            
            # 通用平台匹配
            templates = self.find_templates(
                platform=PlatformType.GENERIC,
                template_type=template_type,
                source_lang=source_lang,
                target_lang=target_lang
            )
            
            if templates:
                return templates[0]
            
            # 只匹配模板类型和语言
            templates = self.find_templates(
                template_type=template_type,
                source_lang=source_lang,
                target_lang=target_lang
            )
            
            if templates:
                return templates[0]
            
            return None
            
        except Exception as e:
            logger.error(f"获取最佳翻译模板失败: {e}")
            return None
    
    def apply_template(self, template: TranslationTemplate, text: str) -> Dict[str, Any]:
        """
        应用翻译模板
        
        Args:
            template: 翻译模板
            text: 待翻译文本
        
        Returns:
            Dict[str, Any]: 应用结果
        """
        try:
            result = {
                "template_id": template.template_id,
                "original_text": text,
                "processed_text": text,
                "system_prompt": template.system_prompt,
                "user_prompt": "",
                "constraints": asdict(template.constraints),
                "quality_requirements": {
                    "min_quality_score": template.min_quality_score,
                    "quality_checks": template.quality_checks
                },
                "applied_rules": []
            }
            
            # 应用预处理规则
            processed_text = text
            for rule in template.pre_processing_rules:
                if rule.enabled and rule.apply_before_translation:
                    processed_text, applied = self._apply_rule(rule, processed_text)
                    if applied:
                        result["applied_rules"].append({
                            "rule_id": rule.rule_id,
                            "rule_name": rule.name,
                            "stage": "pre_processing"
                        })
            
            result["processed_text"] = processed_text
            
            # 生成用户提示词
            if template.user_prompt_template:
                result["user_prompt"] = template.user_prompt_template.format(
                    text=processed_text,
                    source_lang=template.source_lang,
                    target_lang=template.target_lang,
                    platform=template.platform.value,
                    template_type=template.template_type.value
                )
            
            return result
            
        except Exception as e:
            logger.error(f"应用翻译模板失败: {template.template_id}, {e}")
            return {
                "template_id": template.template_id,
                "original_text": text,
                "processed_text": text,
                "system_prompt": "",
                "user_prompt": "",
                "error": str(e)
            }
    
    def post_process_translation(self, template: TranslationTemplate, 
                               translated_text: str) -> str:
        """
        后处理翻译结果
        
        Args:
            template: 翻译模板
            translated_text: 翻译结果
        
        Returns:
            str: 后处理后的翻译结果
        """
        try:
            processed_text = translated_text
            
            # 应用后处理规则
            for rule in template.post_processing_rules:
                if rule.enabled and rule.apply_after_translation:
                    processed_text, _ = self._apply_rule(rule, processed_text)
            
            # 应用平台约束
            processed_text = self._apply_constraints(template.constraints, processed_text)
            
            return processed_text
            
        except Exception as e:
            logger.error(f"后处理翻译结果失败: {template.template_id}, {e}")
            return translated_text
    
    def _apply_rule(self, rule: TranslationRule, text: str) -> tuple[str, bool]:
        """应用翻译规则"""
        try:
            if not rule.pattern:
                return text, False
            
            import re
            
            flags = 0 if rule.case_sensitive else re.IGNORECASE
            
            if rule.use_regex:
                # 使用正则表达式
                if re.search(rule.pattern, text, flags):
                    new_text = re.sub(rule.pattern, rule.replacement, text, flags=flags)
                    return new_text, new_text != text
            else:
                # 简单字符串替换
                if rule.case_sensitive:
                    if rule.pattern in text:
                        return text.replace(rule.pattern, rule.replacement), True
                else:
                    if rule.pattern.lower() in text.lower():
                        # 保持大小写的替换
                        pattern = re.compile(re.escape(rule.pattern), re.IGNORECASE)
                        new_text = pattern.sub(rule.replacement, text)
                        return new_text, new_text != text
            
            return text, False
            
        except Exception as e:
            logger.error(f"应用翻译规则失败: {rule.rule_id}, {e}")
            return text, False
    
    def _apply_constraints(self, constraints: PlatformConstraints, text: str) -> str:
        """应用平台约束"""
        try:
            processed_text = text
            
            # 长度限制（这里假设是标题，实际应该根据类型判断）
            if constraints.max_title_length and len(processed_text) > constraints.max_title_length:
                processed_text = processed_text[:constraints.max_title_length].rstrip()
            
            # 移除禁用词汇
            for forbidden_word in constraints.forbidden_words:
                processed_text = processed_text.replace(forbidden_word, "")
            
            # 清理多余空格
            processed_text = " ".join(processed_text.split())
            
            return processed_text
            
        except Exception as e:
            logger.error(f"应用平台约束失败: {e}")
            return text
    
    def _validate_template(self, template: TranslationTemplate):
        """验证模板"""
        if not template.name:
            raise ValueError("模板名称不能为空")
        
        if not template.source_lang or not template.target_lang:
            raise ValueError("源语言和目标语言不能为空")
        
        if template.min_quality_score < 0 or template.min_quality_score > 10:
            raise ValueError("质量评分必须在0-10之间")
    
    def _load_default_templates(self):
        """加载默认模板"""
        try:
            # Amazon商品标题模板
            amazon_title_template = TranslationTemplate(
                template_id="amazon_title_en_zh",
                name="Amazon商品标题模板(英文->中文)",
                platform=PlatformType.AMAZON,
                template_type=TemplateType.PRODUCT_TITLE,
                source_lang="en",
                target_lang="zh",
                system_prompt="你是一个专业的Amazon商品标题翻译专家。请将英文商品标题翻译成中文，保持简洁、准确、吸引人。",
                user_prompt_template="请将以下Amazon商品标题翻译成中文：\n\n{text}\n\n要求：\n1. 保持关键词的准确性\n2. 符合中文表达习惯\n3. 长度控制在200字符以内\n4. 突出商品特色",
                constraints=PlatformConstraints(
                    max_title_length=200,
                    forbidden_words=["假货", "盗版", "山寨"],
                    required_keywords=[]
                ),
                min_quality_score=8.0,
                description="专用于Amazon平台的商品标题翻译模板"
            )
            
            self.templates[amazon_title_template.template_id] = amazon_title_template
            
            # 通用商品描述模板
            generic_desc_template = TranslationTemplate(
                template_id="generic_desc_en_zh",
                name="通用商品描述模板(英文->中文)",
                platform=PlatformType.GENERIC,
                template_type=TemplateType.PRODUCT_DESCRIPTION,
                source_lang="en",
                target_lang="zh",
                system_prompt="你是一个专业的商品描述翻译专家。请将英文商品描述翻译成中文，保持详细、准确、有说服力。",
                user_prompt_template="请将以下商品描述翻译成中文：\n\n{text}\n\n要求：\n1. 保持技术参数的准确性\n2. 使用流畅的中文表达\n3. 保持原文的结构和格式\n4. 突出商品优势和特色",
                min_quality_score=7.5,
                description="通用的商品描述翻译模板，适用于各种平台"
            )
            
            self.templates[generic_desc_template.template_id] = generic_desc_template
            
            logger.info("加载默认模板完成")
            
        except Exception as e:
            logger.error(f"加载默认模板失败: {e}")
    
    def _load_templates_from_disk(self):
        """从磁盘加载模板"""
        try:
            if not os.path.exists(self.templates_dir):
                return
            
            for filename in os.listdir(self.templates_dir):
                if filename.endswith('.json'):
                    template_file = os.path.join(self.templates_dir, filename)
                    try:
                        with open(template_file, 'r', encoding='utf-8') as f:
                            template_data = json.load(f)
                        
                        # 重建模板对象
                        template = self._dict_to_template(template_data)
                        self.templates[template.template_id] = template
                        
                    except Exception as e:
                        logger.error(f"加载模板文件失败: {template_file}, {e}")
            
            self._update_stats()
            logger.info(f"从磁盘加载模板: {len(self.templates)}个")
            
        except Exception as e:
            logger.error(f"从磁盘加载模板失败: {e}")
    
    def _save_template_to_disk(self, template: TranslationTemplate):
        """保存模板到磁盘"""
        try:
            template_file = os.path.join(self.templates_dir, f"{template.template_id}.json")
            template_data = self._template_to_dict(template)
            
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"保存模板到磁盘失败: {template.template_id}, {e}")
    
    def _template_to_dict(self, template: TranslationTemplate) -> Dict[str, Any]:
        """模板对象转字典"""
        data = asdict(template)
        # 转换枚举类型
        data['platform'] = template.platform.value
        data['template_type'] = template.template_type.value
        return data
    
    def _dict_to_template(self, data: Dict[str, Any]) -> TranslationTemplate:
        """字典转模板对象"""
        # 转换枚举类型
        data['platform'] = PlatformType(data['platform'])
        data['template_type'] = TemplateType(data['template_type'])
        
        # 转换日期时间
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 重建规则对象
        if 'pre_processing_rules' in data:
            data['pre_processing_rules'] = [
                TranslationRule(**rule) for rule in data['pre_processing_rules']
            ]
        
        if 'post_processing_rules' in data:
            data['post_processing_rules'] = [
                TranslationRule(**rule) for rule in data['post_processing_rules']
            ]
        
        # 重建约束对象
        if 'constraints' in data:
            data['constraints'] = PlatformConstraints(**data['constraints'])
        
        return TranslationTemplate(**data)
    
    def _backup_template(self, template: TranslationTemplate):
        """备份模板"""
        try:
            backup_dir = os.path.join(self.templates_dir, "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"{template.template_id}_{timestamp}.json")
            
            template_data = self._template_to_dict(template)
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"备份模板失败: {template.template_id}, {e}")
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            self.stats["total_templates"] = len(self.templates)
            self.stats["active_templates"] = len([t for t in self.templates.values() if t.enabled])
            self.stats["total_usage"] = sum(t.usage_count for t in self.templates.values())
            
            platforms = set(t.platform for t in self.templates.values())
            self.stats["platforms_count"] = len(platforms)
            
            template_types = set(t.template_type for t in self.templates.values())
            self.stats["template_types_count"] = len(template_types)
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            self._update_stats()
            
            # 平台分布
            platform_distribution = {}
            for template in self.templates.values():
                platform = template.platform.value
                platform_distribution[platform] = platform_distribution.get(platform, 0) + 1
            
            # 模板类型分布
            type_distribution = {}
            for template in self.templates.values():
                template_type = template.template_type.value
                type_distribution[template_type] = type_distribution.get(template_type, 0) + 1
            
            # 语言对分布
            language_pairs = {}
            for template in self.templates.values():
                pair = f"{template.source_lang}->{template.target_lang}"
                language_pairs[pair] = language_pairs.get(pair, 0) + 1
            
            return {
                "basic_stats": self.stats.copy(),
                "platform_distribution": platform_distribution,
                "type_distribution": type_distribution,
                "language_pairs": language_pairs,
                "config": self.config.copy()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"basic_stats": {}, "platform_distribution": {}, "type_distribution": {}}
