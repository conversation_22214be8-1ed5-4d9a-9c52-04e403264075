"""
任务调度API端点

提供任务调度、监控和管理的API接口
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from app.core.logging import get_logger
from app.services.task_scheduler_service import TaskSchedulerService

logger = get_logger(__name__)
router = APIRouter()

# 全局任务调度服务实例
scheduler_service = TaskSchedulerService()


class CrawlTaskRequest(BaseModel):
    """爬取任务请求模型"""
    urls: List[str] = Field(..., description="URL列表")
    platform: str = Field(..., description="平台类型")
    product_type: str = Field(..., description="商品类型")
    priority: str = Field("medium", description="任务优先级")
    batch_name: Optional[str] = Field(None, description="批次名称")


class BatchCrawlRequest(BaseModel):
    """批量爬取请求模型"""
    task_configs: List[Dict[str, Any]] = Field(..., description="任务配置列表")
    strategy: str = Field("batch_optimize", description="调度策略")


class AnalysisWorkflowRequest(BaseModel):
    """分析工作流请求模型"""
    crawl_task_ids: List[str] = Field(..., description="爬取任务ID列表")


@router.post("/crawl/single", summary="提交单个爬取任务")
async def submit_single_crawl_task(request: CrawlTaskRequest):
    """提交单个爬取任务"""
    try:
        task_id = scheduler_service.submit_single_crawl_task(
            urls=request.urls,
            platform=request.platform,
            product_type=request.product_type,
            priority=request.priority,
            batch_name=request.batch_name
        )
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "爬取任务已提交",
            "task_info": {
                "urls_count": len(request.urls),
                "platform": request.platform,
                "product_type": request.product_type,
                "priority": request.priority
            }
        }
        
    except Exception as e:
        logger.error(f"提交爬取任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/crawl/batch", summary="提交批量爬取任务")
async def submit_batch_crawl_tasks(request: BatchCrawlRequest):
    """提交批量爬取任务"""
    try:
        batch_id = scheduler_service.submit_batch_crawl_tasks(
            task_configs=request.task_configs,
            strategy=request.strategy
        )
        
        return {
            "success": True,
            "batch_id": batch_id,
            "message": "批量爬取任务已提交",
            "batch_info": {
                "task_count": len(request.task_configs),
                "strategy": request.strategy
            }
        }
        
    except Exception as e:
        logger.error(f"提交批量爬取任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analysis/workflow", summary="提交分析工作流")
async def submit_analysis_workflow(request: AnalysisWorkflowRequest):
    """提交分析工作流"""
    try:
        workflow_id = scheduler_service.submit_analysis_workflow(
            crawl_task_ids=request.crawl_task_ids
        )
        
        return {
            "success": True,
            "workflow_id": workflow_id,
            "message": "分析工作流已提交",
            "workflow_info": {
                "crawl_tasks_count": len(request.crawl_task_ids)
            }
        }
        
    except Exception as e:
        logger.error(f"提交分析工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analysis/parallel", summary="提交并行分析任务")
async def submit_parallel_analysis(task_groups: List[List[str]] = Body(...)):
    """提交并行分析任务"""
    try:
        group_id = scheduler_service.submit_parallel_analysis(task_groups)
        
        return {
            "success": True,
            "group_id": group_id,
            "message": "并行分析任务已提交",
            "group_info": {
                "groups_count": len(task_groups),
                "total_tasks": sum(len(group) for group in task_groups)
            }
        }
        
    except Exception as e:
        logger.error(f"提交并行分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", summary="获取任务状态")
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        task_status = scheduler_service.get_task_status(task_id)
        
        if task_status:
            return {
                "success": True,
                "task": {
                    "task_id": task_status.task_id,
                    "task_name": task_status.task_name,
                    "status": task_status.status.value,
                    "created_at": task_status.created_at.isoformat(),
                    "started_at": task_status.started_at.isoformat() if task_status.started_at else None,
                    "completed_at": task_status.completed_at.isoformat() if task_status.completed_at else None,
                    "retry_count": task_status.retry_count,
                    "result": task_status.result,
                    "error": task_status.error
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batches/{batch_id}", summary="获取批次状态")
async def get_batch_status(batch_id: str):
    """获取批次状态"""
    try:
        batch_status = scheduler_service.get_batch_status(batch_id)
        
        if batch_status:
            return {
                "success": True,
                "batch": {
                    "batch_id": batch_status.batch_id,
                    "batch_type": batch_status.batch_type,
                    "status": batch_status.status.value,
                    "progress": batch_status.progress,
                    "created_at": batch_status.created_at.isoformat(),
                    "task_ids": batch_status.task_ids,
                    "results": batch_status.results
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"批次不存在: {batch_id}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批次状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}", summary="取消任务")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        success = scheduler_service.cancel_task(task_id)
        
        if success:
            return {
                "success": True,
                "task_id": task_id,
                "message": "任务已取消"
            }
        else:
            raise HTTPException(status_code=400, detail="任务取消失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/retry", summary="重试失败任务")
async def retry_failed_task(task_id: str):
    """重试失败任务"""
    try:
        new_task_id = scheduler_service.retry_failed_task(task_id)
        
        if new_task_id:
            return {
                "success": True,
                "original_task_id": task_id,
                "new_task_id": new_task_id,
                "message": "任务重试已提交"
            }
        else:
            raise HTTPException(status_code=400, detail="任务无法重试")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", summary="获取活跃任务列表")
async def get_active_tasks(
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取活跃任务列表"""
    try:
        active_tasks = scheduler_service.get_active_tasks()
        
        # 分页处理
        total = len(active_tasks)
        tasks_page = active_tasks[offset:offset + limit]
        
        tasks_data = []
        for task in tasks_page:
            tasks_data.append({
                "task_id": task.task_id,
                "task_name": task.task_name,
                "status": task.status.value,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "retry_count": task.retry_count
            })
        
        return {
            "success": True,
            "total": total,
            "limit": limit,
            "offset": offset,
            "tasks": tasks_data
        }
        
    except Exception as e:
        logger.error(f"获取活跃任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", summary="获取任务统计信息")
async def get_task_statistics():
    """获取任务统计信息"""
    try:
        stats = scheduler_service.get_task_statistics()
        
        return {
            "success": True,
            "statistics": stats,
            "timestamp": scheduler_service.active_tasks and max(
                task.created_at for task in scheduler_service.active_tasks.values()
            ).isoformat() if scheduler_service.active_tasks else None
        }
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", summary="任务调度系统健康检查")
async def scheduler_health_check():
    """任务调度系统健康检查"""
    try:
        # 检查Celery连接
        from app.core.celery_app import celery_app
        
        # 获取活跃worker
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        # 获取队列信息
        queue_info = {}
        if active_workers:
            for worker, tasks in active_workers.items():
                queue_info[worker] = len(tasks)
        
        # 获取统计信息
        stats = scheduler_service.get_task_statistics()
        
        return {
            "success": True,
            "status": "healthy",
            "celery_workers": len(active_workers) if active_workers else 0,
            "active_workers": list(active_workers.keys()) if active_workers else [],
            "queue_info": queue_info,
            "task_statistics": stats,
            "timestamp": scheduler_service.active_tasks and max(
                task.created_at for task in scheduler_service.active_tasks.values()
            ).isoformat() if scheduler_service.active_tasks else None
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }
