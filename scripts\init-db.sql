-- Moniit 系统数据库初始化脚本
-- 创建数据库和基础表结构

-- 注意：数据库已由Docker环境变量创建，这里直接创建表结构

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建商品表
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    product_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    category VARCHAR(100),
    description TEXT,
    image_url VARCHAR(500),
    source_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- 创建供货商表
CREATE TABLE IF NOT EXISTS suppliers (
    id SERIAL PRIMARY KEY,
    supplier_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(50),
    address TEXT,
    website VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建监控任务表
CREATE TABLE IF NOT EXISTS monitor_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    product_id VARCHAR(100) REFERENCES products(product_id),
    url VARCHAR(500) NOT NULL,
    css_selector VARCHAR(255),
    interval_minutes INTEGER DEFAULT 60,
    status VARCHAR(20) DEFAULT 'active',
    last_run TIMESTAMP,
    next_run TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- 创建价格记录表
CREATE TABLE IF NOT EXISTS price_records (
    id SERIAL PRIMARY KEY,
    product_id VARCHAR(100) REFERENCES products(product_id),
    price DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'CNY',
    source_url VARCHAR(500),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_id VARCHAR(100) REFERENCES monitor_tasks(task_id)
);

-- 创建系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    module VARCHAR(100),
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_status ON monitor_tasks(status);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_next_run ON monitor_tasks(next_run);
CREATE INDEX IF NOT EXISTS idx_price_records_product_id ON price_records(product_id);
CREATE INDEX IF NOT EXISTS idx_price_records_recorded_at ON price_records(recorded_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- 插入默认管理员用户
INSERT INTO users (username, email, full_name, hashed_password, is_superuser) 
VALUES (
    'admin', 
    '<EMAIL>', 
    '系统管理员', 
    '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', -- 密码: admin123
    TRUE
) ON CONFLICT (username) DO NOTHING;

-- 插入示例商品数据
INSERT INTO products (product_id, name, brand, category, source_url, created_by) 
VALUES 
    ('prod_001', 'iPhone 15 Pro', 'Apple', '电子产品', 'https://www.apple.com/iphone-15-pro/', 1),
    ('prod_002', 'MacBook Pro', 'Apple', '电子产品', 'https://www.apple.com/macbook-pro/', 1),
    ('prod_003', 'AirPods Pro', 'Apple', '电子产品', 'https://www.apple.com/airpods-pro/', 1)
ON CONFLICT (product_id) DO NOTHING;

-- 插入示例供货商数据
INSERT INTO suppliers (supplier_id, name, contact_person, email, phone) 
VALUES 
    ('sup_001', 'Apple官方旗舰店', '张经理', '<EMAIL>', '138-0000-0001'),
    ('sup_002', '华为官方旗舰店', '李经理', '<EMAIL>', '138-0000-0002')
ON CONFLICT (supplier_id) DO NOTHING;

-- 插入示例监控任务
INSERT INTO monitor_tasks (task_id, name, product_id, url, css_selector, created_by) 
VALUES 
    ('task_001', 'iPhone 15 Pro 价格监控', 'prod_001', 'https://www.apple.com/iphone-15-pro/', '.price', 1),
    ('task_002', 'MacBook Pro 价格监控', 'prod_002', 'https://www.apple.com/macbook-pro/', '.price', 1)
ON CONFLICT (task_id) DO NOTHING;

-- 创建视图
CREATE OR REPLACE VIEW product_monitor_summary AS
SELECT 
    p.product_id,
    p.name as product_name,
    p.brand,
    p.category,
    COUNT(mt.id) as monitor_count,
    COUNT(pr.id) as price_record_count,
    MAX(pr.recorded_at) as last_price_update
FROM products p
LEFT JOIN monitor_tasks mt ON p.product_id = mt.product_id
LEFT JOIN price_records pr ON p.product_id = pr.product_id
WHERE p.is_active = TRUE
GROUP BY p.product_id, p.name, p.brand, p.category;

-- 创建函数：获取商品最新价格
CREATE OR REPLACE FUNCTION get_latest_price(product_id_param VARCHAR(100))
RETURNS DECIMAL(10,2) AS $$
DECLARE
    latest_price DECIMAL(10,2);
BEGIN
    SELECT price INTO latest_price
    FROM price_records
    WHERE product_id = product_id_param
    ORDER BY recorded_at DESC
    LIMIT 1;
    
    RETURN COALESCE(latest_price, 0.00);
END;
$$ LANGUAGE plpgsql;

-- 创建触发器：更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 应用触发器到相关表
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_tasks_updated_at BEFORE UPDATE ON monitor_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 完成初始化
INSERT INTO system_logs (level, message, module) 
VALUES ('INFO', '数据库初始化完成', 'database');

COMMIT;
