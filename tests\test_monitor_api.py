"""
监控管理API测试

测试监控管理API的所有功能
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import uuid4

from app.main import app
from app.core.database import get_db_session
from app.models.database import Product as DBProduct


@pytest.fixture
async def client():
    """创建测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def db_session():
    """创建测试数据库会话"""
    async with get_db_session() as session:
        yield session


@pytest.fixture
async def sample_products(db_session: AsyncSession):
    """创建测试商品"""
    products = []
    for i in range(3):
        product = DBProduct(
            url=f"https://example.com/test-product-{i}",
            platform="test_platform",
            title=f"测试商品{i+1}",
            category="electronics",
            status="active",
            monitoring_frequency=24,
            is_active=True
        )
        db_session.add(product)
        products.append(product)
    
    await db_session.commit()
    for product in products:
        await db_session.refresh(product)
    
    return products


class TestMonitorTasksAPI:
    """监控任务API测试类"""
    
    async def test_get_monitor_tasks_empty(self, client: AsyncClient):
        """测试获取空监控任务列表"""
        response = await client.get("/api/v1/monitor/tasks")
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert data["total"] >= 0
    
    async def test_create_monitor_task_success(self, client: AsyncClient, sample_products):
        """测试成功创建监控任务"""
        task_data = {
            "name": "测试监控任务",
            "description": "这是一个测试监控任务",
            "product_ids": [str(product.id) for product in sample_products],
            "schedule_type": "interval",
            "schedule_config": {
                "interval_hours": 2
            },
            "priority": "normal",
            "is_active": True
        }
        
        response = await client.post("/api/v1/monitor/tasks", json=task_data)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == task_data["name"]
        assert data["product_count"] == len(sample_products)
        assert data["status"] == "pending"
        assert "id" in data
        
        return data["id"]  # 返回任务ID供其他测试使用
    
    async def test_create_monitor_task_invalid_products(self, client: AsyncClient):
        """测试创建监控任务时使用无效商品ID"""
        task_data = {
            "name": "无效商品任务",
            "description": "使用无效商品ID的任务",
            "product_ids": [str(uuid4()), str(uuid4())],
            "schedule_type": "interval",
            "schedule_config": {
                "interval_hours": 1
            },
            "priority": "normal",
            "is_active": True
        }
        
        response = await client.post("/api/v1/monitor/tasks", json=task_data)
        assert response.status_code == 400
        assert "没有找到有效的商品ID" in response.json()["detail"]
    
    async def test_get_monitor_task_detail(self, client: AsyncClient, sample_products):
        """测试获取监控任务详情"""
        # 先创建一个任务
        task_data = {
            "name": "详情测试任务",
            "description": "用于测试获取详情的任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "high",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        assert create_response.status_code == 200
        task_id = create_response.json()["id"]
        
        # 获取任务详情
        response = await client.get(f"/api/v1/monitor/tasks/{task_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == task_id
        assert data["name"] == task_data["name"]
        assert "products" in data
        assert len(data["products"]) == 1
    
    async def test_get_monitor_task_not_found(self, client: AsyncClient):
        """测试获取不存在的监控任务"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/monitor/tasks/{fake_id}")
        assert response.status_code == 404
        assert "监控任务不存在" in response.json()["detail"]
    
    async def test_update_monitor_task(self, client: AsyncClient, sample_products):
        """测试更新监控任务"""
        # 先创建一个任务
        task_data = {
            "name": "更新测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 更新任务
        update_data = {
            "name": "更新后的任务名称",
            "description": "更新后的描述",
            "priority": "high"
        }
        
        response = await client.put(f"/api/v1/monitor/tasks/{task_id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["priority"] == update_data["priority"]
    
    async def test_delete_monitor_task(self, client: AsyncClient, sample_products):
        """测试删除监控任务"""
        # 先创建一个任务
        task_data = {
            "name": "删除测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 删除任务
        response = await client.delete(f"/api/v1/monitor/tasks/{task_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert "删除成功" in data["message"]
        
        # 验证任务已删除
        get_response = await client.get(f"/api/v1/monitor/tasks/{task_id}")
        assert get_response.status_code == 404


class TestMonitorTaskControl:
    """监控任务控制测试类"""
    
    async def create_test_task(self, client: AsyncClient, sample_products):
        """创建测试任务的辅助方法"""
        task_data = {
            "name": "控制测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        response = await client.post("/api/v1/monitor/tasks", json=task_data)
        return response.json()["id"]
    
    async def test_start_monitor_task(self, client: AsyncClient, sample_products):
        """测试启动监控任务"""
        task_id = await self.create_test_task(client, sample_products)
        
        response = await client.post(f"/api/v1/monitor/tasks/{task_id}/start")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert data["status"] == "running"
        assert "启动成功" in data["message"]
    
    async def test_pause_monitor_task(self, client: AsyncClient, sample_products):
        """测试暂停监控任务"""
        task_id = await self.create_test_task(client, sample_products)
        
        # 先启动任务
        await client.post(f"/api/v1/monitor/tasks/{task_id}/start")
        
        # 暂停任务
        response = await client.post(f"/api/v1/monitor/tasks/{task_id}/pause")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert data["status"] == "pending"
        assert "暂停成功" in data["message"]
    
    async def test_stop_monitor_task(self, client: AsyncClient, sample_products):
        """测试停止监控任务"""
        task_id = await self.create_test_task(client, sample_products)
        
        response = await client.post(f"/api/v1/monitor/tasks/{task_id}/stop")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert data["status"] == "cancelled"
        assert "停止成功" in data["message"]
    
    async def test_start_inactive_task(self, client: AsyncClient, sample_products):
        """测试启动未激活的任务"""
        # 创建未激活的任务
        task_data = {
            "name": "未激活任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": False
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 尝试启动未激活的任务
        response = await client.post(f"/api/v1/monitor/tasks/{task_id}/start")
        assert response.status_code == 400
        assert "任务未激活" in response.json()["detail"]


class TestMonitorTaskStatus:
    """监控任务状态测试类"""
    
    async def test_get_task_logs(self, client: AsyncClient, sample_products):
        """测试获取任务执行日志"""
        # 创建任务
        task_data = {
            "name": "日志测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 获取日志
        response = await client.get(f"/api/v1/monitor/tasks/{task_id}/logs")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert "logs" in data
        assert "total" in data
    
    async def test_get_task_logs_with_filter(self, client: AsyncClient, sample_products):
        """测试带筛选条件的日志查询"""
        # 创建任务
        task_data = {
            "name": "筛选日志测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 获取ERROR级别的日志
        response = await client.get(f"/api/v1/monitor/tasks/{task_id}/logs?level=ERROR&limit=10")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        # 验证所有日志都是ERROR级别
        for log in data["logs"]:
            assert log["level"] == "ERROR"
    
    async def test_get_task_status(self, client: AsyncClient, sample_products):
        """测试获取任务实时状态"""
        # 创建任务
        task_data = {
            "name": "状态测试任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "normal",
            "is_active": True
        }
        
        create_response = await client.post("/api/v1/monitor/tasks", json=task_data)
        task_id = create_response.json()["id"]
        
        # 获取状态
        response = await client.get(f"/api/v1/monitor/tasks/{task_id}/status")
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == task_id
        assert "status" in data
        assert "success_rate" in data
        assert "product_count" in data
    
    async def test_get_tasks_with_filters(self, client: AsyncClient, sample_products):
        """测试带筛选条件的任务列表查询"""
        # 创建不同优先级的任务
        high_priority_task = {
            "name": "高优先级任务",
            "product_ids": [str(sample_products[0].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 1},
            "priority": "high",
            "is_active": True
        }
        
        low_priority_task = {
            "name": "低优先级任务",
            "product_ids": [str(sample_products[1].id)],
            "schedule_type": "interval",
            "schedule_config": {"interval_hours": 2},
            "priority": "low",
            "is_active": False
        }
        
        await client.post("/api/v1/monitor/tasks", json=high_priority_task)
        await client.post("/api/v1/monitor/tasks", json=low_priority_task)
        
        # 测试优先级筛选
        response = await client.get("/api/v1/monitor/tasks?priority=high")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        for task in data["items"]:
            assert task["priority"] == "high"
        
        # 测试激活状态筛选
        response = await client.get("/api/v1/monitor/tasks?is_active=false")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        for task in data["items"]:
            assert task["is_active"] is False
        
        # 测试搜索功能
        response = await client.get("/api/v1/monitor/tasks?search=高优先级")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1


if __name__ == "__main__":
    pytest.main([__file__])
