/**
 * 供货商列表页面
 */

import React from 'react';
import { Card, Table, Button, Tag, Space, Alert } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  products: number;
}

const SupplierListPage: React.FC = () => {
  // 模拟数据
  const suppliers: Supplier[] = [
    {
      id: '1',
      name: 'Apple官方旗舰店',
      contact: '张经理',
      email: '<EMAIL>',
      phone: '138-0000-0001',
      status: 'active',
      products: 15,
    },
    {
      id: '2',
      name: '华为官方旗舰店',
      contact: '李经理',
      email: '<EMAIL>',
      phone: '138-0000-0002',
      status: 'active',
      products: 12,
    },
  ];

  const columns: ColumnsType<Supplier> = [
    {
      title: '供货商名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      key: 'contact',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '商品数量',
      dataIndex: 'products',
      key: 'products',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button size="small" icon={<EditOutlined />}>
            编辑
          </Button>
          <Button size="small" danger icon={<DeleteOutlined />}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>供货商管理 [未完成]</h2>
        <Button type="primary" icon={<PlusOutlined />} disabled>
          新增供货商 [未完成]
        </Button>
      </div>

      <Alert
        message="功能开发中"
        description="供货商管理功能尚未完成，当前显示的是模拟数据。实际的供货商增删改查、联系人管理等功能需要后端API支持。"
        type="warning"
        showIcon
        className="mb-4"
      />

      <Card>
        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );
};

export default SupplierListPage;
