"""
价格分析引擎

负责商品价格趋势分析、价格波动检测和异常预警
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import math

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductChangeRecord

logger = get_logger(__name__)


class PriceTrend(Enum):
    """价格趋势"""
    RISING = "rising"           # 上涨
    FALLING = "falling"         # 下跌
    STABLE = "stable"           # 稳定
    VOLATILE = "volatile"       # 波动
    UNKNOWN = "unknown"         # 未知


class PriceAlert(Enum):
    """价格预警级别"""
    CRITICAL = "critical"       # 严重：价格异常波动
    HIGH = "high"              # 高：价格大幅变化
    MEDIUM = "medium"          # 中：价格明显变化
    LOW = "low"                # 低：价格轻微变化
    INFO = "info"              # 信息：正常价格变化


@dataclass
class PricePoint:
    """价格数据点"""
    timestamp: datetime
    price: float
    source: str = "crawl"
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class PriceAnalysisResult:
    """价格分析结果"""
    product_id: str
    product_type: ProductType
    analysis_period: Tuple[datetime, datetime]
    current_price: float
    price_trend: PriceTrend
    trend_strength: float  # 趋势强度 0-1
    volatility: float      # 波动率
    price_change_percent: float
    price_change_absolute: float
    average_price: float
    min_price: float
    max_price: float
    price_alerts: List[Dict[str, Any]]
    analysis_timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class CompetitorPriceAnalysis:
    """竞品价格分析"""
    product_id: str
    competitor_products: List[str]
    price_comparison: Dict[str, float]
    market_position: str  # "lowest", "highest", "middle"
    price_advantage: float  # 价格优势百分比
    recommendations: List[str]


@dataclass
class SupplierPriceAnalysis:
    """供应商价格分析"""
    product_id: str
    supplier_id: str
    cost_trend: PriceTrend
    cost_change_percent: float
    profit_margin_impact: float
    supply_risk_level: str  # "low", "medium", "high"
    recommendations: List[str]


class PriceAnalyzer:
    """价格分析引擎"""
    
    def __init__(self):
        self.price_history: Dict[str, List[PricePoint]] = {}
        self.analysis_cache: Dict[str, PriceAnalysisResult] = {}
        self.alert_thresholds = {
            ProductType.COMPETITOR: {
                "critical": 0.20,  # 20%变化
                "high": 0.15,      # 15%变化
                "medium": 0.10,    # 10%变化
                "low": 0.05        # 5%变化
            },
            ProductType.SUPPLIER: {
                "critical": 0.30,  # 30%变化
                "high": 0.20,      # 20%变化
                "medium": 0.15,    # 15%变化
                "low": 0.10        # 10%变化
            },
            ProductType.OTHER: {
                "critical": 0.25,  # 25%变化
                "high": 0.18,      # 18%变化
                "medium": 0.12,    # 12%变化
                "low": 0.08        # 8%变化
            }
        }
    
    async def add_price_data(self, product_id: str, price: float, 
                           timestamp: Optional[datetime] = None,
                           source: str = "crawl",
                           metadata: Optional[Dict[str, Any]] = None):
        """
        添加价格数据点
        
        Args:
            product_id: 商品ID
            price: 价格
            timestamp: 时间戳
            source: 数据源
            metadata: 元数据
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        price_point = PricePoint(
            timestamp=timestamp,
            price=price,
            source=source,
            metadata=metadata or {}
        )
        
        if product_id not in self.price_history:
            self.price_history[product_id] = []
        
        self.price_history[product_id].append(price_point)
        
        # 按时间排序
        self.price_history[product_id].sort(key=lambda x: x.timestamp)
        
        # 清除缓存
        if product_id in self.analysis_cache:
            del self.analysis_cache[product_id]
        
        logger.debug(f"添加价格数据: {product_id} - ¥{price}")
    
    async def analyze_price_trend(self, product: Product, 
                                days: int = 30) -> PriceAnalysisResult:
        """
        分析商品价格趋势
        
        Args:
            product: 商品对象
            days: 分析天数
        
        Returns:
            PriceAnalysisResult: 价格分析结果
        """
        try:
            logger.info(f"开始价格趋势分析: {product.id}")
            
            # 检查缓存
            cache_key = f"{product.id}_{days}"
            if cache_key in self.analysis_cache:
                cached_result = self.analysis_cache[cache_key]
                # 如果缓存不超过1小时，直接返回
                if (datetime.now() - cached_result.analysis_timestamp).seconds < 3600:
                    return cached_result
            
            # 获取价格历史数据
            price_data = await self._get_price_data(product.id, days)
            
            if not price_data:
                # 如果没有历史数据，从商品对象获取当前价格
                current_price = product.price.current_price if product.price else 0.0
                return self._create_empty_analysis(product, current_price)
            
            # 执行价格分析
            analysis_result = await self._perform_price_analysis(product, price_data, days)
            
            # 缓存结果
            self.analysis_cache[cache_key] = analysis_result
            
            logger.info(f"价格趋势分析完成: {product.id} - 趋势: {analysis_result.price_trend.value}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"价格趋势分析失败: {e}")
            current_price = product.price.current_price if product.price else 0.0
            return self._create_empty_analysis(product, current_price)
    
    async def _get_price_data(self, product_id: str, days: int) -> List[PricePoint]:
        """获取指定天数的价格数据"""
        if product_id not in self.price_history:
            return []
        
        cutoff_date = datetime.now() - timedelta(days=days)
        price_data = [
            point for point in self.price_history[product_id]
            if point.timestamp >= cutoff_date
        ]
        
        return price_data
    
    async def _perform_price_analysis(self, product: Product, 
                                    price_data: List[PricePoint], 
                                    days: int) -> PriceAnalysisResult:
        """执行价格分析"""
        prices = [point.price for point in price_data]
        timestamps = [point.timestamp for point in price_data]
        
        # 基础统计
        current_price = prices[-1]
        average_price = statistics.mean(prices)
        min_price = min(prices)
        max_price = max(prices)
        
        # 价格变化
        if len(prices) > 1:
            first_price = prices[0]
            price_change_absolute = current_price - first_price
            price_change_percent = (price_change_absolute / first_price) * 100 if first_price > 0 else 0
        else:
            price_change_absolute = 0
            price_change_percent = 0
        
        # 趋势分析
        price_trend, trend_strength = await self._calculate_trend(prices, timestamps)
        
        # 波动率计算
        volatility = await self._calculate_volatility(prices)
        
        # 价格预警
        price_alerts = await self._generate_price_alerts(
            product, current_price, price_change_percent, volatility
        )
        
        return PriceAnalysisResult(
            product_id=product.id,
            product_type=product.product_type,
            analysis_period=(timestamps[0], timestamps[-1]),
            current_price=current_price,
            price_trend=price_trend,
            trend_strength=trend_strength,
            volatility=volatility,
            price_change_percent=price_change_percent,
            price_change_absolute=price_change_absolute,
            average_price=average_price,
            min_price=min_price,
            max_price=max_price,
            price_alerts=price_alerts
        )
    
    async def _calculate_trend(self, prices: List[float], 
                             timestamps: List[datetime]) -> Tuple[PriceTrend, float]:
        """计算价格趋势"""
        if len(prices) < 2:
            return PriceTrend.UNKNOWN, 0.0
        
        # 使用线性回归计算趋势
        n = len(prices)
        x_values = list(range(n))
        
        # 计算斜率
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(prices)
        
        numerator = sum((x_values[i] - x_mean) * (prices[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return PriceTrend.STABLE, 0.0
        
        slope = numerator / denominator
        
        # 计算相关系数（趋势强度）
        y_variance = sum((prices[i] - y_mean) ** 2 for i in range(n))
        if y_variance == 0:
            correlation = 0.0
        else:
            correlation = abs(numerator) / math.sqrt(denominator * y_variance)
        
        # 判断趋势
        slope_threshold = y_mean * 0.001  # 0.1%的变化阈值
        
        if abs(slope) < slope_threshold:
            trend = PriceTrend.STABLE
        elif slope > slope_threshold:
            trend = PriceTrend.RISING
        else:
            trend = PriceTrend.FALLING
        
        # 检查波动性
        if correlation < 0.5 and self._is_volatile(prices):
            trend = PriceTrend.VOLATILE
        
        return trend, correlation
    
    def _is_volatile(self, prices: List[float]) -> bool:
        """检查价格是否波动"""
        if len(prices) < 3:
            return False
        
        # 计算相邻价格变化的标准差
        changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] > 0]
        
        if not changes:
            return False
        
        change_std = statistics.stdev(changes) if len(changes) > 1 else 0
        return change_std > 0.05  # 5%的标准差阈值
    
    async def _calculate_volatility(self, prices: List[float]) -> float:
        """计算价格波动率"""
        if len(prices) < 2:
            return 0.0
        
        # 计算价格变化率
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] > 0:
                return_rate = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(return_rate)
        
        if not returns:
            return 0.0
        
        # 计算标准差作为波动率
        return statistics.stdev(returns) if len(returns) > 1 else 0.0
    
    async def _generate_price_alerts(self, product: Product, current_price: float,
                                   price_change_percent: float, 
                                   volatility: float) -> List[Dict[str, Any]]:
        """生成价格预警"""
        alerts = []
        
        # 获取阈值
        thresholds = self.alert_thresholds.get(product.product_type, 
                                             self.alert_thresholds[ProductType.OTHER])
        
        # 价格变化预警
        abs_change = abs(price_change_percent)
        
        if abs_change >= thresholds["critical"]:
            alert_level = PriceAlert.CRITICAL
        elif abs_change >= thresholds["high"]:
            alert_level = PriceAlert.HIGH
        elif abs_change >= thresholds["medium"]:
            alert_level = PriceAlert.MEDIUM
        elif abs_change >= thresholds["low"]:
            alert_level = PriceAlert.LOW
        else:
            alert_level = PriceAlert.INFO
        
        if alert_level != PriceAlert.INFO:
            direction = "上涨" if price_change_percent > 0 else "下跌"
            alerts.append({
                "type": "price_change",
                "level": alert_level.value,
                "message": f"价格{direction} {abs_change:.1f}%",
                "current_price": current_price,
                "change_percent": price_change_percent,
                "timestamp": datetime.now().isoformat()
            })
        
        # 波动率预警
        if volatility > 0.15:  # 15%波动率阈值
            alerts.append({
                "type": "high_volatility",
                "level": PriceAlert.HIGH.value,
                "message": f"价格波动率过高: {volatility:.1%}",
                "volatility": volatility,
                "timestamp": datetime.now().isoformat()
            })
        elif volatility > 0.10:  # 10%波动率阈值
            alerts.append({
                "type": "medium_volatility",
                "level": PriceAlert.MEDIUM.value,
                "message": f"价格波动率较高: {volatility:.1%}",
                "volatility": volatility,
                "timestamp": datetime.now().isoformat()
            })
        
        return alerts
    
    def _create_empty_analysis(self, product: Product, current_price: float) -> PriceAnalysisResult:
        """创建空的分析结果"""
        now = datetime.now()
        return PriceAnalysisResult(
            product_id=product.id,
            product_type=product.product_type,
            analysis_period=(now, now),
            current_price=current_price,
            price_trend=PriceTrend.UNKNOWN,
            trend_strength=0.0,
            volatility=0.0,
            price_change_percent=0.0,
            price_change_absolute=0.0,
            average_price=current_price,
            min_price=current_price,
            max_price=current_price,
            price_alerts=[]
        )
    
    async def analyze_competitor_prices(self, product: Product, 
                                      competitor_products: List[Product]) -> CompetitorPriceAnalysis:
        """
        分析竞品价格
        
        Args:
            product: 目标商品
            competitor_products: 竞品商品列表
        
        Returns:
            CompetitorPriceAnalysis: 竞品价格分析结果
        """
        try:
            logger.info(f"开始竞品价格分析: {product.id}")
            
            current_price = product.price.current_price if product.price else 0.0
            
            # 收集竞品价格
            price_comparison = {}
            competitor_prices = []
            
            for competitor in competitor_products:
                if competitor.price and competitor.price.current_price:
                    comp_price = competitor.price.current_price
                    price_comparison[competitor.id] = comp_price
                    competitor_prices.append(comp_price)
            
            if not competitor_prices:
                return CompetitorPriceAnalysis(
                    product_id=product.id,
                    competitor_products=[],
                    price_comparison={},
                    market_position="unknown",
                    price_advantage=0.0,
                    recommendations=["无竞品价格数据"]
                )
            
            # 分析市场位置
            market_position = self._determine_market_position(current_price, competitor_prices)
            
            # 计算价格优势
            avg_competitor_price = statistics.mean(competitor_prices)
            price_advantage = ((avg_competitor_price - current_price) / avg_competitor_price * 100 
                             if avg_competitor_price > 0 else 0.0)
            
            # 生成建议
            recommendations = self._generate_competitor_recommendations(
                current_price, competitor_prices, market_position, price_advantage
            )
            
            return CompetitorPriceAnalysis(
                product_id=product.id,
                competitor_products=[comp.id for comp in competitor_products],
                price_comparison=price_comparison,
                market_position=market_position,
                price_advantage=price_advantage,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"竞品价格分析失败: {e}")
            return CompetitorPriceAnalysis(
                product_id=product.id,
                competitor_products=[],
                price_comparison={},
                market_position="unknown",
                price_advantage=0.0,
                recommendations=[f"分析失败: {str(e)}"]
            )
    
    def _determine_market_position(self, current_price: float, 
                                 competitor_prices: List[float]) -> str:
        """确定市场价格位置"""
        if not competitor_prices:
            return "unknown"
        
        min_price = min(competitor_prices)
        max_price = max(competitor_prices)
        
        if current_price <= min_price:
            return "lowest"
        elif current_price >= max_price:
            return "highest"
        else:
            return "middle"
    
    def _generate_competitor_recommendations(self, current_price: float,
                                           competitor_prices: List[float],
                                           market_position: str,
                                           price_advantage: float) -> List[str]:
        """生成竞品价格建议"""
        recommendations = []
        
        if market_position == "highest":
            recommendations.append("价格高于所有竞品，考虑降价提高竞争力")
            if price_advantage < -20:
                recommendations.append("价格劣势超过20%，建议立即调整价格策略")
        elif market_position == "lowest":
            recommendations.append("价格低于所有竞品，具有价格优势")
            if price_advantage > 15:
                recommendations.append("价格优势明显，可考虑适当提价增加利润")
        else:
            recommendations.append("价格处于市场中等水平")
            if abs(price_advantage) < 5:
                recommendations.append("价格与竞品接近，关注产品差异化")
        
        # 基于价格优势的建议
        if price_advantage > 10:
            recommendations.append("价格优势超过10%，有利于市场竞争")
        elif price_advantage < -10:
            recommendations.append("价格劣势超过10%，需要关注成本控制")
        
        return recommendations
    
    async def analyze_supplier_prices(self, product: Product, 
                                    supplier_id: str) -> SupplierPriceAnalysis:
        """
        分析供应商价格
        
        Args:
            product: 商品对象
            supplier_id: 供应商ID
        
        Returns:
            SupplierPriceAnalysis: 供应商价格分析结果
        """
        try:
            logger.info(f"开始供应商价格分析: {product.id}")
            
            # 获取价格趋势
            price_analysis = await self.analyze_price_trend(product, days=30)
            cost_trend = price_analysis.price_trend
            cost_change_percent = price_analysis.price_change_percent
            
            # 计算利润率影响（简化计算）
            profit_margin_impact = self._calculate_profit_margin_impact(cost_change_percent)
            
            # 评估供应风险
            supply_risk_level = self._assess_supply_risk(
                cost_change_percent, price_analysis.volatility
            )
            
            # 生成建议
            recommendations = self._generate_supplier_recommendations(
                cost_trend, cost_change_percent, supply_risk_level
            )
            
            return SupplierPriceAnalysis(
                product_id=product.id,
                supplier_id=supplier_id,
                cost_trend=cost_trend,
                cost_change_percent=cost_change_percent,
                profit_margin_impact=profit_margin_impact,
                supply_risk_level=supply_risk_level,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"供应商价格分析失败: {e}")
            return SupplierPriceAnalysis(
                product_id=product.id,
                supplier_id=supplier_id,
                cost_trend=PriceTrend.UNKNOWN,
                cost_change_percent=0.0,
                profit_margin_impact=0.0,
                supply_risk_level="unknown",
                recommendations=[f"分析失败: {str(e)}"]
            )
    
    def _calculate_profit_margin_impact(self, cost_change_percent: float) -> float:
        """计算利润率影响"""
        # 简化计算：假设成本变化直接影响利润率
        # 实际应用中需要考虑更复杂的成本结构
        return -cost_change_percent  # 成本上涨，利润率下降
    
    def _assess_supply_risk(self, cost_change_percent: float, volatility: float) -> str:
        """评估供应风险"""
        risk_score = 0
        
        # 成本变化风险
        if abs(cost_change_percent) > 20:
            risk_score += 3
        elif abs(cost_change_percent) > 10:
            risk_score += 2
        elif abs(cost_change_percent) > 5:
            risk_score += 1
        
        # 波动率风险
        if volatility > 0.15:
            risk_score += 2
        elif volatility > 0.10:
            risk_score += 1
        
        # 风险等级
        if risk_score >= 4:
            return "high"
        elif risk_score >= 2:
            return "medium"
        else:
            return "low"
    
    def _generate_supplier_recommendations(self, cost_trend: PriceTrend,
                                         cost_change_percent: float,
                                         supply_risk_level: str) -> List[str]:
        """生成供应商价格建议"""
        recommendations = []
        
        if cost_trend == PriceTrend.RISING:
            recommendations.append("供应商成本上涨，考虑寻找替代供应商")
            if cost_change_percent > 15:
                recommendations.append("成本上涨超过15%，建议重新谈判价格")
        elif cost_trend == PriceTrend.FALLING:
            recommendations.append("供应商成本下降，可争取更优惠价格")
        elif cost_trend == PriceTrend.VOLATILE:
            recommendations.append("供应商价格波动较大，建议签订价格稳定协议")
        
        if supply_risk_level == "high":
            recommendations.append("供应风险较高，建议多元化供应商")
        elif supply_risk_level == "medium":
            recommendations.append("供应风险中等，密切关注价格变化")
        
        return recommendations
    
    async def batch_analyze_prices(self, products: List[Product], 
                                 days: int = 30) -> List[PriceAnalysisResult]:
        """批量分析商品价格"""
        logger.info(f"开始批量价格分析: {len(products)} 个商品")
        
        tasks = [self.analyze_price_trend(product, days) for product in products]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"商品 {products[i].id} 价格分析失败: {result}")
                # 创建错误结果
                error_result = self._create_empty_analysis(products[i], 0.0)
                valid_results.append(error_result)
            else:
                valid_results.append(result)
        
        logger.info(f"批量价格分析完成: {len(valid_results)} 个结果")
        return valid_results
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        total_products = len(self.price_history)
        total_price_points = sum(len(points) for points in self.price_history.values())
        cached_analyses = len(self.analysis_cache)
        
        # 统计趋势分布
        trend_distribution = {}
        for analysis in self.analysis_cache.values():
            trend = analysis.price_trend.value
            trend_distribution[trend] = trend_distribution.get(trend, 0) + 1
        
        return {
            "total_products": total_products,
            "total_price_points": total_price_points,
            "cached_analyses": cached_analyses,
            "trend_distribution": trend_distribution,
            "alert_thresholds": self.alert_thresholds
        }
