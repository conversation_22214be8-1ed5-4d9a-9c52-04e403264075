"""
数据分析API测试

测试数据分析API的所有功能
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import uuid4
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.core.database import get_db_session
from app.models.database import Product as DBProduct, ProductHistory as DBProductHistory


@pytest.fixture
async def client():
    """创建测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def db_session():
    """创建测试数据库会话"""
    async with get_db_session() as session:
        yield session


@pytest.fixture
async def sample_product_with_history(db_session: AsyncSession):
    """创建带历史数据的测试商品"""
    # 创建商品
    product = DBProduct(
        url="https://example.com/analytics-test-product",
        platform="test_platform",
        title="分析测试商品",
        title_translated="Analytics Test Product",
        category="electronics",
        status="active",
        monitoring_frequency=24,
        is_active=True
    )
    db_session.add(product)
    await db_session.commit()
    await db_session.refresh(product)
    
    # 创建历史价格数据
    base_time = datetime.now() - timedelta(days=30)
    for i in range(30):
        history = DBProductHistory(
            product_id=product.id,
            price=Decimal(str(100 + i * 2)),  # 价格从100递增
            currency="USD",
            stock_quantity=50 - i,
            rating=Decimal("4.5"),
            review_count=100 + i * 5,
            created_at=base_time + timedelta(days=i)
        )
        db_session.add(history)
    
    await db_session.commit()
    return product


@pytest.fixture
async def multiple_products(db_session: AsyncSession):
    """创建多个测试商品"""
    products = []
    platforms = ["platform1", "platform2", "platform3"]
    categories = ["electronics", "books", "clothing"]
    
    for i in range(9):  # 3x3 = 9个商品
        product = DBProduct(
            url=f"https://example.com/product-{i}",
            platform=platforms[i % 3],
            title=f"测试商品{i+1}",
            category=categories[i % 3],
            status="active",
            monitoring_frequency=24,
            is_active=i % 2 == 0  # 一半激活，一半不激活
        )
        db_session.add(product)
        products.append(product)
    
    await db_session.commit()
    for product in products:
        await db_session.refresh(product)
    
    return products


class TestPriceTrendsAPI:
    """价格趋势API测试类"""
    
    async def test_get_price_trends_success(self, client: AsyncClient, sample_product_with_history):
        """测试成功获取价格趋势"""
        response = await client.get(f"/api/v1/analytics/price-trends/{sample_product_with_history.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["product_id"] == str(sample_product_with_history.id)
        assert data["product_title"] == sample_product_with_history.title
        assert "data_points" in data
        assert "statistics" in data
        assert len(data["data_points"]) > 0
        
        # 验证数据点结构
        first_point = data["data_points"][0]
        assert "timestamp" in first_point
        assert "price" in first_point
        assert "currency" in first_point
        
        # 验证统计信息
        stats = data["statistics"]
        assert "min_price" in stats
        assert "max_price" in stats
        assert "avg_price" in stats
        assert "total_change" in stats
    
    async def test_get_price_trends_with_params(self, client: AsyncClient, sample_product_with_history):
        """测试带参数的价格趋势查询"""
        response = await client.get(
            f"/api/v1/analytics/price-trends/{sample_product_with_history.id}?days=7&interval=1d"
        )
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["data_points"]) <= 7  # 最多7天的数据
    
    async def test_get_price_trends_not_found(self, client: AsyncClient):
        """测试获取不存在商品的价格趋势"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/analytics/price-trends/{fake_id}")
        assert response.status_code == 404
        assert "商品不存在" in response.json()["detail"]
    
    async def test_get_price_trends_invalid_id(self, client: AsyncClient):
        """测试无效商品ID"""
        response = await client.get("/api/v1/analytics/price-trends/invalid-id")
        assert response.status_code == 400
        assert "无效的商品ID格式" in response.json()["detail"]


class TestStatisticsAPI:
    """统计数据API测试类"""
    
    async def test_get_statistics_success(self, client: AsyncClient, multiple_products):
        """测试成功获取统计数据"""
        response = await client.get("/api/v1/analytics/statistics")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_products" in data
        assert "active_products" in data
        assert "total_price_records" in data
        assert "platforms" in data
        assert "categories" in data
        assert "price_ranges" in data
        
        # 验证数据类型
        assert isinstance(data["total_products"], int)
        assert isinstance(data["active_products"], int)
        assert isinstance(data["platforms"], dict)
        assert isinstance(data["categories"], dict)
    
    async def test_get_statistics_with_platform_filter(self, client: AsyncClient, multiple_products):
        """测试带平台筛选的统计数据"""
        response = await client.get("/api/v1/analytics/statistics?platform=platform1")
        assert response.status_code == 200
        
        data = response.json()
        # 验证平台筛选生效
        assert data["total_products"] >= 0
        if "platform1" in data["platforms"]:
            assert data["platforms"]["platform1"] > 0
    
    async def test_get_statistics_with_category_filter(self, client: AsyncClient, multiple_products):
        """测试带分类筛选的统计数据"""
        response = await client.get("/api/v1/analytics/statistics?category=electronics")
        assert response.status_code == 200
        
        data = response.json()
        # 验证分类筛选生效
        assert data["total_products"] >= 0
        if "electronics" in data["categories"]:
            assert data["categories"]["electronics"] > 0
    
    async def test_get_statistics_with_days_filter(self, client: AsyncClient, multiple_products):
        """测试带天数筛选的统计数据"""
        response = await client.get("/api/v1/analytics/statistics?days=7")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_products" in data
        assert data["total_products"] >= 0


class TestReportsAPI:
    """报表生成API测试类"""
    
    async def test_generate_summary_report(self, client: AsyncClient, multiple_products):
        """测试生成摘要报表"""
        report_filters = {
            "platforms": ["platform1", "platform2"],
            "categories": ["electronics"]
        }
        
        response = await client.post(
            "/api/v1/analytics/reports/generate?report_type=summary&format=json",
            json=report_filters
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["report_type"] == "summary"
        assert "generated_at" in data
        assert "summary" in data
        assert "filters" in data
        
        # 验证摘要数据结构
        summary = data["summary"]
        assert "total_products" in summary
        assert "platforms" in summary
        assert "categories" in summary
        assert "active_products" in summary
    
    async def test_generate_detailed_report(self, client: AsyncClient, multiple_products):
        """测试生成详细报表"""
        report_filters = {
            "platforms": ["platform1"]
        }
        
        response = await client.post(
            "/api/v1/analytics/reports/generate?report_type=detailed&format=json",
            json=report_filters
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["report_type"] == "detailed"
        assert "data" in data
        assert isinstance(data["data"], list)
        
        # 验证详细数据结构
        if data["data"]:
            first_item = data["data"][0]
            assert "id" in first_item
            assert "title" in first_item
            assert "platform" in first_item
    
    async def test_generate_comparison_report(self, client: AsyncClient, multiple_products):
        """测试生成对比报表"""
        report_filters = {}
        
        response = await client.post(
            "/api/v1/analytics/reports/generate?report_type=comparison&format=json",
            json=report_filters
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["report_type"] == "comparison"
        assert "comparison" in data
        assert isinstance(data["comparison"], dict)
        
        # 验证对比数据结构
        for platform, stats in data["comparison"].items():
            assert "total" in stats
            assert "active" in stats
            assert "categories" in stats
    
    async def test_generate_report_invalid_type(self, client: AsyncClient):
        """测试生成无效类型的报表"""
        response = await client.post(
            "/api/v1/analytics/reports/generate?report_type=invalid&format=json",
            json={}
        )
        assert response.status_code == 400
        assert "不支持的报表类型" in response.json()["detail"]
    
    async def test_generate_report_invalid_format(self, client: AsyncClient):
        """测试生成无效格式的报表"""
        response = await client.post(
            "/api/v1/analytics/reports/generate?report_type=summary&format=invalid",
            json={}
        )
        assert response.status_code == 400
        assert "不支持的输出格式" in response.json()["detail"]


class TestSearchAPI:
    """搜索API测试类"""
    
    async def test_search_without_filters(self, client: AsyncClient, multiple_products):
        """测试无筛选条件的搜索"""
        response = await client.get("/api/v1/analytics/search")
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert "filters" in data
        
        # 验证返回的商品数据结构
        if data["items"]:
            first_item = data["items"][0]
            assert "id" in first_item
            assert "title" in first_item
            assert "platform" in first_item
            assert "latest_price" in first_item
    
    async def test_search_with_keyword(self, client: AsyncClient, multiple_products):
        """测试关键词搜索"""
        response = await client.get("/api/v1/analytics/search?keyword=测试商品1")
        assert response.status_code == 200
        
        data = response.json()
        assert data["filters"]["keyword"] == "测试商品1"
        # 验证搜索结果包含关键词
        for item in data["items"]:
            assert "测试商品1" in item["title"] or "测试商品1" in (item["title_translated"] or "")
    
    async def test_search_with_platform_filter(self, client: AsyncClient, multiple_products):
        """测试平台筛选搜索"""
        response = await client.get("/api/v1/analytics/search?platform=platform1")
        assert response.status_code == 200
        
        data = response.json()
        assert data["filters"]["platform"] == "platform1"
        # 验证所有结果都是指定平台
        for item in data["items"]:
            assert item["platform"] == "platform1"
    
    async def test_search_with_category_filter(self, client: AsyncClient, multiple_products):
        """测试分类筛选搜索"""
        response = await client.get("/api/v1/analytics/search?category=electronics")
        assert response.status_code == 200
        
        data = response.json()
        assert data["filters"]["category"] == "electronics"
        # 验证所有结果都是指定分类
        for item in data["items"]:
            assert item["category"] == "electronics"
    
    async def test_search_with_pagination(self, client: AsyncClient, multiple_products):
        """测试分页搜索"""
        response = await client.get("/api/v1/analytics/search?skip=2&limit=3")
        assert response.status_code == 200
        
        data = response.json()
        assert data["skip"] == 2
        assert data["limit"] == 3
        assert len(data["items"]) <= 3
    
    async def test_search_with_sorting(self, client: AsyncClient, multiple_products):
        """测试排序搜索"""
        response = await client.get("/api/v1/analytics/search?sort_by=title&sort_order=asc")
        assert response.status_code == 200
        
        data = response.json()
        # 验证结果按标题升序排列
        if len(data["items"]) > 1:
            titles = [item["title"] for item in data["items"]]
            assert titles == sorted(titles)


if __name__ == "__main__":
    pytest.main([__file__])
