"""
商品管理业务层测试

测试商品信息处理、分类归档、质量检查等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import (
    Product, ProductType, ProductStatus, DataQuality,
    ProductPrice, ProductImage, ProductSeller, ProductSpecs, ProductMetrics
)
from app.services.product_management.data_processor import ProductDataProcessor
from app.services.product_management.quality_checker import DataQualityChecker, QualityIssue
from app.services.product_management.product_classifier import ProductClassifier
from app.services.product_management.archive_manager import ArchiveManager, ArchiveReason
from app.services.task_middleware.config_manager import Platform


class TestProductDataProcessor:
    """商品数据处理器测试"""
    
    @pytest.fixture
    def processor(self):
        """创建数据处理器实例"""
        return ProductDataProcessor()
    
    @pytest.fixture
    def sample_1688_data(self):
        """示例1688数据"""
        return {
            "title": "苹果iPhone 15 Pro Max 手机壳 透明保护套",
            "price": "29.90",
            "original_price": "39.90",
            "images": [
                "https://img.1688.com/image1.jpg",
                "https://img.1688.com/image2.jpg"
            ],
            "seller_name": "深圳手机配件工厂",
            "min_order": "10",
            "brand": "苹果",
            "sales_count": "1580",
            "stock": "9999"
        }
    
    @pytest.mark.asyncio
    async def test_process_1688_data(self, processor, sample_1688_data):
        """测试处理1688数据"""
        url = "https://detail.1688.com/offer/123456.html"
        
        product = await processor.process_crawl_result(
            raw_data=sample_1688_data,
            url=url,
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.SUPPLIER
        )
        
        assert product.title == "苹果iPhone 15 Pro Max 手机壳 透明保护套"
        assert product.url == url
        assert product.platform == "1688"
        assert product.product_type == ProductType.SUPPLIER
        assert product.platform_product_id == "123456"
        
        # 检查价格信息
        assert product.price is not None
        assert product.price.current_price == 29.90
        assert product.price.original_price == 39.90
        assert product.price.currency == "CNY"
        assert product.price.min_order_quantity == 10
        
        # 检查图片信息
        assert len(product.images) == 2
        assert product.images[0].is_main is True
        assert product.images[1].is_main is False
        
        # 检查卖家信息
        assert product.seller is not None
        assert product.seller.name == "深圳手机配件工厂"
        
        # 检查规格信息
        assert product.specs is not None
        assert product.specs.brand == "苹果"
        
        # 检查指标信息
        assert product.metrics is not None
        assert product.metrics.sales_count == 1580
        assert product.metrics.stock_quantity == 9999
        
        # 检查数据质量
        assert product.data_quality_score > 0.8
        assert product.data_quality in [DataQuality.GOOD, DataQuality.EXCELLENT]
    
    @pytest.mark.asyncio
    async def test_parse_price_formats(self, processor):
        """测试价格解析格式"""
        test_cases = [
            ("29.90", 29.90),
            ("¥29.90", 29.90),
            ("$29.90", 29.90),
            ("1,299.00", 1299.00),
            ("1,299", 1299.0),
            ("29,90", 29.90),  # 欧式格式
            ("invalid", None),
            ("", None),
        ]
        
        for price_str, expected in test_cases:
            result = processor._parse_price(price_str)
            assert result == expected, f"解析 '{price_str}' 失败，期望 {expected}，得到 {result}"
    
    @pytest.mark.asyncio
    async def test_parse_sales_count(self, processor):
        """测试销量解析"""
        test_cases = [
            ("1580", 1580),
            ("1.5万", 15000),
            ("2.3w", 23000),
            ("500千", 500000),
            ("1.2k", 1200),
            ("已售1580件", 1580),
            ("月销1.5万+", 15000),
            ("invalid", None),
        ]
        
        for sales_str, expected in test_cases:
            result = processor._parse_sales_count(sales_str)
            assert result == expected, f"解析 '{sales_str}' 失败，期望 {expected}，得到 {result}"
    
    @pytest.mark.asyncio
    async def test_extract_product_id(self, processor):
        """测试商品ID提取"""
        test_cases = [
            ("https://detail.1688.com/offer/123456.html", Platform.ALIBABA_1688, "123456"),
            ("https://item.taobao.com/item.htm?id=789012", Platform.TAOBAO, "789012"),
            ("https://item.jd.com/345678.html", Platform.JD, "345678"),
            ("https://invalid.url", Platform.ALIBABA_1688, None),
        ]
        
        for url, platform, expected in test_cases:
            result = processor._extract_product_id(url, platform)
            assert result == expected, f"提取 '{url}' 的ID失败，期望 {expected}，得到 {result}"


class TestDataQualityChecker:
    """数据质量检查器测试"""
    
    @pytest.fixture
    def checker(self):
        """创建质量检查器实例"""
        return DataQualityChecker()
    
    @pytest.fixture
    def high_quality_product(self):
        """高质量商品"""
        return Product(
            url="https://detail.1688.com/offer/123456.html",
            title="苹果iPhone 15 Pro Max 256GB 深空黑色 5G手机",
            platform="1688",
            price=ProductPrice(
                current_price=8999.00,
                original_price=9999.00,
                currency="CNY"
            ),
            images=[
                ProductImage(url="https://img.1688.com/image1.jpg", is_main=True),
                ProductImage(url="https://img.1688.com/image2.jpg"),
                ProductImage(url="https://img.1688.com/image3.jpg"),
            ],
            seller=ProductSeller(name="苹果官方旗舰店", rating=4.9),
            specs=ProductSpecs(brand="苹果", model="iPhone 15 Pro Max"),
            metrics=ProductMetrics(sales_count=15000, rating=4.8, review_count=2500)
        )
    
    @pytest.fixture
    def low_quality_product(self):
        """低质量商品"""
        return Product(
            url="invalid-url",
            title="手机",  # 标题过短
            platform="",   # 平台缺失
            price=ProductPrice(current_price=-10.0),  # 价格无效
            images=[],     # 无图片
            seller=None,   # 无卖家信息
        )
    
    @pytest.mark.asyncio
    async def test_check_high_quality_product(self, checker, high_quality_product):
        """测试高质量商品检查"""
        report = await checker.check_product_quality(high_quality_product)
        
        assert report.overall_score > 0.8
        assert report.quality_level in [DataQuality.GOOD, DataQuality.EXCELLENT]
        assert report.passed_checks > report.total_checks // 2
        assert len(report.issues) <= 2  # 高质量商品应该问题很少
    
    @pytest.mark.asyncio
    async def test_check_low_quality_product(self, checker, low_quality_product):
        """测试低质量商品检查"""
        report = await checker.check_product_quality(low_quality_product)
        
        assert report.overall_score < 0.5
        assert report.quality_level in [DataQuality.POOR, DataQuality.BAD]
        assert len(report.issues) > 3  # 低质量商品应该有多个问题
        
        # 检查特定问题
        issue_types = [issue.type for issue in report.issues]
        assert "missing_data" in issue_types
        assert "invalid_data" in issue_types
    
    @pytest.mark.asyncio
    async def test_batch_quality_check(self, checker, high_quality_product, low_quality_product):
        """测试批量质量检查"""
        products = [high_quality_product, low_quality_product]
        reports = await checker.batch_check_quality(products)
        
        assert len(reports) == 2
        assert reports[0].overall_score > reports[1].overall_score
        assert reports[0].quality_level.value != reports[1].quality_level.value


class TestProductClassifier:
    """商品分类器测试"""
    
    @pytest.fixture
    def classifier(self):
        """创建商品分类器实例"""
        return ProductClassifier()
    
    @pytest.fixture
    def competitor_product(self):
        """竞品商品"""
        return Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="苹果iPhone 15 Pro Max 官方正品 256GB",
            platform="taobao",
            specs=ProductSpecs(brand="苹果"),
            seller=ProductSeller(name="苹果官方旗舰店"),
            price=ProductPrice(current_price=8999.00)
        )
    
    @pytest.fixture
    def supplier_product(self):
        """供货商商品"""
        return Product(
            url="https://detail.1688.com/offer/789012.html",
            title="手机壳批发 工厂直销 一件代发",
            platform="1688",
            seller=ProductSeller(name="深圳手机配件工厂"),
            price=ProductPrice(current_price=5.80, min_order_quantity=100)
        )
    
    @pytest.mark.asyncio
    async def test_classify_competitor_product(self, classifier, competitor_product):
        """测试竞品分类"""
        result = await classifier.classify_product(competitor_product)
        
        assert result.product_type == ProductType.COMPETITOR
        assert result.confidence > 0.5
        assert len(result.matched_rules) > 0
        assert any("competitor" in rule for rule in result.matched_rules)
        
        # 检查标签
        tag_names = [tag.name for tag in result.tags]
        assert "竞品" in tag_names
        assert "TAOBAO" in tag_names
    
    @pytest.mark.asyncio
    async def test_classify_supplier_product(self, classifier, supplier_product):
        """测试供货商商品分类"""
        result = await classifier.classify_product(supplier_product)
        
        assert result.product_type == ProductType.SUPPLIER
        assert result.confidence > 0.5
        assert len(result.matched_rules) > 0
        
        # 检查标签
        tag_names = [tag.name for tag in result.tags]
        assert "供货商" in tag_names
        assert "1688" in tag_names
        assert "低价位" in tag_names  # 5.80元应该是低价位
    
    @pytest.mark.asyncio
    async def test_batch_classify_products(self, classifier, competitor_product, supplier_product):
        """测试批量商品分类"""
        products = [competitor_product, supplier_product]
        results = await classifier.batch_classify_products(products)
        
        assert len(results) == 2
        assert results[0].product_type == ProductType.COMPETITOR
        assert results[1].product_type == ProductType.SUPPLIER


class TestArchiveManager:
    """归档管理器测试"""
    
    @pytest.fixture
    def archive_manager(self):
        """创建归档管理器实例"""
        return ArchiveManager()
    
    @pytest.fixture
    def active_product(self):
        """活跃商品"""
        return Product(
            url="https://example.com/product1",
            title="活跃商品",
            status=ProductStatus.ACTIVE,
            data_quality_score=0.8,
            last_crawled_at=datetime.now() - timedelta(days=1)
        )
    
    @pytest.fixture
    def inactive_product(self):
        """不活跃商品"""
        return Product(
            url="https://example.com/product2",
            title="不活跃商品",
            status=ProductStatus.INACTIVE,
            data_quality_score=0.6,
            last_crawled_at=datetime.now() - timedelta(days=35)
        )
    
    @pytest.fixture
    def poor_quality_product(self):
        """低质量商品"""
        return Product(
            url="https://example.com/product3",
            title="低质量商品",
            status=ProductStatus.ACTIVE,
            data_quality_score=0.2,
            created_at=datetime.now() - timedelta(days=10)
        )
    
    @pytest.mark.asyncio
    async def test_manual_archive_product(self, archive_manager, active_product):
        """测试手动归档商品"""
        success = await archive_manager.archive_product(
            product=active_product,
            reason=ArchiveReason.MANUAL,
            operator="test_user",
            notes="测试归档"
        )
        
        assert success is True
        assert active_product.status == ProductStatus.ARCHIVED
        assert len(active_product.change_history) > 0
        assert len(archive_manager.archive_operations) > 0
        
        # 检查变更记录
        last_change = active_product.change_history[-1]
        assert last_change.change_type == "status_change"
        assert last_change.new_value == ProductStatus.ARCHIVED.value
    
    @pytest.mark.asyncio
    async def test_restore_archived_product(self, archive_manager, active_product):
        """测试恢复归档商品"""
        # 先归档
        await archive_manager.archive_product(active_product, ArchiveReason.MANUAL)
        
        # 再恢复
        success = await archive_manager.restore_product(
            product=active_product,
            operator="test_user",
            notes="测试恢复"
        )
        
        assert success is True
        assert active_product.status == ProductStatus.ACTIVE
        assert len(archive_manager.archive_operations) == 2  # 归档 + 恢复
    
    @pytest.mark.asyncio
    async def test_auto_archive_products(self, archive_manager, active_product, 
                                       inactive_product, poor_quality_product):
        """测试自动归档商品"""
        products = [active_product, inactive_product, poor_quality_product]
        
        result = await archive_manager.auto_archive_products(products)
        
        assert result["total_checked"] == 3
        assert result["total_archived"] >= 1  # 至少归档一个商品
        assert "rule_results" in result
        
        # 检查不活跃商品是否被归档
        assert inactive_product.status == ProductStatus.ARCHIVED
        
        # 检查低质量商品是否被归档
        assert poor_quality_product.status == ProductStatus.ARCHIVED
    
    @pytest.mark.asyncio
    async def test_archive_statistics(self, archive_manager, active_product):
        """测试归档统计"""
        products = [active_product]
        
        # 执行一些归档操作
        await archive_manager.archive_product(active_product, ArchiveReason.MANUAL)
        
        stats = await archive_manager.get_archive_statistics(products)
        
        assert stats["total_products"] == 1
        assert "status_breakdown" in stats
        assert "archive_operations" in stats
        assert stats["archive_operations"]["total"] > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
