"""
翻译引擎

提供多语言翻译功能，支持多种翻译提供商
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import hashlib

from app.core.logging import get_logger
from .provider_manager import ProviderManager, TranslationProvider
from .quality_assessor import QualityAssessor, QualityScore
from .cache_manager import CacheManager

logger = get_logger(__name__)


class LanguageCode(Enum):
    """语言代码"""
    CHINESE = "zh"          # 中文
    ENGLISH = "en"          # 英文
    JAPANESE = "ja"         # 日文
    KOREAN = "ko"           # 韩文
    SPANISH = "es"          # 西班牙文
    FRENCH = "fr"           # 法文
    GERMAN = "de"           # 德文
    ITALIAN = "it"          # 意大利文
    PORTUGUESE = "pt"       # 葡萄牙文
    RUSSIAN = "ru"          # 俄文
    ARABIC = "ar"           # 阿拉伯文
    THAI = "th"             # 泰文
    VIETNAMESE = "vi"       # 越南文


class TextType(Enum):
    """文本类型"""
    PRODUCT_TITLE = "product_title"         # 商品标题
    PRODUCT_DESCRIPTION = "product_description"  # 商品描述
    CATEGORY_NAME = "category_name"         # 分类名称
    BRAND_NAME = "brand_name"               # 品牌名称
    SPECIFICATION = "specification"         # 规格说明
    MARKETING_COPY = "marketing_copy"       # 营销文案
    TECHNICAL_DOC = "technical_doc"         # 技术文档
    GENERAL_TEXT = "general_text"           # 通用文本


class TranslationStatus(Enum):
    """翻译状态"""
    PENDING = "pending"         # 等待中
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 失败
    CACHED = "cached"           # 缓存命中


@dataclass
class TranslationRequest:
    """翻译请求"""
    request_id: str
    text: str
    source_lang: LanguageCode
    target_lang: LanguageCode
    text_type: TextType = TextType.GENERAL_TEXT
    priority: int = 5  # 1-10，数字越小优先级越高
    context: Optional[str] = None
    custom_prompt: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class TranslationResult:
    """翻译结果"""
    request_id: str
    original_text: str
    translated_text: str
    source_lang: LanguageCode
    target_lang: LanguageCode
    text_type: TextType
    provider: str
    status: TranslationStatus
    quality_score: Optional[QualityScore] = None
    processing_time: float = 0.0
    cost: float = 0.0
    cached: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class TranslationEngine:
    """翻译引擎"""
    
    def __init__(self, provider_manager: ProviderManager, 
                 quality_assessor: QualityAssessor, cache_manager: CacheManager):
        self.provider_manager = provider_manager
        self.quality_assessor = quality_assessor
        self.cache_manager = cache_manager
        
        # 翻译结果存储
        self.translation_results: List[TranslationResult] = []
        
        # 翻译配置
        self.translation_config = {
            "max_concurrent": 10,           # 最大并发数
            "timeout_seconds": 30,          # 超时时间
            "retry_attempts": 3,            # 重试次数
            "cache_enabled": True,          # 启用缓存
            "quality_check_enabled": True,  # 启用质量检查
            "cost_limit_per_day": 100.0,   # 每日成本限制
            "auto_provider_selection": True # 自动选择提供商
        }
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "cache_hits": 0,
            "total_cost": 0.0,
            "total_processing_time": 0.0
        }
    
    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """
        翻译文本
        
        Args:
            request: 翻译请求
        
        Returns:
            TranslationResult: 翻译结果
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            logger.info(f"开始翻译: {request.request_id}")
            
            # 检查缓存
            if self.translation_config["cache_enabled"]:
                cached_result = await self._check_cache(request)
                if cached_result:
                    self.stats["cache_hits"] += 1
                    logger.info(f"缓存命中: {request.request_id}")
                    return cached_result
            
            # 选择翻译提供商
            provider = await self._select_provider(request)
            if not provider:
                raise Exception("没有可用的翻译提供商")
            
            # 执行翻译
            translated_text = await self._perform_translation(provider, request)
            
            # 质量评估
            quality_score = None
            if self.translation_config["quality_check_enabled"]:
                quality_score = await self.quality_assessor.assess_translation(
                    request.text, translated_text, request.source_lang, 
                    request.target_lang, request.text_type
                )
            
            # 计算成本和处理时间
            processing_time = time.time() - start_time
            cost = await self._calculate_cost(provider, request.text, translated_text)
            
            # 创建结果
            result = TranslationResult(
                request_id=request.request_id,
                original_text=request.text,
                translated_text=translated_text,
                source_lang=request.source_lang,
                target_lang=request.target_lang,
                text_type=request.text_type,
                provider=provider.name,
                status=TranslationStatus.COMPLETED,
                quality_score=quality_score,
                processing_time=processing_time,
                cost=cost,
                cached=False,
                completed_at=datetime.now()
            )
            
            # 保存到缓存
            if self.translation_config["cache_enabled"]:
                await self._save_to_cache(request, result)
            
            # 更新统计
            self.stats["successful_translations"] += 1
            self.stats["total_cost"] += cost
            self.stats["total_processing_time"] += processing_time
            
            # 保存结果
            self.translation_results.append(result)
            
            logger.info(f"翻译完成: {request.request_id}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"翻译失败: {request.request_id}, {e}")
            
            # 创建失败结果
            result = TranslationResult(
                request_id=request.request_id,
                original_text=request.text,
                translated_text="",
                source_lang=request.source_lang,
                target_lang=request.target_lang,
                text_type=request.text_type,
                provider="",
                status=TranslationStatus.FAILED,
                processing_time=processing_time,
                error_message=str(e),
                completed_at=datetime.now()
            )
            
            # 更新统计
            self.stats["failed_translations"] += 1
            self.stats["total_processing_time"] += processing_time
            
            # 保存结果
            self.translation_results.append(result)
            
            return result
    
    async def batch_translate(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """
        批量翻译
        
        Args:
            requests: 翻译请求列表
        
        Returns:
            List[TranslationResult]: 翻译结果列表
        """
        logger.info(f"开始批量翻译: {len(requests)} 个请求")
        
        # 按优先级排序
        sorted_requests = sorted(requests, key=lambda x: x.priority)
        
        # 分批处理，控制并发数
        results = []
        batch_size = self.translation_config["max_concurrent"]
        
        for i in range(0, len(sorted_requests), batch_size):
            batch = sorted_requests[i:i + batch_size]
            
            # 并发处理当前批次
            batch_tasks = [self.translate(request) for request in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"批量翻译异常: {result}")
                else:
                    results.append(result)
        
        logger.info(f"批量翻译完成: {len(results)} 个结果")
        return results
    
    async def _check_cache(self, request: TranslationRequest) -> Optional[TranslationResult]:
        """检查缓存"""
        try:
            cache_key = self._generate_cache_key(request)
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                # 创建缓存结果
                result = TranslationResult(
                    request_id=request.request_id,
                    original_text=request.text,
                    translated_text=cached_data["translated_text"],
                    source_lang=request.source_lang,
                    target_lang=request.target_lang,
                    text_type=request.text_type,
                    provider=cached_data.get("provider", "cache"),
                    status=TranslationStatus.CACHED,
                    processing_time=0.001,  # 缓存访问时间很短
                    cost=0.0,  # 缓存不产生成本
                    cached=True,
                    completed_at=datetime.now()
                )
                
                return result
            
            return None
            
        except Exception as e:
            logger.error(f"检查缓存失败: {e}")
            return None
    
    async def _save_to_cache(self, request: TranslationRequest, result: TranslationResult):
        """保存到缓存"""
        try:
            cache_key = self._generate_cache_key(request)
            cache_data = {
                "translated_text": result.translated_text,
                "provider": result.provider,
                "quality_score": result.quality_score.overall_score if result.quality_score else None,
                "cached_at": datetime.now().isoformat()
            }
            
            # 设置缓存过期时间（30天）
            await self.cache_manager.set(cache_key, cache_data, expire_seconds=30 * 24 * 3600)
            
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def _generate_cache_key(self, request: TranslationRequest) -> str:
        """生成缓存键"""
        # 使用文本内容、源语言、目标语言、文本类型生成唯一键
        content = f"{request.text}|{request.source_lang.value}|{request.target_lang.value}|{request.text_type.value}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    async def _select_provider(self, request: TranslationRequest) -> Optional[TranslationProvider]:
        """选择翻译提供商"""
        try:
            if self.translation_config["auto_provider_selection"]:
                # 自动选择最优提供商
                return await self.provider_manager.select_best_provider(
                    request.source_lang, request.target_lang, request.text_type
                )
            else:
                # 使用默认提供商
                return await self.provider_manager.get_default_provider()
                
        except Exception as e:
            logger.error(f"选择提供商失败: {e}")
            return None
    
    async def _perform_translation(self, provider: TranslationProvider, 
                                 request: TranslationRequest) -> str:
        """执行翻译"""
        try:
            # 构建翻译提示
            prompt = self._build_translation_prompt(request)
            
            # 调用提供商翻译
            translated_text = await provider.translate(
                text=request.text,
                source_lang=request.source_lang.value,
                target_lang=request.target_lang.value,
                prompt=prompt,
                timeout=self.translation_config["timeout_seconds"]
            )
            
            return translated_text
            
        except Exception as e:
            logger.error(f"执行翻译失败: {provider.name}, {e}")
            raise
    
    def _build_translation_prompt(self, request: TranslationRequest) -> str:
        """构建翻译提示"""
        base_prompt = f"请将以下{request.source_lang.value}文本翻译成{request.target_lang.value}："
        
        # 根据文本类型添加特定提示
        type_prompts = {
            TextType.PRODUCT_TITLE: "这是商品标题，请保持简洁有吸引力。",
            TextType.PRODUCT_DESCRIPTION: "这是商品描述，请保持详细准确。",
            TextType.CATEGORY_NAME: "这是分类名称，请使用标准术语。",
            TextType.BRAND_NAME: "这是品牌名称，请保持原有特色。",
            TextType.SPECIFICATION: "这是规格说明，请保持技术准确性。",
            TextType.MARKETING_COPY: "这是营销文案，请保持吸引力和说服力。",
            TextType.TECHNICAL_DOC: "这是技术文档，请保持专业性和准确性。"
        }
        
        type_prompt = type_prompts.get(request.text_type, "")
        
        # 添加上下文
        context_prompt = ""
        if request.context:
            context_prompt = f"上下文信息：{request.context}\n"
        
        # 组合提示
        full_prompt = f"{base_prompt}\n{type_prompt}\n{context_prompt}"
        
        # 使用自定义提示（如果有）
        if request.custom_prompt:
            full_prompt = request.custom_prompt
        
        return full_prompt
    
    async def _calculate_cost(self, provider: TranslationProvider, 
                            original_text: str, translated_text: str) -> float:
        """计算翻译成本"""
        try:
            # 基于字符数计算成本
            char_count = len(original_text) + len(translated_text)
            cost_per_char = provider.pricing.get("cost_per_char", 0.0001)
            
            return char_count * cost_per_char
            
        except Exception as e:
            logger.error(f"计算成本失败: {e}")
            return 0.0
    
    def get_translation_results(self, limit: Optional[int] = None) -> List[TranslationResult]:
        """获取翻译结果"""
        results = sorted(self.translation_results, key=lambda x: x.created_at, reverse=True)
        
        if limit:
            results = results[:limit]
        
        return results
    
    def get_translation_statistics(self) -> Dict[str, Any]:
        """获取翻译统计信息"""
        try:
            # 成功率
            success_rate = (self.stats["successful_translations"] / 
                          max(self.stats["total_requests"], 1) * 100)
            
            # 缓存命中率
            cache_hit_rate = (self.stats["cache_hits"] / 
                            max(self.stats["total_requests"], 1) * 100)
            
            # 平均处理时间
            avg_processing_time = (self.stats["total_processing_time"] / 
                                 max(self.stats["successful_translations"], 1))
            
            # 按语言对统计
            language_pairs = {}
            for result in self.translation_results:
                pair = f"{result.source_lang.value}->{result.target_lang.value}"
                language_pairs[pair] = language_pairs.get(pair, 0) + 1
            
            # 按文本类型统计
            text_types = {}
            for result in self.translation_results:
                text_type = result.text_type.value
                text_types[text_type] = text_types.get(text_type, 0) + 1
            
            # 按提供商统计
            providers = {}
            for result in self.translation_results:
                if result.provider:
                    providers[result.provider] = providers.get(result.provider, 0) + 1
            
            return {
                "total_requests": self.stats["total_requests"],
                "successful_translations": self.stats["successful_translations"],
                "failed_translations": self.stats["failed_translations"],
                "cache_hits": self.stats["cache_hits"],
                "success_rate": success_rate,
                "cache_hit_rate": cache_hit_rate,
                "total_cost": self.stats["total_cost"],
                "avg_processing_time": avg_processing_time,
                "language_pairs": language_pairs,
                "text_types": text_types,
                "providers": providers,
                "available_languages": [lang.value for lang in LanguageCode],
                "available_text_types": [text_type.value for text_type in TextType]
            }
            
        except Exception as e:
            logger.error(f"获取翻译统计失败: {e}")
            return {
                "total_requests": 0,
                "successful_translations": 0,
                "failed_translations": 0,
                "cache_hits": 0,
                "success_rate": 0,
                "cache_hit_rate": 0,
                "total_cost": 0.0,
                "avg_processing_time": 0.0,
                "language_pairs": {},
                "text_types": {},
                "providers": {},
                "available_languages": [lang.value for lang in LanguageCode],
                "available_text_types": [text_type.value for text_type in TextType]
            }
