"""
智能预警系统测试

测试预警引擎、规则管理、通知发送、预警处理等功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.models.product import Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
from app.services.alert_system.alert_engine import (
    AlertEngine, Alert, AlertLevel, AlertType, AlertCategory, AlertRule
)
from app.services.alert_system.rule_manager import RuleManager, RuleTemplate
from app.services.alert_system.notification_sender import (
    NotificationSender, NotificationChannel, NotificationConfig, NotificationTemplate
)
from app.services.alert_system.alert_processor import (
    AlertProcessor, ProcessingRule, ProcessingAction
)


class TestAlertEngine:
    """预警引擎测试"""
    
    @pytest.fixture
    def mock_comprehensive_analyzer(self):
        """模拟综合分析器"""
        analyzer = Mock()
        analyzer.generate_comprehensive_report = Mock(return_value=None)
        return analyzer
    
    @pytest.fixture
    def mock_cost_manager(self):
        """模拟成本管理器"""
        cost_manager = Mock()
        cost_manager.get_cost_alerts = Mock(return_value=[])
        return cost_manager
    
    @pytest.fixture
    def mock_profit_calculator(self):
        """模拟利润计算器"""
        calculator = Mock()
        calculator.calculate_profit = Mock(return_value=None)
        return calculator
    
    @pytest.fixture
    def alert_engine(self, mock_comprehensive_analyzer, mock_cost_manager, mock_profit_calculator):
        """创建预警引擎实例"""
        return AlertEngine(mock_comprehensive_analyzer, mock_cost_manager, mock_profit_calculator)
    
    @pytest.fixture
    def sample_products(self):
        """示例商品列表"""
        return [
            Product(
                url="https://item.taobao.com/item.htm?id=111111",
                title="iPhone 15 Pro 256GB",
                platform="taobao",
                product_type=ProductType.COMPETITOR,
                price=ProductPrice(current_price=7999.00),
                specs=ProductSpecs(brand="Apple"),
                metrics=ProductMetrics(sales_count=15000, rating=4.8)
            ),
            Product(
                url="https://item.jd.com/item.htm?id=222222",
                title="Samsung Galaxy S24 256GB",
                platform="jd",
                product_type=ProductType.SUPPLIER,
                price=ProductPrice(current_price=6999.00),
                specs=ProductSpecs(brand="Samsung"),
                metrics=ProductMetrics(sales_count=12000, rating=4.7)
            )
        ]
    
    @pytest.mark.asyncio
    async def test_process_products(self, alert_engine, sample_products):
        """测试处理商品预警"""
        alerts = await alert_engine.process_products(sample_products)
        
        # 应该生成一些预警
        assert isinstance(alerts, list)
        
        # 检查预警引擎的状态
        assert len(alert_engine.alerts) >= 0
        assert len(alert_engine.alert_rules) > 0
    
    @pytest.mark.asyncio
    async def test_price_anomaly_detection(self, alert_engine, sample_products):
        """测试价格异常检测"""
        # 使用有价格变化的商品
        product = sample_products[0]  # iPhone有价格下降
        
        alerts = await alert_engine.process_products([product])
        
        # 检查是否生成了价格异常预警
        price_alerts = [a for a in alerts if a.alert_type == AlertType.PRICE_ANOMALY]
        
        if price_alerts:
            alert = price_alerts[0]
            assert alert.product_id == product.id
            assert alert.alert_category == AlertCategory.COMPETITOR_ALERT
            assert alert.current_value == product.price.current_price
            assert alert.change_percentage < 0  # 价格下降
    
    @pytest.mark.asyncio
    async def test_sales_change_detection(self, alert_engine, sample_products):
        """测试销量变化检测"""
        # 使用有销量变化的商品
        product = sample_products[0]  # iPhone销量增长50%
        
        alerts = await alert_engine.process_products([product])
        
        # 检查是否生成了销量变化预警
        sales_alerts = [a for a in alerts if a.alert_type == AlertType.SALES_DECLINE]
        
        if sales_alerts:
            alert = sales_alerts[0]
            assert alert.product_id == product.id
            assert alert.current_value == product.metrics.sales_count
    
    @pytest.mark.asyncio
    async def test_inventory_shortage_detection(self, alert_engine, sample_products):
        """测试库存不足检测"""
        # 使用库存不足的商品
        product = sample_products[1]  # Samsung库存只有50件
        
        alerts = await alert_engine.process_products([product])
        
        # 检查是否生成了库存不足预警
        inventory_alerts = [a for a in alerts if a.alert_type == AlertType.INVENTORY_SHORTAGE]
        
        if inventory_alerts:
            alert = inventory_alerts[0]
            assert alert.product_id == product.id
            assert alert.alert_category == AlertCategory.INVENTORY_ALERT
    
    @pytest.mark.asyncio
    async def test_alert_management(self, alert_engine):
        """测试预警管理功能"""
        # 创建测试预警
        test_alert = Alert(
            alert_id="test_alert_001",
            rule_id="test_rule",
            product_id="test_product",
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            alert_level=AlertLevel.HIGH,
            title="测试预警",
            description="这是一个测试预警",
            current_value=100.0,
            threshold_value=110.0
        )
        
        alert_engine.alerts.append(test_alert)
        
        # 测试确认预警
        result = await alert_engine.acknowledge_alert(test_alert.alert_id)
        assert result is True
        
        # 测试解决预警
        result = await alert_engine.resolve_alert(test_alert.alert_id)
        assert result is True
        
        # 测试忽略预警
        test_alert.status = test_alert.status.__class__.ACTIVE  # 重置状态
        result = await alert_engine.dismiss_alert(test_alert.alert_id)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_alerts(self, alert_engine):
        """测试获取预警列表"""
        # 添加测试预警
        test_alerts = [
            Alert(
                alert_id=f"test_alert_{i:03d}",
                rule_id="test_rule",
                product_id=f"test_product_{i}",
                alert_type=AlertType.PRICE_ANOMALY,
                alert_category=AlertCategory.COMPETITOR_ALERT,
                alert_level=AlertLevel.HIGH if i % 2 == 0 else AlertLevel.MEDIUM,
                title=f"测试预警 {i}",
                description=f"这是第 {i} 个测试预警",
                current_value=100.0 + i,
                threshold_value=110.0
            )
            for i in range(5)
        ]
        
        alert_engine.alerts.extend(test_alerts)
        
        # 测试获取所有预警
        all_alerts = await alert_engine.get_alerts()
        assert len(all_alerts) >= 5
        
        # 测试按级别过滤
        high_alerts = await alert_engine.get_alerts(alert_level=AlertLevel.HIGH)
        assert all(a.alert_level == AlertLevel.HIGH for a in high_alerts)
        
        # 测试按商品ID过滤
        product_alerts = await alert_engine.get_alerts(product_id="test_product_0")
        assert all(a.product_id == "test_product_0" for a in product_alerts)
        
        # 测试数量限制
        limited_alerts = await alert_engine.get_alerts(limit=3)
        assert len(limited_alerts) <= 3
    
    @pytest.mark.asyncio
    async def test_alert_summary(self, alert_engine):
        """测试预警汇总"""
        # 添加不同类型的测试预警
        test_alerts = [
            Alert(
                alert_id=f"summary_test_{i:03d}",
                rule_id="test_rule",
                product_id=f"product_{i}",
                alert_type=AlertType.PRICE_ANOMALY if i % 2 == 0 else AlertType.SALES_DECLINE,
                alert_category=AlertCategory.COMPETITOR_ALERT,
                alert_level=AlertLevel.CRITICAL if i < 2 else AlertLevel.HIGH,
                title=f"汇总测试预警 {i}",
                description=f"汇总测试预警描述 {i}",
                current_value=100.0 + i,
                threshold_value=110.0
            )
            for i in range(6)
        ]
        
        alert_engine.alerts.extend(test_alerts)
        
        # 获取预警汇总
        summary = await alert_engine.get_alert_summary()
        
        assert summary.total_alerts >= 6
        assert summary.active_alerts >= 6
        assert summary.critical_alerts >= 2
        assert summary.high_alerts >= 4
        assert len(summary.recent_alerts) <= 10
        assert isinstance(summary.category_distribution, dict)
        assert isinstance(summary.type_distribution, dict)
        assert isinstance(summary.top_affected_products, list)
    
    def test_custom_rule_management(self, alert_engine):
        """测试自定义规则管理"""
        # 创建自定义规则
        custom_rule = AlertRule(
            rule_id="custom_test_rule",
            rule_name="自定义测试规则",
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            product_types=[ProductType.COMPETITOR],
            conditions={"price_drop_percentage": 0.15},
            alert_level=AlertLevel.HIGH,
            description="自定义测试规则"
        )
        
        # 添加规则
        result = alert_engine.add_custom_rule(custom_rule)
        assert result is True
        assert custom_rule.rule_id in alert_engine.alert_rules
        
        # 更新规则
        updates = {"alert_level": AlertLevel.CRITICAL, "enabled": False}
        result = alert_engine.update_rule(custom_rule.rule_id, updates)
        assert result is True
        
        updated_rule = alert_engine.alert_rules[custom_rule.rule_id]
        assert updated_rule.alert_level == AlertLevel.CRITICAL
        assert updated_rule.enabled is False
    
    def test_rule_statistics(self, alert_engine):
        """测试规则统计"""
        stats = alert_engine.get_rule_statistics()
        
        assert "total_rules" in stats
        assert "enabled_rules" in stats
        assert "disabled_rules" in stats
        assert "type_distribution" in stats
        assert "category_distribution" in stats
        assert "level_distribution" in stats
        assert "alert_types" in stats
        assert "alert_categories" in stats
        assert "alert_levels" in stats
        
        # 验证枚举值
        assert len(stats["alert_types"]) == len(AlertType)
        assert len(stats["alert_categories"]) == len(AlertCategory)
        assert len(stats["alert_levels"]) == len(AlertLevel)


class TestRuleManager:
    """规则管理器测试"""
    
    @pytest.fixture
    def rule_manager(self):
        """创建规则管理器实例"""
        return RuleManager()
    
    def test_rule_templates(self, rule_manager):
        """测试规则模板"""
        templates = rule_manager.get_templates()
        
        assert len(templates) > 0
        assert all(isinstance(t, RuleTemplate) for t in templates)
        
        # 测试获取特定模板
        template_id = templates[0].template_id
        template = rule_manager.get_template(template_id)
        assert template is not None
        assert template.template_id == template_id
    
    def test_create_rule_from_template(self, rule_manager):
        """测试从模板创建规则"""
        templates = rule_manager.get_templates()
        template = templates[0]
        
        # 从模板创建规则
        rule = rule_manager.create_rule_from_template(
            template_id=template.template_id,
            rule_name="从模板创建的测试规则",
            custom_conditions={"test_condition": 0.5},
            alert_level=AlertLevel.HIGH
        )
        
        assert rule is not None
        assert rule.rule_name == "从模板创建的测试规则"
        assert rule.alert_type == template.alert_type
        assert rule.alert_category == template.alert_category
        assert rule.alert_level == AlertLevel.HIGH
        assert rule.conditions["test_condition"] == 0.5
    
    def test_custom_rule_creation(self, rule_manager):
        """测试自定义规则创建"""
        rule_data = {
            "rule_name": "自定义规则测试",
            "alert_type": "price_anomaly",
            "alert_category": "competitor_alert",
            "conditions": {
                "price_drop_percentage": 0.20,
                "time_window_hours": 12
            },
            "product_types": ["competitor"],
            "alert_level": "high",
            "enabled": True,
            "description": "这是一个自定义规则测试"
        }
        
        rule = rule_manager.create_custom_rule(rule_data)
        
        assert rule is not None
        assert rule.rule_name == rule_data["rule_name"]
        assert rule.alert_type == AlertType.PRICE_ANOMALY
        assert rule.alert_category == AlertCategory.COMPETITOR_ALERT
        assert rule.alert_level == AlertLevel.HIGH
        assert rule.enabled is True
        assert rule.conditions == rule_data["conditions"]
    
    def test_rule_validation(self, rule_manager):
        """测试规则验证"""
        # 有效规则
        valid_rule = AlertRule(
            rule_id="valid_test_rule",
            rule_name="有效测试规则",
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            product_types=[ProductType.COMPETITOR],
            conditions={"price_drop_percentage": 0.10},
            alert_level=AlertLevel.HIGH
        )
        
        assert rule_manager.validate_rule(valid_rule) is True
        
        # 无效规则（缺少规则名称）
        invalid_rule = AlertRule(
            rule_id="invalid_test_rule",
            rule_name="",  # 空名称
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            product_types=[ProductType.COMPETITOR],
            conditions={"price_drop_percentage": 0.10},
            alert_level=AlertLevel.HIGH
        )
        
        assert rule_manager.validate_rule(invalid_rule) is False
    
    def test_rule_management(self, rule_manager):
        """测试规则管理功能"""
        # 创建测试规则
        rule_data = {
            "rule_name": "管理测试规则",
            "alert_type": "sales_decline",
            "alert_category": "competitor_alert",
            "conditions": {"sales_drop_percentage": 0.30},
            "product_types": ["competitor"],
            "alert_level": "medium"
        }
        
        rule = rule_manager.create_custom_rule(rule_data)
        assert rule is not None
        
        rule_id = rule.rule_id
        
        # 测试获取规则
        retrieved_rule = rule_manager.get_rule(rule_id)
        assert retrieved_rule is not None
        assert retrieved_rule.rule_id == rule_id
        
        # 测试更新规则
        updates = {
            "alert_level": "high",
            "enabled": False,
            "conditions": {"sales_drop_percentage": 0.25}
        }
        
        result = rule_manager.update_rule(rule_id, updates)
        assert result is True
        
        updated_rule = rule_manager.get_rule(rule_id)
        assert updated_rule.alert_level == AlertLevel.HIGH
        assert updated_rule.enabled is False
        assert updated_rule.conditions["sales_drop_percentage"] == 0.25
        
        # 测试删除规则
        result = rule_manager.delete_rule(rule_id)
        assert result is True
        
        deleted_rule = rule_manager.get_rule(rule_id)
        assert deleted_rule is None
    
    def test_rule_statistics(self, rule_manager):
        """测试规则统计"""
        # 添加一些测试规则
        for i in range(3):
            rule_data = {
                "rule_name": f"统计测试规则 {i}",
                "alert_type": "price_anomaly",
                "alert_category": "competitor_alert",
                "conditions": {"test_condition": i * 0.1},
                "product_types": ["competitor"],
                "alert_level": "medium"
            }
            rule_manager.create_custom_rule(rule_data)
        
        stats = rule_manager.get_rule_statistics()
        
        assert stats["total_rules"] >= 3
        assert stats["enabled_rules"] >= 3
        assert "type_distribution" in stats
        assert "category_distribution" in stats
        assert "level_distribution" in stats
        assert len(stats["available_types"]) == len(AlertType)
        assert len(stats["available_categories"]) == len(AlertCategory)
        assert len(stats["available_levels"]) == len(AlertLevel)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
