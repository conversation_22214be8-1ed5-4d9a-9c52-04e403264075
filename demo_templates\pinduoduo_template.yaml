created_at: '2025-08-20T02:19:14.021071'
description: 拼多多平台配置模板
name: pinduoduo_template
platform: !!python/object/apply:app.services.task_middleware.config_manager.Platform
- pdd
template_data:
  base_config:
    base_url: https://www.pinduoduo.com
    javascript_required: true
    name: 拼多多
    proxy_required: true
    rate_limit:
      concurrent_requests: 1
      delay_between_requests: 4.0
      requests_per_minute: 15
    selectors:
      price: .goods-price
      sales_count: .goods-sales
      title: .goods-title
  enabled: true
  platform: pdd
  product_types:
    competitor:
      monitoring_frequency: 3600
      priority_boost: 1
    other:
      monitoring_frequency: 7200
version: '1.0'
