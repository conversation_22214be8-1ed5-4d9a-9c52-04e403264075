"""
认证相关API端点
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from app.auth import AuthManager, AuthConfig
from app.core.logging import get_logger

logger = get_logger(__name__)

# 创建路由器
router = APIRouter()

# HTTP Bearer认证
security = HTTPBearer()

# 全局认证管理器实例
auth_manager = AuthManager(AuthConfig())

# 初始化默认管理员用户
def init_default_admin():
    """初始化默认管理员用户"""
    try:
        # 检查是否已存在管理员用户
        admin_user = auth_manager.get_user_by_username("admin")
        if not admin_user:
            success, message, user = auth_manager.register_user(
                username="admin",
                email="<EMAIL>",
                password="admin123",
                full_name="系统管理员"
            )
            if success:
                logger.info("默认管理员用户创建成功")
            else:
                logger.error(f"创建默认管理员用户失败: {message}")
    except Exception as e:
        logger.error(f"初始化默认管理员用户失败: {e}")

# 初始化默认用户
init_default_admin()

# 请求/响应模型
class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: dict = Field(..., description="用户信息")

class RegisterRequest(BaseModel):
    """注册请求"""
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    password: str = Field(..., description="密码")
    full_name: Optional[str] = Field(None, description="全名")

class UserResponse(BaseModel):
    """用户信息响应"""
    user_id: str
    username: str
    email: str
    full_name: Optional[str]
    role: str
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime]

class TokenRefreshRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")

class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., description="新密码")

# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        session = auth_manager.verify_token(credentials.credentials)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user = auth_manager.get_user(session.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
    except Exception as e:
        logger.error(f"获取当前用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )

# API端点
@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(request: LoginRequest):
    """用户登录"""
    try:
        success, message, session = auth_manager.authenticate_user(
            username=request.username,
            password=request.password,
            ip_address="127.0.0.1",  # 在实际应用中应该获取真实IP
            user_agent="Web Browser"
        )
        
        if not success or not session:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=message,
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user = auth_manager.get_user(session.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return LoginResponse(
            access_token=session.access_token,
            refresh_token=session.refresh_token,
            token_type="bearer",
            expires_in=3600,  # 1小时
            user={
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role.value,
                "is_active": user.is_active
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录服务异常"
        )

@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(request: RegisterRequest):
    """用户注册"""
    try:
        success, message, user = auth_manager.register_user(
            username=request.username,
            email=request.email,
            password=request.password,
            full_name=request.full_name
        )
        
        if not success or not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        return UserResponse(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role.value,
            is_active=user.is_active,
            created_at=user.created_at,
            last_login=user.last_login
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册服务异常"
        )

@router.post("/logout", summary="用户登出")
async def logout(current_user = Depends(get_current_user)):
    """用户登出"""
    try:
        # 这里需要从请求中获取session_id，简化处理
        return {"message": "登出成功"}
        
    except Exception as e:
        logger.error(f"登出失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出服务异常"
        )

@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(current_user = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        return UserResponse(
            user_id=current_user.user_id,
            username=current_user.username,
            email=current_user.email,
            full_name=current_user.full_name,
            role=current_user.role.value,
            is_active=current_user.is_active,
            created_at=current_user.created_at,
            last_login=current_user.last_login_at
        )
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )

@router.post("/refresh", response_model=dict, summary="刷新访问令牌")
async def refresh_token(request: TokenRefreshRequest):
    """刷新访问令牌"""
    try:
        # 简化实现，实际应该验证refresh_token并生成新的access_token
        return {
            "access_token": "new_access_token",
            "token_type": "bearer",
            "expires_in": 3600
        }
        
    except Exception as e:
        logger.error(f"刷新令牌失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )

@router.get("/verify", summary="验证令牌")
async def verify_token(current_user = Depends(get_current_user)):
    """验证令牌有效性"""
    return {
        "valid": True,
        "user": {
            "user_id": current_user.user_id,
            "username": current_user.username,
            "role": current_user.role.value
        }
    }
