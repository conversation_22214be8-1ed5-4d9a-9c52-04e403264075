"""
商品数据模型

定义商品信息的数据结构和状态管理
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import uuid


class ProductType(Enum):
    """商品类型"""
    COMPETITOR = "competitor"      # 竞品
    SUPPLIER = "supplier"         # 供货商商品
    OTHER = "other"              # 其他商品
    UNKNOWN = "unknown"          # 未分类


class ProductStatus(Enum):
    """商品状态"""
    NEW = "new"                  # 新增
    ACTIVE = "active"            # 监控中
    PAUSED = "paused"           # 暂停监控
    INACTIVE = "inactive"        # 停用
    ARCHIVED = "archived"        # 已归档
    DELETED = "deleted"          # 已删除


class DataQuality(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"      # 优秀 (0.9-1.0)
    GOOD = "good"               # 良好 (0.7-0.9)
    FAIR = "fair"               # 一般 (0.5-0.7)
    POOR = "poor"               # 较差 (0.3-0.5)
    BAD = "bad"                 # 很差 (0.0-0.3)


@dataclass
class ProductCategory:
    """商品分类"""
    id: str
    name: str
    parent_id: Optional[str] = None
    level: int = 1
    description: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ProductTag:
    """商品标签"""
    id: str
    name: str
    color: str = "#007bff"
    description: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ProductImage:
    """商品图片"""
    url: str
    alt_text: Optional[str] = None
    is_main: bool = False
    width: Optional[int] = None
    height: Optional[int] = None
    file_size: Optional[int] = None


@dataclass
class ProductPrice:
    """商品价格信息"""
    current_price: float
    original_price: Optional[float] = None
    currency: str = "CNY"
    discount_rate: Optional[float] = None
    price_unit: Optional[str] = None  # 如：/件、/套、/斤
    min_order_quantity: Optional[int] = None
    wholesale_price: Optional[float] = None


@dataclass
class ProductSeller:
    """商品卖家信息"""
    name: str
    shop_url: Optional[str] = None
    rating: Optional[float] = None
    location: Optional[str] = None
    seller_type: Optional[str] = None  # 如：工厂、贸易商、品牌方
    contact_info: Optional[Dict[str, str]] = None


@dataclass
class ProductSpecs:
    """商品规格参数"""
    brand: Optional[str] = None
    model: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    material: Optional[str] = None
    weight: Optional[str] = None
    dimensions: Optional[str] = None
    custom_specs: Optional[Dict[str, str]] = None


@dataclass
class ProductMetrics:
    """商品指标数据"""
    sales_count: Optional[int] = None
    review_count: Optional[int] = None
    rating: Optional[float] = None
    stock_quantity: Optional[int] = None
    view_count: Optional[int] = None
    favorite_count: Optional[int] = None


@dataclass
class ProductChangeRecord:
    """商品变更记录"""
    id: str
    product_id: str
    change_type: str  # price_change, stock_change, status_change, etc.
    old_value: Any
    new_value: Any
    change_reason: Optional[str] = None
    changed_by: Optional[str] = None
    changed_at: datetime = field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class Product:
    """商品主体模型"""
    # 基础信息
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    url: str = ""
    title: str = ""
    description: Optional[str] = None
    
    # 分类信息
    product_type: ProductType = ProductType.UNKNOWN
    categories: List[ProductCategory] = field(default_factory=list)
    tags: List[ProductTag] = field(default_factory=list)
    
    # 价格信息
    price: Optional[ProductPrice] = None
    
    # 图片信息
    images: List[ProductImage] = field(default_factory=list)
    
    # 卖家信息
    seller: Optional[ProductSeller] = None
    
    # 规格参数
    specs: Optional[ProductSpecs] = None
    
    # 指标数据
    metrics: Optional[ProductMetrics] = None
    
    # 平台信息
    platform: str = ""
    platform_product_id: Optional[str] = None
    
    # 状态管理
    status: ProductStatus = ProductStatus.NEW
    data_quality: DataQuality = DataQuality.FAIR
    data_quality_score: float = 0.5
    
    # 监控配置
    monitoring_enabled: bool = True
    monitoring_frequency: int = 3600  # 秒
    last_crawled_at: Optional[datetime] = None
    next_crawl_at: Optional[datetime] = None
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 版本管理
    version: int = 1
    change_history: List[ProductChangeRecord] = field(default_factory=list)
    
    # 原始数据
    raw_data: Optional[Dict[str, Any]] = None
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def increment_version(self):
        """增加版本号"""
        self.version += 1
        self.update_timestamp()
    
    def add_change_record(self, change_type: str, old_value: Any,
                         new_value: Any, reason: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None):
        """添加变更记录"""
        record = ProductChangeRecord(
            id=str(uuid.uuid4()),
            product_id=self.id,
            change_type=change_type,
            old_value=old_value,
            new_value=new_value,
            change_reason=reason,
            metadata=metadata
        )
        self.change_history.append(record)
        self.increment_version()
    
    def get_main_image(self) -> Optional[ProductImage]:
        """获取主图"""
        for image in self.images:
            if image.is_main:
                return image
        return self.images[0] if self.images else None
    
    def get_category_path(self) -> List[str]:
        """获取分类路径"""
        if not self.categories:
            return []
        
        # 简化实现，返回分类名称列表
        return [cat.name for cat in sorted(self.categories, key=lambda x: x.level)]
    
    def calculate_discount_rate(self) -> Optional[float]:
        """计算折扣率"""
        if not self.price or not self.price.original_price:
            return None
        
        if self.price.original_price <= 0:
            return None
        
        discount = (self.price.original_price - self.price.current_price) / self.price.original_price
        return round(discount * 100, 2)
    
    def is_in_stock(self) -> bool:
        """是否有库存"""
        if not self.metrics or self.metrics.stock_quantity is None:
            return True  # 默认有库存
        
        return self.metrics.stock_quantity > 0
    
    def get_price_display(self) -> str:
        """获取价格显示文本"""
        if not self.price:
            return "价格未知"
        
        price_text = f"¥{self.price.current_price:.2f}"
        
        if self.price.price_unit:
            price_text += f"/{self.price.price_unit}"
        
        if self.price.min_order_quantity and self.price.min_order_quantity > 1:
            price_text += f" (起订{self.price.min_order_quantity}件)"
        
        return price_text
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "url": self.url,
            "title": self.title,
            "description": self.description,
            "product_type": self.product_type.value,
            "platform": self.platform,
            "status": self.status.value,
            "data_quality": self.data_quality.value,
            "data_quality_score": self.data_quality_score,
            "price": {
                "current_price": self.price.current_price,
                "currency": self.price.currency,
                "discount_rate": self.calculate_discount_rate()
            } if self.price else None,
            "main_image": self.get_main_image().url if self.get_main_image() else None,
            "seller_name": self.seller.name if self.seller else None,
            "categories": [cat.name for cat in self.categories],
            "tags": [tag.name for tag in self.tags],
            "monitoring_enabled": self.monitoring_enabled,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version
        }
