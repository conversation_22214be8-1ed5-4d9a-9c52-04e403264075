#!/bin/bash

# Moniit 系统管理脚本
# 统一的运维管理入口

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示帮助信息
show_help() {
    echo "Moniit 系统管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "可用命令:"
    echo ""
    echo "部署管理:"
    echo "  deploy [选项]        部署系统"
    echo "  start               启动所有服务"
    echo "  stop                停止所有服务"
    echo "  restart             重启所有服务"
    echo "  status              显示服务状态"
    echo ""
    echo "备份恢复:"
    echo "  backup [选项]        备份系统数据"
    echo "  restore <备份路径>   恢复系统数据"
    echo "  list-backups        列出所有备份"
    echo ""
    echo "监控检查:"
    echo "  monitor [选项]       系统监控"
    echo "  health              健康检查"
    echo "  alert <消息>        发送告警"
    echo ""
    echo "维护工具:"
    echo "  cleanup [选项]       清理系统"
    echo "  optimize [选项]      优化系统"
    echo "  logs [服务名]        查看日志"
    echo ""
    echo "其他:"
    echo "  help                显示此帮助信息"
    echo "  version             显示版本信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy -e dev     部署开发环境"
    echo "  $0 backup -a         完整备份"
    echo "  $0 health            健康检查"
    echo "  $0 cleanup -a        清理所有"
    echo ""
    echo "获取特定命令的帮助:"
    echo "  $0 deploy --help"
    echo "  $0 backup --help"
}

# 显示版本信息
show_version() {
    echo "Moniit 系统管理脚本 v1.0.0"
    echo "Copyright (c) 2023 Moniit Team"
}

# 检查脚本是否存在
check_script() {
    local script_name=$1
    local script_path="${SCRIPT_DIR}/${script_name}"
    
    if [ ! -f "$script_path" ]; then
        log_error "脚本不存在: $script_path"
        return 1
    fi
    
    if [ ! -x "$script_path" ]; then
        log_error "脚本不可执行: $script_path"
        return 1
    fi
    
    return 0
}

# 执行脚本
run_script() {
    local script_name=$1
    shift
    local script_path="${SCRIPT_DIR}/${script_name}"
    
    if check_script "$script_name"; then
        log_info "执行脚本: $script_name"
        "$script_path" "$@"
    else
        return 1
    fi
}

# 部署管理
cmd_deploy() {
    run_script "deploy.sh" "$@"
}

# 启动服务
cmd_start() {
    log_info "启动所有服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose up -d
        log_success "服务启动完成"
        cmd_status
    else
        log_error "docker-compose.yml 文件不存在"
        return 1
    fi
}

# 停止服务
cmd_stop() {
    log_info "停止所有服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_success "服务停止完成"
    else
        log_error "docker-compose.yml 文件不存在"
        return 1
    fi
}

# 重启服务
cmd_restart() {
    log_info "重启所有服务..."
    cmd_stop
    sleep 5
    cmd_start
}

# 显示服务状态
cmd_status() {
    log_info "服务状态:"
    echo ""
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        log_error "docker-compose 命令不存在"
        return 1
    fi
    
    echo ""
    log_info "系统资源使用:"
    echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' || echo "N/A")"
    echo "内存: $(free -h | grep Mem | awk '{print $3"/"$2}' || echo "N/A")"
    echo "磁盘: $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5")"}' || echo "N/A")"
}

# 备份管理
cmd_backup() {
    run_script "backup.sh" "$@"
}

# 恢复管理
cmd_restore() {
    run_script "restore.sh" "$@"
}

# 列出备份
cmd_list_backups() {
    run_script "backup.sh" --list
}

# 系统监控
cmd_monitor() {
    if check_script "monitor.sh"; then
        run_script "monitor.sh" "$@"
    else
        log_warning "monitor.sh 不存在，使用健康检查替代"
        cmd_health
    fi
}

# 健康检查
cmd_health() {
    run_script "health-check.sh" "$@"
}

# 发送告警
cmd_alert() {
    run_script "alert.sh" "$@"
}

# 系统清理
cmd_cleanup() {
    run_script "cleanup.sh" "$@"
}

# 系统优化
cmd_optimize() {
    run_script "optimize.sh" "$@"
}

# 查看日志
cmd_logs() {
    local service=$1
    
    if [ -n "$service" ]; then
        log_info "查看服务日志: $service"
        docker-compose logs -f "$service"
    else
        log_info "查看所有服务日志"
        docker-compose logs -f
    fi
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    local command=$1
    shift
    
    # 执行命令
    case $command in
        deploy)
            cmd_deploy "$@"
            ;;
        start)
            cmd_start "$@"
            ;;
        stop)
            cmd_stop "$@"
            ;;
        restart)
            cmd_restart "$@"
            ;;
        status)
            cmd_status "$@"
            ;;
        backup)
            cmd_backup "$@"
            ;;
        restore)
            cmd_restore "$@"
            ;;
        list-backups)
            cmd_list_backups "$@"
            ;;
        monitor)
            cmd_monitor "$@"
            ;;
        health)
            cmd_health "$@"
            ;;
        alert)
            cmd_alert "$@"
            ;;
        cleanup)
            cmd_cleanup "$@"
            ;;
        optimize)
            cmd_optimize "$@"
            ;;
        logs)
            cmd_logs "$@"
            ;;
        help|--help|-h)
            show_help
            ;;
        version|--version|-v)
            show_version
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
