"""
标签管理器

负责商品标签的创建、管理和自动标记
"""

import asyncio
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductTag, ProductType

logger = get_logger(__name__)


class TagType(Enum):
    """标签类型"""
    SYSTEM = "system"           # 系统标签
    AUTO = "auto"              # 自动标签
    MANUAL = "manual"          # 手动标签
    CUSTOM = "custom"          # 自定义标签


@dataclass
class TagRule:
    """标签规则"""
    id: str
    name: str
    tag_id: str
    conditions: Dict[str, Any]
    priority: int = 1
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)


class TagManager:
    """标签管理器"""
    
    def __init__(self):
        self.tags: Dict[str, ProductTag] = {}
        self.tag_rules: List[TagRule] = []
        self._initialize_default_tags()
        self._initialize_default_rules()
    
    def _initialize_default_tags(self):
        """初始化默认标签"""
        default_tags = [
            # 商品类型标签
            ProductTag(
                id="tag_competitor",
                name="竞品",
                color="#dc3545",
                description="竞争对手商品"
            ),
            ProductTag(
                id="tag_supplier",
                name="供货商",
                color="#28a745",
                description="供货商商品"
            ),
            ProductTag(
                id="tag_other",
                name="其他",
                color="#6c757d",
                description="其他类型商品"
            ),
            
            # 平台标签
            ProductTag(
                id="tag_1688",
                name="1688",
                color="#ff6900",
                description="1688平台商品"
            ),
            ProductTag(
                id="tag_taobao",
                name="淘宝",
                color="#ff4400",
                description="淘宝平台商品"
            ),
            ProductTag(
                id="tag_jd",
                name="京东",
                color="#e1251b",
                description="京东平台商品"
            ),
            ProductTag(
                id="tag_pdd",
                name="拼多多",
                color="#e02e24",
                description="拼多多平台商品"
            ),
            
            # 价格标签
            ProductTag(
                id="tag_price_low",
                name="低价位",
                color="#28a745",
                description="价格低于50元"
            ),
            ProductTag(
                id="tag_price_medium",
                name="中价位",
                color="#ffc107",
                description="价格50-200元"
            ),
            ProductTag(
                id="tag_price_high",
                name="高价位",
                color="#fd7e14",
                description="价格200-1000元"
            ),
            ProductTag(
                id="tag_price_premium",
                name="奢侈品",
                color="#6f42c1",
                description="价格超过1000元"
            ),
            
            # 质量标签
            ProductTag(
                id="tag_quality_excellent",
                name="优质",
                color="#198754",
                description="数据质量优秀"
            ),
            ProductTag(
                id="tag_quality_good",
                name="良好",
                color="#20c997",
                description="数据质量良好"
            ),
            ProductTag(
                id="tag_quality_poor",
                name="待完善",
                color="#fd7e14",
                description="数据质量需要完善"
            ),
            
            # 热度标签
            ProductTag(
                id="tag_hot",
                name="热销",
                color="#dc3545",
                description="销量超过1万"
            ),
            ProductTag(
                id="tag_trending",
                name="趋势",
                color="#0d6efd",
                description="近期热门商品"
            ),
            ProductTag(
                id="tag_new",
                name="新品",
                color="#6610f2",
                description="新上架商品"
            ),
            
            # 品牌标签
            ProductTag(
                id="tag_brand_apple",
                name="苹果",
                color="#000000",
                description="苹果品牌商品"
            ),
            ProductTag(
                id="tag_brand_huawei",
                name="华为",
                color="#ff0000",
                description="华为品牌商品"
            ),
            ProductTag(
                id="tag_brand_xiaomi",
                name="小米",
                color="#ff6900",
                description="小米品牌商品"
            ),
            
            # 特殊标签
            ProductTag(
                id="tag_discount",
                name="折扣",
                color="#e83e8c",
                description="有折扣的商品"
            ),
            ProductTag(
                id="tag_stock_low",
                name="库存紧张",
                color="#fd7e14",
                description="库存不足100件"
            ),
            ProductTag(
                id="tag_no_image",
                name="缺少图片",
                color="#6c757d",
                description="没有商品图片"
            ),
        ]
        
        for tag in default_tags:
            self.tags[tag.id] = tag
    
    def _initialize_default_rules(self):
        """初始化默认标签规则"""
        default_rules = [
            # 商品类型规则
            TagRule(
                id="rule_competitor_type",
                name="竞品类型标签",
                tag_id="tag_competitor",
                conditions={"product_type": "competitor"},
                priority=10
            ),
            TagRule(
                id="rule_supplier_type",
                name="供货商类型标签",
                tag_id="tag_supplier",
                conditions={"product_type": "supplier"},
                priority=10
            ),
            
            # 平台规则
            TagRule(
                id="rule_1688_platform",
                name="1688平台标签",
                tag_id="tag_1688",
                conditions={"platform": "1688"},
                priority=9
            ),
            TagRule(
                id="rule_taobao_platform",
                name="淘宝平台标签",
                tag_id="tag_taobao",
                conditions={"platform": "taobao"},
                priority=9
            ),
            TagRule(
                id="rule_jd_platform",
                name="京东平台标签",
                tag_id="tag_jd",
                conditions={"platform": "jd"},
                priority=9
            ),
            TagRule(
                id="rule_pdd_platform",
                name="拼多多平台标签",
                tag_id="tag_pdd",
                conditions={"platform": "pdd"},
                priority=9
            ),
            
            # 价格规则
            TagRule(
                id="rule_price_low",
                name="低价位标签",
                tag_id="tag_price_low",
                conditions={"price_max": 50},
                priority=8
            ),
            TagRule(
                id="rule_price_medium",
                name="中价位标签",
                tag_id="tag_price_medium",
                conditions={"price_min": 50, "price_max": 200},
                priority=8
            ),
            TagRule(
                id="rule_price_high",
                name="高价位标签",
                tag_id="tag_price_high",
                conditions={"price_min": 200, "price_max": 1000},
                priority=8
            ),
            TagRule(
                id="rule_price_premium",
                name="奢侈品标签",
                tag_id="tag_price_premium",
                conditions={"price_min": 1000},
                priority=8
            ),
            
            # 质量规则
            TagRule(
                id="rule_quality_excellent",
                name="优质标签",
                tag_id="tag_quality_excellent",
                conditions={"quality_score_min": 0.9},
                priority=7
            ),
            TagRule(
                id="rule_quality_good",
                name="良好标签",
                tag_id="tag_quality_good",
                conditions={"quality_score_min": 0.7, "quality_score_max": 0.9},
                priority=7
            ),
            TagRule(
                id="rule_quality_poor",
                name="待完善标签",
                tag_id="tag_quality_poor",
                conditions={"quality_score_max": 0.5},
                priority=7
            ),
            
            # 热度规则
            TagRule(
                id="rule_hot_sales",
                name="热销标签",
                tag_id="tag_hot",
                conditions={"sales_count_min": 10000},
                priority=6
            ),
            TagRule(
                id="rule_new_product",
                name="新品标签",
                tag_id="tag_new",
                conditions={"days_since_created_max": 7},
                priority=6
            ),
            
            # 品牌规则
            TagRule(
                id="rule_brand_apple",
                name="苹果品牌标签",
                tag_id="tag_brand_apple",
                conditions={"brand_contains": ["苹果", "apple", "iphone", "ipad"]},
                priority=5
            ),
            TagRule(
                id="rule_brand_huawei",
                name="华为品牌标签",
                tag_id="tag_brand_huawei",
                conditions={"brand_contains": ["华为", "huawei", "荣耀", "honor"]},
                priority=5
            ),
            TagRule(
                id="rule_brand_xiaomi",
                name="小米品牌标签",
                tag_id="tag_brand_xiaomi",
                conditions={"brand_contains": ["小米", "xiaomi", "红米", "redmi"]},
                priority=5
            ),
            
            # 特殊情况规则
            TagRule(
                id="rule_discount",
                name="折扣标签",
                tag_id="tag_discount",
                conditions={"has_discount": True},
                priority=4
            ),
            TagRule(
                id="rule_stock_low",
                name="库存紧张标签",
                tag_id="tag_stock_low",
                conditions={"stock_max": 100},
                priority=4
            ),
            TagRule(
                id="rule_no_image",
                name="缺少图片标签",
                tag_id="tag_no_image",
                conditions={"image_count": 0},
                priority=4
            ),
        ]
        
        self.tag_rules = default_rules
    
    async def auto_tag_product(self, product: Product) -> List[ProductTag]:
        """
        自动为商品添加标签
        
        Args:
            product: 商品对象
        
        Returns:
            List[ProductTag]: 标签列表
        """
        try:
            logger.info(f"开始自动标记商品: {product.id}")
            
            tags = []
            
            # 应用所有启用的标签规则
            for rule in self.tag_rules:
                if not rule.enabled:
                    continue
                
                if await self._product_matches_tag_rule(product, rule):
                    if rule.tag_id in self.tags:
                        tags.append(self.tags[rule.tag_id])
            
            # 去重
            unique_tags = self._deduplicate_tags(tags)
            
            logger.info(f"商品自动标记完成: {product.id}, 标签数: {len(unique_tags)}")
            return unique_tags
            
        except Exception as e:
            logger.error(f"自动标记商品失败: {e}")
            return []
    
    async def _product_matches_tag_rule(self, product: Product, rule: TagRule) -> bool:
        """检查商品是否匹配标签规则"""
        try:
            conditions = rule.conditions
            
            # 检查商品类型
            if "product_type" in conditions:
                if product.product_type.value != conditions["product_type"]:
                    return False
            
            # 检查平台
            if "platform" in conditions:
                if product.platform != conditions["platform"]:
                    return False
            
            # 检查价格范围
            if product.price and product.price.current_price:
                price = product.price.current_price
                
                if "price_min" in conditions:
                    if price < conditions["price_min"]:
                        return False
                
                if "price_max" in conditions:
                    if price > conditions["price_max"]:
                        return False
            else:
                # 如果没有价格信息，价格相关规则不匹配
                if any(key in conditions for key in ["price_min", "price_max"]):
                    return False
            
            # 检查质量分数
            if "quality_score_min" in conditions:
                if product.data_quality_score < conditions["quality_score_min"]:
                    return False
            
            if "quality_score_max" in conditions:
                if product.data_quality_score > conditions["quality_score_max"]:
                    return False
            
            # 检查销量
            if "sales_count_min" in conditions:
                if not product.metrics or not product.metrics.sales_count:
                    return False
                if product.metrics.sales_count < conditions["sales_count_min"]:
                    return False
            
            # 检查创建时间
            if "days_since_created_max" in conditions:
                days_since_created = (datetime.now() - product.created_at).days
                if days_since_created > conditions["days_since_created_max"]:
                    return False
            
            # 检查品牌关键词
            if "brand_contains" in conditions:
                keywords = conditions["brand_contains"]
                brand_text = ""
                
                if product.specs and product.specs.brand:
                    brand_text += product.specs.brand.lower()
                
                brand_text += " " + product.title.lower()
                
                if not any(kw.lower() in brand_text for kw in keywords):
                    return False
            
            # 检查折扣
            if "has_discount" in conditions:
                has_discount = (product.price and 
                              product.price.original_price and 
                              product.price.current_price and
                              product.price.original_price > product.price.current_price)
                
                if conditions["has_discount"] != has_discount:
                    return False
            
            # 检查库存
            if "stock_max" in conditions:
                if not product.metrics or not product.metrics.stock_quantity:
                    return False
                if product.metrics.stock_quantity > conditions["stock_max"]:
                    return False
            
            # 检查图片数量
            if "image_count" in conditions:
                image_count = len(product.images)
                if image_count != conditions["image_count"]:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查标签规则失败: {e}")
            return False
    
    def _deduplicate_tags(self, tags: List[ProductTag]) -> List[ProductTag]:
        """去重标签列表"""
        seen_ids = set()
        unique_tags = []
        
        for tag in tags:
            if tag.id not in seen_ids:
                seen_ids.add(tag.id)
                unique_tags.append(tag)
        
        return unique_tags
    
    async def create_tag(self, tag: ProductTag) -> bool:
        """创建新标签"""
        try:
            if tag.id in self.tags:
                logger.warning(f"标签已存在: {tag.id}")
                return False
            
            self.tags[tag.id] = tag
            logger.info(f"创建标签成功: {tag.id} - {tag.name}")
            return True
            
        except Exception as e:
            logger.error(f"创建标签失败: {e}")
            return False
    
    async def update_tag(self, tag_id: str, updates: Dict[str, Any]) -> bool:
        """更新标签信息"""
        try:
            if tag_id not in self.tags:
                logger.warning(f"标签不存在: {tag_id}")
                return False
            
            tag = self.tags[tag_id]
            
            if "name" in updates:
                tag.name = updates["name"]
            if "color" in updates:
                tag.color = updates["color"]
            if "description" in updates:
                tag.description = updates["description"]
            
            logger.info(f"更新标签成功: {tag_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新标签失败: {e}")
            return False
    
    async def delete_tag(self, tag_id: str) -> bool:
        """删除标签"""
        try:
            if tag_id not in self.tags:
                logger.warning(f"标签不存在: {tag_id}")
                return False
            
            # 删除相关的标签规则
            self.tag_rules = [rule for rule in self.tag_rules if rule.tag_id != tag_id]
            
            # 删除标签
            del self.tags[tag_id]
            
            logger.info(f"删除标签成功: {tag_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除标签失败: {e}")
            return False
    
    def get_all_tags(self) -> List[Dict[str, Any]]:
        """获取所有标签"""
        return [
            {
                "id": tag.id,
                "name": tag.name,
                "color": tag.color,
                "description": tag.description,
                "created_at": tag.created_at.isoformat()
            }
            for tag in self.tags.values()
        ]
    
    def get_tags_by_type(self, tag_type: TagType) -> List[ProductTag]:
        """根据类型获取标签"""
        # 这里简化实现，实际可以在标签模型中添加type字段
        type_prefixes = {
            TagType.SYSTEM: ["tag_competitor", "tag_supplier", "tag_other"],
            TagType.AUTO: ["tag_price_", "tag_quality_", "tag_hot", "tag_new"],
            TagType.MANUAL: [],  # 手动标签需要额外标记
            TagType.CUSTOM: []   # 自定义标签需要额外标记
        }
        
        if tag_type in type_prefixes:
            prefixes = type_prefixes[tag_type]
            return [
                tag for tag in self.tags.values()
                if any(tag.id.startswith(prefix) for prefix in prefixes)
            ]
        
        return list(self.tags.values())
    
    def add_tag_rule(self, rule: TagRule):
        """添加标签规则"""
        self.tag_rules.append(rule)
        # 按优先级排序
        self.tag_rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"添加标签规则: {rule.name}")
    
    def remove_tag_rule(self, rule_id: str) -> bool:
        """移除标签规则"""
        original_count = len(self.tag_rules)
        self.tag_rules = [r for r in self.tag_rules if r.id != rule_id]
        
        if len(self.tag_rules) < original_count:
            logger.info(f"移除标签规则: {rule_id}")
            return True
        
        return False
    
    def get_tag_rules(self) -> List[Dict[str, Any]]:
        """获取标签规则列表"""
        return [
            {
                "id": rule.id,
                "name": rule.name,
                "tag_id": rule.tag_id,
                "tag_name": self.tags[rule.tag_id].name if rule.tag_id in self.tags else "未知",
                "conditions": rule.conditions,
                "priority": rule.priority,
                "enabled": rule.enabled,
                "created_at": rule.created_at.isoformat()
            }
            for rule in self.tag_rules
        ]
    
    async def batch_tag_products(self, products: List[Product]) -> Dict[str, List[ProductTag]]:
        """批量为商品添加标签"""
        logger.info(f"开始批量标记商品: {len(products)} 个商品")
        
        results = {}
        
        for product in products:
            try:
                tags = await self.auto_tag_product(product)
                results[product.id] = tags
            except Exception as e:
                logger.error(f"商品 {product.id} 标记失败: {e}")
                results[product.id] = []
        
        logger.info(f"批量标记完成: {len(results)} 个结果")
        return results
