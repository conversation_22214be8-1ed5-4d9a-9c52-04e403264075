<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 前端登录功能测试</h1>
        <p>测试修复后的前端登录功能</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="0)q=Y6(ZeTi8" required>
            </div>
            
            <button type="submit" id="loginBtn">登录测试</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            const loginBtn = document.getElementById('loginBtn');
            
            // 显示加载状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在登录...';
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录成功!
状态码: ${response.status}
用户信息:
  - 用户名: ${data.user.username}
  - 邮箱: ${data.user.email}
  - 角色: ${data.user.role}
  - 全名: ${data.user.full_name}
令牌信息:
  - 访问令牌: ${data.access_token.substring(0, 30)}...
  - 刷新令牌: ${data.refresh_token.substring(0, 30)}...
  - 令牌类型: ${data.token_type}
  - 过期时间: ${data.expires_in}秒

🎉 前端登录功能修复成功！`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败!
状态码: ${response.status}
错误信息: ${data.detail || data.message || '未知错误'}
响应数据: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!
错误信息: ${error.message}
请检查:
1. 前端服务是否正常运行
2. 后端API是否可访问
3. 代理配置是否正确`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录测试';
            }
        });
    </script>
</body>
</html>
