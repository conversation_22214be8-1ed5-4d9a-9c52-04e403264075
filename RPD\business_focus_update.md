# 电商商品监控系统业务需求更新总结

## 更新背景

基于用户的核心业务需求反馈，系统的主要使用目的是：
1. **销量趋势分析** - 监控目标商品的销量变化趋势，预测销售表现
2. **利差计算** - 基于目标商品售价趋势和供货商成本计算利润空间

原设计文档缺少了对商品库存、销量相关趋势的深度分析，以及供货商成本管理和利差计算功能。

## 核心业务需求

### 1. 销量趋势监控
- **销量数据采集**：从电商平台获取商品销量、库存等关键数据
- **趋势分析**：分析销量变化趋势、增长率、波动性
- **销量预测**：基于历史数据预测未来销量表现
- **库存监控**：跟踪库存水平变化，识别缺货风险
- **表现对比**：多商品销量表现对比分析

### 2. 利差计算分析
- **供货商管理**：管理多个供货商的基本信息和联系方式
- **成本管理**：录入和管理不同供货商的商品成本信息
- **利润计算**：实时计算商品利润率和利润金额
- **供货商对比**：对比不同供货商的成本和条件
- **机会识别**：自动识别高利润商品机会

## 系统架构更新

### 原架构问题
- 数据分析模块功能过于泛化，缺乏针对性
- 缺少专门的成本管理和利润分析功能
- 预警系统不够智能，无法针对业务关键指标

### 更新后架构
```
电商监控系统 (业务优化架构)
├── 商品监控模块 (基础功能)
│   ├── 商品管理
│   ├── 监控任务
│   └── 平台配置
├── 销量分析模块 (核心业务)
│   ├── 销量趋势分析
│   ├── 库存变化监控
│   └── 销售预测
├── 利差计算模块 (核心业务)
│   ├── 成本管理
│   ├── 利润分析
│   └── 供货商对比
├── 预警报表模块 (业务支撑)
│   ├── 智能预警
│   ├── 趋势报表
│   └── 利润报告
└── 翻译服务模块 (辅助功能)
    ├── LLM集成
    ├── 批量处理
    └── 质量评估
```

## 数据模型更新

### 新增数据表

#### 供货商表 (suppliers)
```sql
CREATE TABLE suppliers (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,                    -- 供货商名称
    contact_info JSONB,                    -- 联系信息
    payment_terms TEXT,                    -- 付款条件
    shipping_cost DECIMAL(10,4),           -- 运费
    minimum_order INTEGER,                 -- 最小订单量
    lead_time_days INTEGER,                -- 交货周期
    quality_rating DECIMAL(3,2),           -- 质量评级
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 商品成本表 (product_costs)
```sql
CREATE TABLE product_costs (
    id UUID PRIMARY KEY,
    product_id UUID REFERENCES products(id),
    supplier_id UUID REFERENCES suppliers(id),
    unit_cost DECIMAL(12,4) NOT NULL,      -- 单位成本
    currency TEXT DEFAULT 'USD',           -- 货币
    minimum_quantity INTEGER,              -- 最小采购量
    shipping_cost DECIMAL(10,4),           -- 运费
    other_fees DECIMAL(10,4),              -- 其他费用
    valid_from TIMESTAMPTZ,                -- 有效期开始
    valid_until TIMESTAMPTZ,               -- 有效期结束
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 增强现有数据表

#### 商品历史表增强
- 重点关注 `sales_count` 字段的数据采集和分析
- 增加销量相关的索引优化
- 支持销量趋势的时间序列分析

## 核心功能更新

### 1. 销量分析功能

#### 销量趋势分析
```python
async def get_sales_trend(product_id: str, days: int) -> SalesTrend:
    """获取销量趋势分析"""
    # 查询销量历史数据
    # 计算增长率、波动性、趋势强度
    # 生成趋势图表数据
    # 提供趋势分析结论
```

#### 销量预测
```python
async def forecast_sales(product_id: str, forecast_days: int) -> SalesForecast:
    """销量预测"""
    # 基于历史销量数据
    # 使用线性回归或时间序列分析
    # 预测未来销量趋势
    # 提供预测置信度
```

### 2. 利差计算功能

#### 利润分析
```python
async def calculate_profit_margin(product_id: str) -> ProfitAnalysis:
    """计算利润分析"""
    # 获取商品价格趋势
    # 获取供货商成本信息
    # 计算利润率和利润金额
    # 推荐最优供货商
```

#### 利润机会识别
```python
async def find_profit_opportunities() -> List[ProfitOpportunity]:
    """寻找利润机会"""
    # 分析所有商品的利润空间
    # 识别高利润商品
    # 按利润率排序
    # 提供采购建议
```

### 3. 智能预警功能

#### 业务关键指标预警
- **销量异常预警**：销量突增或突降
- **利润空间预警**：利润率变化超过阈值
- **库存不足预警**：库存水平过低
- **成本变化预警**：供货商成本波动

## API接口更新

### 销量分析API
```
GET /api/v1/sales/trends/{product_id}     # 获取销量趋势
GET /api/v1/sales/inventory/{product_id}  # 获取库存趋势
POST /api/v1/sales/forecast               # 销量预测
GET /api/v1/sales/performance/compare     # 商品表现对比
```

### 利差计算API
```
POST /api/v1/profit/suppliers             # 添加供货商
POST /api/v1/profit/costs                 # 添加成本信息
GET /api/v1/profit/profit/{product_id}    # 计算利润分析
GET /api/v1/profit/comparison/{product_id} # 供货商对比
GET /api/v1/profit/opportunities          # 利润机会识别
```

### 预警报表API
```
GET /api/v1/alerts/rules                  # 预警规则管理
GET /api/v1/alerts/history               # 预警历史
POST /api/v1/reports/sales               # 销量报表
POST /api/v1/reports/profit              # 利润报表
```

## 前端界面更新

### 1. 销量分析界面
- **销量趋势图表**：交互式销量趋势图，支持多时间维度
- **销量统计面板**：关键销量指标展示
- **销量预测图表**：未来销量预测可视化
- **商品对比界面**：多商品销量表现对比

### 2. 利差计算界面
- **供货商管理**：供货商信息录入和管理界面
- **成本管理**：商品成本录入和历史查看
- **利润分析仪表板**：实时利润分析展示
- **利润机会列表**：高利润商品机会展示

### 3. 综合报表界面
- **销量分析报表**：综合销量分析报告
- **利润分析报表**：利润和成本分析报告
- **供货商对比报表**：供货商成本和条件对比
- **预警中心**：集中展示各类业务预警

## 实施优先级

### 高优先级 (核心业务功能)
1. **销量趋势分析** - 用户最关心的核心功能
2. **利差计算引擎** - 直接影响用户盈利决策
3. **供货商成本管理** - 利差计算的基础数据
4. **智能预警系统** - 及时提醒业务机会和风险

### 中优先级 (业务支撑功能)
1. **销量预测** - 帮助用户制定采购计划
2. **利润机会识别** - 自动发现高利润商品
3. **综合报表生成** - 业务分析和决策支持
4. **商品表现对比** - 多商品分析对比

### 低优先级 (辅助功能)
1. **翻译服务优化** - 提升用户体验
2. **高级图表功能** - 数据可视化增强
3. **移动端适配** - 移动设备支持
4. **API扩展** - 第三方集成支持

## 预期业务价值

### 直接价值
- **提升采购效率**：通过销量趋势分析，优化采购时机和数量
- **增加利润空间**：通过利差计算，选择最优供货商和商品
- **降低库存风险**：通过库存监控，避免缺货和积压
- **快速决策支持**：通过智能预警，及时响应市场变化

### 间接价值
- **数据驱动决策**：基于真实数据而非经验做决策
- **竞争优势**：更精准的市场分析和成本控制
- **业务扩展支持**：为业务规模化提供数据支撑
- **风险控制**：提前识别和规避业务风险

## 成功指标

### 功能指标
- 销量趋势分析准确率 > 85%
- 利润计算实时性 < 5秒
- 预警响应时间 < 1分钟
- 数据更新频率 ≤ 1小时

### 业务指标
- 用户采购决策效率提升 30%
- 商品利润率提升 15%
- 库存周转率提升 20%
- 业务风险识别率 > 90%

这次更新完全围绕用户的核心业务需求，将系统从通用的商品监控平台转变为专业的销量分析和利差计算工具，更好地服务于电商运营人员的实际工作需求。
