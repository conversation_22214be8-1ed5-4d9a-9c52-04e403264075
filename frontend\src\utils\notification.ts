/**
 * 通知管理器
 */

import { message, notification } from 'antd';
import React from 'react';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

// 消息类型
export type MessageType = 'success' | 'error' | 'warning' | 'info';

// 通知配置
interface NotificationConfig {
  title: string;
  description?: string;
  duration?: number;
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
}

// 消息配置
interface MessageConfig {
  content: string;
  duration?: number;
}

class NotificationManager {
  // 默认配置
  private defaultDuration = 3;
  private defaultPlacement: 'topRight' = 'topRight';

  /**
   * 显示成功消息
   */
  success(config: string | MessageConfig) {
    const messageConfig = typeof config === 'string' ? { content: config } : config;
    message.success({
      content: messageConfig.content,
      duration: messageConfig.duration || this.defaultDuration,
      icon: React.createElement(CheckCircleOutlined, { style: { color: '#52c41a' } }),
    });
  }

  /**
   * 显示错误消息
   */
  error(config: string | MessageConfig) {
    const messageConfig = typeof config === 'string' ? { content: config } : config;
    message.error({
      content: messageConfig.content,
      duration: messageConfig.duration || this.defaultDuration,
      icon: React.createElement(CloseCircleOutlined, { style: { color: '#ff4d4f' } }),
    });
  }

  /**
   * 显示警告消息
   */
  warning(config: string | MessageConfig) {
    const messageConfig = typeof config === 'string' ? { content: config } : config;
    message.warning({
      content: messageConfig.content,
      duration: messageConfig.duration || this.defaultDuration,
      icon: React.createElement(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
    });
  }

  /**
   * 显示信息消息
   */
  info(config: string | MessageConfig) {
    const messageConfig = typeof config === 'string' ? { content: config } : config;
    message.info({
      content: messageConfig.content,
      duration: messageConfig.duration || this.defaultDuration,
      icon: React.createElement(InfoCircleOutlined, { style: { color: '#1890ff' } }),
    });
  }

  /**
   * 显示加载消息
   */
  loading(content: string, duration?: number) {
    return message.loading(content, duration || 0);
  }

  /**
   * 显示成功通知
   */
  notifySuccess(config: NotificationConfig) {
    notification.success({
      message: config.title,
      description: config.description,
      duration: config.duration || this.defaultDuration,
      placement: config.placement || this.defaultPlacement,
      icon: React.createElement(CheckCircleOutlined, { style: { color: '#52c41a' } }),
    });
  }

  /**
   * 显示错误通知
   */
  notifyError(config: NotificationConfig) {
    notification.error({
      message: config.title,
      description: config.description,
      duration: config.duration || this.defaultDuration,
      placement: config.placement || this.defaultPlacement,
      icon: React.createElement(CloseCircleOutlined, { style: { color: '#ff4d4f' } }),
    });
  }

  /**
   * 显示警告通知
   */
  notifyWarning(config: NotificationConfig) {
    notification.warning({
      message: config.title,
      description: config.description,
      duration: config.duration || this.defaultDuration,
      placement: config.placement || this.defaultPlacement,
      icon: React.createElement(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
    });
  }

  /**
   * 显示信息通知
   */
  notifyInfo(config: NotificationConfig) {
    notification.info({
      message: config.title,
      description: config.description,
      duration: config.duration || this.defaultDuration,
      placement: config.placement || this.defaultPlacement,
      icon: React.createElement(InfoCircleOutlined, { style: { color: '#1890ff' } }),
    });
  }

  /**
   * 关闭所有消息
   */
  destroyAll() {
    message.destroy();
    notification.destroy();
  }

  /**
   * 处理API错误
   */
  handleApiError(error: any, defaultMessage = '操作失败') {
    let errorMessage = defaultMessage;
    
    if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    // 根据错误类型显示不同的消息
    if (error?.response?.status === 401) {
      this.warning('登录已过期，请重新登录');
    } else if (error?.response?.status === 403) {
      this.warning('没有权限执行此操作');
    } else if (error?.response?.status === 404) {
      this.error('请求的资源不存在');
    } else if (error?.response?.status >= 500) {
      this.error('服务器错误，请稍后重试');
    } else {
      this.error(errorMessage);
    }
  }

  /**
   * 显示操作成功反馈
   */
  operationSuccess(operation: string, target?: string) {
    const message = target ? `${operation}${target}成功` : `${operation}成功`;
    this.success(message);
  }

  /**
   * 显示操作失败反馈
   */
  operationError(operation: string, target?: string, error?: any) {
    const baseMessage = target ? `${operation}${target}失败` : `${operation}失败`;
    
    if (error) {
      this.handleApiError(error, baseMessage);
    } else {
      this.error(baseMessage);
    }
  }
}

// 创建单例实例
export const notify = new NotificationManager();

// 导出默认实例
export default notify;
