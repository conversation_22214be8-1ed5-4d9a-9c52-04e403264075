"""
质量评估器

评估翻译质量并提供评分
"""

import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import statistics

from app.core.logging import get_logger

logger = get_logger(__name__)


class QualityMetric(Enum):
    """质量指标"""
    ACCURACY = "accuracy"           # 准确性
    FLUENCY = "fluency"             # 流畅性
    COMPLETENESS = "completeness"   # 完整性
    CONSISTENCY = "consistency"     # 一致性
    TERMINOLOGY = "terminology"     # 术语准确性
    STYLE = "style"                 # 风格适应性


class QualityLevel(Enum):
    """质量等级"""
    EXCELLENT = "excellent"     # 优秀 (9-10分)
    GOOD = "good"              # 良好 (7-8分)
    FAIR = "fair"              # 一般 (5-6分)
    POOR = "poor"              # 较差 (3-4分)
    VERY_POOR = "very_poor"    # 很差 (1-2分)


@dataclass
class QualityScore:
    """质量评分"""
    overall_score: float                    # 总体评分 (1-10)
    quality_level: QualityLevel            # 质量等级
    metric_scores: Dict[str, float]        # 各项指标评分
    issues: List[str] = field(default_factory=list)  # 发现的问题
    suggestions: List[str] = field(default_factory=list)  # 改进建议
    confidence: float = 0.8                # 评估置信度
    assessed_at: datetime = field(default_factory=datetime.now)


@dataclass
class QualityRule:
    """质量规则"""
    rule_id: str
    name: str
    description: str
    metric: QualityMetric
    weight: float = 1.0
    enabled: bool = True
    pattern: Optional[str] = None
    threshold: Optional[float] = None


class QualityAssessor:
    """质量评估器"""
    
    def __init__(self):
        # 质量规则
        self.quality_rules: Dict[str, QualityRule] = {}
        
        # 评估历史
        self.assessment_history: List[QualityScore] = []
        
        # 评估配置
        self.assessment_config = {
            "enable_automatic_assessment": True,
            "enable_rule_based_assessment": True,
            "enable_statistical_assessment": True,
            "min_confidence_threshold": 0.6,
            "default_timeout": 10
        }
        
        # 初始化默认规则
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """初始化默认质量规则"""
        default_rules = [
            QualityRule(
                rule_id="length_check",
                name="长度检查",
                description="检查翻译结果长度是否合理",
                metric=QualityMetric.COMPLETENESS,
                weight=1.0,
                threshold=0.3  # 长度差异不超过30%
            ),
            QualityRule(
                rule_id="empty_translation",
                name="空翻译检查",
                description="检查翻译结果是否为空",
                metric=QualityMetric.COMPLETENESS,
                weight=2.0
            ),
            QualityRule(
                rule_id="special_chars",
                name="特殊字符检查",
                description="检查特殊字符是否正确保留",
                metric=QualityMetric.ACCURACY,
                weight=1.5,
                pattern=r'[^\w\s\u4e00-\u9fff]'
            ),
            QualityRule(
                rule_id="number_consistency",
                name="数字一致性检查",
                description="检查数字是否正确翻译",
                metric=QualityMetric.ACCURACY,
                weight=2.0,
                pattern=r'\d+'
            ),
            QualityRule(
                rule_id="brand_names",
                name="品牌名称检查",
                description="检查品牌名称是否正确处理",
                metric=QualityMetric.TERMINOLOGY,
                weight=1.5
            ),
            QualityRule(
                rule_id="repetition_check",
                name="重复检查",
                description="检查是否有不必要的重复",
                metric=QualityMetric.FLUENCY,
                weight=1.0
            )
        ]
        
        for rule in default_rules:
            self.quality_rules[rule.rule_id] = rule
    
    async def assess_translation(self, original_text: str, translated_text: str,
                               source_lang, target_lang, text_type) -> QualityScore:
        """
        评估翻译质量
        
        Args:
            original_text: 原文
            translated_text: 译文
            source_lang: 源语言
            target_lang: 目标语言
            text_type: 文本类型
        
        Returns:
            QualityScore: 质量评分
        """
        try:
            logger.info(f"开始质量评估: {original_text[:50]}...")
            
            # 初始化评分结果
            metric_scores = {}
            issues = []
            suggestions = []
            
            # 基于规则的评估
            if self.assessment_config["enable_rule_based_assessment"]:
                rule_results = await self._assess_by_rules(
                    original_text, translated_text, source_lang, target_lang
                )
                metric_scores.update(rule_results["scores"])
                issues.extend(rule_results["issues"])
                suggestions.extend(rule_results["suggestions"])
            
            # 统计评估
            if self.assessment_config["enable_statistical_assessment"]:
                stat_results = await self._assess_by_statistics(
                    original_text, translated_text, text_type
                )
                # 合并统计评分
                for metric, score in stat_results["scores"].items():
                    if metric in metric_scores:
                        metric_scores[metric] = (metric_scores[metric] + score) / 2
                    else:
                        metric_scores[metric] = score
                
                issues.extend(stat_results["issues"])
                suggestions.extend(stat_results["suggestions"])
            
            # 计算总体评分
            overall_score = self._calculate_overall_score(metric_scores)
            
            # 确定质量等级
            quality_level = self._determine_quality_level(overall_score)
            
            # 计算置信度
            confidence = self._calculate_confidence(metric_scores, len(issues))
            
            # 创建质量评分
            quality_score = QualityScore(
                overall_score=overall_score,
                quality_level=quality_level,
                metric_scores=metric_scores,
                issues=issues,
                suggestions=suggestions,
                confidence=confidence
            )
            
            # 保存评估历史
            self.assessment_history.append(quality_score)
            
            logger.info(f"质量评估完成: 总分 {overall_score:.2f}, 等级 {quality_level.value}")
            return quality_score
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            
            # 返回默认评分
            return QualityScore(
                overall_score=5.0,
                quality_level=QualityLevel.FAIR,
                metric_scores={"error": 0.0},
                issues=[f"评估失败: {str(e)}"],
                suggestions=["请检查翻译质量"],
                confidence=0.1
            )
    
    async def _assess_by_rules(self, original_text: str, translated_text: str,
                             source_lang, target_lang) -> Dict[str, Any]:
        """基于规则的评估"""
        scores = {}
        issues = []
        suggestions = []
        
        try:
            for rule in self.quality_rules.values():
                if not rule.enabled:
                    continue
                
                score, rule_issues, rule_suggestions = await self._apply_rule(
                    rule, original_text, translated_text
                )
                
                metric_name = rule.metric.value
                if metric_name in scores:
                    # 加权平均
                    existing_weight = 1.0
                    new_weight = rule.weight
                    scores[metric_name] = (
                        scores[metric_name] * existing_weight + score * new_weight
                    ) / (existing_weight + new_weight)
                else:
                    scores[metric_name] = score
                
                issues.extend(rule_issues)
                suggestions.extend(rule_suggestions)
            
            return {
                "scores": scores,
                "issues": issues,
                "suggestions": suggestions
            }
            
        except Exception as e:
            logger.error(f"规则评估失败: {e}")
            return {"scores": {}, "issues": [], "suggestions": []}
    
    async def _apply_rule(self, rule: QualityRule, original_text: str, 
                         translated_text: str) -> Tuple[float, List[str], List[str]]:
        """应用单个规则"""
        score = 8.0  # 默认评分
        issues = []
        suggestions = []
        
        try:
            if rule.rule_id == "length_check":
                # 长度检查
                orig_len = len(original_text)
                trans_len = len(translated_text)
                
                if orig_len > 0:
                    length_ratio = abs(trans_len - orig_len) / orig_len
                    if length_ratio > rule.threshold:
                        score = max(3.0, 8.0 - length_ratio * 10)
                        issues.append(f"翻译长度差异过大: {length_ratio:.2%}")
                        suggestions.append("检查翻译是否完整或过于冗长")
            
            elif rule.rule_id == "empty_translation":
                # 空翻译检查
                if not translated_text.strip():
                    score = 1.0
                    issues.append("翻译结果为空")
                    suggestions.append("重新翻译")
                elif len(translated_text.strip()) < 3:
                    score = 3.0
                    issues.append("翻译结果过短")
                    suggestions.append("检查翻译完整性")
            
            elif rule.rule_id == "special_chars":
                # 特殊字符检查
                if rule.pattern:
                    orig_chars = set(re.findall(rule.pattern, original_text))
                    trans_chars = set(re.findall(rule.pattern, translated_text))
                    
                    missing_chars = orig_chars - trans_chars
                    if missing_chars:
                        score = max(5.0, 8.0 - len(missing_chars))
                        issues.append(f"缺少特殊字符: {', '.join(missing_chars)}")
                        suggestions.append("保留原文中的特殊字符")
            
            elif rule.rule_id == "number_consistency":
                # 数字一致性检查
                if rule.pattern:
                    orig_numbers = re.findall(rule.pattern, original_text)
                    trans_numbers = re.findall(rule.pattern, translated_text)
                    
                    if len(orig_numbers) != len(trans_numbers):
                        score = max(4.0, 8.0 - abs(len(orig_numbers) - len(trans_numbers)))
                        issues.append("数字数量不一致")
                        suggestions.append("检查数字翻译准确性")
                    elif orig_numbers != trans_numbers:
                        score = 6.0
                        issues.append("数字内容不一致")
                        suggestions.append("确保数字正确翻译")
            
            elif rule.rule_id == "brand_names":
                # 品牌名称检查
                common_brands = ["iPhone", "Samsung", "Huawei", "Xiaomi", "OPPO", "Vivo"]
                for brand in common_brands:
                    if brand in original_text and brand not in translated_text:
                        # 检查是否有合理的翻译
                        brand_translations = {
                            "iPhone": ["苹果手机", "iPhone"],
                            "Samsung": ["三星", "Samsung"],
                            "Huawei": ["华为", "Huawei"],
                            "Xiaomi": ["小米", "Xiaomi"]
                        }
                        
                        if brand in brand_translations:
                            found_translation = any(
                                trans in translated_text 
                                for trans in brand_translations[brand]
                            )
                            if not found_translation:
                                score = max(6.0, score - 1.0)
                                issues.append(f"品牌名称 {brand} 可能翻译不当")
                                suggestions.append(f"检查 {brand} 的翻译")
            
            elif rule.rule_id == "repetition_check":
                # 重复检查
                words = translated_text.split()
                if len(words) > 1:
                    word_counts = {}
                    for word in words:
                        if len(word) > 2:  # 只检查长度大于2的词
                            word_counts[word] = word_counts.get(word, 0) + 1
                    
                    repeated_words = [word for word, count in word_counts.items() if count > 2]
                    if repeated_words:
                        score = max(6.0, 8.0 - len(repeated_words) * 0.5)
                        issues.append(f"发现重复词汇: {', '.join(repeated_words[:3])}")
                        suggestions.append("减少不必要的重复")
            
            return score, issues, suggestions
            
        except Exception as e:
            logger.error(f"应用规则失败 {rule.rule_id}: {e}")
            return 5.0, [f"规则 {rule.name} 评估失败"], []
    
    async def _assess_by_statistics(self, original_text: str, translated_text: str,
                                  text_type) -> Dict[str, Any]:
        """基于统计的评估"""
        scores = {}
        issues = []
        suggestions = []
        
        try:
            # 长度比例评估
            orig_len = len(original_text)
            trans_len = len(translated_text)
            
            if orig_len > 0:
                length_ratio = trans_len / orig_len
                
                # 根据语言对调整期望比例
                expected_ratios = {
                    "en_zh": (0.5, 1.5),  # 英文到中文
                    "zh_en": (0.7, 2.0),  # 中文到英文
                    "default": (0.6, 1.8)
                }
                
                min_ratio, max_ratio = expected_ratios.get("default", (0.6, 1.8))
                
                if min_ratio <= length_ratio <= max_ratio:
                    scores["completeness"] = 8.0
                else:
                    scores["completeness"] = max(4.0, 8.0 - abs(length_ratio - 1.0) * 2)
                    if length_ratio < min_ratio:
                        issues.append("翻译可能不完整")
                        suggestions.append("检查是否遗漏内容")
                    else:
                        issues.append("翻译可能过于冗长")
                        suggestions.append("简化翻译表达")
            
            # 字符多样性评估
            orig_chars = len(set(original_text))
            trans_chars = len(set(translated_text))
            
            if orig_chars > 0:
                diversity_ratio = trans_chars / orig_chars
                if 0.5 <= diversity_ratio <= 2.0:
                    scores["fluency"] = 7.5
                else:
                    scores["fluency"] = max(5.0, 7.5 - abs(diversity_ratio - 1.0))
            
            # 基于文本类型的特殊评估
            if hasattr(text_type, 'value'):
                text_type_value = text_type.value
                
                if text_type_value == "product_title":
                    # 商品标题应该简洁
                    if trans_len > 100:
                        scores["style"] = max(6.0, 8.0 - (trans_len - 100) / 20)
                        issues.append("商品标题过长")
                        suggestions.append("简化标题表达")
                    else:
                        scores["style"] = 8.5
                
                elif text_type_value == "product_description":
                    # 商品描述应该详细
                    if trans_len < 50:
                        scores["style"] = max(6.0, 8.0 - (50 - trans_len) / 10)
                        issues.append("商品描述过短")
                        suggestions.append("增加描述细节")
                    else:
                        scores["style"] = 8.0
            
            return {
                "scores": scores,
                "issues": issues,
                "suggestions": suggestions
            }
            
        except Exception as e:
            logger.error(f"统计评估失败: {e}")
            return {"scores": {}, "issues": [], "suggestions": []}
    
    def _calculate_overall_score(self, metric_scores: Dict[str, float]) -> float:
        """计算总体评分"""
        if not metric_scores:
            return 5.0
        
        # 指标权重
        metric_weights = {
            "accuracy": 0.3,
            "fluency": 0.25,
            "completeness": 0.25,
            "consistency": 0.1,
            "terminology": 0.05,
            "style": 0.05
        }
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for metric, score in metric_scores.items():
            weight = metric_weights.get(metric, 0.1)
            weighted_sum += score * weight
            total_weight += weight
        
        if total_weight > 0:
            return min(10.0, max(1.0, weighted_sum / total_weight))
        else:
            return statistics.mean(metric_scores.values())
    
    def _determine_quality_level(self, overall_score: float) -> QualityLevel:
        """确定质量等级"""
        if overall_score >= 9.0:
            return QualityLevel.EXCELLENT
        elif overall_score >= 7.0:
            return QualityLevel.GOOD
        elif overall_score >= 5.0:
            return QualityLevel.FAIR
        elif overall_score >= 3.0:
            return QualityLevel.POOR
        else:
            return QualityLevel.VERY_POOR
    
    def _calculate_confidence(self, metric_scores: Dict[str, float], issue_count: int) -> float:
        """计算评估置信度"""
        base_confidence = 0.8
        
        # 根据指标数量调整置信度
        if len(metric_scores) >= 3:
            base_confidence += 0.1
        elif len(metric_scores) <= 1:
            base_confidence -= 0.2
        
        # 根据问题数量调整置信度
        if issue_count == 0:
            base_confidence += 0.1
        elif issue_count > 3:
            base_confidence -= 0.2
        
        return min(1.0, max(0.1, base_confidence))
    
    def add_quality_rule(self, rule: QualityRule) -> bool:
        """添加质量规则"""
        try:
            self.quality_rules[rule.rule_id] = rule
            logger.info(f"质量规则已添加: {rule.rule_id}")
            return True
        except Exception as e:
            logger.error(f"添加质量规则失败: {rule.rule_id}, {e}")
            return False
    
    def remove_quality_rule(self, rule_id: str) -> bool:
        """移除质量规则"""
        try:
            if rule_id in self.quality_rules:
                del self.quality_rules[rule_id]
                logger.info(f"质量规则已移除: {rule_id}")
                return True
            else:
                logger.error(f"质量规则不存在: {rule_id}")
                return False
        except Exception as e:
            logger.error(f"移除质量规则失败: {rule_id}, {e}")
            return False
    
    def get_quality_rules(self) -> List[QualityRule]:
        """获取所有质量规则"""
        return list(self.quality_rules.values())
    
    def get_assessment_history(self, limit: Optional[int] = None) -> List[QualityScore]:
        """获取评估历史"""
        history = sorted(self.assessment_history, key=lambda x: x.assessed_at, reverse=True)
        
        if limit:
            history = history[:limit]
        
        return history
    
    def get_quality_statistics(self) -> Dict[str, Any]:
        """获取质量统计信息"""
        try:
            if not self.assessment_history:
                return {
                    "total_assessments": 0,
                    "average_score": 0.0,
                    "score_distribution": {
                        "excellent": 0,
                        "good": 0,
                        "fair": 0,
                        "poor": 0,
                        "very_poor": 0
                    },
                    "quality_distribution": {},
                    "common_issues": [],
                    "average_confidence": 0.0,
                    "total_rules": len(self.quality_rules),
                    "enabled_rules": len([r for r in self.quality_rules.values() if r.enabled])
                }
            
            # 基本统计
            total_assessments = len(self.assessment_history)
            scores = [score.overall_score for score in self.assessment_history]
            average_score = statistics.mean(scores) if scores else 0.0
            
            # 质量等级分布
            quality_distribution = {}
            for score in self.assessment_history:
                level = score.quality_level.value
                quality_distribution[level] = quality_distribution.get(level, 0) + 1
            
            # 常见问题
            all_issues = []
            for score in self.assessment_history:
                all_issues.extend(score.issues)
            
            issue_counts = {}
            for issue in all_issues:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
            
            common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # 平均置信度
            confidences = [score.confidence for score in self.assessment_history]
            average_confidence = statistics.mean(confidences) if confidences else 0.0
            
            return {
                "total_assessments": total_assessments,
                "average_score": average_score,
                "score_distribution": {
                    "excellent": len([s for s in scores if s >= 9.0]),
                    "good": len([s for s in scores if 7.0 <= s < 9.0]),
                    "fair": len([s for s in scores if 5.0 <= s < 7.0]),
                    "poor": len([s for s in scores if 3.0 <= s < 5.0]),
                    "very_poor": len([s for s in scores if s < 3.0])
                },
                "quality_distribution": quality_distribution,
                "common_issues": [{"issue": issue, "count": count} for issue, count in common_issues],
                "average_confidence": average_confidence,
                "total_rules": len(self.quality_rules),
                "enabled_rules": len([r for r in self.quality_rules.values() if r.enabled])
            }
            
        except Exception as e:
            logger.error(f"获取质量统计失败: {e}")
            return {
                "total_assessments": 0,
                "average_score": 0.0,
                "quality_distribution": {},
                "common_issues": [],
                "average_confidence": 0.0,
                "total_rules": len(self.quality_rules),
                "enabled_rules": len([r for r in self.quality_rules.values() if r.enabled])
            }
