-- TimescaleDB连续聚合视图刷新策略优化脚本
-- 根据不同业务场景调整刷新策略

-- 删除现有策略（如果需要重新配置）
-- SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
-- SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
-- SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
-- SELECT remove_continuous_aggregate_policy('system_logs_hourly', if_exists => TRUE);

-- =============================================================================
-- 场景1：高频价格监控（实时性要求高）
-- =============================================================================

-- 每小时聚合 - 高频刷新策略
SELECT add_continuous_aggregate_policy('price_records_hourly',
    start_offset => INTERVAL '2 hours',      -- 减少到2小时（减少重复计算）
    end_offset => INTERVAL '15 minutes',     -- 减少到15分钟（提高实时性）
    schedule_interval => INTERVAL '15 minutes', -- 每15分钟刷新一次
    if_not_exists => TRUE
);

-- 每日聚合 - 平衡策略
SELECT add_continuous_aggregate_policy('price_records_daily',
    start_offset => INTERVAL '2 days',       -- 减少到2天
    end_offset => INTERVAL '2 hours',        -- 减少到2小时
    schedule_interval => INTERVAL '2 hours', -- 每2小时刷新一次
    if_not_exists => TRUE
);

-- 每周聚合 - 低频策略
SELECT add_continuous_aggregate_policy('price_records_weekly',
    start_offset => INTERVAL '2 weeks',      -- 减少到2周
    end_offset => INTERVAL '1 day',          -- 保持1天
    schedule_interval => INTERVAL '6 hours', -- 每6小时刷新一次
    if_not_exists => TRUE
);

-- =============================================================================
-- 场景2：节省资源的策略（适合资源受限环境）
-- =============================================================================

/*
-- 每小时聚合 - 节省资源策略
SELECT add_continuous_aggregate_policy('price_records_hourly',
    start_offset => INTERVAL '1 hour',       -- 最小化重复计算
    end_offset => INTERVAL '30 minutes',     -- 适中的延迟
    schedule_interval => INTERVAL '30 minutes', -- 适中的频率
    if_not_exists => TRUE
);

-- 每日聚合 - 节省资源策略
SELECT add_continuous_aggregate_policy('price_records_daily',
    start_offset => INTERVAL '1 day',        -- 最小化重复计算
    end_offset => INTERVAL '4 hours',        -- 较大的延迟
    schedule_interval => INTERVAL '4 hours', -- 较低的频率
    if_not_exists => TRUE
);

-- 每周聚合 - 节省资源策略
SELECT add_continuous_aggregate_policy('price_records_weekly',
    start_offset => INTERVAL '1 week',       -- 最小化重复计算
    end_offset => INTERVAL '1 day',          -- 保持1天
    schedule_interval => INTERVAL '12 hours', -- 每12小时刷新一次
    if_not_exists => TRUE
);
*/

-- =============================================================================
-- 场景3：数据质量优先策略（适合数据延迟较多的环境）
-- =============================================================================

/*
-- 每小时聚合 - 数据质量优先
SELECT add_continuous_aggregate_policy('price_records_hourly',
    start_offset => INTERVAL '6 hours',      -- 更大的窗口确保数据完整
    end_offset => INTERVAL '2 hours',        -- 更大的缓冲时间
    schedule_interval => INTERVAL '1 hour',  -- 标准频率
    if_not_exists => TRUE
);

-- 每日聚合 - 数据质量优先
SELECT add_continuous_aggregate_policy('price_records_daily',
    start_offset => INTERVAL '5 days',       -- 更大的窗口
    end_offset => INTERVAL '6 hours',        -- 更大的缓冲时间
    schedule_interval => INTERVAL '6 hours', -- 较低频率
    if_not_exists => TRUE
);

-- 每周聚合 - 数据质量优先
SELECT add_continuous_aggregate_policy('price_records_weekly',
    start_offset => INTERVAL '4 weeks',      -- 更大的窗口
    end_offset => INTERVAL '2 days',         -- 更大的缓冲时间
    schedule_interval => INTERVAL '1 day',   -- 每天刷新
    if_not_exists => TRUE
);
*/

-- =============================================================================
-- 系统日志聚合策略优化
-- =============================================================================

-- 系统日志 - 平衡策略
SELECT add_continuous_aggregate_policy('system_logs_hourly',
    start_offset => INTERVAL '2 hours',      -- 减少重复计算
    end_offset => INTERVAL '30 minutes',     -- 适中的延迟
    schedule_interval => INTERVAL '30 minutes', -- 每30分钟刷新
    if_not_exists => TRUE
);

-- =============================================================================
-- 动态策略调整函数
-- =============================================================================

-- 创建函数：根据数据量动态调整刷新策略
CREATE OR REPLACE FUNCTION adjust_refresh_policy_by_volume()
RETURNS void AS $$
DECLARE
    hourly_count INTEGER;
    daily_count INTEGER;
BEGIN
    -- 获取最近24小时的数据量
    SELECT COUNT(*) INTO hourly_count
    FROM price_records
    WHERE recorded_at >= NOW() - INTERVAL '24 hours';
    
    -- 获取最近7天的数据量
    SELECT COUNT(*) INTO daily_count
    FROM price_records
    WHERE recorded_at >= NOW() - INTERVAL '7 days';
    
    -- 根据数据量调整策略
    IF hourly_count > 10000 THEN
        -- 高数据量：降低刷新频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '1 hour',
            end_offset => INTERVAL '30 minutes',
            schedule_interval => INTERVAL '30 minutes'
        );
        
        RAISE NOTICE '高数据量检测：调整为低频刷新策略';
        
    ELSIF hourly_count < 1000 THEN
        -- 低数据量：提高刷新频率
        PERFORM remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        PERFORM add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 hours',
            end_offset => INTERVAL '15 minutes',
            schedule_interval => INTERVAL '15 minutes'
        );
        
        RAISE NOTICE '低数据量检测：调整为高频刷新策略';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 监控和诊断查询
-- =============================================================================

-- 查看当前刷新策略
CREATE OR REPLACE VIEW current_refresh_policies AS
SELECT 
    application_name as view_name,
    config->>'start_offset' as start_offset,
    config->>'end_offset' as end_offset,
    config->>'schedule_interval' as schedule_interval,
    next_start_time,
    last_run_success_time,
    last_run_status
FROM timescaledb_information.jobs j
JOIN timescaledb_information.job_stats js ON j.job_id = js.job_id
WHERE j.proc_name = 'policy_refresh_continuous_aggregate';

-- 查看刷新性能统计
CREATE OR REPLACE VIEW refresh_performance_stats AS
SELECT 
    application_name as view_name,
    total_runs,
    total_successes,
    total_failures,
    total_crashes,
    ROUND(total_successes::numeric / NULLIF(total_runs, 0) * 100, 2) as success_rate,
    last_run_duration,
    average_run_duration
FROM timescaledb_information.jobs j
JOIN timescaledb_information.job_stats js ON j.job_id = js.job_id
WHERE j.proc_name = 'policy_refresh_continuous_aggregate';

-- 查看聚合视图的数据新鲜度
CREATE OR REPLACE FUNCTION check_aggregate_freshness()
RETURNS TABLE(
    view_name TEXT,
    latest_raw_data TIMESTAMP,
    latest_aggregate_data TIMESTAMP,
    freshness_lag INTERVAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'price_records_hourly'::TEXT,
        (SELECT MAX(recorded_at) FROM price_records),
        (SELECT MAX(hour) FROM price_records_hourly),
        (SELECT MAX(recorded_at) FROM price_records) - (SELECT MAX(hour) FROM price_records_hourly);
    
    RETURN QUERY
    SELECT 
        'price_records_daily'::TEXT,
        (SELECT MAX(recorded_at) FROM price_records),
        (SELECT MAX(day) FROM price_records_daily),
        (SELECT MAX(recorded_at) FROM price_records) - (SELECT MAX(day) FROM price_records_daily);
        
    RETURN QUERY
    SELECT 
        'price_records_weekly'::TEXT,
        (SELECT MAX(recorded_at) FROM price_records),
        (SELECT MAX(week) FROM price_records_weekly),
        (SELECT MAX(recorded_at) FROM price_records) - (SELECT MAX(week) FROM price_records_weekly);
END;
$$ LANGUAGE plpgsql;

-- 记录策略优化完成
INSERT INTO system_logs (level, message, module) 
VALUES ('INFO', 'TimescaleDB连续聚合视图刷新策略优化完成', 'timescaledb-optimization');

COMMIT;
