#!/usr/bin/env python3
"""
完整系统功能演示

演示用户权限系统和监控日志系统的所有功能
"""

import asyncio
import tempfile
import shutil
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.auth import (
    AuthManager, AuthConfig, PasswordManager, JWTHandler, TokenType,
    PermissionManager, PermissionDeniedError, AuditLogger, AuditAction, AuditResult,
    User, UserRole, Permission
)

from app.monitoring import (
    HealthChecker, HealthStatus, LogManager, LogLevel, LogCategory,
    SystemMonitor, ResourceMetrics, AlertThreshold
)


async def demo_auth_system():
    """演示用户权限系统"""
    print("🔐 用户权限系统演示")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建认证管理器
        config = AuthConfig()
        auth_manager = AuthManager(config, temp_dir)
        
        print("1. 用户注册")
        # 注册管理员用户（使用不同的用户名避免冲突）
        success, message, admin_user = auth_manager.register_user(
            username="demo_admin",
            email="<EMAIL>",
            password="AdminPass123!",
            full_name="演示管理员",
            role=UserRole.ADMIN
        )
        print(f"   管理员注册: {'✅' if success else '❌'} {message}")

        # 如果注册失败，尝试获取现有用户
        if not success and admin_user is None:
            admin_user = auth_manager.get_user_by_username("admin")
            if admin_user:
                print(f"   使用现有管理员用户: ✅")

        # 注册操作员用户
        success, message, operator_user = auth_manager.register_user(
            username="demo_operator",
            email="<EMAIL>",
            password="OperatorPass123!",
            full_name="演示操作员",
            role=UserRole.OPERATOR
        )
        print(f"   操作员注册: {'✅' if success else '❌'} {message}")
        
        print("\n2. 用户登录")
        # 管理员登录
        admin_session = None
        if admin_user:
            success, message, admin_session = auth_manager.authenticate_user(
                username=admin_user.username,
                password="AdminPass123!" if admin_user.username == "demo_admin" else "EN?%,Od[M:9q",
                ip_address="*************"
            )
            print(f"   管理员登录: {'✅' if success else '❌'} {message}")

        # 操作员登录
        operator_session = None
        if operator_user:
            success, message, operator_session = auth_manager.authenticate_user(
                username="demo_operator",
                password="OperatorPass123!",
                ip_address="*************"
            )
            print(f"   操作员登录: {'✅' if success else '❌'} {message}")
        
        print("\n3. 权限检查")
        permission_manager = PermissionManager()

        # 管理员权限检查
        if admin_user:
            admin_can_manage_users = permission_manager.check_permission(
                admin_user, Permission.USER_CREATE
            )
            print(f"   管理员创建用户权限: {'✅' if admin_can_manage_users else '❌'}")
        else:
            print(f"   管理员创建用户权限: ❌ 用户不存在")

        # 操作员权限检查
        if operator_user:
            operator_can_manage_users = permission_manager.check_permission(
                operator_user, Permission.USER_CREATE
            )
            print(f"   操作员创建用户权限: {'✅' if operator_can_manage_users else '❌'}")

            operator_can_read_products = permission_manager.check_permission(
                operator_user, Permission.PRODUCT_READ
            )
            print(f"   操作员读取商品权限: {'✅' if operator_can_read_products else '❌'}")
        else:
            print(f"   操作员权限检查: ❌ 用户不存在")
        
        print("\n4. 权限装饰器演示")

        @permission_manager.require_permission(Permission.SYSTEM_ADMIN)
        def admin_only_function(user):
            return f"管理员专用功能执行成功 - 用户: {user.username}"

        if admin_user:
            try:
                result = admin_only_function(admin_user)
                print(f"   管理员调用: ✅ {result}")
            except PermissionDeniedError as e:
                print(f"   管理员调用: ❌ {e}")
        else:
            print(f"   管理员调用: ❌ 用户不存在")

        if operator_user:
            try:
                result = admin_only_function(operator_user)
                print(f"   操作员调用: ✅ {result}")
            except PermissionDeniedError as e:
                print(f"   操作员调用: ❌ 权限不足")
        else:
            print(f"   操作员调用: ❌ 用户不存在")
        
        print("\n5. 密码管理")
        password_manager = PasswordManager()
        
        # 密码强度检查
        weak_password = "123456"
        strong_password = "StrongPass123!@#"
        
        weak_result = password_manager.check_password_strength(weak_password)
        strong_result = password_manager.check_password_strength(strong_password)
        
        print(f"   弱密码检查: {'❌' if not weak_result['is_valid'] else '✅'} 强度: {weak_result['strength']}")
        print(f"   强密码检查: {'✅' if strong_result['is_valid'] else '❌'} 强度: {strong_result['strength']}")
        
        # 生成安全密码
        generated_password = password_manager.generate_secure_password(12)
        print(f"   生成安全密码: {generated_password}")
        
        print("\n6. JWT令牌管理")
        jwt_handler = JWTHandler("demo-secret-key")
        
        # 生成令牌
        payload = {
            "user_id": admin_user.user_id,
            "username": admin_user.username,
            "role": admin_user.role.value
        }
        
        access_token = jwt_handler.generate_token(payload, TokenType.ACCESS)
        refresh_token = jwt_handler.generate_token(payload, TokenType.REFRESH)
        
        print(f"   访问令牌生成: ✅ 长度: {len(access_token)}")
        print(f"   刷新令牌生成: ✅ 长度: {len(refresh_token)}")
        
        # 验证令牌
        decoded_payload = jwt_handler.verify_token(access_token, TokenType.ACCESS)
        print(f"   令牌验证: {'✅' if decoded_payload else '❌'}")
        
        print("\n7. 审计日志")
        audit_logger = AuditLogger(os.path.join(temp_dir, "audit"))
        
        # 记录审计日志
        audit_logger.log_action(
            user_id=admin_user.user_id,
            action=AuditAction.LOGIN,
            resource_type="auth",
            resource_id="login",
            username=admin_user.username,
            result=AuditResult.SUCCESS,
            ip_address="*************"
        )
        
        audit_logger.log_action(
            user_id=operator_user.user_id,
            action=AuditAction.PERMISSION_DENIED,
            resource_type="system",
            resource_id="admin_function",
            username=operator_user.username,
            result=AuditResult.FAILURE,
            error_message="权限不足"
        )
        
        # 查询审计日志
        logs = audit_logger.query_audit_logs(limit=10)
        print(f"   审计日志记录: ✅ 共{len(logs)}条")
        
        # 获取用户活动
        admin_activity = audit_logger.get_user_activity(admin_user.user_id)
        print(f"   管理员活动: ✅ {admin_activity['total_actions']}个操作")
        
        # 获取安全事件
        security_events = audit_logger.get_security_events()
        print(f"   安全事件: ✅ {len(security_events)}个事件")
        
        print("\n8. 会话管理")
        session_manager = auth_manager.session_manager
        
        # 获取用户会话
        admin_sessions = session_manager.get_user_sessions(admin_user.user_id)
        print(f"   管理员活跃会话: ✅ {len(admin_sessions)}个")
        
        # 会话统计
        session_stats = session_manager.get_session_statistics()
        print(f"   会话统计: ✅ 总会话: {session_stats['stats']['total_created']}")
        
        print("\n✅ 用户权限系统演示完成!")


async def demo_monitoring_system():
    """演示监控日志系统"""
    print("\n🔍 监控日志系统演示")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print("1. 健康检查系统")
        health_checker = HealthChecker()
        
        # 执行健康检查
        health_result = await health_checker.check_health()
        
        print(f"   系统健康状态: {health_result['status'].upper()}")
        print(f"   检查组件数量: {len(health_result['components'])}")
        
        for component_name, component_data in health_result['components'].items():
            status_emoji = "✅" if component_data['status'] == 'healthy' else "⚠️" if component_data['status'] == 'warning' else "❌"
            print(f"   {status_emoji} {component_name}: {component_data['status']} ({component_data['response_time_ms']:.1f}ms)")
        
        print("\n2. 日志管理系统")
        log_manager = LogManager(os.path.join(temp_dir, "logs"))
        
        # 记录不同类型的日志
        log_manager.info(LogCategory.SYSTEM, "系统启动完成", user_id="system")
        log_manager.warning(LogCategory.MONITORING, "CPU使用率较高: 85%", user_id="monitor")
        log_manager.error(LogCategory.ERROR, "数据库连接失败", user_id="system", extra_data={"error_code": "DB001"})
        log_manager.debug(LogCategory.AUTH, "用户登录调试信息", user_id="auth_service")
        
        # 等待异步日志处理
        import time
        time.sleep(2)
        
        # 日志统计
        log_stats = log_manager.get_log_statistics()
        print(f"   日志记录总数: ✅ {log_stats['stats']['total_logs']}")
        print(f"   INFO级别日志: ✅ {log_stats['stats']['by_level']['INFO']}")
        print(f"   ERROR级别日志: ✅ {log_stats['stats']['by_level']['ERROR']}")
        
        # 搜索日志
        all_logs = log_manager.search_logs(limit=100)
        error_logs = log_manager.search_logs(level=LogLevel.ERROR, limit=100)
        system_logs = log_manager.search_logs(category=LogCategory.SYSTEM, limit=100)
        
        print(f"   全部日志: ✅ {len(all_logs)}条")
        print(f"   错误日志: ✅ {len(error_logs)}条")
        print(f"   系统日志: ✅ {len(system_logs)}条")
        
        print("\n3. 系统监控")
        system_monitor = SystemMonitor(
            os.path.join(temp_dir, "monitor_config.json"),
            temp_dir
        )
        
        # 收集系统指标
        metrics = await system_monitor._collect_system_metrics()
        print(f"   CPU使用率: {metrics.cpu_percent:.1f}%")
        print(f"   内存使用率: {metrics.memory_percent:.1f}%")
        print(f"   磁盘使用率: {metrics.disk_percent:.1f}%")
        print(f"   进程数量: {metrics.process_count}")
        
        # 存储指标
        system_monitor._store_metrics(metrics)
        
        # 模拟高CPU使用率告警
        high_cpu_metrics = ResourceMetrics(
            timestamp=datetime.now(),
            cpu_percent=95.0,  # 超过阈值
            memory_percent=50.0,
            disk_percent=50.0,
            network_bytes_sent=1000,
            network_bytes_recv=2000,
            process_count=100
        )
        
        await system_monitor._check_alerts(high_cpu_metrics)
        
        # 获取系统状态
        system_status = system_monitor.get_system_status()
        print(f"   系统健康: {'✅' if system_status['system_healthy'] else '❌'}")
        print(f"   告警数量: ✅ {len(system_monitor.alert_history)}")
        
        # 获取指标历史
        metrics_history = system_monitor.get_metrics_history(hours=1)
        print(f"   指标历史: ✅ {len(metrics_history)}条记录")
        
        print("\n4. 告警系统")
        if system_monitor.alert_history:
            latest_alert = system_monitor.alert_history[-1]
            print(f"   最新告警: ⚠️ {latest_alert['message']}")
            print(f"   告警级别: {latest_alert['level'].upper()}")
            print(f"   当前值: {latest_alert['current_value']:.1f}")
            print(f"   阈值: {latest_alert['threshold']:.1f}")
        
        print("\n✅ 监控日志系统演示完成!")


async def demo_integration():
    """演示系统集成"""
    print("\n🔗 系统集成演示")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建集成系统
        auth_config = AuthConfig()
        auth_manager = AuthManager(auth_config, os.path.join(temp_dir, "auth"))
        
        log_manager = LogManager(os.path.join(temp_dir, "logs"))
        audit_logger = AuditLogger(os.path.join(temp_dir, "audit"))
        permission_manager = PermissionManager(audit_logger)
        
        print("1. 用户操作审计")
        
        # 注册用户
        success, message, user = auth_manager.register_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPass123!",
            role=UserRole.OPERATOR
        )
        
        if success:
            # 记录用户注册审计
            audit_logger.log_action(
                user_id=user.user_id,
                action=AuditAction.USER_CREATED,
                resource_type="user",
                resource_id=user.user_id,
                username=user.username,
                result=AuditResult.SUCCESS,
                details={"role": user.role.value}
            )
            
            # 记录系统日志
            log_manager.info(
                LogCategory.AUTH,
                f"新用户注册: {user.username}",
                user_id="system",
                extra_data={"user_id": user.user_id, "role": user.role.value}
            )
            
            print(f"   用户注册: ✅ {user.username}")
        
        # 用户登录
        success, message, session = auth_manager.authenticate_user(
            username="testuser",
            password="TestPass123!",
            ip_address="*************"
        )
        
        if success:
            # 记录登录审计
            audit_logger.log_action(
                user_id=user.user_id,
                action=AuditAction.LOGIN,
                resource_type="auth",
                resource_id="login",
                username=user.username,
                result=AuditResult.SUCCESS,
                ip_address="*************"
            )
            
            print(f"   用户登录: ✅ 会话ID: {session.session_id[:8]}...")
        
        print("\n2. 权限检查与审计")
        
        # 尝试执行需要管理员权限的操作
        try:
            @permission_manager.require_permission(Permission.SYSTEM_ADMIN)
            def admin_operation(user):
                return "管理员操作执行成功"
            
            result = admin_operation(user)
            print(f"   管理员操作: ✅ {result}")
            
        except PermissionDeniedError:
            print(f"   管理员操作: ❌ 权限不足（已记录审计日志）")
        
        # 执行用户有权限的操作
        if permission_manager.check_permission(user, Permission.PRODUCT_READ):
            audit_logger.log_action(
                user_id=user.user_id,
                action=AuditAction.DATA_READ,
                resource_type="product",
                resource_id="product_list",
                username=user.username,
                result=AuditResult.SUCCESS
            )
            print(f"   商品读取: ✅ 权限验证通过")
        
        print("\n3. 系统监控与日志")
        
        # 记录系统监控日志
        log_manager.info(
            LogCategory.MONITORING,
            "系统监控正常运行",
            user_id="monitor_service",
            extra_data={"cpu_usage": 45.2, "memory_usage": 62.1}
        )
        
        # 记录性能日志
        log_manager.warning(
            LogCategory.PERFORMANCE,
            "API响应时间较慢",
            user_id="api_service",
            extra_data={"response_time_ms": 1500, "endpoint": "/api/products"}
        )
        
        # 等待异步处理
        import time
        time.sleep(2)
        
        print("\n4. 综合统计")
        
        # 审计统计
        audit_stats = audit_logger.get_audit_statistics()
        print(f"   审计日志总数: ✅ {audit_stats['stats']['total_entries']}")
        
        # 日志统计
        log_stats = log_manager.get_log_statistics()
        print(f"   系统日志总数: ✅ {log_stats['stats']['total_logs']}")
        
        # 认证统计
        auth_stats = auth_manager.get_auth_statistics()
        print(f"   注册用户数: ✅ {auth_stats['stats']['total_users']}")
        print(f"   成功登录数: ✅ {auth_stats['stats']['successful_logins']}")
        
        # 权限统计
        permission_stats = permission_manager.get_permission_statistics()
        print(f"   权限检查总数: ✅ {permission_stats['stats']['total_checks']}")
        print(f"   权限通过数: ✅ {permission_stats['stats']['granted']}")
        print(f"   权限拒绝数: ✅ {permission_stats['stats']['denied']}")
        
        print("\n✅ 系统集成演示完成!")


async def main():
    """主演示函数"""
    print("🎉 Moniit 完整系统功能演示")
    print("=" * 60)
    print("演示用户权限系统和监控日志系统的完整功能")
    print("=" * 60)
    
    try:
        # 演示用户权限系统
        await demo_auth_system()
        
        # 演示监控日志系统
        await demo_monitoring_system()
        
        # 演示系统集成
        await demo_integration()
        
        print("\n" + "=" * 60)
        print("🎊 所有演示完成！系统功能验证通过")
        print("=" * 60)
        
        print("\n📋 演示总结:")
        print("✅ 用户认证和权限管理")
        print("✅ JWT令牌管理")
        print("✅ 密码安全管理")
        print("✅ 会话管理")
        print("✅ 权限装饰器")
        print("✅ 审计日志记录")
        print("✅ 系统健康检查")
        print("✅ 结构化日志管理")
        print("✅ 系统资源监控")
        print("✅ 告警系统")
        print("✅ 系统集成")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
