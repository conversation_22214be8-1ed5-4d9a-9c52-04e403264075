"""
权限管理器

实现基于角色的访问控制(RBAC)和功能权限检查
"""

from typing import Dict, List, Set, Optional, Callable, Any
from functools import wraps
from datetime import datetime
import inspect

from .models import User, UserRole, Permission
from .audit_logger import AuditLogger, AuditAction, AuditResult
from app.core.logging import get_logger

logger = get_logger(__name__)


class PermissionDeniedError(Exception):
    """权限拒绝异常"""
    pass


class PermissionManager:
    """权限管理器"""
    
    def __init__(self, audit_logger: AuditLogger = None):
        self.audit_logger = audit_logger or AuditLogger()
        
        # 权限组定义
        self.permission_groups = {
            "system": {
                Permission.SYSTEM_ADMIN,
                Permission.SYSTEM_CONFIG,
                Permission.SYSTEM_MONITOR
            },
            "user": {
                Permission.USER_CREATE,
                Permission.USER_READ,
                Permission.USER_UPDATE,
                Permission.USER_DELETE
            },
            "product": {
                Permission.PRODUCT_CREATE,
                Permission.PRODUCT_READ,
                Permission.PRODUCT_UPDATE,
                Permission.PRODUCT_DELETE,
                Permission.PRODUCT_IMPORT,
                Permission.PRODUCT_EXPORT
            },
            "translation": {
                Permission.TRANSLATION_USE,
                Permission.TRANSLATION_BATCH,
                Permission.TRANSLATION_CONFIG,
                Permission.TRANSLATION_MONITOR
            },
            "analytics": {
                Permission.ANALYTICS_VIEW,
                Permission.ANALYTICS_EXPORT
            },
            "audit": {
                Permission.AUDIT_VIEW,
                Permission.AUDIT_EXPORT
            }
        }
        
        # 权限层级定义（高级权限包含低级权限）
        self.permission_hierarchy = {
            Permission.SYSTEM_ADMIN: {
                Permission.SYSTEM_CONFIG,
                Permission.SYSTEM_MONITOR,
                Permission.USER_CREATE,
                Permission.USER_READ,
                Permission.USER_UPDATE,
                Permission.USER_DELETE
            },
            Permission.USER_DELETE: {Permission.USER_UPDATE, Permission.USER_READ},
            Permission.USER_UPDATE: {Permission.USER_READ},
            Permission.PRODUCT_DELETE: {Permission.PRODUCT_UPDATE, Permission.PRODUCT_READ},
            Permission.PRODUCT_UPDATE: {Permission.PRODUCT_READ},
            Permission.PRODUCT_EXPORT: {Permission.PRODUCT_READ},
            Permission.PRODUCT_IMPORT: {Permission.PRODUCT_CREATE, Permission.PRODUCT_READ},
            Permission.TRANSLATION_CONFIG: {Permission.TRANSLATION_USE},
            Permission.TRANSLATION_BATCH: {Permission.TRANSLATION_USE},
            Permission.TRANSLATION_MONITOR: {Permission.TRANSLATION_USE},
            Permission.ANALYTICS_EXPORT: {Permission.ANALYTICS_VIEW},
            Permission.AUDIT_EXPORT: {Permission.AUDIT_VIEW}
        }
        
        # 资源权限映射
        self.resource_permissions = {
            "users": {
                "create": Permission.USER_CREATE,
                "read": Permission.USER_READ,
                "update": Permission.USER_UPDATE,
                "delete": Permission.USER_DELETE
            },
            "products": {
                "create": Permission.PRODUCT_CREATE,
                "read": Permission.PRODUCT_READ,
                "update": Permission.PRODUCT_UPDATE,
                "delete": Permission.PRODUCT_DELETE,
                "import": Permission.PRODUCT_IMPORT,
                "export": Permission.PRODUCT_EXPORT
            },
            "translation": {
                "use": Permission.TRANSLATION_USE,
                "batch": Permission.TRANSLATION_BATCH,
                "config": Permission.TRANSLATION_CONFIG,
                "monitor": Permission.TRANSLATION_MONITOR
            },
            "analytics": {
                "view": Permission.ANALYTICS_VIEW,
                "export": Permission.ANALYTICS_EXPORT
            },
            "audit": {
                "view": Permission.AUDIT_VIEW,
                "export": Permission.AUDIT_EXPORT
            },
            "system": {
                "admin": Permission.SYSTEM_ADMIN,
                "config": Permission.SYSTEM_CONFIG,
                "monitor": Permission.SYSTEM_MONITOR
            }
        }
        
        # 权限检查统计
        self.permission_stats = {
            "total_checks": 0,
            "granted": 0,
            "denied": 0,
            "by_permission": {},
            "by_user_role": {}
        }
    
    def check_permission(self, user: User, permission: Permission, 
                        resource_id: str = None, context: Dict = None) -> bool:
        """
        检查用户权限
        
        Args:
            user: 用户对象
            permission: 所需权限
            resource_id: 资源ID（可选）
            context: 上下文信息（可选）
        
        Returns:
            bool: 是否有权限
        """
        try:
            self.permission_stats["total_checks"] += 1
            
            # 检查用户状态
            if not user.is_active or user.is_locked:
                self._record_permission_check(user, permission, False, "用户账户不可用")
                return False
            
            # 检查直接权限
            if permission in user.permissions:
                self._record_permission_check(user, permission, True, "直接权限")
                return True
            
            # 检查权限层级（高级权限包含低级权限）
            for high_perm, low_perms in self.permission_hierarchy.items():
                if high_perm in user.permissions and permission in low_perms:
                    self._record_permission_check(user, permission, True, f"层级权限: {high_perm.value}")
                    return True
            
            # 检查资源所有权（如果提供了资源ID）
            if resource_id and self._check_resource_ownership(user, permission, resource_id, context):
                self._record_permission_check(user, permission, True, "资源所有权")
                return True
            
            self._record_permission_check(user, permission, False, "权限不足")
            return False
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            self._record_permission_check(user, permission, False, f"检查失败: {str(e)}")
            return False
    
    def check_any_permission(self, user: User, permissions: List[Permission],
                           resource_id: str = None, context: Dict = None) -> bool:
        """
        检查用户是否有任意一个权限
        
        Args:
            user: 用户对象
            permissions: 权限列表
            resource_id: 资源ID（可选）
            context: 上下文信息（可选）
        
        Returns:
            bool: 是否有任意一个权限
        """
        return any(self.check_permission(user, perm, resource_id, context) for perm in permissions)
    
    def check_all_permissions(self, user: User, permissions: List[Permission],
                            resource_id: str = None, context: Dict = None) -> bool:
        """
        检查用户是否有所有权限
        
        Args:
            user: 用户对象
            permissions: 权限列表
            resource_id: 资源ID（可选）
            context: 上下文信息（可选）
        
        Returns:
            bool: 是否有所有权限
        """
        return all(self.check_permission(user, perm, resource_id, context) for perm in permissions)
    
    def check_resource_permission(self, user: User, resource: str, action: str,
                                resource_id: str = None, context: Dict = None) -> bool:
        """
        检查资源权限
        
        Args:
            user: 用户对象
            resource: 资源名称
            action: 操作名称
            resource_id: 资源ID（可选）
            context: 上下文信息（可选）
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 获取资源对应的权限
            resource_perms = self.resource_permissions.get(resource, {})
            permission = resource_perms.get(action)
            
            if not permission:
                logger.warning(f"未定义的资源权限: {resource}.{action}")
                return False
            
            return self.check_permission(user, permission, resource_id, context)
            
        except Exception as e:
            logger.error(f"检查资源权限失败: {e}")
            return False
    
    def get_user_permissions(self, user: User) -> Set[Permission]:
        """
        获取用户的所有有效权限（包括层级权限）
        
        Args:
            user: 用户对象
        
        Returns:
            Set[Permission]: 权限集合
        """
        try:
            effective_permissions = set(user.permissions)
            
            # 添加层级权限
            for high_perm, low_perms in self.permission_hierarchy.items():
                if high_perm in user.permissions:
                    effective_permissions.update(low_perms)
            
            return effective_permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return set()
    
    def get_user_resources(self, user: User) -> Dict[str, List[str]]:
        """
        获取用户可访问的资源和操作
        
        Args:
            user: 用户对象
        
        Returns:
            Dict[str, List[str]]: 资源和操作映射
        """
        try:
            user_permissions = self.get_user_permissions(user)
            accessible_resources = {}
            
            for resource, actions in self.resource_permissions.items():
                accessible_actions = []
                for action, permission in actions.items():
                    if permission in user_permissions:
                        accessible_actions.append(action)
                
                if accessible_actions:
                    accessible_resources[resource] = accessible_actions
            
            return accessible_resources
            
        except Exception as e:
            logger.error(f"获取用户资源失败: {e}")
            return {}
    
    def require_permission(self, permission: Permission, resource_id: str = None):
        """
        权限装饰器
        
        Args:
            permission: 所需权限
            resource_id: 资源ID（可选）
        
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 尝试从参数中获取用户对象
                user = None
                
                # 检查关键字参数
                if 'user' in kwargs:
                    user = kwargs['user']
                elif 'current_user' in kwargs:
                    user = kwargs['current_user']
                else:
                    # 检查位置参数
                    for arg in args:
                        if isinstance(arg, User):
                            user = arg
                            break
                
                if not user:
                    raise PermissionDeniedError("无法获取用户信息")
                
                # 检查权限
                if not self.check_permission(user, permission, resource_id):
                    # 记录审计日志
                    self.audit_logger.log_action(
                        user_id=user.user_id,
                        action=AuditAction.ACCESS_DENIED,
                        resource_type="function",
                        resource_id=func.__name__,
                        details={
                            "required_permission": permission.value,
                            "function": func.__name__,
                            "module": func.__module__
                        },
                        result=AuditResult.FAILURE,
                        error_message=f"权限不足: 需要{permission.value}权限"
                    )
                    
                    raise PermissionDeniedError(f"权限不足: 需要{permission.value}权限")
                
                # 记录成功的访问
                self.audit_logger.log_action(
                    user_id=user.user_id,
                    action=AuditAction.FUNCTION_ACCESS,
                    resource_type="function",
                    resource_id=func.__name__,
                    details={
                        "permission": permission.value,
                        "function": func.__name__,
                        "module": func.__module__
                    },
                    result=AuditResult.SUCCESS
                )
                
                return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def require_any_permission(self, permissions: List[Permission]):
        """
        需要任意权限的装饰器
        
        Args:
            permissions: 权限列表
        
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 获取用户对象
                user = None
                if 'user' in kwargs:
                    user = kwargs['user']
                elif 'current_user' in kwargs:
                    user = kwargs['current_user']
                else:
                    for arg in args:
                        if isinstance(arg, User):
                            user = arg
                            break
                
                if not user:
                    raise PermissionDeniedError("无法获取用户信息")
                
                # 检查权限
                if not self.check_any_permission(user, permissions):
                    permission_names = [p.value for p in permissions]
                    raise PermissionDeniedError(f"权限不足: 需要以下权限之一: {', '.join(permission_names)}")
                
                return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def _check_resource_ownership(self, user: User, permission: Permission,
                                resource_id: str, context: Dict = None) -> bool:
        """
        检查资源所有权
        
        Args:
            user: 用户对象
            permission: 权限
            resource_id: 资源ID
            context: 上下文信息
        
        Returns:
            bool: 是否拥有资源
        """
        try:
            # 这里可以实现具体的资源所有权检查逻辑
            # 例如：检查用户是否是资源的创建者或负责人
            
            # 简化实现：如果用户有读权限且是自己的资源，则允许访问
            if context and context.get("owner_id") == user.user_id:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查资源所有权失败: {e}")
            return False
    
    def _record_permission_check(self, user: User, permission: Permission,
                               granted: bool, reason: str):
        """记录权限检查结果"""
        try:
            # 更新统计
            if granted:
                self.permission_stats["granted"] += 1
            else:
                self.permission_stats["denied"] += 1
            
            # 按权限统计
            perm_key = permission.value
            if perm_key not in self.permission_stats["by_permission"]:
                self.permission_stats["by_permission"][perm_key] = {"granted": 0, "denied": 0}
            
            if granted:
                self.permission_stats["by_permission"][perm_key]["granted"] += 1
            else:
                self.permission_stats["by_permission"][perm_key]["denied"] += 1
            
            # 按角色统计
            role_key = user.role.value
            if role_key not in self.permission_stats["by_user_role"]:
                self.permission_stats["by_user_role"][role_key] = {"granted": 0, "denied": 0}
            
            if granted:
                self.permission_stats["by_user_role"][role_key]["granted"] += 1
            else:
                self.permission_stats["by_user_role"][role_key]["denied"] += 1
            
            # 记录审计日志
            if not granted:
                self.audit_logger.log_action(
                    user_id=user.user_id,
                    action=AuditAction.PERMISSION_DENIED,
                    resource_type="permission",
                    resource_id=permission.value,
                    details={
                        "permission": permission.value,
                        "reason": reason,
                        "user_role": user.role.value,
                        "user_permissions": [p.value for p in user.permissions]
                    },
                    result=AuditResult.FAILURE,
                    error_message=reason
                )
            
        except Exception as e:
            logger.error(f"记录权限检查失败: {e}")
    
    def get_permission_statistics(self) -> Dict:
        """获取权限统计信息"""
        return {
            "stats": self.permission_stats.copy(),
            "permission_groups": {
                name: [p.value for p in perms] 
                for name, perms in self.permission_groups.items()
            },
            "resource_permissions": {
                resource: {action: perm.value for action, perm in actions.items()}
                for resource, actions in self.resource_permissions.items()
            }
        }
