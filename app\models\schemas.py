"""
Pydantic数据模型（API序列化）
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl, validator


# 基础模型
class BaseSchema(BaseModel):
    """基础模型"""
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v) if v is not None else None
        }


class TimestampSchema(BaseSchema):
    """时间戳模型"""
    created_at: datetime
    updated_at: datetime


# 商品相关模型
class ProductBase(BaseSchema):
    """商品基础模型"""
    url: HttpUrl = Field(..., description="商品URL")
    platform: str = Field(..., max_length=50, description="平台名称")
    title: Optional[str] = Field(None, max_length=500, description="商品标题")
    title_translated: Optional[str] = Field(None, max_length=500, description="翻译后标题")
    category: Optional[str] = Field(None, max_length=100, description="商品分类")
    status: str = Field("active", max_length=20, description="状态")
    monitoring_frequency: int = Field(24, ge=1, le=168, description="监控频率(小时)")
    is_active: bool = Field(True, description="是否激活")
    tags: Optional[Dict[str, Any]] = Field(None, description="标签")
    notes: Optional[str] = Field(None, description="备注")


class ProductCreate(ProductBase):
    """创建商品模型"""
    pass


class ProductUpdate(BaseSchema):
    """更新商品模型"""
    title: Optional[str] = Field(None, max_length=500, description="商品标题")
    title_translated: Optional[str] = Field(None, max_length=500, description="翻译后标题")
    category: Optional[str] = Field(None, max_length=100, description="商品分类")
    status: Optional[str] = Field(None, max_length=20, description="状态")
    monitoring_frequency: Optional[int] = Field(None, ge=1, le=168, description="监控频率(小时)")
    is_active: Optional[bool] = Field(None, description="是否激活")
    tags: Optional[Dict[str, Any]] = Field(None, description="标签")
    notes: Optional[str] = Field(None, description="备注")


class Product(ProductBase, TimestampSchema):
    """商品模型"""
    id: UUID
    last_monitored_at: Optional[datetime] = Field(None, description="最后监控时间")


class ProductList(BaseSchema):
    """商品列表模型"""
    items: List[Product]
    total: int
    skip: int
    limit: int


# 商品历史数据模型
class ProductHistoryBase(BaseSchema):
    """商品历史数据基础模型"""
    time: datetime = Field(..., description="时间")
    platform: str = Field(..., max_length=50, description="平台名称")
    title: Optional[str] = Field(None, max_length=500, description="商品标题")
    title_translated: Optional[str] = Field(None, max_length=500, description="翻译后标题")
    price: Optional[Decimal] = Field(None, ge=0, description="当前价格")
    currency: Optional[str] = Field(None, max_length=10, description="货币类型")
    sales_count: Optional[int] = Field(None, ge=0, description="销售数量")
    stock_quantity: Optional[int] = Field(None, ge=0, description="库存数量")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="商品评分")
    review_count: Optional[int] = Field(None, ge=0, description="评论数量")
    change_type: Optional[str] = Field(None, max_length=50, description="变化类型")
    change_value: Optional[Decimal] = Field(None, description="变化数值")
    data_quality_score: Optional[Decimal] = Field(None, ge=0, le=1, description="数据质量评分")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始数据")


class ProductHistory(ProductHistoryBase):
    """商品历史数据模型"""
    product_id: UUID
    created_at: datetime


class ProductHistoryList(BaseSchema):
    """商品历史数据列表模型"""
    product_id: UUID
    items: List[ProductHistory]
    total: int
    time_range: Dict[str, Any]


# 供货商相关模型
class SupplierBase(BaseSchema):
    """供货商基础模型"""
    name: str = Field(..., max_length=200, description="供货商名称")
    contact_person: Optional[str] = Field(None, max_length=100, description="联系人")
    phone: Optional[str] = Field(None, max_length=50, description="电话")
    email: Optional[str] = Field(None, max_length=200, description="邮箱")
    address: Optional[str] = Field(None, description="地址")
    payment_terms: Optional[str] = Field(None, max_length=200, description="付款条件")
    delivery_time: Optional[int] = Field(None, ge=1, description="交货时间(天)")
    min_order_quantity: Optional[int] = Field(None, ge=1, description="最小订货量")
    is_active: bool = Field(True, description="是否激活")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="供货商评分")
    notes: Optional[str] = Field(None, description="备注")


class SupplierCreate(SupplierBase):
    """创建供货商模型"""
    pass


class SupplierUpdate(BaseSchema):
    """更新供货商模型"""
    name: Optional[str] = Field(None, max_length=200, description="供货商名称")
    contact_person: Optional[str] = Field(None, max_length=100, description="联系人")
    phone: Optional[str] = Field(None, max_length=50, description="电话")
    email: Optional[str] = Field(None, max_length=200, description="邮箱")
    address: Optional[str] = Field(None, description="地址")
    payment_terms: Optional[str] = Field(None, max_length=200, description="付款条件")
    delivery_time: Optional[int] = Field(None, ge=1, description="交货时间(天)")
    min_order_quantity: Optional[int] = Field(None, ge=1, description="最小订货量")
    is_active: Optional[bool] = Field(None, description="是否激活")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="供货商评分")
    notes: Optional[str] = Field(None, description="备注")


class Supplier(SupplierBase, TimestampSchema):
    """供货商模型"""
    id: UUID


class SupplierList(BaseSchema):
    """供货商列表模型"""
    items: List[Supplier]
    total: int
    skip: int
    limit: int


# 商品成本相关模型
class ProductCostBase(BaseSchema):
    """商品成本基础模型"""
    unit_cost: Decimal = Field(..., gt=0, description="单位成本")
    currency: str = Field("USD", max_length=10, description="货币类型")
    shipping_cost: Optional[Decimal] = Field(None, ge=0, description="运费")
    other_costs: Optional[Decimal] = Field(None, ge=0, description="其他费用")
    total_cost: Decimal = Field(..., gt=0, description="总成本")
    min_quantity: Optional[int] = Field(None, ge=1, description="最小数量")
    max_quantity: Optional[int] = Field(None, ge=1, description="最大数量")
    valid_from: datetime = Field(..., description="有效开始时间")
    valid_until: Optional[datetime] = Field(None, description="有效结束时间")
    is_active: bool = Field(True, description="是否激活")
    notes: Optional[str] = Field(None, description="备注")
    
    @validator('max_quantity')
    def validate_max_quantity(cls, v, values):
        if v is not None and 'min_quantity' in values and values['min_quantity'] is not None:
            if v < values['min_quantity']:
                raise ValueError('最大数量不能小于最小数量')
        return v


class ProductCostCreate(ProductCostBase):
    """创建商品成本模型"""
    product_id: UUID
    supplier_id: UUID


class ProductCostUpdate(BaseSchema):
    """更新商品成本模型"""
    unit_cost: Optional[Decimal] = Field(None, gt=0, description="单位成本")
    currency: Optional[str] = Field(None, max_length=10, description="货币类型")
    shipping_cost: Optional[Decimal] = Field(None, ge=0, description="运费")
    other_costs: Optional[Decimal] = Field(None, ge=0, description="其他费用")
    total_cost: Optional[Decimal] = Field(None, gt=0, description="总成本")
    min_quantity: Optional[int] = Field(None, ge=1, description="最小数量")
    max_quantity: Optional[int] = Field(None, ge=1, description="最大数量")
    valid_from: Optional[datetime] = Field(None, description="有效开始时间")
    valid_until: Optional[datetime] = Field(None, description="有效结束时间")
    is_active: Optional[bool] = Field(None, description="是否激活")
    notes: Optional[str] = Field(None, description="备注")


class ProductCost(ProductCostBase, TimestampSchema):
    """商品成本模型"""
    id: UUID
    product_id: UUID
    supplier_id: UUID
    supplier: Optional[Supplier] = None


class ProductCostList(BaseSchema):
    """商品成本列表模型"""
    product_id: UUID
    items: List[ProductCost]
    total: int


# 分析相关模型
class TrendPoint(BaseSchema):
    """趋势点模型"""
    timestamp: datetime
    value: Optional[Decimal]
    change_rate: Optional[Decimal] = Field(None, description="变化率")
    data_points: int = Field(0, description="数据点数量")


class TrendStatistics(BaseSchema):
    """趋势统计模型"""
    avg_value: Optional[Decimal] = Field(None, description="平均值")
    max_value: Optional[Decimal] = Field(None, description="最大值")
    min_value: Optional[Decimal] = Field(None, description="最小值")
    volatility: Optional[Decimal] = Field(None, description="波动率")
    trend_direction: Optional[str] = Field(None, description="趋势方向")
    avg_growth_rate: Optional[Decimal] = Field(None, description="平均增长率")


class PriceTrend(BaseSchema):
    """价格趋势模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    trend_points: List[TrendPoint]
    statistics: TrendStatistics


class SalesTrend(BaseSchema):
    """销量趋势模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    trend_points: List[TrendPoint]
    statistics: TrendStatistics


class InventoryTrend(BaseSchema):
    """库存趋势模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    trend_points: List[TrendPoint]
    current_stock: int
    stock_trend: str
    low_stock_risk: bool


class RatingTrend(BaseSchema):
    """好评率趋势模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    trend_points: List[TrendPoint]
    statistics: TrendStatistics


class ComprehensiveAnalysis(BaseSchema):
    """综合分析模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    price_analysis: PriceTrend
    sales_analysis: SalesTrend
    inventory_analysis: InventoryTrend
    rating_analysis: RatingTrend
    overall_score: Decimal = Field(..., ge=0, le=10, description="综合评分")
    recommendations: List[str] = Field(default_factory=list, description="建议")


# 利润分析相关模型
class ProfitAnalysis(BaseSchema):
    """利润分析模型"""
    product_id: UUID
    time_range: Dict[str, Any]
    current_profit_rate: Decimal = Field(..., description="当前利润率")
    profit_trend: List[TrendPoint]
    cost_analysis: Dict[str, Any]
    supplier_comparison: List[Dict[str, Any]]


class ProfitOpportunity(BaseSchema):
    """利润机会模型"""
    product_id: UUID
    product_title: str
    current_price: Decimal
    best_cost: Decimal
    profit_rate: Decimal
    potential_profit: Decimal
    supplier_name: str
    opportunity_score: Decimal


# 监控任务相关模型
class MonitoringTaskBase(BaseSchema):
    """监控任务基础模型"""
    task_type: str = Field(..., max_length=50, description="任务类型")
    priority: int = Field(5, ge=1, le=10, description="优先级")
    scheduled_at: datetime = Field(..., description="计划执行时间")
    max_retries: int = Field(3, ge=0, le=10, description="最大重试次数")


class MonitoringTaskCreate(MonitoringTaskBase):
    """创建监控任务模型"""
    product_id: UUID


class MonitoringTask(MonitoringTaskBase, TimestampSchema):
    """监控任务模型"""
    id: UUID
    product_id: UUID
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    error_message: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None


# 预警相关模型
class AlertBase(BaseSchema):
    """预警基础模型"""
    alert_type: str = Field(..., max_length=50, description="预警类型")
    severity: str = Field(..., max_length=20, description="严重程度")
    title: str = Field(..., max_length=200, description="预警标题")
    message: str = Field(..., description="预警消息")
    data: Optional[Dict[str, Any]] = Field(None, description="相关数据")


class AlertCreate(AlertBase):
    """创建预警模型"""
    product_id: Optional[UUID] = None


class Alert(AlertBase, TimestampSchema):
    """预警模型"""
    id: UUID
    product_id: Optional[UUID] = None
    is_read: bool = False
    is_resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolved_by: Optional[str] = None


# 响应模型
class ResponseModel(BaseSchema):
    """通用响应模型"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None


class ErrorResponse(BaseSchema):
    """错误响应模型"""
    error: bool = True
    message: str
    status_code: int
    path: str
