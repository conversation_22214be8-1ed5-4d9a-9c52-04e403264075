/**
 * 供货商相关API
 */

import { api } from './api';
import { Supplier, SupplierForm, PaginatedResponse, SearchParams, ApiResponse } from '../types';

// 供货商查询参数
interface SupplierQueryParams {
  page?: number;
  page_size?: number;
  search?: SearchParams;
}

export const supplierApi = {
  // 获取供货商列表
  getSuppliers: (params?: SupplierQueryParams): Promise<ApiResponse<PaginatedResponse<Supplier>>> => {
    return api.get('/api/v1/suppliers/', { params });
  },

  // 获取供货商详情
  getSupplierById: (supplierId: string): Promise<ApiResponse<Supplier>> => {
    return api.get(`/api/v1/suppliers/${supplierId}`);
  },

  // 创建供货商
  createSupplier: (supplierData: SupplierForm): Promise<ApiResponse<Supplier>> => {
    return api.post('/api/v1/suppliers/', supplierData);
  },

  // 更新供货商
  updateSupplier: (supplierId: string, supplierData: Partial<SupplierForm>): Promise<ApiResponse<Supplier>> => {
    return api.put(`/api/v1/suppliers/${supplierId}`, supplierData);
  },

  // 删除供货商
  deleteSupplier: (supplierId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/api/v1/suppliers/${supplierId}`);
  },

  // 获取供货商的商品列表
  getSupplierProducts: (supplierId: string): Promise<ApiResponse<any[]>> => {
    return api.get(`/api/v1/suppliers/${supplierId}/products`);
  },

  // 获取供货商统计信息
  getSupplierStats: (supplierId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/v1/suppliers/${supplierId}/stats`);
  },
};
