"""
会话管理器

实现用户会话的创建、管理、验证和清理功能
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from collections import defaultdict

from .models import Session, User
from .jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, TokenType
from app.core.logging import get_logger

logger = get_logger(__name__)


class SessionManager:
    """会话管理器"""
    
    def __init__(self, jwt_handler: <PERSON><PERSON><PERSON><PERSON><PERSON>, data_dir: str = "session_data"):
        self.jwt_handler = jwt_handler
        self.data_dir = data_dir
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 会话存储
        self.sessions: Dict[str, Session] = {}
        self.user_sessions: Dict[str, Set[str]] = defaultdict(set)  # user_id -> session_ids
        
        # 会话配置
        self.session_config = {
            "max_concurrent_sessions_per_user": 5,
            "session_timeout_minutes": 480,  # 8小时
            "cleanup_interval_minutes": 60,   # 1小时清理一次
            "remember_me_days": 30,          # 记住我功能30天
            "force_logout_on_password_change": True,
            "track_device_info": True
        }
        
        # 会话统计
        self.session_stats = {
            "total_created": 0,
            "total_expired": 0,
            "total_revoked": 0,
            "active_sessions": 0,
            "concurrent_sessions_peak": 0
        }
        
        # 加载已存在的会话
        self._load_sessions()
    
    def create_session(self, user: User, ip_address: str = "", user_agent: str = "",
                      device_info: str = "", remember_me: bool = False) -> Session:
        """
        创建用户会话
        
        Args:
            user: 用户对象
            ip_address: IP地址
            user_agent: 用户代理
            device_info: 设备信息
            remember_me: 是否记住登录
        
        Returns:
            Session: 会话对象
        """
        try:
            # 检查并发会话限制
            self._enforce_concurrent_session_limit(user.user_id, user.max_concurrent_sessions)
            
            # 生成JWT令牌对
            user_payload = {
                "user_id": user.user_id,
                "username": user.username,
                "role": user.role.value,
                "permissions": [p.value for p in user.permissions]
            }
            
            access_token, refresh_token = self.jwt_handler.generate_token_pair(user_payload)
            
            # 设置会话过期时间
            if remember_me:
                expires_at = datetime.now() + timedelta(days=self.session_config["remember_me_days"])
            else:
                expires_at = datetime.now() + timedelta(minutes=user.session_timeout_minutes)
            
            # 创建会话对象
            session = Session(
                session_id="",  # 将在__post_init__中生成
                user_id=user.user_id,
                access_token=access_token,
                refresh_token=refresh_token,
                ip_address=ip_address,
                user_agent=user_agent,
                device_info=device_info,
                expires_at=expires_at
            )
            
            # 存储会话
            self.sessions[session.session_id] = session
            self.user_sessions[user.user_id].add(session.session_id)
            
            # 更新统计
            self.session_stats["total_created"] += 1
            self.session_stats["active_sessions"] = len([s for s in self.sessions.values() if s.is_valid()])
            self.session_stats["concurrent_sessions_peak"] = max(
                self.session_stats["concurrent_sessions_peak"],
                len(self.user_sessions[user.user_id])
            )
            
            # 保存会话到磁盘
            self._save_session(session)
            
            logger.info(f"创建会话成功: 用户{user.username}, 会话ID{session.session_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """
        获取会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            Optional[Session]: 会话对象
        """
        try:
            session = self.sessions.get(session_id)
            if session and session.is_valid():
                # 更新最后访问时间
                session.last_accessed_at = datetime.now()
                self._save_session(session)
                return session
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return None
    
    def get_session_by_token(self, access_token: str) -> Optional[Session]:
        """
        通过访问令牌获取会话
        
        Args:
            access_token: 访问令牌
        
        Returns:
            Optional[Session]: 会话对象
        """
        try:
            for session in self.sessions.values():
                if session.access_token == access_token and session.is_valid():
                    session.last_accessed_at = datetime.now()
                    self._save_session(session)
                    return session
            
            return None
            
        except Exception as e:
            logger.error(f"通过令牌获取会话失败: {e}")
            return None
    
    def refresh_session(self, refresh_token: str) -> Optional[Session]:
        """
        刷新会话
        
        Args:
            refresh_token: 刷新令牌
        
        Returns:
            Optional[Session]: 更新后的会话对象
        """
        try:
            # 查找对应的会话
            session = None
            for s in self.sessions.values():
                if s.refresh_token == refresh_token and s.is_valid():
                    session = s
                    break
            
            if not session:
                logger.warning("未找到有效的刷新令牌对应的会话")
                return None
            
            # 刷新JWT令牌
            token_result = self.jwt_handler.refresh_token(refresh_token)
            if not token_result:
                logger.warning("JWT令牌刷新失败")
                return None
            
            new_access_token, new_refresh_token = token_result
            
            # 更新会话
            session.access_token = new_access_token
            session.refresh_token = new_refresh_token
            session.last_accessed_at = datetime.now()
            
            # 延长会话时间
            session.extend_session(hours=8)
            
            # 保存更新后的会话
            self._save_session(session)
            
            logger.info(f"刷新会话成功: 会话ID{session.session_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"刷新会话失败: {e}")
            return None
    
    def revoke_session(self, session_id: str) -> bool:
        """
        撤销会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            bool: 是否成功撤销
        """
        try:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            # 撤销会话
            session.revoke()
            
            # 撤销JWT令牌
            self.jwt_handler.revoke_token(session.access_token)
            self.jwt_handler.revoke_token(session.refresh_token)
            
            # 从用户会话集合中移除
            self.user_sessions[session.user_id].discard(session_id)
            
            # 更新统计
            self.session_stats["total_revoked"] += 1
            self.session_stats["active_sessions"] = len([s for s in self.sessions.values() if s.is_valid()])
            
            # 保存更新后的会话
            self._save_session(session)
            
            logger.info(f"撤销会话成功: 会话ID{session_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"撤销会话失败: {e}")
            return False
    
    def revoke_user_sessions(self, user_id: str, exclude_session_id: str = None) -> int:
        """
        撤销用户的所有会话
        
        Args:
            user_id: 用户ID
            exclude_session_id: 排除的会话ID（通常是当前会话）
        
        Returns:
            int: 撤销的会话数量
        """
        try:
            session_ids = self.user_sessions.get(user_id, set()).copy()
            revoked_count = 0
            
            for session_id in session_ids:
                if session_id != exclude_session_id:
                    if self.revoke_session(session_id):
                        revoked_count += 1
            
            logger.info(f"撤销用户{user_id}的{revoked_count}个会话")
            
            return revoked_count
            
        except Exception as e:
            logger.error(f"撤销用户会话失败: {e}")
            return 0
    
    def get_user_sessions(self, user_id: str) -> List[Session]:
        """
        获取用户的所有活跃会话
        
        Args:
            user_id: 用户ID
        
        Returns:
            List[Session]: 会话列表
        """
        try:
            session_ids = self.user_sessions.get(user_id, set())
            sessions = []
            
            for session_id in session_ids:
                session = self.sessions.get(session_id)
                if session and session.is_valid():
                    sessions.append(session)
            
            return sessions
            
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理过期会话
        
        Returns:
            int: 清理的会话数量
        """
        try:
            expired_sessions = []
            
            for session_id, session in self.sessions.items():
                if session.is_expired() or session.is_revoked:
                    expired_sessions.append(session_id)
            
            # 清理过期会话
            for session_id in expired_sessions:
                session = self.sessions[session_id]
                
                # 从用户会话集合中移除
                self.user_sessions[session.user_id].discard(session_id)
                
                # 删除会话文件
                self._delete_session_file(session_id)
                
                # 从内存中移除
                del self.sessions[session_id]
            
            # 更新统计
            self.session_stats["total_expired"] += len(expired_sessions)
            self.session_stats["active_sessions"] = len([s for s in self.sessions.values() if s.is_valid()])
            
            if expired_sessions:
                logger.info(f"清理了{len(expired_sessions)}个过期会话")
            
            return len(expired_sessions)
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0
    
    def _enforce_concurrent_session_limit(self, user_id: str, max_sessions: int):
        """强制执行并发会话限制"""
        try:
            user_session_ids = self.user_sessions.get(user_id, set())
            active_sessions = []
            
            # 获取活跃会话
            for session_id in user_session_ids.copy():
                session = self.sessions.get(session_id)
                if session and session.is_valid():
                    active_sessions.append(session)
                else:
                    # 清理无效会话
                    user_session_ids.discard(session_id)
            
            # 如果超过限制，撤销最旧的会话
            if len(active_sessions) >= max_sessions:
                # 按创建时间排序，撤销最旧的会话
                active_sessions.sort(key=lambda s: s.created_at)
                sessions_to_revoke = active_sessions[:len(active_sessions) - max_sessions + 1]
                
                for session in sessions_to_revoke:
                    self.revoke_session(session.session_id)
                    logger.info(f"因并发会话限制撤销会话: {session.session_id}")
            
        except Exception as e:
            logger.error(f"强制执行并发会话限制失败: {e}")
    
    def _save_session(self, session: Session):
        """保存会话到磁盘"""
        try:
            session_file = os.path.join(self.data_dir, f"{session.session_id}.json")
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存会话失败: {e}")
    
    def _load_sessions(self):
        """从磁盘加载会话"""
        try:
            if not os.path.exists(self.data_dir):
                return
            
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json'):
                    session_file = os.path.join(self.data_dir, filename)
                    try:
                        with open(session_file, 'r', encoding='utf-8') as f:
                            session_data = json.load(f)
                        
                        # 重建会话对象
                        session = Session(
                            session_id=session_data["session_id"],
                            user_id=session_data["user_id"],
                            access_token="",  # 令牌不持久化存储
                            refresh_token="",
                            ip_address=session_data.get("ip_address", ""),
                            user_agent=session_data.get("user_agent", ""),
                            device_info=session_data.get("device_info", ""),
                            created_at=datetime.fromisoformat(session_data["created_at"]),
                            last_accessed_at=datetime.fromisoformat(session_data["last_accessed_at"]),
                            expires_at=datetime.fromisoformat(session_data["expires_at"]),
                            is_active=session_data.get("is_active", True),
                            is_revoked=session_data.get("is_revoked", False)
                        )
                        
                        # 只加载有效会话
                        if session.is_valid():
                            self.sessions[session.session_id] = session
                            self.user_sessions[session.user_id].add(session.session_id)
                        else:
                            # 删除过期会话文件
                            os.remove(session_file)
                            
                    except Exception as e:
                        logger.error(f"加载会话文件{filename}失败: {e}")
                        # 删除损坏的会话文件
                        try:
                            os.remove(session_file)
                        except:
                            pass
            
            logger.info(f"加载了{len(self.sessions)}个有效会话")
            
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
    
    def _delete_session_file(self, session_id: str):
        """删除会话文件"""
        try:
            session_file = os.path.join(self.data_dir, f"{session_id}.json")
            if os.path.exists(session_file):
                os.remove(session_file)
                
        except Exception as e:
            logger.error(f"删除会话文件失败: {e}")
    
    def get_session_statistics(self) -> Dict:
        """获取会话统计信息"""
        return {
            "stats": self.session_stats.copy(),
            "config": self.session_config.copy(),
            "current_active_sessions": len([s for s in self.sessions.values() if s.is_valid()]),
            "total_sessions": len(self.sessions),
            "users_with_sessions": len([uid for uid, sids in self.user_sessions.items() if sids])
        }
