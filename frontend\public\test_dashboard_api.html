<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .stat-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 仪表板API测试</h1>
        <p>测试修复后的仪表板API功能</p>
        
        <div class="test-section">
            <h3>1. 仪表板统计数据测试</h3>
            <p>测试 /api/v1/system/dashboard/stats 端点</p>
            <button onclick="testDashboardStats()">获取仪表板统计</button>
            <div id="dashboardResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 系统健康检查测试</h3>
            <p>测试 /health 端点</p>
            <button onclick="testHealthCheck()">健康检查</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 系统信息测试</h3>
            <p>测试 /api/v1/system/info 端点</p>
            <button onclick="testSystemInfo()">获取系统信息</button>
            <div id="systemInfoResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testDashboardStats() {
            const resultDiv = document.getElementById('dashboardResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在获取仪表板统计数据...';
            
            try {
                const response = await fetch('/api/v1/system/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    
                    // 创建统计卡片显示
                    const statsHtml = `
✅ 仪表板统计数据获取成功!

📊 核心统计:
  • 商品总数: ${data.total_products}
  • 活跃监控: ${data.active_monitors}
  • 价格记录: ${data.total_price_records}
  • 系统健康: ${data.system_health}
  • 最近告警: ${data.recent_alerts}

📈 今日统计:
  • 新增商品: ${data.today_stats?.new_products || 0}
  • 价格更新: ${data.today_stats?.price_updates || 0}
  • 生成告警: ${data.today_stats?.alerts_generated || 0}
  • 系统正常运行时间: ${data.today_stats?.system_uptime || 'N/A'}

⚡ 性能指标:
  • 平均响应时间: ${data.performance_metrics?.avg_response_time || 0}ms
  • CPU使用率: ${data.performance_metrics?.cpu_usage || 0}%
  • 内存使用率: ${data.performance_metrics?.memory_usage || 0}%
  • 磁盘使用率: ${data.performance_metrics?.disk_usage || 0}%

🕒 最后更新: ${data.last_updated}

🎉 API端点修复成功！前端可以正常获取仪表板数据！`;
                    
                    resultDiv.textContent = statsHtml;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败!\n状态码: ${response.status}\n错误: ${data.detail || data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!\n错误: ${error.message}`;
            }
        }

        async function testHealthCheck() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在进行健康检查...';
            
            try {
                const response = await fetch('/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 健康检查通过!\n状态: ${data.status}\n服务状态:\n${JSON.stringify(data.services, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 健康检查失败!\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!\n错误: ${error.message}`;
            }
        }

        async function testSystemInfo() {
            const resultDiv = document.getElementById('systemInfoResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在获取系统信息...';
            
            try {
                const response = await fetch('/api/v1/system/info', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 系统信息获取成功!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败!\n状态码: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误!\n错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
