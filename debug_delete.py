#!/usr/bin/env python3
"""
调试删除功能的脚本
"""

import asyncio
import httpx
import json


async def debug_delete():
    """调试删除功能"""
    print("🔍 调试供货商删除功能...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=30.0) as client:
        try:
            # 1. 先创建一个供货商
            print("  ➕ 创建测试供货商...")
            import datetime
            timestamp = datetime.datetime.now().strftime("%H%M%S")
            supplier_data = {
                "name": f"删除测试供货商_{timestamp}",
                "contact_person": "测试联系人",
                "phone": "13800138000",
                "email": f"delete{timestamp}@test.com",
                "is_active": True,
                "rating": 4.0
            }
            
            response = await client.post("/api/v1/suppliers/", json=supplier_data)
            if response.status_code == 200:
                supplier = response.json()
                supplier_id = supplier["id"]
                print(f"    ✅ 创建成功 - ID: {supplier_id}")
                
                # 2. 尝试软删除
                print("  🗑️ 尝试软删除...")
                response = await client.delete(f"/api/v1/suppliers/{supplier_id}")
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"    ✅ 删除成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"    ❌ 删除失败")
                    try:
                        error_data = response.json()
                        print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                    except:
                        print(f"    错误内容: {response.text}")
                
            else:
                print(f"    ❌ 创建失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 调试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(debug_delete())
