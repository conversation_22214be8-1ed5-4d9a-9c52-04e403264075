"""
密码管理器

实现密码安全存储、验证、强度检查等功能
"""

import hashlib
import secrets
import re
from typing import Tuple, Dict, List
from datetime import datetime, timedelta

from app.core.logging import get_logger

logger = get_logger(__name__)


class PasswordStrength:
    """密码强度等级"""
    VERY_WEAK = 1
    WEAK = 2
    FAIR = 3
    GOOD = 4
    STRONG = 5


class PasswordManager:
    """密码管理器"""
    
    def __init__(self):
        # 密码策略配置
        self.password_policy = {
            "min_length": 8,
            "max_length": 128,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_digits": True,
            "require_special_chars": True,
            "min_special_chars": 1,
            "forbidden_patterns": [
                r"(.)\1{3,}",  # 连续重复字符（4个或更多）
                r"(0123|1234|2345|3456|4567|5678|6789)",  # 连续数字（4位或更多）
                r"(abcd|bcde|cdef|defg|efgh|fghi|ghij|hijk|ijkl|jklm|klmn|lmno|mnop|nopq|opqr|pqrs|qrst|rstu|stuv|tuvw|uvwx|vwxy|wxyz)",  # 连续字母（4位或更多）
            ],
            "common_passwords": [
                "password", "123456", "123456789", "12345678", "12345",
                "1234567", "admin", "administrator", "root", "user",
                "guest", "test", "demo", "qwerty", "abc123"
            ]
        }
        
        # 哈希配置
        self.hash_config = {
            "algorithm": "pbkdf2_sha256",
            "iterations": 100000,
            "salt_length": 32
        }
        
        # 密码历史记录（防止重复使用）
        self.password_history_limit = 5
    
    def generate_salt(self) -> str:
        """生成随机盐值"""
        try:
            salt_bytes = secrets.token_bytes(self.hash_config["salt_length"])
            return salt_bytes.hex()
        except Exception as e:
            logger.error(f"生成盐值失败: {e}")
            raise
    
    def hash_password(self, password: str, salt: str = None) -> Tuple[str, str]:
        """
        哈希密码
        
        Args:
            password: 明文密码
            salt: 盐值，如果为None则自动生成
        
        Returns:
            Tuple[str, str]: (密码哈希, 盐值)
        """
        try:
            if salt is None:
                salt = self.generate_salt()
            
            # 使用PBKDF2进行哈希
            password_bytes = password.encode('utf-8')
            salt_bytes = bytes.fromhex(salt)
            
            hash_bytes = hashlib.pbkdf2_hmac(
                'sha256',
                password_bytes,
                salt_bytes,
                self.hash_config["iterations"]
            )
            
            password_hash = hash_bytes.hex()
            
            return password_hash, salt
            
        except Exception as e:
            logger.error(f"密码哈希失败: {e}")
            raise
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的密码哈希
            salt: 盐值
        
        Returns:
            bool: 密码是否正确
        """
        try:
            # 使用相同的盐值重新哈希
            computed_hash, _ = self.hash_password(password, salt)
            
            # 使用安全的比较方法防止时序攻击
            return secrets.compare_digest(computed_hash, password_hash)
            
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False
    
    def check_password_strength(self, password: str) -> Dict:
        """
        检查密码强度
        
        Args:
            password: 密码
        
        Returns:
            Dict: 密码强度分析结果
        """
        try:
            result = {
                "strength": PasswordStrength.VERY_WEAK,
                "score": 0,
                "issues": [],
                "suggestions": [],
                "is_valid": False
            }
            
            # 长度检查
            if len(password) < self.password_policy["min_length"]:
                result["issues"].append(f"密码长度至少需要{self.password_policy['min_length']}位")
                result["suggestions"].append("增加密码长度")
            elif len(password) > self.password_policy["max_length"]:
                result["issues"].append(f"密码长度不能超过{self.password_policy['max_length']}位")
            else:
                result["score"] += 1
            
            # 大写字母检查
            if self.password_policy["require_uppercase"]:
                if not re.search(r'[A-Z]', password):
                    result["issues"].append("密码必须包含大写字母")
                    result["suggestions"].append("添加大写字母")
                else:
                    result["score"] += 1
            
            # 小写字母检查
            if self.password_policy["require_lowercase"]:
                if not re.search(r'[a-z]', password):
                    result["issues"].append("密码必须包含小写字母")
                    result["suggestions"].append("添加小写字母")
                else:
                    result["score"] += 1
            
            # 数字检查
            if self.password_policy["require_digits"]:
                if not re.search(r'\d', password):
                    result["issues"].append("密码必须包含数字")
                    result["suggestions"].append("添加数字")
                else:
                    result["score"] += 1
            
            # 特殊字符检查
            if self.password_policy["require_special_chars"]:
                special_chars = re.findall(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password)
                if len(special_chars) < self.password_policy["min_special_chars"]:
                    result["issues"].append(f"密码必须包含至少{self.password_policy['min_special_chars']}个特殊字符")
                    result["suggestions"].append("添加特殊字符 (!@#$%^&*等)")
                else:
                    result["score"] += 1
            
            # 禁用模式检查
            for pattern in self.password_policy["forbidden_patterns"]:
                if re.search(pattern, password.lower()):
                    result["issues"].append("密码包含不安全的模式（如连续字符或数字）")
                    result["suggestions"].append("避免使用连续的字符或数字")
                    break
            else:
                result["score"] += 1
            
            # 常见密码检查
            if password.lower() in self.password_policy["common_passwords"]:
                result["issues"].append("密码过于常见，容易被破解")
                result["suggestions"].append("使用更复杂和独特的密码")
            else:
                result["score"] += 1
            
            # 计算强度等级
            if result["score"] >= 6:
                result["strength"] = PasswordStrength.STRONG
            elif result["score"] >= 5:
                result["strength"] = PasswordStrength.GOOD
            elif result["score"] >= 4:
                result["strength"] = PasswordStrength.FAIR
            elif result["score"] >= 2:
                result["strength"] = PasswordStrength.WEAK
            else:
                result["strength"] = PasswordStrength.VERY_WEAK
            
            # 密码是否符合策略
            result["is_valid"] = len(result["issues"]) == 0
            
            return result
            
        except Exception as e:
            logger.error(f"密码强度检查失败: {e}")
            return {
                "strength": PasswordStrength.VERY_WEAK,
                "score": 0,
                "issues": ["密码检查失败"],
                "suggestions": [],
                "is_valid": False
            }
    
    def generate_secure_password(self, length: int = 12) -> str:
        """
        生成安全密码
        
        Args:
            length: 密码长度
        
        Returns:
            str: 生成的安全密码
        """
        try:
            if length < self.password_policy["min_length"]:
                length = self.password_policy["min_length"]
            elif length > self.password_policy["max_length"]:
                length = self.password_policy["max_length"]
            
            # 字符集
            lowercase = "abcdefghijklmnopqrstuvwxyz"
            uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            digits = "0123456789"
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            
            # 确保包含各种字符类型
            password_chars = []
            
            if self.password_policy["require_lowercase"]:
                password_chars.append(secrets.choice(lowercase))
            
            if self.password_policy["require_uppercase"]:
                password_chars.append(secrets.choice(uppercase))
            
            if self.password_policy["require_digits"]:
                password_chars.append(secrets.choice(digits))
            
            if self.password_policy["require_special_chars"]:
                for _ in range(self.password_policy["min_special_chars"]):
                    password_chars.append(secrets.choice(special_chars))
            
            # 填充剩余长度
            all_chars = lowercase + uppercase + digits + special_chars
            remaining_length = length - len(password_chars)
            
            for _ in range(remaining_length):
                password_chars.append(secrets.choice(all_chars))
            
            # 随机打乱字符顺序
            secrets.SystemRandom().shuffle(password_chars)
            
            password = ''.join(password_chars)
            
            # 验证生成的密码是否符合策略
            strength_check = self.check_password_strength(password)
            if not strength_check["is_valid"]:
                # 如果不符合策略，递归重新生成
                return self.generate_secure_password(length)
            
            return password
            
        except Exception as e:
            logger.error(f"生成安全密码失败: {e}")
            raise
    
    def is_password_in_history(self, password: str, password_history: List[Tuple[str, str]]) -> bool:
        """
        检查密码是否在历史记录中
        
        Args:
            password: 新密码
            password_history: 历史密码列表 [(hash, salt), ...]
        
        Returns:
            bool: 是否在历史记录中
        """
        try:
            for password_hash, salt in password_history:
                if self.verify_password(password, password_hash, salt):
                    return True
            return False
            
        except Exception as e:
            logger.error(f"检查密码历史失败: {e}")
            return False
    
    def get_password_policy(self) -> Dict:
        """获取密码策略"""
        return self.password_policy.copy()
    
    def update_password_policy(self, policy_updates: Dict):
        """
        更新密码策略
        
        Args:
            policy_updates: 策略更新
        """
        try:
            self.password_policy.update(policy_updates)
            logger.info("密码策略已更新")
            
        except Exception as e:
            logger.error(f"更新密码策略失败: {e}")
            raise
    
    def get_password_strength_text(self, strength: int) -> str:
        """获取密码强度文本描述"""
        strength_texts = {
            PasswordStrength.VERY_WEAK: "非常弱",
            PasswordStrength.WEAK: "弱",
            PasswordStrength.FAIR: "一般",
            PasswordStrength.GOOD: "良好",
            PasswordStrength.STRONG: "强"
        }
        return strength_texts.get(strength, "未知")
    
    def generate_password_reset_token(self) -> str:
        """生成密码重置令牌"""
        try:
            return secrets.token_urlsafe(32)
        except Exception as e:
            logger.error(f"生成密码重置令牌失败: {e}")
            raise
