# Moniit 部署运维指南

## 📋 目录

1. [部署架构](#部署架构)
2. [环境准备](#环境准备)
3. [生产环境部署](#生产环境部署)
4. [监控与日志](#监控与日志)
5. [备份与恢复](#备份与恢复)
6. [性能优化](#性能优化)
7. [安全配置](#安全配置)
8. [故障排除](#故障排除)

## 🏗️ 部署架构

### 推荐架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx/HAProxy]
    end
    
    subgraph "应用层"
        APP1[Moniit App 1]
        APP2[Moniit App 2]
        APP3[Moniit App 3]
    end
    
    subgraph "任务队列"
        CELERY[Celery Workers]
        REDIS[Redis]
    end
    
    subgraph "数据层"
        DB[(TimescaleDB)]
        CACHE[(Redis Cache)]
    end
    
    subgraph "监控层"
        PROM[Prometheus]
        GRAF[Grafana]
        ELK[ELK Stack]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> DB
    APP2 --> DB
    APP3 --> DB
    
    APP1 --> CACHE
    APP2 --> CACHE
    APP3 --> CACHE
    
    CELERY --> REDIS
    CELERY --> DB
    
    PROM --> APP1
    PROM --> APP2
    PROM --> APP3
    GRAF --> PROM
```

### 组件说明

**Web应用层**:
- **Nginx**: 反向代理和负载均衡
- **Gunicorn**: WSGI服务器
- **Django**: Web应用框架

**数据存储层**:
- **TimescaleDB**: 时序数据存储
- **Redis**: 缓存和消息队列

**任务处理层**:
- **Celery**: 异步任务处理
- **Celery Beat**: 定时任务调度

**监控层**:
- **Prometheus**: 指标收集
- **Grafana**: 可视化监控
- **ELK Stack**: 日志分析

## 🔧 环境准备

### 系统要求

**最低配置**:
- **CPU**: 4核心
- **内存**: 8GB
- **存储**: 100GB SSD
- **网络**: 100Mbps

**推荐配置**:
- **CPU**: 8核心
- **内存**: 16GB
- **存储**: 500GB SSD
- **网络**: 1Gbps

### 软件依赖

**操作系统**: Ubuntu 20.04 LTS / CentOS 8

**基础软件**:
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git vim htop

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 网络配置

**防火墙设置**:
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 5432  # PostgreSQL (内网)
sudo ufw allow 6379  # Redis (内网)

# 启用防火墙
sudo ufw enable
```

**域名配置**:
```bash
# 配置域名解析
# A记录: moniit.example.com -> 服务器IP
# CNAME记录: api.moniit.example.com -> moniit.example.com
```

## 🚀 生产环境部署

### 1. 代码部署

**克隆代码**:
```bash
# 创建部署目录
sudo mkdir -p /opt/moniit
sudo chown $USER:$USER /opt/moniit
cd /opt/moniit

# 克隆代码
git clone https://github.com/your-org/moniit.git .
git checkout main
```

**环境配置**:
```bash
# 复制环境配置
cp .env.example .env.production

# 编辑生产环境配置
vim .env.production
```

**.env.production 配置示例**:
```bash
# 基础配置
DEBUG=False
SECRET_KEY=your-super-secret-key-here
ALLOWED_HOSTS=moniit.example.com,api.moniit.example.com

# 数据库配置
DATABASE_URL=postgresql://moniit:password@localhost:5432/moniit_prod

# Redis配置
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True

# 安全配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# 监控配置
SENTRY_DSN=https://your-sentry-dsn
```

### 2. 数据库部署

**TimescaleDB安装**:
```bash
# 添加TimescaleDB仓库
sudo sh -c "echo 'deb https://packagecloud.io/timescale/timescaledb/ubuntu/ $(lsb_release -c -s) main' > /etc/apt/sources.list.d/timescaledb.list"
wget --quiet -O - https://packagecloud.io/timescale/timescaledb/gpgkey | sudo apt-key add -
sudo apt update

# 安装TimescaleDB
sudo apt install -y timescaledb-2-postgresql-14

# 配置TimescaleDB
sudo timescaledb-tune --quiet --yes

# 重启PostgreSQL
sudo systemctl restart postgresql
```

**数据库初始化**:
```bash
# 创建数据库用户
sudo -u postgres createuser -P moniit

# 创建数据库
sudo -u postgres createdb -O moniit moniit_prod

# 启用TimescaleDB扩展
sudo -u postgres psql -d moniit_prod -c "CREATE EXTENSION IF NOT EXISTS timescaledb;"
```

### 3. Redis部署

**Redis安装**:
```bash
# 安装Redis
sudo apt install -y redis-server

# 配置Redis
sudo vim /etc/redis/redis.conf
```

**Redis配置优化**:
```bash
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 安全配置
requirepass your-redis-password
bind 127.0.0.1
```

### 4. 应用部署

**Docker Compose部署**:
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENV_FILE=.env.production
    volumes:
      - ./logs:/app/logs
      - ./media:/app/media
    depends_on:
      - db
      - redis
    restart: unless-stopped

  celery:
    build: .
    command: celery -A moniit worker -l info
    environment:
      - ENV_FILE=.env.production
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  celery-beat:
    build: .
    command: celery -A moniit beat -l info
    environment:
      - ENV_FILE=.env.production
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
```

**启动服务**:
```bash
# 构建镜像
docker-compose -f docker-compose.prod.yml build

# 数据库迁移
docker-compose -f docker-compose.prod.yml run --rm web python manage.py migrate

# 创建超级用户
docker-compose -f docker-compose.prod.yml run --rm web python manage.py createsuperuser

# 收集静态文件
docker-compose -f docker-compose.prod.yml run --rm web python manage.py collectstatic --noinput

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

### 5. Nginx配置

**nginx.conf**:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream moniit_backend {
        server web:8000;
    }

    server {
        listen 80;
        server_name moniit.example.com api.moniit.example.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name moniit.example.com api.moniit.example.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        client_max_body_size 100M;

        location / {
            proxy_pass http://moniit_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /app/static/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /app/media/;
            expires 7d;
        }
    }
}
```

## 📊 监控与日志

### 1. Prometheus监控

**prometheus.yml**:
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'moniit'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
```

### 2. Grafana仪表板

**关键指标**:
- **应用指标**: 请求量、响应时间、错误率
- **系统指标**: CPU、内存、磁盘、网络
- **数据库指标**: 连接数、查询性能、锁等待
- **业务指标**: 监控任务数、成功率、数据量

### 3. 日志管理

**日志配置**:
```python
# settings/production.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/moniit.log',
            'maxBytes': 100*1024*1024,  # 100MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/error.log',
            'maxBytes': 100*1024*1024,
            'backupCount': 5,
            'formatter': 'json',
        }
    },
    'root': {
        'handlers': ['file', 'error_file'],
        'level': 'INFO',
    }
}
```

**日志轮转**:
```bash
# /etc/logrotate.d/moniit
/opt/moniit/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        docker-compose -f /opt/moniit/docker-compose.prod.yml restart web
    endscript
}
```

## 💾 备份与恢复

### 1. 数据库备份

**自动备份脚本**:
```bash
#!/bin/bash
# backup_db.sh

BACKUP_DIR="/opt/backups/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="moniit_prod"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U moniit -d $DB_NAME | gzip > $BACKUP_DIR/moniit_${DATE}.sql.gz

# 保留最近30天的备份
find $BACKUP_DIR -name "moniit_*.sql.gz" -mtime +30 -delete

# 上传到云存储（可选）
# aws s3 cp $BACKUP_DIR/moniit_${DATE}.sql.gz s3://your-backup-bucket/database/
```

**定时任务**:
```bash
# 添加到crontab
0 2 * * * /opt/moniit/scripts/backup_db.sh
```

### 2. 文件备份

**应用文件备份**:
```bash
#!/bin/bash
# backup_files.sh

BACKUP_DIR="/opt/backups/files"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/moniit"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/moniit_files_${DATE}.tar.gz \
    --exclude='logs' \
    --exclude='__pycache__' \
    --exclude='.git' \
    $APP_DIR

# 保留最近7天的备份
find $BACKUP_DIR -name "moniit_files_*.tar.gz" -mtime +7 -delete
```

### 3. 恢复流程

**数据库恢复**:
```bash
# 停止应用
docker-compose -f docker-compose.prod.yml stop web celery celery-beat

# 恢复数据库
gunzip -c /opt/backups/database/moniit_20250824_020000.sql.gz | psql -h localhost -U moniit -d moniit_prod

# 重启应用
docker-compose -f docker-compose.prod.yml start web celery celery-beat
```

## ⚡ 性能优化

### 1. 数据库优化

**PostgreSQL配置优化**:
```bash
# postgresql.conf
shared_buffers = 2GB
effective_cache_size = 6GB
maintenance_work_mem = 512MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

**索引优化**:
```sql
-- 价格数据时序索引
CREATE INDEX CONCURRENTLY idx_price_records_time 
ON price_records USING BTREE (recorded_at DESC);

-- 商品查询索引
CREATE INDEX CONCURRENTLY idx_products_platform_category 
ON products (platform, category);

-- 复合索引
CREATE INDEX CONCURRENTLY idx_price_records_product_time 
ON price_records (product_id, recorded_at DESC);
```

### 2. 应用优化

**缓存策略**:
```python
# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/0',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            }
        }
    }
}

# 缓存超时设置
CACHE_TTL = {
    'product_list': 300,      # 5分钟
    'price_data': 600,        # 10分钟
    'analysis_result': 1800,  # 30分钟
}
```

**连接池优化**:
```python
# 数据库连接池
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'moniit_prod',
        'USER': 'moniit',
        'PASSWORD': 'password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        }
    }
}
```

### 3. 系统优化

**内核参数优化**:
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 应用配置
sysctl -p
```

## 🔒 安全配置

### 1. 网络安全

**防火墙配置**:
```bash
# 只允许必要端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

**SSL/TLS配置**:
```bash
# 生成SSL证书（Let's Encrypt）
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d moniit.example.com -d api.moniit.example.com
```

### 2. 应用安全

**Django安全设置**:
```python
# settings/production.py
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
```

### 3. 数据安全

**数据库安全**:
```bash
# 限制数据库访问
# pg_hba.conf
local   all             moniit                                  md5
host    all             moniit          127.0.0.1/32            md5
```

**敏感数据加密**:
```python
# 使用django-cryptography加密敏感字段
from django_cryptography.fields import encrypt

class Product(models.Model):
    name = models.CharField(max_length=200)
    api_key = encrypt(models.CharField(max_length=100))  # 加密存储
```

## 🚨 故障排除

### 1. 常见问题

**应用无法启动**:
```bash
# 检查日志
docker-compose -f docker-compose.prod.yml logs web

# 检查配置
docker-compose -f docker-compose.prod.yml config

# 检查端口占用
netstat -tlnp | grep 8000
```

**数据库连接失败**:
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U moniit -d moniit_prod

# 检查连接数
SELECT count(*) FROM pg_stat_activity;
```

**Redis连接问题**:
```bash
# 检查Redis状态
sudo systemctl status redis

# 测试连接
redis-cli ping

# 检查内存使用
redis-cli info memory
```

### 2. 性能问题

**慢查询分析**:
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000;
SELECT pg_reload_conf();

-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

**内存泄漏排查**:
```bash
# 监控内存使用
watch -n 1 'free -m'

# 查看进程内存
ps aux --sort=-%mem | head

# 分析内存使用
cat /proc/meminfo
```

### 3. 监控告警

**告警规则**:
```yaml
# prometheus_rules.yml
groups:
  - name: moniit_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection count is high"
```

---

## 📞 运维支持

- **运维文档**: https://docs.moniit.com/ops
- **监控面板**: https://monitor.moniit.com
- **告警通知**: <EMAIL>
- **紧急联系**: +86-400-xxx-xxxx

---

*本指南最后更新时间: 2025-08-24*
