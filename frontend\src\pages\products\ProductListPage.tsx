/**
 * 商品列表页面
 */

import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Card,
  Tag,
  Avatar,
  Dropdown,
  Modal,
  Upload,
  message,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  DownloadOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import type { UploadProps } from 'antd';

import { useAppDispatch, useAppSelector } from '../../store';
import { productApi } from '../../services/productApi';
import {
  fetchProductsAsync,
  deleteProductAsync,
  batchDeleteProductsAsync,
  importProductsAsync,
  exportProductsAsync,
  setSearchParams,
  setPage,
  setPageSize,
  toggleProductSelection,
  selectAllProducts,
  clearSelection,
  selectProducts,
  selectProductsLoading,
  selectProductsPagination,
  selectProductsSearchParams,
  selectSelectedProducts,
} from '../../store/slices/productSlice';
import { Product, SearchParams } from '../../types';

const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

const ProductListPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const products = useAppSelector(selectProducts);
  const loading = useAppSelector(selectProductsLoading);
  const pagination = useAppSelector(selectProductsPagination);
  const searchParams = useAppSelector(selectProductsSearchParams);
  const selectedProducts = useAppSelector(selectSelectedProducts);

  const [importModalVisible, setImportModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);

  // 页面加载时获取数据
  useEffect(() => {
    dispatch(fetchProductsAsync({
      page: pagination.page,
      page_size: pagination.pageSize,
      search: searchParams,
    }));
  }, [dispatch, pagination.page, pagination.pageSize, searchParams]);

  // 处理搜索
  const handleSearch = (keyword: string) => {
    dispatch(setSearchParams({ ...searchParams, keyword }));
  };

  // 处理分类筛选
  const handleCategoryChange = (category: string) => {
    dispatch(setSearchParams({ ...searchParams, category: category || undefined }));
  };

  // 处理状态筛选
  const handleStatusChange = (isActive: boolean | undefined) => {
    dispatch(setSearchParams({ ...searchParams, is_active: isActive }));
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    dispatch(setPage(page));
    dispatch(setPageSize(pageSize));
  };

  // 处理行选择
  const handleRowSelection = {
    selectedRowKeys: selectedProducts,
    onChange: (selectedRowKeys: React.Key[]) => {
      dispatch(clearSelection());
      selectedRowKeys.forEach(key => {
        dispatch(toggleProductSelection(key as string));
      });
    },
    onSelectAll: (selected: boolean) => {
      if (selected) {
        dispatch(selectAllProducts());
      } else {
        dispatch(clearSelection());
      }
    },
  };

  // 处理单个删除
  const handleDelete = (productId: string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个商品吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        dispatch(deleteProductAsync(productId));
      },
    });
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedProducts.length === 0) {
      message.warning('请先选择要删除的商品');
      return;
    }

    confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedProducts.length} 个商品吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        dispatch(batchDeleteProductsAsync(selectedProducts));
      },
    });
  };

  // 处理导出
  const handleExport = (format: 'excel' | 'csv') => {
    dispatch(exportProductsAsync({ format, filters: searchParams }));
  };

  // 处理下载模板
  const handleDownloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {
    try {
      setDownloadingTemplate(true);
      await productApi.downloadImportTemplate(format);
      message.success('模板下载成功');
    } catch (error: any) {
      message.error(`模板下载失败：${error.message}`);
    } finally {
      setDownloadingTemplate(false);
    }
  };

  // 处理导入
  const uploadProps: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file) => {
      setUploading(true);
      dispatch(importProductsAsync(file))
        .unwrap()
        .then((result) => {
          message.success(`导入成功：${result.success_count} 个商品`);
          if (result.error_count > 0) {
            message.warning(`导入失败：${result.error_count} 个商品`);
          }
          setImportModalVisible(false);
          // 重新加载数据
          dispatch(fetchProductsAsync({
            page: 1,
            page_size: pagination.pageSize,
            search: searchParams,
          }));
        })
        .catch((error) => {
          message.error(`导入失败：${error.message}`);
        })
        .finally(() => {
          setUploading(false);
        });
      return false; // 阻止自动上传
    },
    showUploadList: false,
  };

  // 表格列定义
  const columns: ColumnsType<Product> = [
    {
      title: '商品信息',
      key: 'info',
      width: 300,
      render: (_, record) => (
        <div className="d-flex align-items-center">
          <Avatar
            size={48}
            src={record.image_url}
            style={{ backgroundColor: '#f0f0f0', marginRight: 12 }}
          >
            {record.name.charAt(0)}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>
              {record.name}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {record.brand && `品牌：${record.brand}`}
              {record.model && ` | 型号：${record.model}`}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 120,
      render: (category) => category ? <Tag>{category}</Tag> : '-',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 180,
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: '查看详情',
                onClick: () => navigate(`/products/${record.product_id}`),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: '编辑',
                onClick: () => navigate(`/products/${record.product_id}?mode=edit`),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: '删除',
                danger: true,
                onClick: () => handleDelete(record.product_id),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题和统计 */}
      <div className="mb-4">
        <h2 style={{ marginBottom: 16 }}>商品管理</h2>
        <Row gutter={16}>
          <Col span={6}>
            <Card size="small">
              <Statistic title="总商品数" value={pagination.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic 
                title="启用商品" 
                value={products.filter(p => p.is_active).length} 
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic 
                title="停用商品" 
                value={products.filter(p => !p.is_active).length} 
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic title="已选择" value={selectedProducts.length} />
            </Card>
          </Col>
        </Row>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-3">
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索商品名称、品牌、型号..."
              allowClear
              onSearch={handleSearch}
              style={{ maxWidth: 400 }}
            />
          </Col>
          <Col>
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 120 }}
              onChange={handleCategoryChange}
            >
              <Option value="电子产品">电子产品</Option>
              <Option value="服装">服装</Option>
              <Option value="家居">家居</Option>
              <Option value="食品">食品</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: 100 }}
              onChange={handleStatusChange}
            >
              <Option value={true}>启用</Option>
              <Option value={false}>停用</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 操作按钮 */}
      <Card className="mb-3">
        <div className="d-flex justify-content-between align-items-center">
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/products/new')}
            >
              新增商品
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              disabled={selectedProducts.length === 0}
            >
              批量删除
            </Button>
          </Space>
          
          <Space>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入商品
            </Button>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'excel',
                    label: '导出为Excel',
                    onClick: () => handleExport('excel'),
                  },
                  {
                    key: 'csv',
                    label: '导出为CSV',
                    onClick: () => handleExport('csv'),
                  },
                ],
              }}
            >
              <Button icon={<DownloadOutlined />}>
                导出商品
              </Button>
            </Dropdown>
          </Space>
        </div>
      </Card>

      {/* 商品表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="product_id"
          loading={loading}
          rowSelection={handleRowSelection}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 导入模态框 */}
      <Modal
        title="导入商品"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <div className="text-center" style={{ padding: '20px 0' }}>
          <Upload.Dragger {...uploadProps} disabled={uploading}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 Excel (.xlsx, .xls) 和 CSV 格式文件
            </p>
          </Upload.Dragger>
          
          <div className="mt-3">
            <Space>
              <Button
                type="link"
                size="small"
                loading={downloadingTemplate}
                onClick={() => handleDownloadTemplate('excel')}
              >
                下载Excel模板
              </Button>
              <Button
                type="link"
                size="small"
                loading={downloadingTemplate}
                onClick={() => handleDownloadTemplate('csv')}
              >
                下载CSV模板
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProductListPage;
