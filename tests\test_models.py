"""
数据模型测试
"""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4

from pydantic import ValidationError
from app.models.schemas import (
    ProductCreate, ProductUpdate, Product,
    SupplierCreate, Supplier,
    ProductCostCreate, ProductCost,
    TrendPoint, TrendStatistics,
    PriceTrend, ComprehensiveAnalysis
)


class TestProductSchemas:
    """商品模型测试"""
    
    def test_product_create_valid(self):
        """测试有效的商品创建模型"""
        product_data = {
            "url": "https://www.1688.com/product/123456.html",
            "platform": "1688",
            "title": "测试商品",
            "title_translated": "Test Product",
            "category": "电子产品",
            "status": "active",
            "monitoring_frequency": 24,
            "is_active": True,
            "tags": {"brand": "测试品牌"},
            "notes": "测试备注"
        }
        
        product = ProductCreate(**product_data)
        assert str(product.url) == "https://www.1688.com/product/123456.html"
        assert product.platform == "1688"
        assert product.title == "测试商品"
        assert product.monitoring_frequency == 24
        assert product.is_active == True
    
    def test_product_create_invalid_url(self):
        """测试无效URL的商品创建"""
        product_data = {
            "url": "invalid-url",
            "platform": "1688"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ProductCreate(**product_data)
        
        assert "url" in str(exc_info.value)
    
    def test_product_create_invalid_frequency(self):
        """测试无效监控频率"""
        product_data = {
            "url": "https://www.1688.com/product/123456.html",
            "platform": "1688",
            "monitoring_frequency": 0  # 无效值
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ProductCreate(**product_data)
        
        assert "monitoring_frequency" in str(exc_info.value)
    
    def test_product_update_partial(self):
        """测试部分更新商品模型"""
        update_data = {
            "title": "更新后的标题",
            "is_active": False
        }
        
        product_update = ProductUpdate(**update_data)
        assert product_update.title == "更新后的标题"
        assert product_update.is_active == False
        # ProductUpdate模型中没有platform字段，检查其他可选字段
        assert product_update.category is None  # 未设置的字段应为None
    
    def test_product_full_model(self):
        """测试完整商品模型"""
        product_data = {
            "id": uuid4(),
            "url": "https://www.1688.com/product/123456.html",
            "platform": "1688",
            "title": "测试商品",
            "title_translated": "Test Product",
            "category": "电子产品",
            "status": "active",
            "monitoring_frequency": 24,
            "is_active": True,
            "tags": {"brand": "测试品牌"},
            "notes": "测试备注",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "last_monitored_at": datetime.utcnow()
        }
        
        product = Product(**product_data)
        assert isinstance(product.id, type(uuid4()))
        assert isinstance(product.created_at, datetime)


class TestSupplierSchemas:
    """供货商模型测试"""
    
    def test_supplier_create_valid(self):
        """测试有效的供货商创建模型"""
        supplier_data = {
            "name": "测试供货商",
            "contact_person": "张经理",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "address": "测试地址",
            "payment_terms": "30天付款",
            "delivery_time": 7,
            "min_order_quantity": 100,
            "is_active": True,
            "rating": Decimal("4.5"),
            "notes": "优质供货商"
        }
        
        supplier = SupplierCreate(**supplier_data)
        assert supplier.name == "测试供货商"
        assert supplier.delivery_time == 7
        assert supplier.rating == Decimal("4.5")
    
    def test_supplier_invalid_rating(self):
        """测试无效评分"""
        supplier_data = {
            "name": "测试供货商",
            "rating": Decimal("6.0")  # 超出范围
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SupplierCreate(**supplier_data)
        
        assert "rating" in str(exc_info.value)
    
    def test_supplier_invalid_delivery_time(self):
        """测试无效交货时间"""
        supplier_data = {
            "name": "测试供货商",
            "delivery_time": 0  # 无效值
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SupplierCreate(**supplier_data)
        
        assert "delivery_time" in str(exc_info.value)


class TestProductCostSchemas:
    """商品成本模型测试"""
    
    def test_product_cost_create_valid(self):
        """测试有效的商品成本创建模型"""
        cost_data = {
            "product_id": uuid4(),
            "supplier_id": uuid4(),
            "unit_cost": Decimal("25.50"),
            "currency": "USD",
            "shipping_cost": Decimal("5.00"),
            "other_costs": Decimal("2.50"),
            "total_cost": Decimal("33.00"),
            "min_quantity": 100,
            "max_quantity": 1000,
            "valid_from": datetime.utcnow(),
            "valid_until": datetime.utcnow() + timedelta(days=90),
            "is_active": True,
            "notes": "测试成本"
        }
        
        cost = ProductCostCreate(**cost_data)
        assert cost.unit_cost == Decimal("25.50")
        assert cost.total_cost == Decimal("33.00")
        assert cost.min_quantity == 100
    
    def test_product_cost_invalid_cost(self):
        """测试无效成本"""
        cost_data = {
            "product_id": uuid4(),
            "supplier_id": uuid4(),
            "unit_cost": Decimal("0"),  # 无效值
            "total_cost": Decimal("10.00"),
            "valid_from": datetime.utcnow()
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ProductCostCreate(**cost_data)
        
        assert "unit_cost" in str(exc_info.value)
    
    def test_product_cost_quantity_validation(self):
        """测试数量验证"""
        cost_data = {
            "product_id": uuid4(),
            "supplier_id": uuid4(),
            "unit_cost": Decimal("25.50"),
            "total_cost": Decimal("33.00"),
            "min_quantity": 1000,
            "max_quantity": 100,  # 最大数量小于最小数量
            "valid_from": datetime.utcnow()
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ProductCostCreate(**cost_data)
        
        assert "最大数量不能小于最小数量" in str(exc_info.value)


class TestAnalysisSchemas:
    """分析模型测试"""
    
    def test_trend_point_valid(self):
        """测试有效的趋势点模型"""
        trend_data = {
            "timestamp": datetime.utcnow(),
            "value": Decimal("29.99"),
            "change_rate": Decimal("0.05"),
            "data_points": 10
        }
        
        trend_point = TrendPoint(**trend_data)
        assert trend_point.value == Decimal("29.99")
        assert trend_point.change_rate == Decimal("0.05")
        assert trend_point.data_points == 10
    
    def test_trend_statistics_valid(self):
        """测试有效的趋势统计模型"""
        stats_data = {
            "avg_value": Decimal("25.50"),
            "max_value": Decimal("35.00"),
            "min_value": Decimal("20.00"),
            "volatility": Decimal("0.15"),
            "trend_direction": "上升",
            "avg_growth_rate": Decimal("0.03")
        }
        
        stats = TrendStatistics(**stats_data)
        assert stats.avg_value == Decimal("25.50")
        assert stats.trend_direction == "上升"
    
    def test_price_trend_valid(self):
        """测试有效的价格趋势模型"""
        trend_data = {
            "product_id": uuid4(),
            "time_range": {"days": 30, "interval": "1d"},
            "trend_points": [
                {
                    "timestamp": datetime.utcnow(),
                    "value": Decimal("29.99"),
                    "data_points": 1
                }
            ],
            "statistics": {
                "avg_value": Decimal("29.99"),
                "max_value": Decimal("29.99"),
                "min_value": Decimal("29.99")
            }
        }
        
        price_trend = PriceTrend(**trend_data)
        assert len(price_trend.trend_points) == 1
        assert price_trend.statistics.avg_value == Decimal("29.99")
    
    def test_comprehensive_analysis_valid(self):
        """测试有效的综合分析模型"""
        analysis_data = {
            "product_id": uuid4(),
            "time_range": {"days": 30},
            "price_analysis": {
                "product_id": uuid4(),
                "time_range": {"days": 30},
                "trend_points": [],
                "statistics": {"avg_value": Decimal("29.99")}
            },
            "sales_analysis": {
                "product_id": uuid4(),
                "time_range": {"days": 30},
                "trend_points": [],
                "statistics": {"avg_value": Decimal("100")}
            },
            "inventory_analysis": {
                "product_id": uuid4(),
                "time_range": {"days": 30},
                "trend_points": [],
                "current_stock": 50,
                "stock_trend": "稳定",
                "low_stock_risk": False
            },
            "rating_analysis": {
                "product_id": uuid4(),
                "time_range": {"days": 30},
                "trend_points": [],
                "statistics": {"avg_value": Decimal("4.5")}
            },
            "overall_score": Decimal("8.5"),
            "recommendations": ["建议增加库存", "价格趋势良好"]
        }
        
        analysis = ComprehensiveAnalysis(**analysis_data)
        assert analysis.overall_score == Decimal("8.5")
        assert len(analysis.recommendations) == 2


class TestModelSerialization:
    """模型序列化测试"""
    
    def test_product_json_serialization(self):
        """测试商品模型JSON序列化"""
        product_data = {
            "id": uuid4(),
            "url": "https://www.1688.com/product/123456.html",
            "platform": "1688",
            "title": "测试商品",
            "status": "active",
            "monitoring_frequency": 24,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        product = Product(**product_data)
        json_data = product.model_dump()
        
        assert "id" in json_data
        assert "url" in json_data
        assert "created_at" in json_data
    
    def test_decimal_serialization(self):
        """测试Decimal类型序列化"""
        cost_data = {
            "product_id": uuid4(),
            "supplier_id": uuid4(),
            "unit_cost": Decimal("25.50"),
            "total_cost": Decimal("33.00"),
            "valid_from": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "id": uuid4()
        }
        
        cost = ProductCost(**cost_data)
        json_data = cost.model_dump()

        # 在Pydantic V2中，model_dump()不会自动应用json_encoders
        # Decimal字段保持为Decimal类型，这是正确的行为
        assert isinstance(json_data["unit_cost"], Decimal)
        assert isinstance(json_data["total_cost"], Decimal)
