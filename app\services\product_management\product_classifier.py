"""
商品分类器

负责商品的自动分类和归档，区分竞品、供应商商品、其他商品
"""

import re
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass
from datetime import datetime

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductCategory, ProductTag

logger = get_logger(__name__)


@dataclass
class ClassificationRule:
    """分类规则"""
    name: str
    product_type: ProductType
    url_patterns: List[str] = None
    keyword_patterns: List[str] = None
    brand_patterns: List[str] = None
    price_range: Tuple[float, float] = None
    seller_patterns: List[str] = None
    priority: int = 1  # 优先级，数字越大优先级越高


@dataclass
class ClassificationResult:
    """分类结果"""
    product_type: ProductType
    confidence: float
    matched_rules: List[str]
    categories: List[ProductCategory]
    tags: List[ProductTag]
    reasoning: str


class ProductClassifier:
    """商品分类器"""
    
    def __init__(self):
        self.classification_rules = self._load_default_rules()
        self.competitor_keywords = self._load_competitor_keywords()
        self.supplier_keywords = self._load_supplier_keywords()
        self.category_mapping = self._load_category_mapping()
        
    def _load_default_rules(self) -> List[ClassificationRule]:
        """加载默认分类规则"""
        return [
            # 竞品识别规则
            ClassificationRule(
                name="competitor_brand_match",
                product_type=ProductType.COMPETITOR,
                brand_patterns=[
                    r"苹果|Apple|iPhone|iPad",
                    r"华为|HUAWEI|荣耀|Honor",
                    r"小米|Xiaomi|红米|Redmi",
                    r"OPPO|OnePlus|一加",
                    r"vivo|iQOO",
                    r"三星|Samsung",
                ],
                priority=3
            ),
            ClassificationRule(
                name="competitor_keyword_match",
                product_type=ProductType.COMPETITOR,
                keyword_patterns=[
                    r"竞品|对标|同款|爆款",
                    r"热销|畅销|网红款",
                    r"品牌|正品|官方",
                ],
                priority=2
            ),
            
            # 供货商商品识别规则
            ClassificationRule(
                name="supplier_keyword_match",
                product_type=ProductType.SUPPLIER,
                keyword_patterns=[
                    r"工厂|厂家|生产|制造",
                    r"批发|代发|一件代发",
                    r"现货|库存|大量",
                    r"定制|OEM|ODM",
                ],
                priority=2
            ),
            ClassificationRule(
                name="supplier_seller_match",
                product_type=ProductType.SUPPLIER,
                seller_patterns=[
                    r".*工厂.*|.*厂.*",
                    r".*批发.*|.*贸易.*",
                    r".*制造.*|.*生产.*",
                ],
                priority=2
            ),
            ClassificationRule(
                name="supplier_price_range",
                product_type=ProductType.SUPPLIER,
                price_range=(1.0, 100.0),  # 低价区间通常是供货商
                priority=1
            ),
            
            # 1688平台特殊规则
            ClassificationRule(
                name="1688_supplier_default",
                product_type=ProductType.SUPPLIER,
                url_patterns=[r".*1688\.com.*"],
                priority=1
            ),
        ]
    
    def _load_competitor_keywords(self) -> Set[str]:
        """加载竞品关键词"""
        return {
            # 品牌关键词
            "苹果", "华为", "小米", "OPPO", "vivo", "三星",
            "Apple", "HUAWEI", "Xiaomi", "Samsung",
            
            # 产品关键词
            "iPhone", "iPad", "MacBook", "AirPods",
            "华为P", "华为Mate", "小米手机", "红米",
            
            # 竞品标识词
            "竞品", "对标", "同款", "爆款", "热销",
            "品牌", "正品", "官方", "旗舰店",
        }
    
    def _load_supplier_keywords(self) -> Set[str]:
        """加载供货商关键词"""
        return {
            # 供应商标识词
            "工厂", "厂家", "生产", "制造", "批发",
            "代发", "一件代发", "现货", "库存",
            "定制", "OEM", "ODM", "贴牌",
            
            # 价格相关
            "起订", "批量", "大量", "优惠",
            "出厂价", "批发价", "成本价",
        }
    
    def _load_category_mapping(self) -> Dict[str, List[str]]:
        """加载分类映射"""
        return {
            "手机数码": [
                "手机", "平板", "电脑", "笔记本", "数码",
                "iPhone", "iPad", "手机壳", "充电器", "耳机"
            ],
            "服装鞋包": [
                "服装", "衣服", "裤子", "裙子", "鞋子",
                "包包", "背包", "钱包", "帽子", "围巾"
            ],
            "家居用品": [
                "家居", "家具", "床上用品", "厨具", "餐具",
                "收纳", "装饰", "灯具", "窗帘", "地毯"
            ],
            "美妆护肤": [
                "化妆品", "护肤品", "面膜", "口红", "香水",
                "洗发水", "护发", "美容", "彩妆", "护理"
            ],
            "食品饮料": [
                "食品", "零食", "饮料", "茶叶", "咖啡",
                "保健品", "营养品", "水果", "坚果", "糖果"
            ],
            "运动户外": [
                "运动", "健身", "户外", "跑步", "瑜伽",
                "球类", "游泳", "登山", "骑行", "钓鱼"
            ],
        }
    
    async def classify_product(self, product: Product) -> ClassificationResult:
        """
        对商品进行分类
        
        Args:
            product: 商品对象
        
        Returns:
            ClassificationResult: 分类结果
        """
        try:
            logger.info(f"开始商品分类: {product.id}")
            
            # 应用分类规则
            rule_results = await self._apply_classification_rules(product)
            
            # 确定最终商品类型
            product_type, confidence, matched_rules = await self._determine_product_type(rule_results)
            
            # 生成分类和标签
            categories = await self._assign_categories(product)
            tags = await self._generate_tags(product, product_type)
            
            # 生成推理说明
            reasoning = await self._generate_reasoning(product, matched_rules, product_type)
            
            result = ClassificationResult(
                product_type=product_type,
                confidence=confidence,
                matched_rules=matched_rules,
                categories=categories,
                tags=tags,
                reasoning=reasoning
            )
            
            logger.info(f"商品分类完成: {product.id}, 类型: {product_type.value}, 置信度: {confidence:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"商品分类失败: {e}")
            return ClassificationResult(
                product_type=ProductType.UNKNOWN,
                confidence=0.0,
                matched_rules=[],
                categories=[],
                tags=[],
                reasoning=f"分类失败: {str(e)}"
            )
    
    async def _apply_classification_rules(self, product: Product) -> List[Tuple[ClassificationRule, float]]:
        """应用分类规则"""
        results = []
        
        for rule in self.classification_rules:
            score = await self._evaluate_rule(product, rule)
            if score > 0:
                results.append((rule, score))
        
        # 按优先级和分数排序
        results.sort(key=lambda x: (x[0].priority, x[1]), reverse=True)
        return results
    
    async def _evaluate_rule(self, product: Product, rule: ClassificationRule) -> float:
        """评估单个规则的匹配度"""
        score = 0.0
        max_score = 0.0
        
        # URL模式匹配
        if rule.url_patterns:
            max_score += 1.0
            for pattern in rule.url_patterns:
                if re.search(pattern, product.url, re.IGNORECASE):
                    score += 1.0
                    break
        
        # 关键词模式匹配
        if rule.keyword_patterns:
            max_score += 1.0
            text_content = f"{product.title} {product.description or ''}"
            for pattern in rule.keyword_patterns:
                if re.search(pattern, text_content, re.IGNORECASE):
                    score += 1.0
                    break
        
        # 品牌模式匹配
        if rule.brand_patterns:
            max_score += 1.0
            brand_text = ""
            if product.specs and product.specs.brand:
                brand_text += product.specs.brand
            brand_text += f" {product.title}"
            
            for pattern in rule.brand_patterns:
                if re.search(pattern, brand_text, re.IGNORECASE):
                    score += 1.0
                    break
        
        # 价格范围匹配
        if rule.price_range and product.price and product.price.current_price:
            max_score += 1.0
            min_price, max_price = rule.price_range
            if min_price <= product.price.current_price <= max_price:
                score += 1.0
        
        # 卖家模式匹配
        if rule.seller_patterns and product.seller and product.seller.name:
            max_score += 1.0
            for pattern in rule.seller_patterns:
                if re.search(pattern, product.seller.name, re.IGNORECASE):
                    score += 1.0
                    break
        
        return score / max_score if max_score > 0 else 0.0
    
    async def _determine_product_type(self, rule_results: List[Tuple[ClassificationRule, float]]) -> Tuple[ProductType, float, List[str]]:
        """确定最终商品类型"""
        if not rule_results:
            return ProductType.OTHER, 0.0, []
        
        # 按商品类型分组计算得分
        type_scores = {}
        type_rules = {}
        
        for rule, score in rule_results:
            product_type = rule.product_type
            weighted_score = score * rule.priority
            
            if product_type not in type_scores:
                type_scores[product_type] = 0.0
                type_rules[product_type] = []
            
            type_scores[product_type] += weighted_score
            type_rules[product_type].append(rule.name)
        
        # 找到得分最高的类型
        best_type = max(type_scores.keys(), key=lambda x: type_scores[x])
        best_score = type_scores[best_type]
        
        # 计算置信度
        total_score = sum(type_scores.values())
        confidence = best_score / total_score if total_score > 0 else 0.0
        
        # 如果置信度太低，归类为其他
        if confidence < 0.3:
            return ProductType.OTHER, confidence, []
        
        return best_type, confidence, type_rules[best_type]
    
    async def _assign_categories(self, product: Product) -> List[ProductCategory]:
        """分配商品分类"""
        categories = []
        
        # 基于标题和描述进行分类
        text_content = f"{product.title} {product.description or ''}".lower()
        
        for category_name, keywords in self.category_mapping.items():
            for keyword in keywords:
                if keyword.lower() in text_content:
                    category = ProductCategory(
                        id=f"cat_{category_name}",
                        name=category_name,
                        level=1
                    )
                    categories.append(category)
                    break
        
        # 如果没有匹配到分类，添加默认分类
        if not categories:
            categories.append(ProductCategory(
                id="cat_other",
                name="其他商品",
                level=1
            ))
        
        return categories
    
    async def _generate_tags(self, product: Product, product_type: ProductType) -> List[ProductTag]:
        """生成商品标签"""
        tags = []
        
        # 商品类型标签
        type_tag = ProductTag(
            id=f"tag_{product_type.value}",
            name=self._get_type_display_name(product_type),
            color=self._get_type_color(product_type)
        )
        tags.append(type_tag)
        
        # 平台标签
        if product.platform:
            platform_tag = ProductTag(
                id=f"tag_platform_{product.platform}",
                name=product.platform.upper(),
                color="#6c757d"
            )
            tags.append(platform_tag)
        
        # 价格区间标签
        if product.price and product.price.current_price:
            price_tag = self._get_price_range_tag(product.price.current_price)
            if price_tag:
                tags.append(price_tag)
        
        # 品牌标签
        if product.specs and product.specs.brand:
            brand_tag = ProductTag(
                id=f"tag_brand_{product.specs.brand}",
                name=product.specs.brand,
                color="#17a2b8"
            )
            tags.append(brand_tag)
        
        # 热度标签
        if product.metrics and product.metrics.sales_count:
            if product.metrics.sales_count > 10000:
                hot_tag = ProductTag(
                    id="tag_hot",
                    name="热销",
                    color="#dc3545"
                )
                tags.append(hot_tag)
        
        return tags
    
    def _get_type_display_name(self, product_type: ProductType) -> str:
        """获取商品类型显示名称"""
        type_names = {
            ProductType.COMPETITOR: "竞品",
            ProductType.SUPPLIER: "供货商",
            ProductType.OTHER: "其他",
            ProductType.UNKNOWN: "未分类"
        }
        return type_names.get(product_type, "未知")
    
    def _get_type_color(self, product_type: ProductType) -> str:
        """获取商品类型颜色"""
        type_colors = {
            ProductType.COMPETITOR: "#dc3545",  # 红色
            ProductType.SUPPLIER: "#28a745",   # 绿色
            ProductType.OTHER: "#6c757d",      # 灰色
            ProductType.UNKNOWN: "#ffc107"     # 黄色
        }
        return type_colors.get(product_type, "#6c757d")
    
    def _get_price_range_tag(self, price: float) -> Optional[ProductTag]:
        """获取价格区间标签"""
        if price < 50:
            return ProductTag(
                id="tag_price_low",
                name="低价位",
                color="#28a745"
            )
        elif price < 200:
            return ProductTag(
                id="tag_price_medium",
                name="中价位",
                color="#ffc107"
            )
        elif price < 1000:
            return ProductTag(
                id="tag_price_high",
                name="高价位",
                color="#fd7e14"
            )
        else:
            return ProductTag(
                id="tag_price_premium",
                name="奢侈品",
                color="#6f42c1"
            )
    
    async def _generate_reasoning(self, product: Product, matched_rules: List[str], 
                                product_type: ProductType) -> str:
        """生成分类推理说明"""
        reasoning_parts = []
        
        # 基础信息
        reasoning_parts.append(f"商品标题: {product.title}")
        reasoning_parts.append(f"平台: {product.platform}")
        
        # 匹配的规则
        if matched_rules:
            reasoning_parts.append(f"匹配规则: {', '.join(matched_rules)}")
        
        # 价格信息
        if product.price and product.price.current_price:
            reasoning_parts.append(f"价格: ¥{product.price.current_price}")
        
        # 卖家信息
        if product.seller and product.seller.name:
            reasoning_parts.append(f"卖家: {product.seller.name}")
        
        # 最终分类
        reasoning_parts.append(f"分类结果: {self._get_type_display_name(product_type)}")
        
        return " | ".join(reasoning_parts)
    
    async def identify_competitor(self, product: Product) -> bool:
        """识别是否为竞品"""
        result = await self.classify_product(product)
        return result.product_type == ProductType.COMPETITOR
    
    async def match_supplier_product(self, product: Product) -> bool:
        """匹配是否为供货商商品"""
        result = await self.classify_product(product)
        return result.product_type == ProductType.SUPPLIER
    
    async def batch_classify_products(self, products: List[Product]) -> List[ClassificationResult]:
        """批量分类商品"""
        logger.info(f"开始批量商品分类: {len(products)} 个商品")
        
        tasks = [self.classify_product(product) for product in products]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"商品 {products[i].id} 分类失败: {result}")
                error_result = ClassificationResult(
                    product_type=ProductType.UNKNOWN,
                    confidence=0.0,
                    matched_rules=[],
                    categories=[],
                    tags=[],
                    reasoning=f"分类异常: {str(result)}"
                )
                valid_results.append(error_result)
            else:
                valid_results.append(result)
        
        logger.info(f"批量商品分类完成: {len(valid_results)} 个结果")
        return valid_results
