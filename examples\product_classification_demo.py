"""
商品归档分类系统演示

展示分类体系管理、标签管理、API接口等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductStatus, ProductCategory, ProductTag,
    ProductPrice, ProductImage, ProductSeller, ProductSpecs, ProductMetrics
)
from app.services.product_management.category_manager import CategoryManager, CategoryRule, CategoryType
from app.services.product_management.tag_manager import TagManager, TagRule, TagType


async def demo_category_management():
    """演示分类体系管理"""
    print("=== 分类体系管理演示 ===")
    
    category_manager = CategoryManager()
    
    print(f"\n1. 默认分类体系:")
    print(f"   总分类数: {len(category_manager.categories)}")
    print(f"   根分类数: {len(category_manager.hierarchy.root_categories)}")
    
    # 显示分类树
    tree = category_manager.get_category_tree()
    print(f"\n2. 分类树结构:")
    
    def print_tree_node(node, indent=0):
        prefix = "  " * indent
        print(f"{prefix}- {node['name']} ({node['id']})")
        for child in node.get('children', []):
            print_tree_node(child, indent + 1)
    
    print_tree_node(tree['root'])
    
    # 创建自定义分类
    print(f"\n3. 创建自定义分类:")
    
    custom_category = ProductCategory(
        id="cat_smart_home",
        name="智能家居",
        parent_id="cat_electronics",
        level=2,
        description="智能家居设备和配件"
    )
    
    success = await category_manager.create_category(custom_category)
    print(f"   创建分类 '{custom_category.name}': {'成功' if success else '失败'}")
    
    # 创建子分类
    sub_category = ProductCategory(
        id="cat_smart_speaker",
        name="智能音箱",
        parent_id="cat_smart_home",
        level=3,
        description="智能音箱和语音助手"
    )
    
    success = await category_manager.create_category(sub_category)
    print(f"   创建子分类 '{sub_category.name}': {'成功' if success else '失败'}")
    
    # 测试商品分类
    print(f"\n4. 商品自动分类测试:")
    
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=123456",
            title="小米智能音箱 小爱同学 语音控制 智能家居",
            platform="taobao",
            specs=ProductSpecs(brand="小米"),
            price=ProductPrice(current_price=199.00)
        ),
        Product(
            url="https://detail.1688.com/offer/789012.html",
            title="苹果iPhone 15 手机壳 透明硅胶保护套 批发",
            platform="1688",
            specs=ProductSpecs(brand="苹果"),
            price=ProductPrice(current_price=8.50)
        ),
        Product(
            url="https://item.jd.com/345678.html",
            title="Nike Air Max 270 男士运动鞋 跑步鞋",
            platform="jd",
            specs=ProductSpecs(brand="Nike"),
            price=ProductPrice(current_price=899.00)
        )
    ]
    
    for i, product in enumerate(test_products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        categories = await category_manager.classify_product_advanced(product)
        
        if categories:
            print(f"   分类结果:")
            for category in categories:
                path = category_manager.get_category_path(category.id)
                print(f"     - {category.name} ({' > '.join(path)})")
        else:
            print(f"   未找到匹配的分类")
    
    return test_products


async def demo_tag_management(products):
    """演示标签管理"""
    print("\n=== 标签管理演示 ===")
    
    tag_manager = TagManager()
    
    print(f"\n1. 默认标签体系:")
    print(f"   总标签数: {len(tag_manager.tags)}")
    print(f"   标签规则数: {len(tag_manager.tag_rules)}")
    
    # 显示标签分类
    system_tags = tag_manager.get_tags_by_type(TagType.SYSTEM)
    auto_tags = tag_manager.get_tags_by_type(TagType.AUTO)
    
    print(f"\n2. 标签分类:")
    print(f"   系统标签 ({len(system_tags)}个):")
    for tag in system_tags[:5]:  # 只显示前5个
        print(f"     - {tag.name} ({tag.color})")
    
    print(f"   自动标签 ({len(auto_tags)}个):")
    for tag in auto_tags[:5]:  # 只显示前5个
        print(f"     - {tag.name} ({tag.color})")
    
    # 创建自定义标签
    print(f"\n3. 创建自定义标签:")
    
    custom_tags = [
        ProductTag(
            id="tag_ai_product",
            name="AI产品",
            color="#9c27b0",
            description="人工智能相关产品"
        ),
        ProductTag(
            id="tag_eco_friendly",
            name="环保",
            color="#4caf50",
            description="环保材料或绿色产品"
        ),
        ProductTag(
            id="tag_limited_edition",
            name="限量版",
            color="#ff5722",
            description="限量版或特别版商品"
        )
    ]
    
    for tag in custom_tags:
        success = await tag_manager.create_tag(tag)
        print(f"   创建标签 '{tag.name}': {'成功' if success else '失败'}")
    
    # 自动标记商品
    print(f"\n4. 商品自动标记:")
    
    for i, product in enumerate(products):
        print(f"\n   商品 {i+1}: {product.title[:40]}...")
        tags = await tag_manager.auto_tag_product(product)
        
        if tags:
            print(f"   自动标签 ({len(tags)}个):")
            for tag in tags:
                print(f"     - {tag.name} ({tag.color})")
        else:
            print(f"   未匹配到自动标签")
    
    # 批量标记
    print(f"\n5. 批量标记测试:")
    
    batch_results = await tag_manager.batch_tag_products(products)
    
    total_tags = sum(len(tags) for tags in batch_results.values())
    print(f"   批量标记完成: {len(batch_results)} 个商品，共 {total_tags} 个标签")
    
    # 标签统计
    tag_counts = {}
    for tags in batch_results.values():
        for tag in tags:
            tag_counts[tag.name] = tag_counts.get(tag.name, 0) + 1
    
    print(f"   标签使用统计:")
    for tag_name, count in sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"     - {tag_name}: {count} 次")


async def demo_advanced_classification():
    """演示高级分类功能"""
    print("\n=== 高级分类功能演示 ===")
    
    category_manager = CategoryManager()
    tag_manager = TagManager()
    
    # 创建复杂商品
    complex_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=999999",
            title="Apple iPhone 15 Pro Max 1TB 深空黑色 5G手机 官方正品",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            specs=ProductSpecs(brand="Apple", model="iPhone 15 Pro Max"),
            price=ProductPrice(
                current_price=12999.00,
                original_price=13999.00,
                currency="CNY"
            ),
            metrics=ProductMetrics(
                sales_count=25000,
                rating=4.9,
                review_count=8500
            ),
            data_quality_score=0.98,
            images=[
                ProductImage(url="https://img.taobao.com/image1.jpg", is_main=True),
                ProductImage(url="https://img.taobao.com/image2.jpg"),
                ProductImage(url="https://img.taobao.com/image3.jpg"),
                ProductImage(url="https://img.taobao.com/image4.jpg"),
            ]
        ),
        Product(
            url="https://detail.1688.com/offer/888888.html",
            title="手机壳批发 苹果iPhone系列 透明硅胶保护套 工厂直销 一件代发",
            platform="1688",
            product_type=ProductType.SUPPLIER,
            specs=ProductSpecs(brand="通用", material="硅胶"),
            price=ProductPrice(
                current_price=3.80,
                min_order_quantity=100,
                wholesale_price=2.50,
                currency="CNY"
            ),
            metrics=ProductMetrics(
                sales_count=150000,
                stock_quantity=99999
            ),
            data_quality_score=0.85,
            seller=ProductSeller(
                name="深圳华强北手机配件工厂",
                location="广东深圳"
            )
        )
    ]
    
    print(f"\n1. 复杂商品分析:")
    
    for i, product in enumerate(complex_products):
        print(f"\n   商品 {i+1}: {product.title[:50]}...")
        print(f"   基本信息:")
        print(f"     - 平台: {product.platform}")
        print(f"     - 类型: {product.product_type.value}")
        print(f"     - 价格: {product.get_price_display()}")
        print(f"     - 质量分数: {product.data_quality_score:.2f}")
        
        if product.metrics:
            if product.metrics.sales_count:
                print(f"     - 销量: {product.metrics.sales_count:,}")
            if product.metrics.rating:
                print(f"     - 评分: {product.metrics.rating}")
        
        # 分类分析
        categories = await category_manager.classify_product_advanced(product)
        print(f"   分类结果 ({len(categories)}个):")
        for category in categories:
            path = category_manager.get_category_path(category.id)
            print(f"     - {category.name} ({' > '.join(path)})")
        
        # 标签分析
        tags = await tag_manager.auto_tag_product(product)
        print(f"   标签结果 ({len(tags)}个):")
        for tag in tags:
            print(f"     - {tag.name} ({tag.color})")
    
    # 分类规则演示
    print(f"\n2. 自定义分类规则:")
    
    # 添加高端商品分类规则
    high_end_rule = CategoryRule(
        id="rule_high_end_electronics",
        name="高端电子产品规则",
        category_type=CategoryType.PRICE_RANGE,
        target_category_id="cat_electronics",
        conditions={
            "title_contains": ["iphone", "ipad", "macbook", "pro", "max"],
            "price_range": [5000, 50000],
            "platform_in": ["taobao", "jd"]
        },
        priority=5
    )
    
    category_manager.add_classification_rule(high_end_rule)
    print(f"   添加规则: {high_end_rule.name}")
    
    # 添加批发商品标签规则
    wholesale_tag_rule = TagRule(
        id="rule_wholesale_product",
        name="批发商品标签规则",
        tag_id="tag_supplier",  # 使用现有的供货商标签
        conditions={
            "platform": "1688",
            "price_max": 50,
            "brand_contains": ["批发", "工厂", "代发"]
        },
        priority=8
    )
    
    tag_manager.add_tag_rule(wholesale_tag_rule)
    print(f"   添加标签规则: {wholesale_tag_rule.name}")
    
    # 重新分类测试
    print(f"\n3. 规则应用后重新分类:")
    
    for i, product in enumerate(complex_products):
        print(f"\n   商品 {i+1} 重新分类:")
        
        # 重新分类
        new_categories = await category_manager.classify_product_advanced(product)
        new_tags = await tag_manager.auto_tag_product(product)
        
        print(f"     分类: {[cat.name for cat in new_categories]}")
        print(f"     标签: {[tag.name for tag in new_tags]}")


async def demo_api_simulation():
    """演示API接口模拟"""
    print("\n=== API接口模拟演示 ===")
    
    print(f"\n1. 分类管理API模拟:")
    
    # 模拟获取分类树
    category_manager = CategoryManager()
    tree = category_manager.get_category_tree()
    
    print(f"   GET /api/v1/product-management/categories/tree")
    print(f"   响应: 分类树包含 {len(tree['root']['children'])} 个根分类")
    
    # 模拟创建分类
    print(f"   POST /api/v1/product-management/categories")
    print(f"   请求: 创建 '智能穿戴' 分类")
    
    wearable_category = ProductCategory(
        id="cat_wearable",
        name="智能穿戴",
        parent_id="cat_electronics",
        level=2,
        description="智能手表、手环等穿戴设备"
    )
    
    success = await category_manager.create_category(wearable_category)
    print(f"   响应: {{'success': {str(success).lower()}, 'message': '分类创建{'成功' if success else '失败'}'}}")
    
    print(f"\n2. 标签管理API模拟:")
    
    tag_manager = TagManager()
    
    # 模拟获取所有标签
    all_tags = tag_manager.get_all_tags()
    print(f"   GET /api/v1/product-management/tags")
    print(f"   响应: 返回 {len(all_tags)} 个标签")
    
    # 模拟创建标签
    print(f"   POST /api/v1/product-management/tags")
    print(f"   请求: 创建 '新品推荐' 标签")
    
    new_tag = ProductTag(
        id="tag_recommended",
        name="新品推荐",
        color="#ff6b6b",
        description="推荐的新品商品"
    )
    
    success = await tag_manager.create_tag(new_tag)
    print(f"   响应: {{'success': {str(success).lower()}, 'message': '标签创建{'成功' if success else '失败'}'}}")
    
    print(f"\n3. 系统健康检查模拟:")
    
    health_data = {
        "status": "healthy",
        "categories": {
            "total": len(category_manager.categories),
            "root_categories": len(category_manager.hierarchy.root_categories)
        },
        "tags": {
            "total": len(tag_manager.tags),
            "rules": len(tag_manager.tag_rules)
        },
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"   GET /api/v1/product-management/health")
    print(f"   响应: {health_data}")


async def main():
    """主演示函数"""
    print("🚀 商品归档分类系统演示")
    print("=" * 60)
    
    # 1. 分类体系管理演示
    products = await demo_category_management()
    
    # 2. 标签管理演示
    await demo_tag_management(products)
    
    # 3. 高级分类功能演示
    await demo_advanced_classification()
    
    # 4. API接口模拟演示
    await demo_api_simulation()
    
    print("\n" + "=" * 60)
    print("✅ 商品归档分类系统演示完成！")
    print("\n🎯 核心功能:")
    print("- 层级分类体系：7个根分类 + 6个子分类")
    print("- 智能标签系统：25+个预定义标签 + 20+个标签规则")
    print("- 自动分类引擎：基于标题、品牌、价格、平台的智能分类")
    print("- 自动标记引擎：基于商品属性的智能标签生成")
    print("- 规则配置系统：支持自定义分类规则和标签规则")
    print("- RESTful API：完整的分类和标签管理API接口")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: 5个")
    print(f"- 分类层级: 3层")
    print(f"- 标签类型: 4种")
    print(f"- API端点: 15个")
    print(f"- 自定义规则: 2个")
    
    print(f"\n🔧 技术特性:")
    print(f"- 异步处理：所有分类和标记操作支持异步")
    print(f"- 批量操作：支持批量商品分类和标记")
    print(f"- 规则引擎：灵活的条件匹配和优先级系统")
    print(f"- 层级管理：完整的分类树结构和路径追踪")
    print(f"- 扩展性：易于添加新分类、标签和规则")


if __name__ == "__main__":
    asyncio.run(main())
