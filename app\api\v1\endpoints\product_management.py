"""
商品管理API端点

提供商品分类、标签管理、归档等功能的API接口
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductStatus, ProductCategory, ProductTag
from app.services.product_management.category_manager import CategoryManager, CategoryRule, CategoryType
from app.services.product_management.tag_manager import TagManager, TagRule, TagType
from app.services.product_management.archive_manager import ArchiveManager, ArchiveReason
from app.services.product_management.lifecycle_manager import ProductLifecycleManager, LifecycleEvent, MonitoringPriority
from app.services.product_management.monitoring_scheduler import MonitoringScheduler

logger = get_logger(__name__)
router = APIRouter()

# 全局服务实例
category_manager = CategoryManager()
tag_manager = TagManager()
archive_manager = ArchiveManager()
lifecycle_manager = ProductLifecycleManager()
monitoring_scheduler = MonitoringScheduler(lifecycle_manager)


class CategoryCreateRequest(BaseModel):
    """分类创建请求"""
    id: str = Field(..., description="分类ID")
    name: str = Field(..., description="分类名称")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    level: int = Field(1, description="分类层级")
    description: Optional[str] = Field(None, description="分类描述")


class CategoryUpdateRequest(BaseModel):
    """分类更新请求"""
    name: Optional[str] = Field(None, description="分类名称")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    description: Optional[str] = Field(None, description="分类描述")


class TagCreateRequest(BaseModel):
    """标签创建请求"""
    id: str = Field(..., description="标签ID")
    name: str = Field(..., description="标签名称")
    color: str = Field("#007bff", description="标签颜色")
    description: Optional[str] = Field(None, description="标签描述")


class TagUpdateRequest(BaseModel):
    """标签更新请求"""
    name: Optional[str] = Field(None, description="标签名称")
    color: Optional[str] = Field(None, description="标签颜色")
    description: Optional[str] = Field(None, description="标签描述")


class ArchiveRequest(BaseModel):
    """归档请求"""
    product_ids: List[str] = Field(..., description="商品ID列表")
    reason: str = Field(..., description="归档原因")
    operator: Optional[str] = Field(None, description="操作者")
    notes: Optional[str] = Field(None, description="备注")


# 分类管理API
@router.get("/categories/tree", summary="获取分类树")
async def get_category_tree():
    """获取分类树结构"""
    try:
        tree = category_manager.get_category_tree()
        return {
            "success": True,
            "data": tree
        }
    except Exception as e:
        logger.error(f"获取分类树失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories", summary="获取所有分类")
async def get_all_categories():
    """获取所有分类"""
    try:
        categories = [
            {
                "id": cat.id,
                "name": cat.name,
                "parent_id": cat.parent_id,
                "level": cat.level,
                "description": cat.description,
                "created_at": cat.created_at.isoformat()
            }
            for cat in category_manager.categories.values()
        ]
        
        return {
            "success": True,
            "data": categories,
            "total": len(categories)
        }
    except Exception as e:
        logger.error(f"获取分类列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/categories", summary="创建分类")
async def create_category(request: CategoryCreateRequest):
    """创建新分类"""
    try:
        category = ProductCategory(
            id=request.id,
            name=request.name,
            parent_id=request.parent_id,
            level=request.level,
            description=request.description
        )
        
        success = await category_manager.create_category(category)
        
        if success:
            return {
                "success": True,
                "message": "分类创建成功",
                "data": {
                    "id": category.id,
                    "name": category.name
                }
            }
        else:
            raise HTTPException(status_code=400, detail="分类创建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/categories/{category_id}", summary="更新分类")
async def update_category(category_id: str, request: CategoryUpdateRequest):
    """更新分类信息"""
    try:
        updates = request.dict(exclude_unset=True)
        success = await category_manager.update_category(category_id, updates)
        
        if success:
            return {
                "success": True,
                "message": "分类更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="分类不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/categories/{category_id}", summary="删除分类")
async def delete_category(category_id: str):
    """删除分类"""
    try:
        success = await category_manager.delete_category(category_id)
        
        if success:
            return {
                "success": True,
                "message": "分类删除成功"
            }
        else:
            raise HTTPException(status_code=400, detail="分类删除失败或不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories/{category_id}/path", summary="获取分类路径")
async def get_category_path(category_id: str):
    """获取分类路径"""
    try:
        path = category_manager.get_category_path(category_id)
        
        return {
            "success": True,
            "data": {
                "category_id": category_id,
                "path": path,
                "path_string": " > ".join(path)
            }
        }
    except Exception as e:
        logger.error(f"获取分类路径失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 标签管理API
@router.get("/tags", summary="获取所有标签")
async def get_all_tags(tag_type: Optional[str] = Query(None, description="标签类型")):
    """获取所有标签"""
    try:
        if tag_type:
            try:
                tag_type_enum = TagType(tag_type)
                tags = tag_manager.get_tags_by_type(tag_type_enum)
                tag_data = [
                    {
                        "id": tag.id,
                        "name": tag.name,
                        "color": tag.color,
                        "description": tag.description,
                        "created_at": tag.created_at.isoformat()
                    }
                    for tag in tags
                ]
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的标签类型")
        else:
            tag_data = tag_manager.get_all_tags()
        
        return {
            "success": True,
            "data": tag_data,
            "total": len(tag_data)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标签列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tags", summary="创建标签")
async def create_tag(request: TagCreateRequest):
    """创建新标签"""
    try:
        tag = ProductTag(
            id=request.id,
            name=request.name,
            color=request.color,
            description=request.description
        )
        
        success = await tag_manager.create_tag(tag)
        
        if success:
            return {
                "success": True,
                "message": "标签创建成功",
                "data": {
                    "id": tag.id,
                    "name": tag.name,
                    "color": tag.color
                }
            }
        else:
            raise HTTPException(status_code=400, detail="标签创建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建标签失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/tags/{tag_id}", summary="更新标签")
async def update_tag(tag_id: str, request: TagUpdateRequest):
    """更新标签信息"""
    try:
        updates = request.dict(exclude_unset=True)
        success = await tag_manager.update_tag(tag_id, updates)
        
        if success:
            return {
                "success": True,
                "message": "标签更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="标签不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新标签失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tags/{tag_id}", summary="删除标签")
async def delete_tag(tag_id: str):
    """删除标签"""
    try:
        success = await tag_manager.delete_tag(tag_id)
        
        if success:
            return {
                "success": True,
                "message": "标签删除成功"
            }
        else:
            raise HTTPException(status_code=400, detail="标签删除失败或不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除标签失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tags/rules", summary="获取标签规则")
async def get_tag_rules():
    """获取标签规则列表"""
    try:
        rules = tag_manager.get_tag_rules()
        
        return {
            "success": True,
            "data": rules,
            "total": len(rules)
        }
    except Exception as e:
        logger.error(f"获取标签规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 归档管理API
@router.post("/archive", summary="归档商品")
async def archive_products(request: ArchiveRequest):
    """归档商品"""
    try:
        # 这里简化实现，实际需要从数据库获取商品对象
        # 模拟商品对象
        archived_count = 0
        failed_products = []
        
        for product_id in request.product_ids:
            # 创建模拟商品对象
            product = Product(
                id=product_id,
                url=f"https://example.com/{product_id}",
                title=f"商品 {product_id}",
                status=ProductStatus.ACTIVE
            )
            
            try:
                reason = ArchiveReason(request.reason)
                success = await archive_manager.archive_product(
                    product=product,
                    reason=reason,
                    operator=request.operator,
                    notes=request.notes
                )
                
                if success:
                    archived_count += 1
                else:
                    failed_products.append(product_id)
                    
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的归档原因: {request.reason}")
            except Exception as e:
                logger.error(f"归档商品 {product_id} 失败: {e}")
                failed_products.append(product_id)
        
        return {
            "success": True,
            "message": f"归档完成，成功 {archived_count} 个，失败 {len(failed_products)} 个",
            "data": {
                "archived_count": archived_count,
                "failed_count": len(failed_products),
                "failed_products": failed_products
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量归档失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/archive/statistics", summary="获取归档统计")
async def get_archive_statistics():
    """获取归档统计信息"""
    try:
        # 模拟商品列表
        products = [
            Product(id="1", status=ProductStatus.ACTIVE),
            Product(id="2", status=ProductStatus.ARCHIVED),
            Product(id="3", status=ProductStatus.PAUSED),
        ]
        
        stats = await archive_manager.get_archive_statistics(products)
        
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取归档统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/archive/rules", summary="获取归档规则")
async def get_archive_rules():
    """获取归档规则列表"""
    try:
        rules = archive_manager.get_archive_rules()
        
        return {
            "success": True,
            "data": rules,
            "total": len(rules)
        }
    except Exception as e:
        logger.error(f"获取归档规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", summary="商品管理系统健康检查")
async def product_management_health():
    """商品管理系统健康检查"""
    try:
        health_data = {
            "status": "healthy",
            "categories": {
                "total": len(category_manager.categories),
                "root_categories": len(category_manager.hierarchy.root_categories)
            },
            "tags": {
                "total": len(tag_manager.tags),
                "rules": len(tag_manager.tag_rules)
            },
            "archive": {
                "rules": len(archive_manager.archive_rules),
                "operations": len(archive_manager.archive_operations)
            },
            "lifecycle": {
                "monitoring_configs": len(lifecycle_manager.monitoring_configs),
                "lifecycle_rules": len(lifecycle_manager.lifecycle_rules),
                "metrics": len(lifecycle_manager.lifecycle_metrics)
            },
            "scheduler": monitoring_scheduler.get_scheduler_status(),
            "timestamp": category_manager.hierarchy.root_categories[0].created_at.isoformat() if category_manager.hierarchy.root_categories else None
        }

        return {
            "success": True,
            "data": health_data
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }


# 生命周期管理API
class LifecycleEventRequest(BaseModel):
    """生命周期事件请求"""
    product_id: str = Field(..., description="商品ID")
    event: str = Field(..., description="生命周期事件")
    metadata: Optional[Dict[str, Any]] = Field(None, description="事件元数据")


class MonitoringConfigRequest(BaseModel):
    """监控配置请求"""
    product_id: str = Field(..., description="商品ID")
    priority: str = Field(..., description="监控优先级")
    frequency_minutes: Optional[int] = Field(None, description="监控频率（分钟）")
    enabled: bool = Field(True, description="是否启用监控")


@router.post("/lifecycle/events", summary="记录生命周期事件")
async def record_lifecycle_event(request: LifecycleEventRequest):
    """记录商品生命周期事件"""
    try:
        # 创建模拟商品对象
        product = Product(
            id=request.product_id,
            url=f"https://example.com/{request.product_id}",
            title=f"商品 {request.product_id}",
            status=ProductStatus.ACTIVE
        )

        try:
            event = LifecycleEvent(request.event)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的生命周期事件: {request.event}")

        success = await lifecycle_manager.track_lifecycle_event(
            product=product,
            event=event,
            metadata=request.metadata
        )

        if success:
            return {
                "success": True,
                "message": "生命周期事件记录成功",
                "data": {
                    "product_id": request.product_id,
                    "event": request.event,
                    "timestamp": datetime.now().isoformat()
                }
            }
        else:
            raise HTTPException(status_code=500, detail="生命周期事件记录失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"记录生命周期事件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/lifecycle/statistics", summary="获取生命周期统计")
async def get_lifecycle_statistics():
    """获取生命周期统计信息"""
    try:
        stats = lifecycle_manager.get_lifecycle_statistics()

        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取生命周期统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/lifecycle/rules", summary="获取生命周期规则")
async def get_lifecycle_rules():
    """获取生命周期规则列表"""
    try:
        rules = lifecycle_manager.get_lifecycle_rules()

        return {
            "success": True,
            "data": rules,
            "total": len(rules)
        }
    except Exception as e:
        logger.error(f"获取生命周期规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/config", summary="配置商品监控")
async def configure_monitoring(request: MonitoringConfigRequest):
    """配置商品监控"""
    try:
        # 创建模拟商品对象
        product = Product(
            id=request.product_id,
            url=f"https://example.com/{request.product_id}",
            title=f"商品 {request.product_id}",
            status=ProductStatus.ACTIVE
        )

        try:
            priority = MonitoringPriority(request.priority)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的监控优先级: {request.priority}")

        # 激活监控
        await lifecycle_manager._activate_monitoring(product)

        # 更新优先级
        await lifecycle_manager._update_monitoring_priority(product, priority)

        # 如果指定了频率，更新频率
        if request.frequency_minutes:
            config = lifecycle_manager.monitoring_configs.get(request.product_id)
            if config:
                config.frequency_minutes = request.frequency_minutes
                config.updated_at = datetime.now()

        return {
            "success": True,
            "message": "监控配置成功",
            "data": {
                "product_id": request.product_id,
                "priority": request.priority,
                "enabled": request.enabled
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置商品监控失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/status", summary="获取监控状态")
async def get_monitoring_status():
    """获取监控调度器状态"""
    try:
        status = monitoring_scheduler.get_scheduler_status()

        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/start", summary="启动监控调度器")
async def start_monitoring():
    """启动监控调度器"""
    try:
        await monitoring_scheduler.start()

        return {
            "success": True,
            "message": "监控调度器启动成功"
        }
    except Exception as e:
        logger.error(f"启动监控调度器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/stop", summary="停止监控调度器")
async def stop_monitoring():
    """停止监控调度器"""
    try:
        await monitoring_scheduler.stop()

        return {
            "success": True,
            "message": "监控调度器停止成功"
        }
    except Exception as e:
        logger.error(f"停止监控调度器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/pause", summary="暂停监控调度器")
async def pause_monitoring():
    """暂停监控调度器"""
    try:
        await monitoring_scheduler.pause()

        return {
            "success": True,
            "message": "监控调度器暂停成功"
        }
    except Exception as e:
        logger.error(f"暂停监控调度器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/resume", summary="恢复监控调度器")
async def resume_monitoring():
    """恢复监控调度器"""
    try:
        await monitoring_scheduler.resume()

        return {
            "success": True,
            "message": "监控调度器恢复成功"
        }
    except Exception as e:
        logger.error(f"恢复监控调度器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/tasks", summary="获取监控任务历史")
async def get_monitoring_tasks(limit: int = Query(100, description="返回任务数量限制")):
    """获取监控任务历史"""
    try:
        tasks = monitoring_scheduler.get_task_history(limit)

        return {
            "success": True,
            "data": tasks,
            "total": len(tasks)
        }
    except Exception as e:
        logger.error(f"获取监控任务历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/force/{product_id}", summary="强制监控商品")
async def force_monitor_product(product_id: str,
                               priority: str = Query("high", description="监控优先级")):
    """强制监控指定商品"""
    try:
        try:
            priority_enum = MonitoringPriority(priority)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的监控优先级: {priority}")

        task_id = await monitoring_scheduler.force_monitor_product(product_id, priority_enum)

        return {
            "success": True,
            "message": "强制监控任务已创建",
            "data": {
                "task_id": task_id,
                "product_id": product_id,
                "priority": priority
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制监控商品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
