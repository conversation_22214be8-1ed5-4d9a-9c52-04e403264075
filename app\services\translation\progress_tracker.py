"""
进度跟踪和状态管理器

实现翻译进度跟踪和状态管理，实时显示翻译进度和预计完成时间，失败重试机制
"""

import asyncio
import uuid
from typing import Dict, Any, List, Optional, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import statistics

from app.core.logging import get_logger

logger = get_logger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"         # 等待中
    RUNNING = "running"         # 运行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消
    RETRYING = "retrying"      # 重试中
    PAUSED = "paused"          # 已暂停


class ProgressEvent(Enum):
    """进度事件"""
    TASK_CREATED = "task_created"
    TASK_STARTED = "task_started"
    TASK_PROGRESS = "task_progress"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    TASK_CANCELLED = "task_cancelled"
    TASK_RETRYING = "task_retrying"


@dataclass
class ProgressInfo:
    """进度信息"""
    current: int = 0
    total: int = 0
    percentage: float = 0.0
    rate: float = 0.0  # 处理速率（项目/秒）
    eta_seconds: Optional[float] = None  # 预计剩余时间（秒）
    eta_datetime: Optional[datetime] = None  # 预计完成时间
    
    def update(self, current: int, total: int):
        """更新进度"""
        self.current = current
        self.total = total
        self.percentage = (current / max(total, 1)) * 100


@dataclass
class TaskMetrics:
    """任务指标"""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    retry_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    throughput: float = 0.0  # 吞吐量
    success_rate: float = 0.0  # 成功率


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    name: str
    description: str = ""
    status: TaskStatus = TaskStatus.PENDING
    progress: ProgressInfo = field(default_factory=ProgressInfo)
    metrics: TaskMetrics = field(default_factory=TaskMetrics)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 回调函数
    progress_callback: Optional[Callable[['TaskInfo'], None]] = None
    status_callback: Optional[Callable[['TaskInfo', TaskStatus], None]] = None
    completion_callback: Optional[Callable[['TaskInfo'], None]] = None
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        # 任务存储
        self.tasks: Dict[str, TaskInfo] = {}
        
        # 事件监听器
        self.event_listeners: Dict[ProgressEvent, List[Callable]] = {
            event: [] for event in ProgressEvent
        }
        
        # 全局配置
        self.config = {
            "update_interval": 1.0,         # 进度更新间隔（秒）
            "cleanup_interval": 3600,       # 清理间隔（秒）
            "keep_completed_hours": 24,     # 保留已完成任务的时间（小时）
            "max_tasks": 10000,            # 最大任务数
            "enable_metrics": True,         # 启用指标收集
            "enable_callbacks": True        # 启用回调
        }
        
        # 统计信息
        self.global_stats = {
            "total_tasks": 0,
            "active_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "cancelled_tasks": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "success_rate": 0.0
        }
        
        # 后台任务
        self.update_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # 启动后台任务
        self._start_background_tasks()
    
    def create_task(self, name: str, total_items: int = 0, 
                   description: str = "", metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建任务
        
        Args:
            name: 任务名称
            total_items: 总项目数
            description: 任务描述
            metadata: 元数据
        
        Returns:
            str: 任务ID
        """
        try:
            task_id = str(uuid.uuid4())
            
            # 检查任务数量限制
            if len(self.tasks) >= self.config["max_tasks"]:
                # 清理旧任务
                self._cleanup_old_tasks()
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                name=name,
                description=description,
                metadata=metadata or {}
            )
            
            # 初始化进度
            task_info.progress.total = total_items
            
            # 存储任务
            self.tasks[task_id] = task_info
            
            # 更新统计
            self.global_stats["total_tasks"] += 1
            self.global_stats["active_tasks"] += 1
            
            # 触发事件
            self._emit_event(ProgressEvent.TASK_CREATED, task_info)
            
            logger.info(f"创建任务: {task_id}, 名称: {name}, 总项目数: {total_items}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise
    
    def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否启动成功
        """
        try:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task_info = self.tasks[task_id]
            
            if task_info.status != TaskStatus.PENDING:
                logger.error(f"任务状态不正确: {task_id}, 状态: {task_info.status}")
                return False
            
            # 更新任务状态
            task_info.status = TaskStatus.RUNNING
            task_info.metrics.start_time = datetime.now()
            task_info.updated_at = datetime.now()
            
            # 触发事件
            self._emit_event(ProgressEvent.TASK_STARTED, task_info)
            
            logger.info(f"启动任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动任务失败: {task_id}, {e}")
            return False
    
    def update_progress(self, task_id: str, current: int, 
                       additional_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            current: 当前完成数
            additional_info: 额外信息
        
        Returns:
            bool: 是否更新成功
        """
        try:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task_info = self.tasks[task_id]
            
            # 更新进度
            task_info.progress.update(current, task_info.progress.total)
            task_info.updated_at = datetime.now()
            
            # 计算处理速率和预计完成时间
            if task_info.metrics.start_time:
                elapsed = (datetime.now() - task_info.metrics.start_time).total_seconds()
                if elapsed > 0:
                    task_info.progress.rate = current / elapsed
                    
                    if task_info.progress.rate > 0:
                        remaining_items = task_info.progress.total - current
                        task_info.progress.eta_seconds = remaining_items / task_info.progress.rate
                        task_info.progress.eta_datetime = datetime.now() + timedelta(
                            seconds=task_info.progress.eta_seconds
                        )
            
            # 更新元数据
            if additional_info:
                task_info.metadata.update(additional_info)
            
            # 触发事件和回调
            self._emit_event(ProgressEvent.TASK_PROGRESS, task_info)
            
            if self.config["enable_callbacks"] and task_info.progress_callback:
                try:
                    task_info.progress_callback(task_info)
                except Exception as e:
                    logger.error(f"进度回调失败: {task_id}, {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {task_id}, {e}")
            return False
    
    def complete_task(self, task_id: str, success: bool = True, 
                     error_message: Optional[str] = None) -> bool:
        """
        完成任务
        
        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误消息
        
        Returns:
            bool: 是否完成成功
        """
        try:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task_info = self.tasks[task_id]
            
            # 更新任务状态
            if success:
                task_info.status = TaskStatus.COMPLETED
                task_info.progress.percentage = 100.0
                self.global_stats["completed_tasks"] += 1
            else:
                task_info.status = TaskStatus.FAILED
                task_info.metrics.error_count += 1
                task_info.metrics.last_error = error_message
                self.global_stats["failed_tasks"] += 1
            
            # 更新时间指标
            task_info.metrics.end_time = datetime.now()
            task_info.updated_at = datetime.now()
            
            if task_info.metrics.start_time:
                task_info.metrics.duration = (
                    task_info.metrics.end_time - task_info.metrics.start_time
                ).total_seconds()
                
                # 更新全局统计
                self.global_stats["total_processing_time"] += task_info.metrics.duration
                completed_count = self.global_stats["completed_tasks"] + self.global_stats["failed_tasks"]
                if completed_count > 0:
                    self.global_stats["average_processing_time"] = (
                        self.global_stats["total_processing_time"] / completed_count
                    )
            
            # 计算吞吐量和成功率
            if task_info.metrics.duration and task_info.metrics.duration > 0:
                task_info.metrics.throughput = task_info.progress.current / task_info.metrics.duration
            
            if task_info.progress.total > 0:
                task_info.metrics.success_rate = (task_info.progress.current / task_info.progress.total) * 100
            
            # 更新全局成功率
            total_completed = self.global_stats["completed_tasks"] + self.global_stats["failed_tasks"]
            if total_completed > 0:
                self.global_stats["success_rate"] = (
                    self.global_stats["completed_tasks"] / total_completed * 100
                )
            
            # 更新活跃任务数
            self.global_stats["active_tasks"] -= 1
            
            # 触发事件和回调
            if success:
                self._emit_event(ProgressEvent.TASK_COMPLETED, task_info)
            else:
                self._emit_event(ProgressEvent.TASK_FAILED, task_info)
            
            if self.config["enable_callbacks"] and task_info.completion_callback:
                try:
                    task_info.completion_callback(task_info)
                except Exception as e:
                    logger.error(f"完成回调失败: {task_id}, {e}")
            
            logger.info(f"任务完成: {task_id}, 成功: {success}")
            return True
            
        except Exception as e:
            logger.error(f"完成任务失败: {task_id}, {e}")
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否取消成功
        """
        try:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task_info = self.tasks[task_id]
            
            if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                logger.error(f"任务已结束，无法取消: {task_id}, 状态: {task_info.status}")
                return False
            
            # 更新任务状态
            task_info.status = TaskStatus.CANCELLED
            task_info.metrics.end_time = datetime.now()
            task_info.updated_at = datetime.now()
            
            # 更新统计
            self.global_stats["cancelled_tasks"] += 1
            if task_info.status == TaskStatus.RUNNING:
                self.global_stats["active_tasks"] -= 1
            
            # 触发事件
            self._emit_event(ProgressEvent.TASK_CANCELLED, task_info)
            
            logger.info(f"取消任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, {e}")
            return False
    
    def retry_task(self, task_id: str, retry_config: Optional[RetryConfig] = None) -> bool:
        """
        重试任务
        
        Args:
            task_id: 任务ID
            retry_config: 重试配置
        
        Returns:
            bool: 是否重试成功
        """
        try:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task_info = self.tasks[task_id]
            
            if task_info.status != TaskStatus.FAILED:
                logger.error(f"只能重试失败的任务: {task_id}, 状态: {task_info.status}")
                return False
            
            # 使用默认重试配置
            if retry_config is None:
                retry_config = RetryConfig()
            
            # 检查重试次数限制
            if task_info.metrics.retry_count >= retry_config.max_attempts:
                logger.error(f"超过最大重试次数: {task_id}, 重试次数: {task_info.metrics.retry_count}")
                return False
            
            # 更新任务状态
            task_info.status = TaskStatus.RETRYING
            task_info.metrics.retry_count += 1
            task_info.updated_at = datetime.now()
            
            # 重置进度
            task_info.progress.current = 0
            task_info.progress.percentage = 0.0
            task_info.progress.rate = 0.0
            task_info.progress.eta_seconds = None
            task_info.progress.eta_datetime = None
            
            # 重置时间指标
            task_info.metrics.start_time = None
            task_info.metrics.end_time = None
            task_info.metrics.duration = None
            
            # 触发事件
            self._emit_event(ProgressEvent.TASK_RETRYING, task_info)
            
            logger.info(f"重试任务: {task_id}, 重试次数: {task_info.metrics.retry_count}")
            return True
            
        except Exception as e:
            logger.error(f"重试任务失败: {task_id}, {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_tasks(self, status: Optional[TaskStatus] = None) -> List[TaskInfo]:
        """获取任务列表"""
        tasks = list(self.tasks.values())
        
        if status:
            tasks = [task for task in tasks if task.status == status]
        
        # 按更新时间排序
        tasks.sort(key=lambda x: x.updated_at, reverse=True)
        return tasks
    
    def get_active_tasks(self) -> List[TaskInfo]:
        """获取活跃任务列表"""
        return self.get_tasks(TaskStatus.RUNNING)
    
    def add_event_listener(self, event: ProgressEvent, callback: Callable):
        """添加事件监听器"""
        self.event_listeners[event].append(callback)
    
    def remove_event_listener(self, event: ProgressEvent, callback: Callable):
        """移除事件监听器"""
        if callback in self.event_listeners[event]:
            self.event_listeners[event].remove(callback)
    
    def _emit_event(self, event: ProgressEvent, task_info: TaskInfo):
        """触发事件"""
        try:
            for callback in self.event_listeners[event]:
                try:
                    callback(task_info)
                except Exception as e:
                    logger.error(f"事件回调失败: {event.value}, {e}")
        except Exception as e:
            logger.error(f"触发事件失败: {event.value}, {e}")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        try:
            self.update_task = asyncio.create_task(self._update_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        except RuntimeError:
            # 没有运行的事件循环，跳过启动后台任务
            logger.warning("没有运行的事件循环，跳过启动后台任务")
        except Exception as e:
            logger.error(f"启动后台任务失败: {e}")
    
    async def _update_loop(self):
        """更新循环"""
        while True:
            try:
                await asyncio.sleep(self.config["update_interval"])
                # 这里可以添加定期更新逻辑
            except Exception as e:
                logger.error(f"更新循环异常: {e}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.config["cleanup_interval"])
                self._cleanup_old_tasks()
            except Exception as e:
                logger.error(f"清理循环异常: {e}")
    
    def _cleanup_old_tasks(self):
        """清理旧任务"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.config["keep_completed_hours"])
            
            tasks_to_remove = []
            for task_id, task_info in self.tasks.items():
                if (task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task_info.updated_at < cutoff_time):
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
            
            if tasks_to_remove:
                logger.info(f"清理旧任务: {len(tasks_to_remove)}个")
                
        except Exception as e:
            logger.error(f"清理旧任务失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            # 实时统计
            active_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING])
            pending_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING])
            
            return {
                "global_stats": self.global_stats.copy(),
                "current_stats": {
                    "total_tasks": len(self.tasks),
                    "active_tasks": active_tasks,
                    "pending_tasks": pending_tasks,
                    "completed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
                    "failed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
                    "cancelled_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.CANCELLED])
                },
                "config": self.config.copy()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"global_stats": {}, "current_stats": {}, "config": {}}
    
    def stop_background_tasks(self):
        """停止后台任务"""
        if self.update_task:
            self.update_task.cancel()
            self.update_task = None
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            self.cleanup_task = None
