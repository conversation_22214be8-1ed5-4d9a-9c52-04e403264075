"""
商品数据处理器

负责接收、清洗、标准化爬取数据，并转换为标准商品模型
"""

import re
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from urllib.parse import urlparse

from app.core.logging import get_logger
from app.models.product import (
    Product, ProductType, ProductStatus, DataQuality,
    ProductPrice, ProductImage, ProductSeller, ProductSpecs, ProductMetrics
)
from app.services.task_middleware.config_manager import Platform

logger = get_logger(__name__)


class ProductDataProcessor:
    """商品数据处理器"""
    
    def __init__(self):
        self.platform_processors = {
            Platform.ALIBABA_1688: self._process_1688_data,
            Platform.TAOBAO: self._process_taobao_data,
            Platform.JD: self._process_jd_data,
            Platform.PINDUODUO: self._process_pdd_data,
        }
    
    async def process_crawl_result(self, raw_data: Dict[str, Any], 
                                 url: str, platform: Platform,
                                 product_type: ProductType = ProductType.UNKNOWN) -> Product:
        """
        处理爬取结果，转换为标准商品模型
        
        Args:
            raw_data: 原始爬取数据
            url: 商品URL
            platform: 平台类型
            product_type: 商品类型
        
        Returns:
            Product: 标准化商品对象
        """
        try:
            logger.info(f"处理商品数据: {url}")
            
            # 数据清洗
            cleaned_data = await self._clean_raw_data(raw_data)
            
            # 平台特定处理
            processor = self.platform_processors.get(platform, self._process_generic_data)
            processed_data = await processor(cleaned_data, url)
            
            # 创建商品对象
            product = await self._create_product(processed_data, url, platform, product_type)
            
            # 数据质量评估
            product.data_quality_score = await self._calculate_quality_score(product)
            product.data_quality = self._get_quality_level(product.data_quality_score)
            
            # 保存原始数据
            product.raw_data = raw_data
            
            logger.info(f"商品数据处理完成: {product.id}, 质量分数: {product.data_quality_score:.2f}")
            return product
            
        except Exception as e:
            logger.error(f"处理商品数据失败: {e}")
            # 创建基础商品对象
            return await self._create_fallback_product(raw_data, url, platform, product_type)
    
    async def _clean_raw_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗原始数据"""
        cleaned = {}
        
        for key, value in raw_data.items():
            if value is None:
                continue
                
            if isinstance(value, str):
                # 清理字符串
                cleaned_value = value.strip()
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value)  # 合并多个空格
                cleaned_value = re.sub(r'[\r\n\t]', ' ', cleaned_value)  # 替换换行符
                
                if cleaned_value:
                    cleaned[key] = cleaned_value
            else:
                cleaned[key] = value
        
        return cleaned
    
    async def _process_1688_data(self, data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """处理1688平台数据"""
        processed = {
            "title": data.get("title", ""),
            "price": self._parse_price(data.get("price", "")),
            "original_price": self._parse_price(data.get("original_price", "")),
            "images": self._parse_images(data.get("images", [])),
            "seller_name": data.get("seller_name", ""),
            "min_order": self._parse_number(data.get("min_order", "")),
            "wholesale_price": self._parse_price(data.get("wholesale_price", "")),
            "location": data.get("location", ""),
            "brand": data.get("brand", ""),
            "model": data.get("model", ""),
            "material": data.get("material", ""),
            "sales_count": self._parse_number(data.get("sales_count", "")),
            "stock": self._parse_number(data.get("stock", "")),
        }
        
        return processed
    
    async def _process_taobao_data(self, data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """处理淘宝平台数据"""
        processed = {
            "title": data.get("title", ""),
            "price": self._parse_price(data.get("price", "")),
            "images": self._parse_images(data.get("images", [])),
            "seller_name": data.get("shop_name", ""),
            "sales_count": self._parse_sales_count(data.get("sales_count", "")),
            "rating": self._parse_rating(data.get("rating", "")),
            "review_count": self._parse_number(data.get("review_count", "")),
            "location": data.get("location", ""),
            "brand": data.get("brand", ""),
        }
        
        return processed
    
    async def _process_jd_data(self, data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """处理京东平台数据"""
        processed = {
            "title": data.get("title", ""),
            "price": self._parse_price(data.get("price", "")),
            "images": self._parse_images(data.get("images", [])),
            "seller_name": data.get("shop_name", ""),
            "brand": data.get("brand", ""),
            "model": data.get("model", ""),
            "review_count": self._parse_number(data.get("comment_count", "")),
            "rating": self._parse_rating(data.get("rating", "")),
        }
        
        return processed
    
    async def _process_pdd_data(self, data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """处理拼多多平台数据"""
        processed = {
            "title": data.get("title", ""),
            "price": self._parse_price(data.get("price", "")),
            "images": self._parse_images(data.get("images", [])),
            "seller_name": data.get("store_name", ""),
            "sales_count": self._parse_sales_count(data.get("sales", "")),
        }
        
        return processed
    
    async def _process_generic_data(self, data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """处理通用平台数据"""
        return {
            "title": data.get("title", ""),
            "price": self._parse_price(data.get("price", "")),
            "images": self._parse_images(data.get("images", [])),
            "seller_name": data.get("seller", ""),
        }
    
    def _parse_price(self, price_str: str) -> Optional[float]:
        """解析价格字符串"""
        if not price_str:
            return None
        
        # 移除货币符号和其他字符
        price_clean = re.sub(r'[^\d.,]', '', str(price_str))
        
        if not price_clean:
            return None
        
        try:
            # 处理千分位分隔符
            if ',' in price_clean and '.' in price_clean:
                # 如果同时有逗号和点，假设逗号是千分位分隔符
                price_clean = price_clean.replace(',', '')
            elif ',' in price_clean:
                # 只有逗号，可能是小数点或千分位分隔符
                parts = price_clean.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:
                    # 可能是小数点
                    price_clean = price_clean.replace(',', '.')
                else:
                    # 可能是千分位分隔符
                    price_clean = price_clean.replace(',', '')
            
            return float(price_clean)
        except ValueError:
            logger.warning(f"无法解析价格: {price_str}")
            return None
    
    def _parse_number(self, num_str: str) -> Optional[int]:
        """解析数字字符串"""
        if not num_str:
            return None
        
        # 移除非数字字符
        num_clean = re.sub(r'[^\d]', '', str(num_str))
        
        if not num_clean:
            return None
        
        try:
            return int(num_clean)
        except ValueError:
            return None
    
    def _parse_sales_count(self, sales_str: str) -> Optional[int]:
        """解析销量字符串"""
        if not sales_str:
            return None
        
        sales_str = str(sales_str).lower()
        
        # 提取数字
        numbers = re.findall(r'[\d.]+', sales_str)
        if not numbers:
            return None
        
        try:
            base_num = float(numbers[0])
            
            # 处理单位
            if '万' in sales_str or 'w' in sales_str:
                return int(base_num * 10000)
            elif '千' in sales_str or 'k' in sales_str:
                return int(base_num * 1000)
            else:
                return int(base_num)
        except ValueError:
            return None
    
    def _parse_rating(self, rating_str: str) -> Optional[float]:
        """解析评分字符串"""
        if not rating_str:
            return None
        
        # 提取数字
        numbers = re.findall(r'[\d.]+', str(rating_str))
        if not numbers:
            return None
        
        try:
            rating = float(numbers[0])
            # 限制评分范围
            return min(max(rating, 0.0), 5.0)
        except ValueError:
            return None
    
    def _parse_images(self, images_data: List[Any]) -> List[ProductImage]:
        """解析图片数据"""
        images = []
        
        for i, img_data in enumerate(images_data):
            if isinstance(img_data, str):
                # 简单URL字符串
                images.append(ProductImage(
                    url=img_data,
                    is_main=(i == 0)
                ))
            elif isinstance(img_data, dict):
                # 包含更多信息的字典
                images.append(ProductImage(
                    url=img_data.get("url", ""),
                    alt_text=img_data.get("alt", ""),
                    is_main=img_data.get("is_main", i == 0),
                    width=img_data.get("width"),
                    height=img_data.get("height")
                ))
        
        return images
    
    async def _create_product(self, processed_data: Dict[str, Any], 
                            url: str, platform: Platform, 
                            product_type: ProductType) -> Product:
        """创建商品对象"""
        # 解析URL获取平台商品ID
        platform_product_id = self._extract_product_id(url, platform)
        
        # 创建价格对象
        price = None
        if processed_data.get("price"):
            price = ProductPrice(
                current_price=processed_data["price"],
                original_price=processed_data.get("original_price"),
                currency="CNY",
                min_order_quantity=processed_data.get("min_order"),
                wholesale_price=processed_data.get("wholesale_price")
            )
            
            if price.original_price and price.current_price:
                price.discount_rate = ((price.original_price - price.current_price) / 
                                     price.original_price * 100)
        
        # 创建卖家对象
        seller = None
        if processed_data.get("seller_name"):
            seller = ProductSeller(
                name=processed_data["seller_name"],
                location=processed_data.get("location"),
                rating=processed_data.get("seller_rating")
            )
        
        # 创建规格对象
        specs = ProductSpecs(
            brand=processed_data.get("brand"),
            model=processed_data.get("model"),
            material=processed_data.get("material")
        )
        
        # 创建指标对象
        metrics = ProductMetrics(
            sales_count=processed_data.get("sales_count"),
            review_count=processed_data.get("review_count"),
            rating=processed_data.get("rating"),
            stock_quantity=processed_data.get("stock")
        )
        
        # 创建商品对象
        product = Product(
            url=url,
            title=processed_data.get("title", ""),
            product_type=product_type,
            platform=platform.value,
            platform_product_id=platform_product_id,
            price=price,
            images=processed_data.get("images", []),
            seller=seller,
            specs=specs,
            metrics=metrics,
            status=ProductStatus.NEW
        )
        
        return product
    
    def _extract_product_id(self, url: str, platform: Platform) -> Optional[str]:
        """从URL提取商品ID"""
        try:
            if platform == Platform.ALIBABA_1688:
                # 1688: https://detail.1688.com/offer/123456.html
                match = re.search(r'/offer/(\d+)\.html', url)
                return match.group(1) if match else None
            elif platform == Platform.TAOBAO:
                # 淘宝: https://item.taobao.com/item.htm?id=123456
                match = re.search(r'[?&]id=(\d+)', url)
                return match.group(1) if match else None
            elif platform == Platform.JD:
                # 京东: https://item.jd.com/123456.html
                match = re.search(r'/(\d+)\.html', url)
                return match.group(1) if match else None
            else:
                return None
        except Exception:
            return None
    
    async def _calculate_quality_score(self, product: Product) -> float:
        """计算数据质量分数"""
        score = 0.0
        max_score = 0.0
        
        # 基础信息 (40%)
        if product.title:
            score += 0.2
        if product.url:
            score += 0.1
        if product.platform:
            score += 0.1
        max_score += 0.4
        
        # 价格信息 (25%)
        if product.price and product.price.current_price:
            score += 0.2
            if product.price.original_price:
                score += 0.05
        max_score += 0.25
        
        # 图片信息 (15%)
        if product.images:
            score += 0.1
            if len(product.images) > 1:
                score += 0.05
        max_score += 0.15
        
        # 卖家信息 (10%)
        if product.seller and product.seller.name:
            score += 0.1
        max_score += 0.1
        
        # 指标数据 (10%)
        if product.metrics:
            if product.metrics.sales_count is not None:
                score += 0.05
            if product.metrics.rating is not None:
                score += 0.05
        max_score += 0.1
        
        return score / max_score if max_score > 0 else 0.0
    
    def _get_quality_level(self, score: float) -> DataQuality:
        """根据分数获取质量等级"""
        if score >= 0.9:
            return DataQuality.EXCELLENT
        elif score >= 0.7:
            return DataQuality.GOOD
        elif score >= 0.5:
            return DataQuality.FAIR
        elif score >= 0.3:
            return DataQuality.POOR
        else:
            return DataQuality.BAD
    
    async def _create_fallback_product(self, raw_data: Dict[str, Any], 
                                     url: str, platform: Platform,
                                     product_type: ProductType) -> Product:
        """创建备用商品对象（当处理失败时）"""
        product = Product(
            url=url,
            title=raw_data.get("title", "数据处理失败"),
            product_type=product_type,
            platform=platform.value,
            status=ProductStatus.NEW,
            data_quality=DataQuality.BAD,
            data_quality_score=0.1,
            raw_data=raw_data
        )
        
        return product
