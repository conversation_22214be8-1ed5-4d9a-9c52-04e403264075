/**
 * 平台对比饼图组件
 */

import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin, Empty, Radio } from 'antd';
import { analyticsApi } from '../../services/analyticsApi';

interface PlatformComparisonChartProps {
  categories?: string[];
  startDate?: string;
  endDate?: string;
  height?: number;
}

interface PlatformData {
  platform: string;
  avg_price: number;
  product_count: number;
  sales_volume: number;
}

const PlatformComparisonChart: React.FC<PlatformComparisonChartProps> = ({
  categories,
  startDate,
  endDate,
  height = 400,
}) => {
  const [data, setData] = useState<PlatformData[]>([]);
  const [loading, setLoading] = useState(false);
  const [metric, setMetric] = useState<'product_count' | 'sales_volume' | 'avg_price'>('product_count');

  useEffect(() => {
    loadData();
  }, [categories, startDate, endDate]);

  const loadData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getPlatformComparison({
        categories,
        start_date: startDate,
        end_date: endDate,
      });
      setData(response.data);
    } catch (error) {
      console.error('Failed to load platform comparison data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const getOption = () => {
    if (!data || data.length === 0) {
      return {};
    }

    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa8c16', '#13c2c2', '#eb2f96'];
    
    const pieData = data.map((item, index) => ({
      name: item.platform,
      value: item[metric],
      itemStyle: {
        color: colors[index % colors.length],
      },
    }));

    const total = pieData.reduce((sum, item) => sum + item.value, 0);

    const getTitle = () => {
      switch (metric) {
        case 'product_count':
          return '平台商品数量分布';
        case 'sales_volume':
          return '平台销量分布';
        case 'avg_price':
          return '平台平均价格对比';
        default:
          return '平台数据对比';
      }
    };

    const getFormatter = () => {
      switch (metric) {
        case 'avg_price':
          return (params: any) => `${params.name}: ¥${params.value.toFixed(2)}`;
        case 'sales_volume':
          return (params: any) => `${params.name}: ${params.value.toLocaleString()}`;
        default:
          return (params: any) => `${params.name}: ${params.value}`;
      }
    };

    return {
      title: {
        text: getTitle(),
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const percentage = ((params.value / total) * 100).toFixed(1);
          let valueText = '';
          switch (metric) {
            case 'avg_price':
              valueText = `¥${params.value.toFixed(2)}`;
              break;
            case 'sales_volume':
              valueText = params.value.toLocaleString();
              break;
            default:
              valueText = params.value.toString();
          }
          return `
            <div style="margin-bottom: 5px;">${params.name}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">数值:</span>
              <span style="font-weight: bold;">${valueText}</span>
            </div>
            <div style="margin-top: 3px;">占比: <span style="font-weight: bold;">${percentage}%</span></div>
          `;
        },
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        data: data.map(item => item.platform),
      },
      series: [
        {
          name: getTitle(),
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              formatter: (params: any) => {
                const percentage = ((params.value / total) * 100).toFixed(1);
                return `${params.name}\n${percentage}%`;
              },
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData,
        },
      ],
    };
  };

  if (loading) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="暂无数据" />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 16, textAlign: 'center' }}>
        <Radio.Group 
          value={metric} 
          onChange={(e) => setMetric(e.target.value)}
          buttonStyle="solid"
        >
          <Radio.Button value="product_count">商品数量</Radio.Button>
          <Radio.Button value="sales_volume">销量</Radio.Button>
          <Radio.Button value="avg_price">平均价格</Radio.Button>
        </Radio.Group>
      </div>
      <ReactECharts
        option={getOption()}
        style={{ height }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

export default PlatformComparisonChart;
