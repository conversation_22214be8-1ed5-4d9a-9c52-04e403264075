"""
缓存工具测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.utils.cache_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheKeyBuilder


class TestCacheHelper:
    """缓存助手测试"""
    
    @pytest.fixture
    def cache_helper(self):
        """创建缓存助手实例"""
        return CacheHelper()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_or_set_cache_hit(self, mock_get_cache_manager, cache_helper):
        """测试缓存命中的get_or_set"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        cached_data = {"result": "cached_value"}
        mock_cache_manager.get.return_value = cached_data
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟获取函数
        fetch_func = AsyncMock(return_value={"result": "fresh_value"})
        
        # 执行get_or_set
        result = await cache_helper.get_or_set("test_key", fetch_func, ttl=3600)
        
        # 验证
        assert result == cached_data
        mock_cache_manager.get.assert_called_once_with("test_key")
        mock_cache_manager.set.assert_not_called()
        fetch_func.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_or_set_cache_miss(self, mock_get_cache_manager, cache_helper):
        """测试缓存未命中的get_or_set"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # 缓存未命中
        mock_cache_manager.set.return_value = True
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟获取函数
        fresh_data = {"result": "fresh_value"}
        fetch_func = AsyncMock(return_value=fresh_data)
        
        # 执行get_or_set
        result = await cache_helper.get_or_set("test_key", fetch_func, ttl=3600)
        
        # 验证
        assert result == fresh_data
        mock_cache_manager.get.assert_called_once_with("test_key")
        mock_cache_manager.set.assert_called_once_with("test_key", fresh_data, 3600, "default")
        fetch_func.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_or_set_fetch_function_error(self, mock_get_cache_manager, cache_helper):
        """测试获取函数出错的get_or_set"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # 缓存未命中
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟获取函数出错
        fetch_func = AsyncMock(side_effect=Exception("Fetch error"))
        
        # 执行get_or_set，应该抛出异常
        with pytest.raises(Exception, match="Fetch error"):
            await cache_helper.get_or_set("test_key", fetch_func, ttl=3600)
        
        # 验证
        mock_cache_manager.get.assert_called_once_with("test_key")
        mock_cache_manager.set.assert_not_called()
        fetch_func.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_invalidate_product_cache(self, mock_get_cache_manager, cache_helper):
        """测试清除商品缓存"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.clear_pattern.return_value = None  # 实际实现没有返回值
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行清除缓存
        result = await cache_helper.invalidate_product_cache("product123")

        # 验证
        assert result is None  # 实际实现没有返回值
        expected_patterns = [
            "trend:*:product123:*",
            "analysis:*:product123:*",
            "costs:product:product123",
            "profit:*:product123:*"
        ]
        assert mock_cache_manager.clear_pattern.call_count == 4
        for pattern in expected_patterns:
            mock_cache_manager.clear_pattern.assert_any_call(pattern)
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_invalidate_platform_cache(self, mock_get_cache_manager, cache_helper):
        """测试清除平台缓存"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.clear_pattern.return_value = None
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行清除缓存
        result = await cache_helper.invalidate_platform_cache("1688")

        # 验证
        assert result is None
        mock_cache_manager.clear_pattern.assert_called_once_with("config:platform:1688")
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_check_rate_limit_within_limit(self, mock_get_cache_manager, cache_helper):
        """测试在限制范围内的速率检查"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = 50  # 当前请求数
        mock_cache_manager.set.return_value = True
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行速率检查
        result = await cache_helper.check_rate_limit("api_key_123", "/api/v1/test", limit=100, window=3600)

        # 验证
        assert result["allowed"] == True
        assert result["current_count"] == 51
        assert result["limit"] == 100
        assert result["remaining"] == 49
        mock_cache_manager.get.assert_called_once_with("rate_limit:api_key_123:/api/v1/test")
        mock_cache_manager.set.assert_called_once_with("rate_limit:api_key_123:/api/v1/test", 51, 3600, "rate_limit")
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_check_rate_limit_exceeded(self, mock_get_cache_manager, cache_helper):
        """测试超出限制的速率检查"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = 100  # 已达到限制
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行速率检查
        result = await cache_helper.check_rate_limit("api_key_123", "/api/v1/test", limit=100, window=3600)

        # 验证
        assert result["allowed"] == False
        assert result["current_count"] == 100
        assert result["limit"] == 100
        mock_cache_manager.get.assert_called_once_with("rate_limit:api_key_123:/api/v1/test")
        mock_cache_manager.set.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_check_rate_limit_first_request(self, mock_get_cache_manager, cache_helper):
        """测试首次请求的速率检查"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # 首次请求
        mock_cache_manager.set.return_value = True
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行速率检查
        result = await cache_helper.check_rate_limit("api_key_new", "/api/v1/test", limit=100, window=3600)

        # 验证
        assert result["allowed"] == True
        assert result["current_count"] == 1
        assert result["limit"] == 100
        assert result["remaining"] == 99
        mock_cache_manager.get.assert_called_once_with("rate_limit:api_key_new:/api/v1/test")
        mock_cache_manager.set.assert_called_once_with("rate_limit:api_key_new:/api/v1/test", 1, 3600, "rate_limit")
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_cache_translation(self, mock_get_cache_manager, cache_helper):
        """测试缓存翻译结果"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.set.return_value = True
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行缓存翻译
        result = await cache_helper.cache_translation("Hello World", "你好世界", "zh")

        # 验证
        assert result == True
        mock_cache_manager.set.assert_called_once()
        call_args = mock_cache_manager.set.call_args
        assert call_args[0][1] == "你好世界"  # 翻译结果
        assert call_args[0][2] == 7 * 24 * 3600  # TTL (7天)
        assert call_args[0][3] == "translation"  # cache_type
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_translation_hit(self, mock_get_cache_manager, cache_helper):
        """测试获取缓存的翻译（命中）"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = "你好世界"
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行获取缓存翻译
        result = await cache_helper.get_translation("Hello World", "zh")

        # 验证
        assert result == "你好世界"
        mock_cache_manager.get.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_translation_miss(self, mock_get_cache_manager, cache_helper):
        """测试获取缓存的翻译（未命中）"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行获取缓存翻译
        result = await cache_helper.get_translation("Hello World", "zh")

        # 验证
        assert result is None
        mock_cache_manager.get.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_warm_up_cache(self, mock_get_cache_manager, cache_helper):
        """测试预热缓存"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.set.return_value = True
        mock_get_cache_manager.return_value = mock_cache_manager

        # 准备预热数据
        product_ids = ["product1", "product2", "product3"]

        # 执行预热缓存
        result = await cache_helper.warm_up_cache(product_ids)

        # 验证 - warm_up_cache实际上是空实现，只是pass
        assert result is None
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_get_cache_stats(self, mock_get_cache_manager, cache_helper):
        """测试获取缓存统计"""
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_stats = {
            "hits": 150,
            "misses": 50,
            "sets": 100,
            "deletes": 10,
            "hit_rate": 0.75
        }
        mock_cache_manager.get_info.return_value = mock_stats
        mock_get_cache_manager.return_value = mock_cache_manager

        # 执行获取统计
        result = await cache_helper.get_cache_stats()

        # 验证
        assert result == mock_stats
        mock_cache_manager.get_info.assert_called_once()


class TestCacheKeyBuilderExtended:
    """缓存键构建器扩展测试"""
    
    def test_user_session_key(self):
        """测试用户会话缓存键"""
        key = CacheKeyBuilder.user_session("user123")
        assert key == "session:user:user123"
    
    def test_search_results_key(self):
        """测试搜索结果缓存键"""
        key = CacheKeyBuilder.search_results("手机", {"category": "electronics", "price_min": 100})
        assert key.startswith("search:")
        assert len(key.split(":")) == 3  # search:query_hash:filters_hash

    def test_translation_cache_key(self):
        """测试翻译缓存键"""
        key = CacheKeyBuilder.translation_cache("Hello World", "zh")
        assert key.startswith("translation:zh:")
        assert len(key.split(":")) == 3  # translation:lang:text_hash
    
    def test_cache_key_with_special_characters(self):
        """测试包含特殊字符的缓存键"""
        # 实际实现不做特殊字符编码，直接使用
        key = CacheKeyBuilder.product_trend("product/123", "price", 30, "1d")
        assert "product/123" in key  # 直接包含特殊字符

    def test_cache_key_length_limit(self):
        """测试缓存键长度"""
        # 创建一个很长的产品ID
        long_product_id = "a" * 500
        key = CacheKeyBuilder.product_trend(long_product_id, "price", 30, "1d")

        # 实际实现没有长度限制，直接拼接
        expected_key = f"trend:price:{long_product_id}:30:1d"
        assert key == expected_key
    
    def test_cache_key_uniqueness(self):
        """测试缓存键的唯一性"""
        # 相同参数应该生成相同的键
        key1 = CacheKeyBuilder.product_trend("product123", "price", 30, "1d")
        key2 = CacheKeyBuilder.product_trend("product123", "price", 30, "1d")
        assert key1 == key2
        
        # 不同参数应该生成不同的键
        key3 = CacheKeyBuilder.product_trend("product123", "sales", 30, "1d")
        assert key1 != key3
        
        key4 = CacheKeyBuilder.product_trend("product123", "price", 7, "1d")
        assert key1 != key4


class TestCacheHelperIntegration:
    """缓存助手集成测试"""
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_product_cache_workflow(self, mock_get_cache_manager):
        """测试商品缓存工作流"""
        cache_helper = CacheHelper()
        
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # 初始缓存为空
        mock_cache_manager.set.return_value = True
        mock_cache_manager.clear_pattern.return_value = 3
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # 模拟数据获取函数
        product_data = {
            "id": "product123",
            "name": "测试商品",
            "price": 99.99
        }
        
        async def fetch_product_data():
            return product_data
        
        # 1. 首次获取数据（缓存未命中）
        result1 = await cache_helper.get_or_set("product:product123", fetch_product_data, ttl=3600)
        assert result1 == product_data
        
        # 2. 模拟缓存命中
        mock_cache_manager.get.return_value = product_data
        result2 = await cache_helper.get_or_set("product:product123", fetch_product_data, ttl=3600)
        assert result2 == product_data
        
        # 3. 清除商品缓存
        cleared_count = await cache_helper.invalidate_product_cache("product123")
        assert cleared_count is None  # 实际实现没有返回值
        
        # 验证调用次数
        assert mock_cache_manager.get.call_count >= 2
        assert mock_cache_manager.set.call_count >= 1
        assert mock_cache_manager.clear_pattern.call_count >= 1
    
    @pytest.mark.asyncio
    @patch('app.utils.cache_utils.get_cache_manager')
    async def test_rate_limiting_workflow(self, mock_get_cache_manager):
        """测试限流工作流"""
        cache_helper = CacheHelper()
        
        # 模拟缓存管理器
        mock_cache_manager = AsyncMock()
        request_count = 0
        
        def mock_get(key):
            if "rate_limit:" in key:
                return request_count if request_count > 0 else None
            return None
        
        def mock_set(key, value, ttl, cache_type):
            nonlocal request_count
            if "rate_limit:" in key:
                request_count = value
            return True
        
        mock_cache_manager.get.side_effect = mock_get
        mock_cache_manager.set.side_effect = mock_set
        mock_get_cache_manager.return_value = mock_cache_manager
        
        api_key = "test_api_key"
        limit = 5
        window = 60
        
        # 模拟多次请求
        for i in range(7):  # 超出限制
            result = await cache_helper.check_rate_limit(api_key, "/api/v1/test", limit=limit, window=window)
            if i < limit:
                assert result["allowed"] == True, f"Request {i+1} should be allowed"
            else:
                assert result["allowed"] == False, f"Request {i+1} should be blocked"
        
        # 验证最终请求计数
        assert request_count == limit  # 应该停在限制数量
