"""
提供商管理器

管理多种翻译服务提供商
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod

from app.core.logging import get_logger

logger = get_logger(__name__)


class ProviderType(Enum):
    """提供商类型"""
    OPENAI = "openai"           # OpenAI GPT
    CLAUDE = "claude"           # Anthropic Claude
    BAIDU = "baidu"             # 百度翻译
    GOOGLE = "google"           # Google翻译
    AZURE = "azure"             # Azure翻译
    DEEPL = "deepl"             # DeepL翻译
    TENCENT = "tencent"         # 腾讯翻译


class ProviderStatus(Enum):
    """提供商状态"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    ERROR = "error"             # 错误
    RATE_LIMITED = "rate_limited"  # 限流中


@dataclass
class ProviderConfig:
    """提供商配置"""
    provider_type: ProviderType
    api_key: str
    api_url: str
    model: str = ""
    max_requests_per_minute: int = 60
    max_requests_per_day: int = 10000
    timeout_seconds: int = 30
    enabled: bool = True
    priority: int = 5  # 1-10，数字越小优先级越高
    pricing: Dict[str, float] = field(default_factory=dict)
    supported_languages: List[str] = field(default_factory=list)
    quality_score: float = 8.0  # 质量评分 1-10


@dataclass
class ProviderStats:
    """提供商统计"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_cost: float = 0.0
    total_processing_time: float = 0.0
    last_request_time: Optional[datetime] = None
    requests_today: int = 0
    requests_this_minute: int = 0
    error_count: int = 0
    last_error_time: Optional[datetime] = None


class TranslationProvider(ABC):
    """翻译提供商基类"""
    
    def __init__(self, config: ProviderConfig):
        self.config = config
        self.stats = ProviderStats()
        self.status = ProviderStatus.ACTIVE
        self.name = config.provider_type.value
        self.pricing = config.pricing or {"cost_per_char": 0.0001}
    
    @abstractmethod
    async def translate(self, text: str, source_lang: str, target_lang: str, 
                       prompt: str = "", timeout: int = 30) -> str:
        """翻译文本"""
        pass
    
    async def check_health(self) -> bool:
        """检查提供商健康状态"""
        try:
            # 简单的健康检查翻译
            test_result = await self.translate("Hello", "en", "zh", timeout=10)
            return len(test_result) > 0
        except Exception as e:
            logger.error(f"提供商健康检查失败 {self.name}: {e}")
            return False
    
    def can_handle_request(self) -> bool:
        """检查是否可以处理请求"""
        if not self.config.enabled or self.status != ProviderStatus.ACTIVE:
            return False
        
        # 检查速率限制
        if self.stats.requests_this_minute >= self.config.max_requests_per_minute:
            return False
        
        if self.stats.requests_today >= self.config.max_requests_per_day:
            return False
        
        return True
    
    def update_stats(self, success: bool, processing_time: float, cost: float = 0.0):
        """更新统计信息"""
        self.stats.total_requests += 1
        self.stats.last_request_time = datetime.now()
        self.stats.requests_today += 1
        self.stats.requests_this_minute += 1
        
        if success:
            self.stats.successful_requests += 1
            self.stats.total_processing_time += processing_time
            self.stats.total_cost += cost
        else:
            self.stats.failed_requests += 1
            self.stats.error_count += 1
            self.stats.last_error_time = datetime.now()


class OpenAIProvider(TranslationProvider):
    """OpenAI翻译提供商"""
    
    async def translate(self, text: str, source_lang: str, target_lang: str, 
                       prompt: str = "", timeout: int = 30) -> str:
        """使用OpenAI进行翻译"""
        try:
            # 模拟OpenAI API调用
            logger.info(f"OpenAI翻译: {text[:50]}... ({source_lang}->{target_lang})")
            
            # 模拟API延迟
            await asyncio.sleep(0.5)
            
            # 模拟翻译结果
            if source_lang == "en" and target_lang == "zh":
                if "iPhone" in text:
                    return text.replace("iPhone", "苹果手机")
                elif "Samsung" in text:
                    return text.replace("Samsung", "三星")
                else:
                    return f"[OpenAI翻译] {text}"
            elif source_lang == "zh" and target_lang == "en":
                if "苹果手机" in text:
                    return text.replace("苹果手机", "iPhone")
                elif "三星" in text:
                    return text.replace("三星", "Samsung")
                else:
                    return f"[OpenAI Translation] {text}"
            else:
                return f"[OpenAI {target_lang}] {text}"
                
        except Exception as e:
            logger.error(f"OpenAI翻译失败: {e}")
            raise


class ClaudeProvider(TranslationProvider):
    """Claude翻译提供商"""
    
    async def translate(self, text: str, source_lang: str, target_lang: str, 
                       prompt: str = "", timeout: int = 30) -> str:
        """使用Claude进行翻译"""
        try:
            # 模拟Claude API调用
            logger.info(f"Claude翻译: {text[:50]}... ({source_lang}->{target_lang})")
            
            # 模拟API延迟
            await asyncio.sleep(0.3)
            
            # 模拟翻译结果
            if source_lang == "en" and target_lang == "zh":
                if "smartphone" in text.lower():
                    return text.replace("smartphone", "智能手机").replace("Smartphone", "智能手机")
                elif "laptop" in text.lower():
                    return text.replace("laptop", "笔记本电脑").replace("Laptop", "笔记本电脑")
                else:
                    return f"[Claude翻译] {text}"
            elif source_lang == "zh" and target_lang == "en":
                if "智能手机" in text:
                    return text.replace("智能手机", "smartphone")
                elif "笔记本电脑" in text:
                    return text.replace("笔记本电脑", "laptop")
                else:
                    return f"[Claude Translation] {text}"
            else:
                return f"[Claude {target_lang}] {text}"
                
        except Exception as e:
            logger.error(f"Claude翻译失败: {e}")
            raise


class BaiduProvider(TranslationProvider):
    """百度翻译提供商"""
    
    async def translate(self, text: str, source_lang: str, target_lang: str, 
                       prompt: str = "", timeout: int = 30) -> str:
        """使用百度翻译进行翻译"""
        try:
            # 模拟百度翻译API调用
            logger.info(f"百度翻译: {text[:50]}... ({source_lang}->{target_lang})")
            
            # 模拟API延迟
            await asyncio.sleep(0.2)
            
            # 模拟翻译结果
            if source_lang == "en" and target_lang == "zh":
                return f"[百度翻译] {text}"
            elif source_lang == "zh" and target_lang == "en":
                return f"[Baidu Translation] {text}"
            else:
                return f"[Baidu {target_lang}] {text}"
                
        except Exception as e:
            logger.error(f"百度翻译失败: {e}")
            raise


class ProviderManager:
    """提供商管理器"""
    
    def __init__(self):
        self.providers: Dict[str, TranslationProvider] = {}
        self.default_provider_name: Optional[str] = None
        
        # 初始化默认提供商
        self._initialize_default_providers()
    
    def _initialize_default_providers(self):
        """初始化默认提供商"""
        # OpenAI配置
        openai_config = ProviderConfig(
            provider_type=ProviderType.OPENAI,
            api_key="sk-placeholder",
            api_url="https://api.openai.com/v1/chat/completions",
            model="gpt-3.5-turbo",
            max_requests_per_minute=60,
            max_requests_per_day=10000,
            priority=1,
            quality_score=9.0,
            pricing={"cost_per_char": 0.0002},
            supported_languages=["en", "zh", "ja", "ko", "es", "fr", "de"]
        )
        
        # Claude配置
        claude_config = ProviderConfig(
            provider_type=ProviderType.CLAUDE,
            api_key="sk-ant-placeholder",
            api_url="https://api.anthropic.com/v1/messages",
            model="claude-3-sonnet-20240229",
            max_requests_per_minute=50,
            max_requests_per_day=8000,
            priority=2,
            quality_score=9.2,
            pricing={"cost_per_char": 0.00015},
            supported_languages=["en", "zh", "ja", "ko", "es", "fr", "de"]
        )
        
        # 百度翻译配置
        baidu_config = ProviderConfig(
            provider_type=ProviderType.BAIDU,
            api_key="baidu-placeholder",
            api_url="https://fanyi-api.baidu.com/api/trans/vip/translate",
            max_requests_per_minute=100,
            max_requests_per_day=50000,
            priority=3,
            quality_score=7.5,
            pricing={"cost_per_char": 0.00005},
            supported_languages=["en", "zh", "ja", "ko", "es", "fr", "de", "ru", "th", "vi"]
        )
        
        # 创建提供商实例
        self.providers["openai"] = OpenAIProvider(openai_config)
        self.providers["claude"] = ClaudeProvider(claude_config)
        self.providers["baidu"] = BaiduProvider(baidu_config)
        
        # 设置默认提供商
        self.default_provider_name = "openai"
    
    def add_provider(self, provider: TranslationProvider) -> bool:
        """添加提供商"""
        try:
            self.providers[provider.name] = provider
            logger.info(f"提供商已添加: {provider.name}")
            return True
        except Exception as e:
            logger.error(f"添加提供商失败: {provider.name}, {e}")
            return False
    
    def remove_provider(self, provider_name: str) -> bool:
        """移除提供商"""
        try:
            if provider_name in self.providers:
                del self.providers[provider_name]
                logger.info(f"提供商已移除: {provider_name}")
                return True
            else:
                logger.error(f"提供商不存在: {provider_name}")
                return False
        except Exception as e:
            logger.error(f"移除提供商失败: {provider_name}, {e}")
            return False
    
    async def select_best_provider(self, source_lang, target_lang, text_type) -> Optional[TranslationProvider]:
        """选择最佳提供商"""
        try:
            available_providers = []
            
            # 筛选可用提供商
            for provider in self.providers.values():
                if provider.can_handle_request():
                    # 检查语言支持
                    if (source_lang.value in provider.config.supported_languages and 
                        target_lang.value in provider.config.supported_languages):
                        available_providers.append(provider)
            
            if not available_providers:
                logger.warning("没有可用的翻译提供商")
                return None
            
            # 按优先级和质量评分排序
            available_providers.sort(
                key=lambda p: (p.config.priority, -p.config.quality_score)
            )
            
            return available_providers[0]
            
        except Exception as e:
            logger.error(f"选择最佳提供商失败: {e}")
            return None
    
    async def get_default_provider(self) -> Optional[TranslationProvider]:
        """获取默认提供商"""
        if self.default_provider_name and self.default_provider_name in self.providers:
            provider = self.providers[self.default_provider_name]
            if provider.can_handle_request():
                return provider
        
        # 如果默认提供商不可用，选择第一个可用的
        for provider in self.providers.values():
            if provider.can_handle_request():
                return provider
        
        return None
    
    def set_default_provider(self, provider_name: str) -> bool:
        """设置默认提供商"""
        if provider_name in self.providers:
            self.default_provider_name = provider_name
            logger.info(f"默认提供商已设置: {provider_name}")
            return True
        else:
            logger.error(f"提供商不存在: {provider_name}")
            return False
    
    async def check_all_providers_health(self) -> Dict[str, bool]:
        """检查所有提供商健康状态"""
        health_status = {}
        
        for name, provider in self.providers.items():
            try:
                is_healthy = await provider.check_health()
                health_status[name] = is_healthy
                
                # 更新提供商状态
                if is_healthy:
                    provider.status = ProviderStatus.ACTIVE
                else:
                    provider.status = ProviderStatus.ERROR
                    
            except Exception as e:
                logger.error(f"检查提供商健康状态失败 {name}: {e}")
                health_status[name] = False
                provider.status = ProviderStatus.ERROR
        
        return health_status
    
    def get_providers(self) -> List[TranslationProvider]:
        """获取所有提供商"""
        return list(self.providers.values())
    
    def get_provider_statistics(self) -> Dict[str, Any]:
        """获取提供商统计信息"""
        try:
            stats = {
                "total_providers": len(self.providers),
                "active_providers": len([p for p in self.providers.values() 
                                       if p.status == ProviderStatus.ACTIVE]),
                "providers": {}
            }
            
            for name, provider in self.providers.items():
                provider_stats = {
                    "name": name,
                    "type": provider.config.provider_type.value,
                    "status": provider.status.value,
                    "priority": provider.config.priority,
                    "quality_score": provider.config.quality_score,
                    "total_requests": provider.stats.total_requests,
                    "successful_requests": provider.stats.successful_requests,
                    "failed_requests": provider.stats.failed_requests,
                    "success_rate": (provider.stats.successful_requests / 
                                   max(provider.stats.total_requests, 1) * 100),
                    "total_cost": provider.stats.total_cost,
                    "avg_processing_time": (provider.stats.total_processing_time / 
                                          max(provider.stats.successful_requests, 1)),
                    "requests_today": provider.stats.requests_today,
                    "supported_languages": provider.config.supported_languages
                }
                
                stats["providers"][name] = provider_stats
            
            return stats
            
        except Exception as e:
            logger.error(f"获取提供商统计失败: {e}")
            return {
                "total_providers": 0,
                "active_providers": 0,
                "providers": {}
            }
