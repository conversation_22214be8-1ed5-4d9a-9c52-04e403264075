"""
成本管理器

提供供应商成本数据管理、成本历史跟踪、成本变化预警等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from app.core.logging import get_logger
from app.models.product import Product

logger = get_logger(__name__)


class CostType(Enum):
    """成本类型"""
    PURCHASE_COST = "purchase_cost"         # 采购成本
    SHIPPING_COST = "shipping_cost"         # 运输成本
    STORAGE_COST = "storage_cost"           # 仓储成本
    HANDLING_COST = "handling_cost"         # 处理成本
    TAX_COST = "tax_cost"                   # 税费成本
    OTHER_COST = "other_cost"               # 其他成本


class CostChangeType(Enum):
    """成本变化类型"""
    INCREASE = "increase"                   # 成本上升
    DECREASE = "decrease"                   # 成本下降
    STABLE = "stable"                       # 成本稳定
    VOLATILE = "volatile"                   # 成本波动


class AlertLevel(Enum):
    """预警级别"""
    INFO = "info"                          # 信息
    LOW = "low"                            # 低级预警
    MEDIUM = "medium"                      # 中级预警
    HIGH = "high"                          # 高级预警
    CRITICAL = "critical"                  # 严重预警


@dataclass
class CostRecord:
    """成本记录"""
    supplier_id: str
    product_id: str
    cost_type: CostType
    cost_value: float
    currency: str = "CNY"
    unit: str = "piece"
    effective_date: datetime = field(default_factory=datetime.now)
    expiry_date: Optional[datetime] = None
    notes: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"


@dataclass
class CostHistory:
    """成本历史"""
    supplier_id: str
    product_id: str
    cost_type: CostType
    cost_records: List[CostRecord] = field(default_factory=list)
    average_cost: float = 0.0
    min_cost: float = 0.0
    max_cost: float = 0.0
    cost_trend: CostChangeType = CostChangeType.STABLE
    volatility: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class CostAlert:
    """成本预警"""
    alert_id: str
    supplier_id: str
    product_id: str
    cost_type: CostType
    alert_level: AlertLevel
    alert_type: str
    current_cost: float
    previous_cost: float
    change_percent: float
    threshold_value: float
    description: str
    recommendations: List[str]
    created_at: datetime = field(default_factory=datetime.now)
    acknowledged: bool = False
    resolved: bool = False


@dataclass
class SupplierCostSummary:
    """供应商成本汇总"""
    supplier_id: str
    supplier_name: str
    total_products: int
    average_cost: float
    total_cost_value: float
    cost_trend: CostChangeType
    cost_stability: float  # 0-1, 1表示最稳定
    last_update: datetime
    cost_breakdown: Dict[CostType, float]
    performance_score: float  # 0-100


class CostManager:
    """成本管理器"""
    
    def __init__(self):
        # 成本数据存储
        self.cost_records: Dict[str, List[CostRecord]] = {}  # key: supplier_id_product_id
        self.cost_histories: Dict[str, CostHistory] = {}
        self.cost_alerts: List[CostAlert] = []
        
        # 预警配置
        self.alert_thresholds = {
            CostType.PURCHASE_COST: {
                "increase_threshold": 0.15,  # 15%增长预警
                "decrease_threshold": 0.10,  # 10%下降预警
                "volatility_threshold": 0.20  # 20%波动预警
            },
            CostType.SHIPPING_COST: {
                "increase_threshold": 0.25,
                "decrease_threshold": 0.15,
                "volatility_threshold": 0.30
            },
            CostType.STORAGE_COST: {
                "increase_threshold": 0.20,
                "decrease_threshold": 0.15,
                "volatility_threshold": 0.25
            }
        }
        
        # 供应商信息缓存
        self.supplier_cache: Dict[str, Dict[str, Any]] = {}
    
    async def add_cost_record(self, cost_record: CostRecord) -> bool:
        """
        添加成本记录
        
        Args:
            cost_record: 成本记录
        
        Returns:
            bool: 是否添加成功
        """
        try:
            key = f"{cost_record.supplier_id}_{cost_record.product_id}"
            
            if key not in self.cost_records:
                self.cost_records[key] = []
            
            # 检查是否已存在相同的记录
            existing_record = self._find_existing_record(cost_record)
            if existing_record:
                logger.warning(f"成本记录已存在: {key}")
                return False
            
            # 添加记录
            self.cost_records[key].append(cost_record)
            
            # 更新成本历史
            await self._update_cost_history(cost_record)
            
            # 检查预警
            await self._check_cost_alerts(cost_record)
            
            logger.info(f"成本记录添加成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"添加成本记录失败: {e}")
            return False
    
    async def batch_add_cost_records(self, cost_records: List[CostRecord]) -> Dict[str, Any]:
        """
        批量添加成本记录
        
        Args:
            cost_records: 成本记录列表
        
        Returns:
            Dict: 批量添加结果
        """
        results = {
            "total": len(cost_records),
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for record in cost_records:
            try:
                success = await self.add_cost_record(record)
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    results["errors"].append(f"记录添加失败: {record.supplier_id}_{record.product_id}")
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"记录处理异常: {str(e)}")
        
        logger.info(f"批量添加成本记录完成: 成功{results['success']}, 失败{results['failed']}")
        return results
    
    async def get_cost_history(self, supplier_id: str, product_id: str, 
                             cost_type: Optional[CostType] = None,
                             days: int = 90) -> Optional[CostHistory]:
        """
        获取成本历史
        
        Args:
            supplier_id: 供应商ID
            product_id: 商品ID
            cost_type: 成本类型
            days: 历史天数
        
        Returns:
            CostHistory: 成本历史
        """
        try:
            key = f"{supplier_id}_{product_id}"
            
            if key not in self.cost_records:
                return None
            
            # 过滤记录
            records = self.cost_records[key]
            cutoff_date = datetime.now() - timedelta(days=days)
            
            filtered_records = [
                record for record in records
                if record.created_at >= cutoff_date
                and (cost_type is None or record.cost_type == cost_type)
            ]
            
            if not filtered_records:
                return None
            
            # 计算统计信息
            costs = [record.cost_value for record in filtered_records]
            
            history = CostHistory(
                supplier_id=supplier_id,
                product_id=product_id,
                cost_type=cost_type or CostType.PURCHASE_COST,
                cost_records=filtered_records,
                average_cost=statistics.mean(costs),
                min_cost=min(costs),
                max_cost=max(costs),
                cost_trend=self._calculate_cost_trend(filtered_records),
                volatility=self._calculate_cost_volatility(costs),
                last_updated=max(record.created_at for record in filtered_records)
            )
            
            return history
            
        except Exception as e:
            logger.error(f"获取成本历史失败: {e}")
            return None
    
    async def compare_supplier_costs(self, product_id: str, 
                                   supplier_ids: List[str],
                                   cost_type: CostType = CostType.PURCHASE_COST) -> Dict[str, Any]:
        """
        对比供应商成本
        
        Args:
            product_id: 商品ID
            supplier_ids: 供应商ID列表
            cost_type: 成本类型
        
        Returns:
            Dict: 对比结果
        """
        try:
            comparison_result = {
                "product_id": product_id,
                "cost_type": cost_type.value,
                "suppliers": {},
                "best_supplier": None,
                "worst_supplier": None,
                "cost_range": {"min": 0, "max": 0},
                "average_cost": 0,
                "recommendations": []
            }
            
            supplier_costs = {}
            
            # 获取每个供应商的成本信息
            for supplier_id in supplier_ids:
                history = await self.get_cost_history(supplier_id, product_id, cost_type)
                if history and history.cost_records:
                    latest_record = max(history.cost_records, key=lambda x: x.created_at)
                    supplier_costs[supplier_id] = {
                        "current_cost": latest_record.cost_value,
                        "average_cost": history.average_cost,
                        "cost_trend": history.cost_trend.value,
                        "volatility": history.volatility,
                        "record_count": len(history.cost_records),
                        "last_updated": latest_record.created_at
                    }
            
            if not supplier_costs:
                return comparison_result
            
            # 分析对比结果
            costs = [data["current_cost"] for data in supplier_costs.values()]
            comparison_result["suppliers"] = supplier_costs
            comparison_result["cost_range"] = {"min": min(costs), "max": max(costs)}
            comparison_result["average_cost"] = statistics.mean(costs)
            
            # 找出最优和最差供应商
            best_supplier = min(supplier_costs.keys(), key=lambda x: supplier_costs[x]["current_cost"])
            worst_supplier = max(supplier_costs.keys(), key=lambda x: supplier_costs[x]["current_cost"])
            
            comparison_result["best_supplier"] = best_supplier
            comparison_result["worst_supplier"] = worst_supplier
            
            # 生成建议
            recommendations = self._generate_supplier_recommendations(supplier_costs)
            comparison_result["recommendations"] = recommendations
            
            return comparison_result
            
        except Exception as e:
            logger.error(f"供应商成本对比失败: {e}")
            return {"error": str(e)}
    
    async def get_cost_alerts(self, supplier_id: Optional[str] = None,
                            alert_level: Optional[AlertLevel] = None,
                            unresolved_only: bool = True) -> List[CostAlert]:
        """
        获取成本预警
        
        Args:
            supplier_id: 供应商ID
            alert_level: 预警级别
            unresolved_only: 只返回未解决的预警
        
        Returns:
            List[CostAlert]: 预警列表
        """
        try:
            alerts = self.cost_alerts
            
            # 过滤条件
            if supplier_id:
                alerts = [alert for alert in alerts if alert.supplier_id == supplier_id]
            
            if alert_level:
                alerts = [alert for alert in alerts if alert.alert_level == alert_level]
            
            if unresolved_only:
                alerts = [alert for alert in alerts if not alert.resolved]
            
            # 按创建时间倒序排列
            alerts.sort(key=lambda x: x.created_at, reverse=True)
            
            return alerts
            
        except Exception as e:
            logger.error(f"获取成本预警失败: {e}")
            return []
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """
        确认预警
        
        Args:
            alert_id: 预警ID
        
        Returns:
            bool: 是否确认成功
        """
        try:
            for alert in self.cost_alerts:
                if alert.alert_id == alert_id:
                    alert.acknowledged = True
                    logger.info(f"预警已确认: {alert_id}")
                    return True
            
            logger.warning(f"预警不存在: {alert_id}")
            return False
            
        except Exception as e:
            logger.error(f"确认预警失败: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """
        解决预警
        
        Args:
            alert_id: 预警ID
        
        Returns:
            bool: 是否解决成功
        """
        try:
            for alert in self.cost_alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    alert.acknowledged = True
                    logger.info(f"预警已解决: {alert_id}")
                    return True
            
            logger.warning(f"预警不存在: {alert_id}")
            return False
            
        except Exception as e:
            logger.error(f"解决预警失败: {e}")
            return False
    
    async def get_supplier_cost_summary(self, supplier_id: str) -> Optional[SupplierCostSummary]:
        """
        获取供应商成本汇总
        
        Args:
            supplier_id: 供应商ID
        
        Returns:
            SupplierCostSummary: 供应商成本汇总
        """
        try:
            # 获取该供应商的所有成本记录
            supplier_records = []
            product_ids = set()
            
            for key, records in self.cost_records.items():
                if key.startswith(f"{supplier_id}_"):
                    supplier_records.extend(records)
                    product_id = key.split("_", 1)[1]
                    product_ids.add(product_id)
            
            if not supplier_records:
                return None
            
            # 计算汇总信息
            costs = [record.cost_value for record in supplier_records]
            cost_breakdown = {}
            
            for cost_type in CostType:
                type_costs = [r.cost_value for r in supplier_records if r.cost_type == cost_type]
                if type_costs:
                    cost_breakdown[cost_type] = statistics.mean(type_costs)
            
            # 计算成本趋势
            recent_records = [r for r in supplier_records 
                            if r.created_at >= datetime.now() - timedelta(days=30)]
            cost_trend = self._calculate_cost_trend(recent_records) if recent_records else CostChangeType.STABLE
            
            # 计算稳定性
            cost_stability = 1.0 - min(1.0, self._calculate_cost_volatility(costs))
            
            # 计算性能评分
            performance_score = self._calculate_supplier_performance_score(
                supplier_records, cost_stability, cost_trend
            )
            
            summary = SupplierCostSummary(
                supplier_id=supplier_id,
                supplier_name=self._get_supplier_name(supplier_id),
                total_products=len(product_ids),
                average_cost=statistics.mean(costs),
                total_cost_value=sum(costs),
                cost_trend=cost_trend,
                cost_stability=cost_stability,
                last_update=max(record.created_at for record in supplier_records),
                cost_breakdown=cost_breakdown,
                performance_score=performance_score
            )
            
            return summary

        except Exception as e:
            logger.error(f"获取供应商成本汇总失败: {e}")
            return None

    def _find_existing_record(self, cost_record: CostRecord) -> Optional[CostRecord]:
        """查找已存在的成本记录"""
        key = f"{cost_record.supplier_id}_{cost_record.product_id}"

        if key not in self.cost_records:
            return None

        for record in self.cost_records[key]:
            if (record.cost_type == cost_record.cost_type and
                record.effective_date.date() == cost_record.effective_date.date()):
                return record

        return None

    async def _update_cost_history(self, cost_record: CostRecord):
        """更新成本历史"""
        key = f"{cost_record.supplier_id}_{cost_record.product_id}_{cost_record.cost_type.value}"

        if key not in self.cost_histories:
            self.cost_histories[key] = CostHistory(
                supplier_id=cost_record.supplier_id,
                product_id=cost_record.product_id,
                cost_type=cost_record.cost_type
            )

        history = self.cost_histories[key]
        history.cost_records.append(cost_record)

        # 重新计算统计信息
        costs = [record.cost_value for record in history.cost_records]
        history.average_cost = statistics.mean(costs)
        history.min_cost = min(costs)
        history.max_cost = max(costs)
        history.cost_trend = self._calculate_cost_trend(history.cost_records)
        history.volatility = self._calculate_cost_volatility(costs)
        history.last_updated = datetime.now()

    async def _check_cost_alerts(self, cost_record: CostRecord):
        """检查成本预警"""
        try:
            # 获取历史记录
            key = f"{cost_record.supplier_id}_{cost_record.product_id}"
            if key not in self.cost_records:
                return

            records = [r for r in self.cost_records[key] if r.cost_type == cost_record.cost_type]
            if len(records) < 2:
                return

            # 按时间排序
            records.sort(key=lambda x: x.created_at)
            current_record = records[-1]
            previous_record = records[-2]

            # 计算变化百分比
            if previous_record.cost_value == 0:
                return

            change_percent = (current_record.cost_value - previous_record.cost_value) / previous_record.cost_value

            # 获取预警阈值
            thresholds = self.alert_thresholds.get(cost_record.cost_type, {
                "increase_threshold": 0.20,
                "decrease_threshold": 0.15,
                "volatility_threshold": 0.25
            })

            # 检查是否需要预警
            alert_level = None
            alert_type = ""
            threshold_value = 0

            if change_percent > thresholds["increase_threshold"]:
                alert_level = self._determine_alert_level(abs(change_percent))
                alert_type = "cost_increase"
                threshold_value = thresholds["increase_threshold"]
            elif change_percent < -thresholds["decrease_threshold"]:
                alert_level = self._determine_alert_level(abs(change_percent))
                alert_type = "cost_decrease"
                threshold_value = thresholds["decrease_threshold"]

            # 检查波动性
            recent_costs = [r.cost_value for r in records[-5:]]  # 最近5个记录
            if len(recent_costs) >= 3:
                volatility = self._calculate_cost_volatility(recent_costs)
                if volatility > thresholds["volatility_threshold"]:
                    vol_alert_level = self._determine_alert_level(volatility)
                    if alert_level is None or vol_alert_level.value > alert_level.value:
                        alert_level = vol_alert_level
                        alert_type = "cost_volatility"
                        threshold_value = thresholds["volatility_threshold"]

            # 创建预警
            if alert_level:
                alert = CostAlert(
                    alert_id=f"alert_{cost_record.supplier_id}_{cost_record.product_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    supplier_id=cost_record.supplier_id,
                    product_id=cost_record.product_id,
                    cost_type=cost_record.cost_type,
                    alert_level=alert_level,
                    alert_type=alert_type,
                    current_cost=current_record.cost_value,
                    previous_cost=previous_record.cost_value,
                    change_percent=change_percent * 100,
                    threshold_value=threshold_value,
                    description=self._generate_alert_description(alert_type, change_percent, cost_record),
                    recommendations=self._generate_alert_recommendations(alert_type, change_percent)
                )

                self.cost_alerts.append(alert)
                logger.info(f"成本预警创建: {alert.alert_id}")

        except Exception as e:
            logger.error(f"检查成本预警失败: {e}")

    def _calculate_cost_trend(self, records: List[CostRecord]) -> CostChangeType:
        """计算成本趋势"""
        if len(records) < 2:
            return CostChangeType.STABLE

        # 按时间排序
        sorted_records = sorted(records, key=lambda x: x.created_at)
        costs = [record.cost_value for record in sorted_records]

        # 计算趋势
        if len(costs) >= 3:
            # 使用线性回归计算趋势
            n = len(costs)
            x = list(range(n))

            # 计算斜率
            x_mean = sum(x) / n
            y_mean = sum(costs) / n

            numerator = sum((x[i] - x_mean) * (costs[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

            if denominator == 0:
                return CostChangeType.STABLE

            slope = numerator / denominator

            # 计算波动性
            volatility = self._calculate_cost_volatility(costs)

            # 判断趋势
            if volatility > 0.2:  # 20%以上波动认为是不稳定
                return CostChangeType.VOLATILE
            elif slope > 0.01:  # 正向趋势
                return CostChangeType.INCREASE
            elif slope < -0.01:  # 负向趋势
                return CostChangeType.DECREASE
            else:
                return CostChangeType.STABLE
        else:
            # 简单比较首尾
            if costs[-1] > costs[0] * 1.05:
                return CostChangeType.INCREASE
            elif costs[-1] < costs[0] * 0.95:
                return CostChangeType.DECREASE
            else:
                return CostChangeType.STABLE

    def _calculate_cost_volatility(self, costs: List[float]) -> float:
        """计算成本波动性"""
        if len(costs) < 2:
            return 0.0

        try:
            mean_cost = statistics.mean(costs)
            if mean_cost == 0:
                return 0.0

            variance = statistics.variance(costs)
            std_dev = variance ** 0.5

            # 变异系数
            coefficient_of_variation = std_dev / mean_cost
            return coefficient_of_variation

        except Exception:
            return 0.0

    def _determine_alert_level(self, change_magnitude: float) -> AlertLevel:
        """确定预警级别"""
        if change_magnitude >= 0.5:  # 50%以上变化
            return AlertLevel.CRITICAL
        elif change_magnitude >= 0.3:  # 30%以上变化
            return AlertLevel.HIGH
        elif change_magnitude >= 0.2:  # 20%以上变化
            return AlertLevel.MEDIUM
        elif change_magnitude >= 0.1:  # 10%以上变化
            return AlertLevel.LOW
        else:
            return AlertLevel.INFO

    def _generate_alert_description(self, alert_type: str, change_percent: float,
                                  cost_record: CostRecord) -> str:
        """生成预警描述"""
        change_str = f"{abs(change_percent) * 100:.1f}%"

        if alert_type == "cost_increase":
            return f"供应商 {cost_record.supplier_id} 的 {cost_record.cost_type.value} 成本上升 {change_str}"
        elif alert_type == "cost_decrease":
            return f"供应商 {cost_record.supplier_id} 的 {cost_record.cost_type.value} 成本下降 {change_str}"
        elif alert_type == "cost_volatility":
            return f"供应商 {cost_record.supplier_id} 的 {cost_record.cost_type.value} 成本波动过大"
        else:
            return f"供应商 {cost_record.supplier_id} 的成本发生异常变化"

    def _generate_alert_recommendations(self, alert_type: str, change_percent: float) -> List[str]:
        """生成预警建议"""
        recommendations = []

        if alert_type == "cost_increase":
            recommendations.extend([
                "联系供应商了解成本上升原因",
                "评估是否需要寻找替代供应商",
                "考虑调整采购策略或数量",
                "分析对产品定价的影响"
            ])
        elif alert_type == "cost_decrease":
            recommendations.extend([
                "确认成本下降的真实性",
                "了解成本下降的原因和持续性",
                "考虑增加采购量以获得更好价格",
                "评估供应商的财务稳定性"
            ])
        elif alert_type == "cost_volatility":
            recommendations.extend([
                "与供应商协商稳定价格机制",
                "考虑签订长期合同锁定价格",
                "寻找价格更稳定的替代供应商",
                "建立成本风险管理策略"
            ])

        return recommendations

    def _generate_supplier_recommendations(self, supplier_costs: Dict[str, Dict[str, Any]]) -> List[str]:
        """生成供应商建议"""
        recommendations = []

        if not supplier_costs:
            return recommendations

        # 找出最优供应商
        best_supplier = min(supplier_costs.keys(), key=lambda x: supplier_costs[x]["current_cost"])
        best_cost = supplier_costs[best_supplier]["current_cost"]

        # 找出最稳定的供应商
        most_stable = min(supplier_costs.keys(), key=lambda x: supplier_costs[x]["volatility"])

        recommendations.append(f"推荐选择供应商 {best_supplier}，成本最低 ({best_cost:.2f})")

        if most_stable != best_supplier:
            recommendations.append(f"供应商 {most_stable} 价格最稳定，适合长期合作")

        # 分析成本差异
        costs = [data["current_cost"] for data in supplier_costs.values()]
        cost_range = max(costs) - min(costs)
        avg_cost = statistics.mean(costs)

        if cost_range > avg_cost * 0.2:  # 成本差异超过20%
            recommendations.append("供应商间成本差异较大，建议重新谈判价格")

        # 分析趋势
        increasing_suppliers = [sid for sid, data in supplier_costs.items()
                              if data["cost_trend"] == "increase"]
        if increasing_suppliers:
            recommendations.append(f"注意供应商 {', '.join(increasing_suppliers)} 成本上升趋势")

        return recommendations

    def _calculate_supplier_performance_score(self, records: List[CostRecord],
                                            stability: float, trend: CostChangeType) -> float:
        """计算供应商性能评分"""
        score = 50.0  # 基础分

        # 稳定性加分
        score += stability * 30  # 最多30分

        # 趋势加分
        if trend == CostChangeType.DECREASE:
            score += 15  # 成本下降加分
        elif trend == CostChangeType.STABLE:
            score += 10  # 成本稳定加分
        elif trend == CostChangeType.INCREASE:
            score -= 10  # 成本上升扣分
        elif trend == CostChangeType.VOLATILE:
            score -= 20  # 成本波动扣分

        # 记录数量加分（数据越多越可靠）
        record_count_bonus = min(10, len(records) * 0.5)
        score += record_count_bonus

        # 时效性加分
        if records:
            latest_record = max(records, key=lambda x: x.created_at)
            days_since_update = (datetime.now() - latest_record.created_at).days
            if days_since_update <= 7:
                score += 5  # 最近更新加分
            elif days_since_update > 30:
                score -= 5  # 更新不及时扣分

        return max(0, min(100, score))

    def _get_supplier_name(self, supplier_id: str) -> str:
        """获取供应商名称"""
        if supplier_id in self.supplier_cache:
            return self.supplier_cache[supplier_id].get("name", supplier_id)
        return supplier_id

    def get_cost_statistics(self) -> Dict[str, Any]:
        """获取成本统计信息"""
        total_records = sum(len(records) for records in self.cost_records.values())
        total_suppliers = len(set(key.split("_")[0] for key in self.cost_records.keys()))
        total_products = len(set(key.split("_", 1)[1] for key in self.cost_records.keys()))

        return {
            "total_cost_records": total_records,
            "total_suppliers": total_suppliers,
            "total_products": total_products,
            "total_alerts": len(self.cost_alerts),
            "unresolved_alerts": len([a for a in self.cost_alerts if not a.resolved]),
            "cost_types": [ct.value for ct in CostType],
            "alert_levels": [al.value for al in AlertLevel]
        }
