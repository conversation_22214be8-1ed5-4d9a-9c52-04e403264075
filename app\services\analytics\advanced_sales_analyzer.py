"""
高级销量趋势分析引擎

提供深度销量分析、异常检测、预警和高级预测功能
"""

import asyncio
import statistics
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType, ProductMetrics
from app.services.analytics.sales_analyzer import SalesAnalyzer, SalesTrend, SalesPerformance
from app.services.analytics.trend_calculator import TrendCalculator
from app.services.analytics.prediction_engine import PredictionEngine

logger = get_logger(__name__)


class SalesAnomalyType(Enum):
    """销量异常类型"""
    SUDDEN_DROP = "sudden_drop"         # 突然下降
    SUDDEN_SPIKE = "sudden_spike"       # 突然激增
    GRADUAL_DECLINE = "gradual_decline" # 逐渐下降
    STAGNATION = "stagnation"          # 停滞不前
    VOLATILITY = "volatility"          # 高波动
    SEASONAL_ANOMALY = "seasonal_anomaly" # 季节性异常


class SalesAlertLevel(Enum):
    """销量预警级别"""
    CRITICAL = "critical"    # 严重：需要立即行动
    HIGH = "high"           # 高：需要密切关注
    MEDIUM = "medium"       # 中：需要监控
    LOW = "low"            # 低：轻微异常
    INFO = "info"          # 信息：正常变化


class MarketPosition(Enum):
    """市场地位"""
    LEADER = "leader"           # 市场领导者
    CHALLENGER = "challenger"   # 挑战者
    FOLLOWER = "follower"      # 跟随者
    NICHE = "niche"            # 细分市场


@dataclass
class SalesAnomaly:
    """销量异常"""
    anomaly_type: SalesAnomalyType
    alert_level: SalesAlertLevel
    detected_at: datetime
    description: str
    severity_score: float  # 0-1
    affected_period: Tuple[datetime, datetime]
    baseline_value: float
    anomaly_value: float
    deviation_percent: float
    recommendations: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketShareAnalysis:
    """市场份额分析"""
    product_id: str
    category: str
    current_market_share: float
    market_share_trend: str
    market_position: MarketPosition
    market_size_estimate: int
    growth_opportunity: float
    competitive_threats: List[str]
    market_insights: List[str]
    share_history: List[Tuple[datetime, float]]


@dataclass
class SalesGrowthAnalysis:
    """销量增长分析"""
    product_id: str
    growth_rate_daily: float
    growth_rate_weekly: float
    growth_rate_monthly: float
    growth_acceleration: float  # 增长加速度
    growth_sustainability: float  # 增长可持续性 0-1
    growth_drivers: List[str]
    growth_barriers: List[str]
    growth_forecast: List[Tuple[datetime, int]]


@dataclass
class CompetitiveAnalysis:
    """竞争分析"""
    product_id: str
    direct_competitors: List[str]
    competitive_advantage: str
    market_differentiation: float  # 0-1
    price_competitiveness: float   # 0-1
    sales_competitiveness: float   # 0-1
    threat_level: str
    strategic_recommendations: List[str]


@dataclass
class SalesSeasonality:
    """销量季节性分析"""
    product_id: str
    has_seasonality: bool
    seasonal_strength: float
    peak_seasons: List[str]
    low_seasons: List[str]
    seasonal_factors: Dict[str, float]
    seasonal_forecast: List[Tuple[datetime, float]]


class AdvancedSalesAnalyzer:
    """高级销量趋势分析引擎"""
    
    def __init__(self):
        self.base_analyzer = SalesAnalyzer()
        self.trend_calculator = TrendCalculator()
        self.prediction_engine = PredictionEngine()
        
        # 异常检测阈值
        self.anomaly_thresholds = {
            SalesAnomalyType.SUDDEN_DROP: 0.3,      # 30%下降
            SalesAnomalyType.SUDDEN_SPIKE: 0.5,     # 50%激增
            SalesAnomalyType.GRADUAL_DECLINE: 0.2,  # 20%逐渐下降
            SalesAnomalyType.STAGNATION: 0.05,      # 5%变化内
            SalesAnomalyType.VOLATILITY: 0.25,      # 25%波动率
            SalesAnomalyType.SEASONAL_ANOMALY: 0.4  # 40%季节偏差
        }
        
        # 市场份额基准
        self.market_benchmarks = {
            ProductType.COMPETITOR: {
                "leader": 0.25,      # 25%以上
                "challenger": 0.15,  # 15-25%
                "follower": 0.05,    # 5-15%
                "niche": 0.05        # 5%以下
            }
        }
    
    async def detect_sales_anomalies(self, product: Product, 
                                   days: int = 30) -> List[SalesAnomaly]:
        """
        检测销量异常
        
        Args:
            product: 商品对象
            days: 分析天数
        
        Returns:
            List[SalesAnomaly]: 异常列表
        """
        try:
            logger.info(f"开始销量异常检测: {product.id}")
            
            # 获取销量历史数据
            if product.id not in self.base_analyzer.sales_history:
                return []
            
            sales_data = await self.base_analyzer._get_sales_data(product.id, days)
            if len(sales_data) < 7:  # 至少需要一周数据
                return []
            
            sales_values = [point.sales_count for point in sales_data]
            timestamps = [point.timestamp for point in sales_data]
            
            anomalies = []
            
            # 1. 突然下降检测
            sudden_drop = await self._detect_sudden_drop(sales_values, timestamps)
            if sudden_drop:
                anomalies.append(sudden_drop)
            
            # 2. 突然激增检测
            sudden_spike = await self._detect_sudden_spike(sales_values, timestamps)
            if sudden_spike:
                anomalies.append(sudden_spike)
            
            # 3. 逐渐下降检测
            gradual_decline = await self._detect_gradual_decline(sales_values, timestamps)
            if gradual_decline:
                anomalies.append(gradual_decline)
            
            # 4. 停滞检测
            stagnation = await self._detect_stagnation(sales_values, timestamps)
            if stagnation:
                anomalies.append(stagnation)
            
            # 5. 高波动检测
            volatility = await self._detect_high_volatility(sales_values, timestamps)
            if volatility:
                anomalies.append(volatility)
            
            # 6. 季节性异常检测
            seasonal_anomaly = await self._detect_seasonal_anomaly(sales_values, timestamps)
            if seasonal_anomaly:
                anomalies.append(seasonal_anomaly)
            
            logger.info(f"销量异常检测完成: {product.id} - 发现 {len(anomalies)} 个异常")
            return anomalies
            
        except Exception as e:
            logger.error(f"销量异常检测失败: {e}")
            return []
    
    async def _detect_sudden_drop(self, sales_values: List[int], 
                                timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测突然下降"""
        if len(sales_values) < 3:
            return None
        
        # 检查最近的销量变化
        recent_values = sales_values[-3:]
        if len(recent_values) < 3:
            return None
        
        # 计算下降幅度
        baseline = statistics.mean(recent_values[:-1])
        current = recent_values[-1]
        
        if baseline == 0:
            return None
        
        drop_percent = (baseline - current) / baseline
        threshold = self.anomaly_thresholds[SalesAnomalyType.SUDDEN_DROP]
        
        if drop_percent >= threshold:
            severity = min(1.0, drop_percent / threshold)
            alert_level = self._determine_alert_level(severity)
            
            return SalesAnomaly(
                anomaly_type=SalesAnomalyType.SUDDEN_DROP,
                alert_level=alert_level,
                detected_at=datetime.now(),
                description=f"销量突然下降 {drop_percent:.1%}",
                severity_score=severity,
                affected_period=(timestamps[-2], timestamps[-1]),
                baseline_value=baseline,
                anomaly_value=current,
                deviation_percent=drop_percent * 100,
                recommendations=[
                    "立即调查销量下降原因",
                    "检查产品库存和供应链",
                    "分析竞品动态和市场变化",
                    "考虑调整营销策略"
                ]
            )
        
        return None
    
    async def _detect_sudden_spike(self, sales_values: List[int], 
                                 timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测突然激增"""
        if len(sales_values) < 3:
            return None
        
        recent_values = sales_values[-3:]
        if len(recent_values) < 3:
            return None
        
        baseline = statistics.mean(recent_values[:-1])
        current = recent_values[-1]
        
        if baseline == 0:
            return None
        
        spike_percent = (current - baseline) / baseline
        threshold = self.anomaly_thresholds[SalesAnomalyType.SUDDEN_SPIKE]
        
        if spike_percent >= threshold:
            severity = min(1.0, spike_percent / threshold)
            alert_level = SalesAlertLevel.INFO  # 激增通常是好事
            
            return SalesAnomaly(
                anomaly_type=SalesAnomalyType.SUDDEN_SPIKE,
                alert_level=alert_level,
                detected_at=datetime.now(),
                description=f"销量突然激增 {spike_percent:.1%}",
                severity_score=severity,
                affected_period=(timestamps[-2], timestamps[-1]),
                baseline_value=baseline,
                anomaly_value=current,
                deviation_percent=spike_percent * 100,
                recommendations=[
                    "分析激增原因，总结成功经验",
                    "确保库存充足，避免缺货",
                    "考虑扩大营销投入",
                    "监控是否为可持续增长"
                ]
            )
        
        return None
    
    async def _detect_gradual_decline(self, sales_values: List[int], 
                                    timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测逐渐下降"""
        if len(sales_values) < 7:
            return None
        
        # 使用线性回归检测下降趋势
        trend_result = self.trend_calculator.calculate_linear_trend(
            [float(v) for v in sales_values]
        )
        
        if (trend_result.direction.value == "down" and 
            trend_result.strength.value in ["strong", "very_strong"] and
            abs(trend_result.change_percent) >= self.anomaly_thresholds[SalesAnomalyType.GRADUAL_DECLINE] * 100):
            
            severity = min(1.0, abs(trend_result.change_percent) / 50)  # 50%为最高严重度
            alert_level = self._determine_alert_level(severity)
            
            return SalesAnomaly(
                anomaly_type=SalesAnomalyType.GRADUAL_DECLINE,
                alert_level=alert_level,
                detected_at=datetime.now(),
                description=f"销量逐渐下降 {abs(trend_result.change_percent):.1f}%",
                severity_score=severity,
                affected_period=(timestamps[0], timestamps[-1]),
                baseline_value=trend_result.start_value,
                anomaly_value=trend_result.end_value,
                deviation_percent=trend_result.change_percent,
                recommendations=[
                    "制定销量提升计划",
                    "分析市场竞争态势",
                    "优化产品定位和营销",
                    "考虑产品创新或升级"
                ]
            )
        
        return None
    
    async def _detect_stagnation(self, sales_values: List[int], 
                               timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测停滞不前"""
        if len(sales_values) < 10:
            return None
        
        # 计算最近期间的变化率
        recent_period = sales_values[-7:]  # 最近一周
        if not recent_period:
            return None
        
        mean_sales = statistics.mean(recent_period)
        if mean_sales == 0:
            return None
        
        # 计算变异系数
        std_sales = statistics.stdev(recent_period) if len(recent_period) > 1 else 0
        coefficient_of_variation = std_sales / mean_sales
        
        threshold = self.anomaly_thresholds[SalesAnomalyType.STAGNATION]
        
        if coefficient_of_variation <= threshold:
            severity = 1.0 - coefficient_of_variation / threshold
            alert_level = self._determine_alert_level(severity)
            
            return SalesAnomaly(
                anomaly_type=SalesAnomalyType.STAGNATION,
                alert_level=alert_level,
                detected_at=datetime.now(),
                description=f"销量停滞不前，变化率仅 {coefficient_of_variation:.1%}",
                severity_score=severity,
                affected_period=(timestamps[-7], timestamps[-1]),
                baseline_value=mean_sales,
                anomaly_value=mean_sales,
                deviation_percent=0,
                recommendations=[
                    "寻找新的增长点",
                    "加强市场推广活动",
                    "分析用户需求变化",
                    "考虑产品差异化策略"
                ]
            )
        
        return None
    
    async def _detect_high_volatility(self, sales_values: List[int], 
                                    timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测高波动"""
        if len(sales_values) < 5:
            return None
        
        # 计算波动率
        volatility = self.trend_calculator._calculate_volatility([float(v) for v in sales_values])
        threshold = self.anomaly_thresholds[SalesAnomalyType.VOLATILITY]
        
        if volatility >= threshold:
            severity = min(1.0, volatility / threshold)
            alert_level = self._determine_alert_level(severity)
            
            return SalesAnomaly(
                anomaly_type=SalesAnomalyType.VOLATILITY,
                alert_level=alert_level,
                detected_at=datetime.now(),
                description=f"销量波动过大，波动率 {volatility:.1%}",
                severity_score=severity,
                affected_period=(timestamps[0], timestamps[-1]),
                baseline_value=statistics.mean(sales_values),
                anomaly_value=volatility,
                deviation_percent=volatility * 100,
                recommendations=[
                    "分析波动原因",
                    "稳定供应链和库存",
                    "优化营销节奏",
                    "建立销量预测模型"
                ]
            )
        
        return None
    
    async def _detect_seasonal_anomaly(self, sales_values: List[int], 
                                     timestamps: List[datetime]) -> Optional[SalesAnomaly]:
        """检测季节性异常"""
        if len(sales_values) < 14:  # 至少需要两周数据
            return None
        
        # 检测季节性
        seasonality_result = self.trend_calculator.detect_seasonality(
            [float(v) for v in sales_values]
        )
        
        if not seasonality_result.has_seasonality:
            return None
        
        # 检查当前值是否偏离季节性模式
        current_day_of_week = timestamps[-1].weekday()
        if current_day_of_week < len(seasonality_result.seasonal_pattern):
            expected_value = seasonality_result.seasonal_pattern[current_day_of_week]
            current_value = sales_values[-1]
            
            if expected_value > 0:
                deviation = abs(current_value - expected_value) / expected_value
                threshold = self.anomaly_thresholds[SalesAnomalyType.SEASONAL_ANOMALY]
                
                if deviation >= threshold:
                    severity = min(1.0, deviation / threshold)
                    alert_level = self._determine_alert_level(severity)
                    
                    return SalesAnomaly(
                        anomaly_type=SalesAnomalyType.SEASONAL_ANOMALY,
                        alert_level=alert_level,
                        detected_at=datetime.now(),
                        description=f"销量偏离季节性模式 {deviation:.1%}",
                        severity_score=severity,
                        affected_period=(timestamps[-1], timestamps[-1]),
                        baseline_value=expected_value,
                        anomaly_value=current_value,
                        deviation_percent=deviation * 100,
                        recommendations=[
                            "检查是否有特殊事件影响",
                            "分析季节性策略有效性",
                            "调整季节性营销计划",
                            "更新季节性预测模型"
                        ]
                    )
        
        return None
    
    def _determine_alert_level(self, severity: float) -> SalesAlertLevel:
        """确定预警级别"""
        if severity >= 0.8:
            return SalesAlertLevel.CRITICAL
        elif severity >= 0.6:
            return SalesAlertLevel.HIGH
        elif severity >= 0.4:
            return SalesAlertLevel.MEDIUM
        elif severity >= 0.2:
            return SalesAlertLevel.LOW
        else:
            return SalesAlertLevel.INFO
    
    async def analyze_market_share(self, product: Product, 
                                 competitor_products: List[Product],
                                 category: str = "default") -> MarketShareAnalysis:
        """
        分析市场份额
        
        Args:
            product: 目标商品
            competitor_products: 竞品列表
            category: 商品类别
        
        Returns:
            MarketShareAnalysis: 市场份额分析结果
        """
        try:
            logger.info(f"开始市场份额分析: {product.id}")
            
            # 获取当前销量
            current_sales = product.metrics.sales_count if product.metrics else 0
            
            # 收集竞品销量
            competitor_sales = []
            for comp in competitor_products:
                if comp.metrics and comp.metrics.sales_count:
                    competitor_sales.append(comp.metrics.sales_count)
            
            # 计算市场总量
            total_market_sales = current_sales + sum(competitor_sales)
            
            # 计算市场份额
            current_market_share = (current_sales / total_market_sales * 100 
                                  if total_market_sales > 0 else 0)
            
            # 确定市场地位
            market_position = self._determine_market_position(
                current_market_share, product.product_type
            )
            
            # 分析市场份额趋势
            market_share_trend = await self._analyze_share_trend(product)
            
            # 估算市场规模
            market_size_estimate = self._estimate_market_size(total_market_sales, len(competitor_products))
            
            # 计算增长机会
            growth_opportunity = self._calculate_growth_opportunity(
                current_market_share, market_position
            )
            
            # 识别竞争威胁
            competitive_threats = self._identify_competitive_threats(
                current_sales, competitor_sales
            )
            
            # 生成市场洞察
            market_insights = self._generate_market_insights(
                current_market_share, market_position, market_share_trend
            )
            
            return MarketShareAnalysis(
                product_id=product.id,
                category=category,
                current_market_share=current_market_share,
                market_share_trend=market_share_trend,
                market_position=market_position,
                market_size_estimate=market_size_estimate,
                growth_opportunity=growth_opportunity,
                competitive_threats=competitive_threats,
                market_insights=market_insights,
                share_history=[]  # 简化实现，实际应包含历史数据
            )
            
        except Exception as e:
            logger.error(f"市场份额分析失败: {e}")
            return MarketShareAnalysis(
                product_id=product.id,
                category=category,
                current_market_share=0.0,
                market_share_trend="unknown",
                market_position=MarketPosition.FOLLOWER,
                market_size_estimate=0,
                growth_opportunity=0.0,
                competitive_threats=[],
                market_insights=[f"分析失败: {str(e)}"],
                share_history=[]
            )
    
    def _determine_market_position(self, market_share: float, 
                                 product_type: ProductType) -> MarketPosition:
        """确定市场地位"""
        benchmarks = self.market_benchmarks.get(product_type, 
                                               self.market_benchmarks[ProductType.COMPETITOR])
        
        if market_share >= benchmarks["leader"] * 100:
            return MarketPosition.LEADER
        elif market_share >= benchmarks["challenger"] * 100:
            return MarketPosition.CHALLENGER
        elif market_share >= benchmarks["follower"] * 100:
            return MarketPosition.FOLLOWER
        else:
            return MarketPosition.NICHE
    
    async def _analyze_share_trend(self, product: Product) -> str:
        """分析市场份额趋势"""
        # 简化实现，实际应基于历史市场份额数据
        if product.id in self.base_analyzer.sales_history:
            sales_data = self.base_analyzer.sales_history[product.id]
            if len(sales_data) >= 2:
                recent_sales = [point.sales_count for point in sales_data[-7:]]
                if len(recent_sales) >= 2:
                    trend = recent_sales[-1] - recent_sales[0]
                    if trend > 0:
                        return "increasing"
                    elif trend < 0:
                        return "decreasing"
                    else:
                        return "stable"
        
        return "unknown"
    
    def _estimate_market_size(self, observed_sales: int, competitor_count: int) -> int:
        """估算市场规模"""
        # 简化的市场规模估算
        # 假设观察到的销量只占市场的一部分
        market_coverage_factor = min(1.0, (competitor_count + 1) / 10)  # 假设需要10个主要参与者
        estimated_total_market = int(observed_sales / market_coverage_factor)
        
        return estimated_total_market
    
    def _calculate_growth_opportunity(self, current_share: float, 
                                    position: MarketPosition) -> float:
        """计算增长机会"""
        # 基于市场地位计算增长机会
        if position == MarketPosition.LEADER:
            return max(0, 100 - current_share) * 0.3  # 领导者增长空间有限
        elif position == MarketPosition.CHALLENGER:
            return max(0, 100 - current_share) * 0.6  # 挑战者有较大增长空间
        elif position == MarketPosition.FOLLOWER:
            return max(0, 100 - current_share) * 0.8  # 跟随者增长空间很大
        else:  # NICHE
            return max(0, 100 - current_share) * 0.5  # 细分市场增长空间中等
    
    def _identify_competitive_threats(self, current_sales: int, 
                                    competitor_sales: List[int]) -> List[str]:
        """识别竞争威胁"""
        threats = []
        
        if not competitor_sales:
            return threats
        
        # 找出销量超过自己的竞品
        stronger_competitors = [sales for sales in competitor_sales if sales > current_sales]
        
        if stronger_competitors:
            threats.append(f"有 {len(stronger_competitors)} 个竞品销量超过本产品")
        
        # 找出快速增长的竞品（简化实现）
        avg_competitor_sales = statistics.mean(competitor_sales)
        if avg_competitor_sales > current_sales * 1.2:
            threats.append("竞品平均销量明显高于本产品")
        
        # 市场集中度威胁
        max_competitor_sales = max(competitor_sales)
        if max_competitor_sales > current_sales * 2:
            threats.append("存在销量远超本产品的强势竞品")
        
        return threats
    
    def _generate_market_insights(self, market_share: float, 
                                position: MarketPosition,
                                trend: str) -> List[str]:
        """生成市场洞察"""
        insights = []
        
        # 市场份额洞察
        if market_share > 20:
            insights.append("市场份额较高，具有市场影响力")
        elif market_share > 10:
            insights.append("市场份额中等，有提升空间")
        else:
            insights.append("市场份额较低，需要加强竞争力")
        
        # 市场地位洞察
        if position == MarketPosition.LEADER:
            insights.append("市场领导地位，需要保持优势")
        elif position == MarketPosition.CHALLENGER:
            insights.append("挑战者地位，有机会争夺领导地位")
        elif position == MarketPosition.FOLLOWER:
            insights.append("跟随者地位，需要寻找差异化优势")
        else:
            insights.append("细分市场定位，专注特定用户群体")
        
        # 趋势洞察
        if trend == "increasing":
            insights.append("市场份额呈上升趋势，发展势头良好")
        elif trend == "decreasing":
            insights.append("市场份额呈下降趋势，需要关注竞争态势")
        elif trend == "stable":
            insights.append("市场份额相对稳定，可考虑突破性策略")
        
        return insights

    async def analyze_sales_growth(self, product: Product,
                                 days: int = 30) -> SalesGrowthAnalysis:
        """
        分析销量增长

        Args:
            product: 商品对象
            days: 分析天数

        Returns:
            SalesGrowthAnalysis: 销量增长分析结果
        """
        try:
            logger.info(f"开始销量增长分析: {product.id}")

            # 获取销量历史数据
            if product.id not in self.base_analyzer.sales_history:
                return self._create_empty_growth_analysis(product)

            sales_data = await self.base_analyzer._get_sales_data(product.id, days)
            if len(sales_data) < 7:
                return self._create_empty_growth_analysis(product)

            sales_values = [point.sales_count for point in sales_data]
            timestamps = [point.timestamp for point in sales_data]

            # 计算不同周期的增长率
            growth_rate_daily = self._calculate_growth_rate(sales_values, 1)
            growth_rate_weekly = self._calculate_growth_rate(sales_values, 7)
            growth_rate_monthly = self._calculate_growth_rate(sales_values, 30)

            # 计算增长加速度
            growth_acceleration = self._calculate_growth_acceleration(sales_values)

            # 评估增长可持续性
            growth_sustainability = self._assess_growth_sustainability(sales_values)

            # 识别增长驱动因素
            growth_drivers = self._identify_growth_drivers(product, sales_values)

            # 识别增长障碍
            growth_barriers = self._identify_growth_barriers(product, sales_values)

            # 生成增长预测
            growth_forecast = await self._generate_growth_forecast(
                product, sales_values, timestamps
            )

            return SalesGrowthAnalysis(
                product_id=product.id,
                growth_rate_daily=growth_rate_daily,
                growth_rate_weekly=growth_rate_weekly,
                growth_rate_monthly=growth_rate_monthly,
                growth_acceleration=growth_acceleration,
                growth_sustainability=growth_sustainability,
                growth_drivers=growth_drivers,
                growth_barriers=growth_barriers,
                growth_forecast=growth_forecast
            )

        except Exception as e:
            logger.error(f"销量增长分析失败: {e}")
            return self._create_empty_growth_analysis(product)

    def _calculate_growth_rate(self, sales_values: List[int], period: int) -> float:
        """计算指定周期的增长率"""
        if len(sales_values) < period + 1:
            return 0.0

        current_period = sales_values[-period:]
        previous_period = sales_values[-period*2:-period] if len(sales_values) >= period*2 else sales_values[:-period]

        if not previous_period:
            return 0.0

        current_avg = statistics.mean(current_period)
        previous_avg = statistics.mean(previous_period)

        if previous_avg == 0:
            return 0.0

        return (current_avg - previous_avg) / previous_avg * 100

    def _calculate_growth_acceleration(self, sales_values: List[int]) -> float:
        """计算增长加速度"""
        if len(sales_values) < 6:
            return 0.0

        # 计算最近三个时期的增长率
        recent_growth_rates = []
        for i in range(3):
            start_idx = len(sales_values) - (i+1)*2
            end_idx = len(sales_values) - i*2

            if start_idx >= 0:
                period_values = sales_values[start_idx:end_idx]
                if len(period_values) >= 2:
                    growth_rate = (period_values[-1] - period_values[0]) / period_values[0] * 100 if period_values[0] > 0 else 0
                    recent_growth_rates.append(growth_rate)

        if len(recent_growth_rates) < 2:
            return 0.0

        # 计算增长率的变化（加速度）
        recent_growth_rates.reverse()  # 按时间顺序排列
        acceleration = recent_growth_rates[-1] - recent_growth_rates[0]

        return acceleration

    def _assess_growth_sustainability(self, sales_values: List[int]) -> float:
        """评估增长可持续性"""
        if len(sales_values) < 5:
            return 0.0

        # 基于多个因素评估可持续性
        sustainability_score = 0.0

        # 1. 增长一致性（波动率越低，可持续性越高）
        growth_rates = []
        for i in range(1, len(sales_values)):
            if sales_values[i-1] > 0:
                growth_rate = (sales_values[i] - sales_values[i-1]) / sales_values[i-1]
                growth_rates.append(growth_rate)

        if growth_rates:
            growth_volatility = statistics.stdev(growth_rates) if len(growth_rates) > 1 else 0
            consistency_score = max(0, 1 - growth_volatility * 2)  # 波动率越低分数越高
            sustainability_score += consistency_score * 0.4

        # 2. 增长趋势强度
        trend_result = self.trend_calculator.calculate_linear_trend([float(v) for v in sales_values])
        if trend_result.direction.value == "up":
            trend_strength = abs(trend_result.correlation)
            sustainability_score += trend_strength * 0.3

        # 3. 增长幅度合理性（过快增长可能不可持续）
        if len(sales_values) >= 2:
            total_growth = (sales_values[-1] - sales_values[0]) / sales_values[0] if sales_values[0] > 0 else 0
            # 合理的增长范围是10%-100%
            if 0.1 <= total_growth <= 1.0:
                sustainability_score += 0.3
            elif total_growth > 1.0:
                # 过快增长，可持续性降低
                sustainability_score += max(0, 0.3 - (total_growth - 1.0) * 0.1)

        return min(1.0, max(0.0, sustainability_score))

    def _identify_growth_drivers(self, product: Product, sales_values: List[int]) -> List[str]:
        """识别增长驱动因素"""
        drivers = []

        # 基于产品类型识别可能的驱动因素
        if product.product_type == ProductType.COMPETITOR:
            drivers.extend([
                "产品创新和功能升级",
                "营销推广活动效果",
                "品牌知名度提升",
                "用户口碑传播"
            ])
        elif product.product_type == ProductType.SUPPLIER:
            drivers.extend([
                "下游需求增长",
                "供应链优化",
                "批发价格优势",
                "渠道拓展"
            ])

        # 基于销量趋势识别驱动因素
        if len(sales_values) >= 2:
            recent_growth = (sales_values[-1] - sales_values[0]) / sales_values[0] if sales_values[0] > 0 else 0

            if recent_growth > 0.5:  # 50%以上增长
                drivers.append("强劲的市场需求")
            elif recent_growth > 0.2:  # 20%以上增长
                drivers.append("稳定的市场需求增长")

        # 基于价格信息识别驱动因素
        if product.price:
            if product.price.current_price < 100:  # 低价商品
                drivers.append("价格优势吸引用户")
            elif product.price.current_price > 1000:  # 高价商品
                drivers.append("高端定位和品质认知")

        return drivers

    def _identify_growth_barriers(self, product: Product, sales_values: List[int]) -> List[str]:
        """识别增长障碍"""
        barriers = []

        # 基于销量趋势识别障碍
        if len(sales_values) >= 2:
            recent_growth = (sales_values[-1] - sales_values[0]) / sales_values[0] if sales_values[0] > 0 else 0

            if recent_growth < -0.1:  # 下降超过10%
                barriers.extend([
                    "市场竞争加剧",
                    "产品生命周期进入衰退期",
                    "用户需求变化"
                ])
            elif recent_growth < 0.05:  # 增长缓慢
                barriers.extend([
                    "市场饱和度较高",
                    "缺乏差异化优势",
                    "营销投入不足"
                ])

        # 基于产品类型识别障碍
        if product.product_type == ProductType.COMPETITOR:
            barriers.extend([
                "竞品价格战",
                "技术迭代压力",
                "用户获取成本上升"
            ])
        elif product.product_type == ProductType.SUPPLIER:
            barriers.extend([
                "原材料成本上涨",
                "下游客户议价能力强",
                "供应链风险"
            ])

        return barriers

    async def _generate_growth_forecast(self, product: Product,
                                      sales_values: List[int],
                                      timestamps: List[datetime]) -> List[Tuple[datetime, int]]:
        """生成增长预测"""
        try:
            # 使用预测引擎生成销量预测
            sales_prediction = await self.prediction_engine.predict_sales_trend(
                product, sales_values, timestamps, prediction_days=14
            )

            forecast = []
            base_date = timestamps[-1] if timestamps else datetime.now()

            for i, predicted_sales in enumerate(sales_prediction.predicted_sales):
                forecast_date = base_date + timedelta(days=i+1)
                forecast.append((forecast_date, predicted_sales))

            return forecast

        except Exception as e:
            logger.error(f"增长预测生成失败: {e}")
            return []

    def _create_empty_growth_analysis(self, product: Product) -> SalesGrowthAnalysis:
        """创建空的增长分析结果"""
        return SalesGrowthAnalysis(
            product_id=product.id,
            growth_rate_daily=0.0,
            growth_rate_weekly=0.0,
            growth_rate_monthly=0.0,
            growth_acceleration=0.0,
            growth_sustainability=0.0,
            growth_drivers=["数据不足，无法分析"],
            growth_barriers=["数据不足，无法分析"],
            growth_forecast=[]
        )

    async def analyze_competitive_landscape(self, product: Product,
                                          competitor_products: List[Product]) -> CompetitiveAnalysis:
        """
        分析竞争格局

        Args:
            product: 目标商品
            competitor_products: 竞品列表

        Returns:
            CompetitiveAnalysis: 竞争分析结果
        """
        try:
            logger.info(f"开始竞争格局分析: {product.id}")

            # 识别直接竞品
            direct_competitors = [comp.id for comp in competitor_products
                                if self._is_direct_competitor(product, comp)]

            # 评估竞争优势
            competitive_advantage = self._assess_competitive_advantage(product, competitor_products)

            # 计算市场差异化程度
            market_differentiation = self._calculate_market_differentiation(product, competitor_products)

            # 评估价格竞争力
            price_competitiveness = self._assess_price_competitiveness(product, competitor_products)

            # 评估销量竞争力
            sales_competitiveness = self._assess_sales_competitiveness(product, competitor_products)

            # 评估威胁级别
            threat_level = self._assess_threat_level(
                price_competitiveness, sales_competitiveness, len(direct_competitors)
            )

            # 生成战略建议
            strategic_recommendations = self._generate_strategic_recommendations(
                competitive_advantage, market_differentiation, threat_level
            )

            return CompetitiveAnalysis(
                product_id=product.id,
                direct_competitors=direct_competitors,
                competitive_advantage=competitive_advantage,
                market_differentiation=market_differentiation,
                price_competitiveness=price_competitiveness,
                sales_competitiveness=sales_competitiveness,
                threat_level=threat_level,
                strategic_recommendations=strategic_recommendations
            )

        except Exception as e:
            logger.error(f"竞争格局分析失败: {e}")
            return CompetitiveAnalysis(
                product_id=product.id,
                direct_competitors=[],
                competitive_advantage="unknown",
                market_differentiation=0.0,
                price_competitiveness=0.0,
                sales_competitiveness=0.0,
                threat_level="unknown",
                strategic_recommendations=[f"分析失败: {str(e)}"]
            )

    def _is_direct_competitor(self, product: Product, competitor: Product) -> bool:
        """判断是否为直接竞品"""
        # 简化的直接竞品判断逻辑

        # 1. 相同产品类型
        if product.product_type != competitor.product_type:
            return False

        # 2. 价格区间相近（±50%）
        if product.price and competitor.price:
            price_ratio = competitor.price.current_price / product.price.current_price
            if not (0.5 <= price_ratio <= 2.0):
                return False

        # 3. 品牌相似度（简化判断）
        if (product.specs and competitor.specs and
            product.specs.brand and competitor.specs.brand):
            # 如果是同品牌，通常是直接竞品
            if product.specs.brand == competitor.specs.brand:
                return True

        return True  # 默认认为是直接竞品

    def _assess_competitive_advantage(self, product: Product,
                                    competitors: List[Product]) -> str:
        """评估竞争优势"""
        if not competitors:
            return "no_competitors"

        current_sales = product.metrics.sales_count if product.metrics else 0
        competitor_sales = [comp.metrics.sales_count for comp in competitors
                          if comp.metrics and comp.metrics.sales_count]

        if not competitor_sales:
            return "unknown"

        avg_competitor_sales = statistics.mean(competitor_sales)
        max_competitor_sales = max(competitor_sales)

        if current_sales > max_competitor_sales:
            return "market_leader"
        elif current_sales > avg_competitor_sales * 1.2:
            return "strong_position"
        elif current_sales > avg_competitor_sales * 0.8:
            return "competitive_position"
        else:
            return "weak_position"

    def _calculate_market_differentiation(self, product: Product,
                                        competitors: List[Product]) -> float:
        """计算市场差异化程度"""
        # 简化的差异化计算
        differentiation_score = 0.0

        # 基于价格差异化
        if product.price and competitors:
            product_price = product.price.current_price
            competitor_prices = [comp.price.current_price for comp in competitors
                               if comp.price and comp.price.current_price]

            if competitor_prices:
                avg_competitor_price = statistics.mean(competitor_prices)
                price_diff = abs(product_price - avg_competitor_price) / avg_competitor_price
                differentiation_score += min(0.5, price_diff)  # 最多贡献0.5分

        # 基于品牌差异化
        if product.specs and product.specs.brand:
            competitor_brands = [comp.specs.brand for comp in competitors
                               if comp.specs and comp.specs.brand]

            if product.specs.brand not in competitor_brands:
                differentiation_score += 0.3  # 独特品牌贡献0.3分

        # 基于销量差异化
        if product.metrics:
            current_sales = product.metrics.sales_count
            competitor_sales = [comp.metrics.sales_count for comp in competitors
                              if comp.metrics and comp.metrics.sales_count]

            if competitor_sales:
                avg_competitor_sales = statistics.mean(competitor_sales)
                if avg_competitor_sales > 0:
                    sales_diff = abs(current_sales - avg_competitor_sales) / avg_competitor_sales
                    differentiation_score += min(0.2, sales_diff)  # 最多贡献0.2分

        return min(1.0, differentiation_score)

    def _assess_price_competitiveness(self, product: Product,
                                    competitors: List[Product]) -> float:
        """评估价格竞争力"""
        if not product.price or not competitors:
            return 0.5  # 中性分数

        product_price = product.price.current_price
        competitor_prices = [comp.price.current_price for comp in competitors
                           if comp.price and comp.price.current_price]

        if not competitor_prices:
            return 0.5

        avg_competitor_price = statistics.mean(competitor_prices)

        if avg_competitor_price == 0:
            return 0.5

        # 价格越低，竞争力越强（在合理范围内）
        price_ratio = product_price / avg_competitor_price

        if price_ratio <= 0.8:  # 价格低20%以上
            return 0.9
        elif price_ratio <= 0.9:  # 价格低10-20%
            return 0.8
        elif price_ratio <= 1.1:  # 价格相近
            return 0.6
        elif price_ratio <= 1.2:  # 价格高10-20%
            return 0.4
        else:  # 价格高20%以上
            return 0.2

    def _assess_sales_competitiveness(self, product: Product,
                                    competitors: List[Product]) -> float:
        """评估销量竞争力"""
        if not product.metrics or not competitors:
            return 0.5

        current_sales = product.metrics.sales_count
        competitor_sales = [comp.metrics.sales_count for comp in competitors
                          if comp.metrics and comp.metrics.sales_count]

        if not competitor_sales:
            return 0.5

        max_competitor_sales = max(competitor_sales)
        avg_competitor_sales = statistics.mean(competitor_sales)

        if max_competitor_sales == 0:
            return 0.5

        # 基于相对销量位置评估竞争力
        if current_sales >= max_competitor_sales:
            return 1.0  # 销量最高
        elif current_sales >= avg_competitor_sales:
            return 0.7  # 销量高于平均
        elif current_sales >= avg_competitor_sales * 0.5:
            return 0.5  # 销量中等
        else:
            return 0.3  # 销量较低

    def _assess_threat_level(self, price_comp: float, sales_comp: float,
                           competitor_count: int) -> str:
        """评估威胁级别"""
        # 综合评估威胁级别
        competitiveness_score = (price_comp + sales_comp) / 2

        # 竞品数量影响
        competitor_factor = min(1.0, competitor_count / 10)  # 10个竞品为满分

        threat_score = (1 - competitiveness_score) * (1 + competitor_factor)

        if threat_score >= 1.5:
            return "very_high"
        elif threat_score >= 1.2:
            return "high"
        elif threat_score >= 0.8:
            return "medium"
        elif threat_score >= 0.5:
            return "low"
        else:
            return "very_low"

    def _generate_strategic_recommendations(self, advantage: str,
                                          differentiation: float,
                                          threat_level: str) -> List[str]:
        """生成战略建议"""
        recommendations = []

        # 基于竞争优势的建议
        if advantage == "market_leader":
            recommendations.extend([
                "保持市场领导地位，继续创新",
                "扩大市场份额，巩固优势",
                "关注新兴竞品威胁"
            ])
        elif advantage == "weak_position":
            recommendations.extend([
                "寻找差异化定位",
                "优化成本结构，提高价格竞争力",
                "加强产品创新和营销"
            ])

        # 基于差异化程度的建议
        if differentiation < 0.3:
            recommendations.append("加强产品差异化，避免同质化竞争")
        elif differentiation > 0.7:
            recommendations.append("利用差异化优势，建立品牌护城河")

        # 基于威胁级别的建议
        if threat_level in ["very_high", "high"]:
            recommendations.extend([
                "制定应对竞争的紧急策略",
                "加强市场监控和快速响应能力"
            ])
        elif threat_level == "very_low":
            recommendations.append("考虑进入新市场或扩大产品线")

        return recommendations
