"""
商品管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from uuid import UUID
import pandas as pd
import io

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.models.database import Product as DBProduct, ProductHistory as DBProductHistory
from app.models.schemas import Product, ProductCreate, ProductUpdate, ProductList

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", summary="获取商品列表")
async def get_products(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    category: Optional[str] = Query(None, description="分类筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取商品列表"""
    logger.info(f"获取商品列表 - skip: {skip}, limit: {limit}, platform: {platform}, category: {category}, status: {status}")

    try:
        # 构建查询条件
        query = select(DBProduct)
        conditions = []

        # 平台筛选
        if platform:
            conditions.append(DBProduct.platform == platform)

        # 分类筛选
        if category:
            conditions.append(DBProduct.category == category)

        # 状态筛选
        if status:
            conditions.append(DBProduct.status == status)

        # 激活状态筛选
        if is_active is not None:
            conditions.append(DBProduct.is_active == is_active)

        # 搜索功能
        if search:
            search_condition = or_(
                DBProduct.title.ilike(f"%{search}%"),
                DBProduct.title_translated.ilike(f"%{search}%"),
                DBProduct.url.ilike(f"%{search}%")
            )
            conditions.append(search_condition)

        # 应用筛选条件
        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        query = query.offset(skip).limit(limit).order_by(DBProduct.created_at.desc())
        result = await db.execute(query)
        products = result.scalars().all()

        # 转换为响应模型
        items = []
        for product in products:
            items.append({
                "id": str(product.id),
                "url": product.url,
                "platform": product.platform,
                "title": product.title,
                "title_translated": product.title_translated,
                "category": product.category,
                "status": product.status,
                "monitoring_frequency": product.monitoring_frequency,
                "last_monitored_at": product.last_monitored_at,
                "is_active": product.is_active,
                "tags": product.tags,
                "notes": product.notes,
                "created_at": product.created_at,
                "updated_at": product.updated_at
            })

        logger.info(f"成功获取商品列表 - 总数: {total}, 返回: {len(items)}")
        return {
            "items": items,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"获取商品列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")


@router.post("/", summary="创建商品")
async def create_product(
    product_data: ProductCreate,
    db: AsyncSession = Depends(get_db_session)
):
    """创建新商品"""
    logger.info(f"创建新商品 - URL: {product_data.url}, 平台: {product_data.platform}")

    try:
        # 检查URL是否已存在
        existing_query = select(DBProduct).where(DBProduct.url == str(product_data.url))
        existing_result = await db.execute(existing_query)
        existing_product = existing_result.scalar_one_or_none()

        if existing_product:
            raise HTTPException(status_code=400, detail="该商品URL已存在")

        # 创建新商品
        new_product = DBProduct(
            url=str(product_data.url),
            platform=product_data.platform,
            title=product_data.title,
            title_translated=product_data.title_translated,
            category=product_data.category,
            status=product_data.status,
            monitoring_frequency=product_data.monitoring_frequency,
            is_active=product_data.is_active,
            tags=product_data.tags,
            notes=product_data.notes
        )

        db.add(new_product)
        await db.commit()
        await db.refresh(new_product)

        logger.info(f"商品创建成功 - ID: {new_product.id}")
        return {
            "id": str(new_product.id),
            "url": new_product.url,
            "platform": new_product.platform,
            "title": new_product.title,
            "title_translated": new_product.title_translated,
            "category": new_product.category,
            "status": new_product.status,
            "monitoring_frequency": new_product.monitoring_frequency,
            "is_active": new_product.is_active,
            "tags": new_product.tags,
            "notes": new_product.notes,
            "created_at": new_product.created_at,
            "updated_at": new_product.updated_at
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建商品失败: {str(e)}")


@router.get("/{product_id}", summary="获取商品详情")
async def get_product(
    product_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取商品详情"""
    logger.info(f"获取商品详情 - ID: {product_id}")

    try:
        # 验证UUID格式
        try:
            product_uuid = UUID(product_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的商品ID格式")

        # 查询商品详情
        query = select(DBProduct).where(DBProduct.id == product_uuid)
        result = await db.execute(query)
        product = result.scalar_one_or_none()

        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")

        # 查询最近的历史记录
        history_query = select(DBProductHistory).where(
            DBProductHistory.product_id == product_uuid
        ).order_by(DBProductHistory.created_at.desc()).limit(5)
        history_result = await db.execute(history_query)
        history_records = history_result.scalars().all()

        # 构建响应数据
        product_detail = {
            "id": str(product.id),
            "url": product.url,
            "platform": product.platform,
            "title": product.title,
            "title_translated": product.title_translated,
            "category": product.category,
            "status": product.status,
            "monitoring_frequency": product.monitoring_frequency,
            "last_monitored_at": product.last_monitored_at,
            "is_active": product.is_active,
            "tags": product.tags,
            "notes": product.notes,
            "created_at": product.created_at,
            "updated_at": product.updated_at,
            "recent_history": [
                {
                    "id": str(record.id),
                    "price": float(record.price) if record.price else None,
                    "currency": record.currency,
                    "stock_quantity": record.stock_quantity,
                    "rating": float(record.rating) if record.rating else None,
                    "review_count": record.review_count,
                    "created_at": record.created_at
                }
                for record in history_records
            ]
        }

        logger.info(f"成功获取商品详情 - ID: {product_id}")
        return product_detail

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商品详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商品详情失败: {str(e)}")


@router.put("/{product_id}", summary="更新商品")
async def update_product(
    product_id: str,
    product_data: ProductUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """更新商品信息"""
    logger.info(f"更新商品 - ID: {product_id}")

    try:
        # 验证UUID格式
        try:
            product_uuid = UUID(product_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的商品ID格式")

        # 查询商品是否存在
        query = select(DBProduct).where(DBProduct.id == product_uuid)
        result = await db.execute(query)
        product = result.scalar_one_or_none()

        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")

        # 更新商品信息（只更新提供的字段）
        update_data = product_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            if hasattr(product, field):
                setattr(product, field, value)

        await db.commit()
        await db.refresh(product)

        logger.info(f"商品更新成功 - ID: {product_id}")
        return {
            "id": str(product.id),
            "url": product.url,
            "platform": product.platform,
            "title": product.title,
            "title_translated": product.title_translated,
            "category": product.category,
            "status": product.status,
            "monitoring_frequency": product.monitoring_frequency,
            "is_active": product.is_active,
            "tags": product.tags,
            "notes": product.notes,
            "created_at": product.created_at,
            "updated_at": product.updated_at
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新商品失败: {str(e)}")


@router.delete("/{product_id}", summary="删除商品")
async def delete_product(
    product_id: str,
    force: bool = Query(False, description="是否强制删除（物理删除）"),
    db: AsyncSession = Depends(get_db_session)
):
    """删除商品（默认软删除）"""
    logger.info(f"删除商品 - ID: {product_id}, 强制删除: {force}")

    try:
        # 验证UUID格式
        try:
            product_uuid = UUID(product_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的商品ID格式")

        # 查询商品是否存在
        query = select(DBProduct).where(DBProduct.id == product_uuid)
        result = await db.execute(query)
        product = result.scalar_one_or_none()

        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")

        if force:
            # 物理删除：删除商品及其相关数据
            await db.delete(product)
            logger.info(f"商品物理删除成功 - ID: {product_id}")
            message = "商品已永久删除"
        else:
            # 软删除：更新状态为deleted并停用
            product.status = "deleted"
            product.is_active = False
            logger.info(f"商品软删除成功 - ID: {product_id}")
            message = "商品已删除（可恢复）"

        await db.commit()

        return {
            "message": message,
            "product_id": product_id,
            "deleted_permanently": force
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除商品失败: {str(e)}")


@router.post("/import", summary="批量导入商品")
async def import_products(
    file: UploadFile = File(..., description="Excel或CSV文件"),
    db: AsyncSession = Depends(get_db_session)
):
    """批量导入商品"""
    logger.info(f"批量导入商品 - 文件: {file.filename}")

    try:
        # 验证文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            raise HTTPException(status_code=400, detail="仅支持Excel(.xlsx, .xls)或CSV文件")

        # 读取文件内容
        content = await file.read()

        # 解析文件
        if file.filename.lower().endswith('.csv'):
            df = pd.read_csv(io.StringIO(content.decode('utf-8')))
        else:
            df = pd.read_excel(io.BytesIO(content))

        # 验证必需的列
        required_columns = ['url', 'platform']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"缺少必需的列: {', '.join(missing_columns)}"
            )

        # 批量导入商品
        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # 检查URL是否已存在
                existing_query = select(DBProduct).where(DBProduct.url == str(row['url']))
                existing_result = await db.execute(existing_query)
                existing_product = existing_result.scalar_one_or_none()

                if existing_product:
                    errors.append(f"第{index+2}行: URL已存在 - {row['url']}")
                    error_count += 1
                    continue

                # 创建新商品
                new_product = DBProduct(
                    url=str(row['url']),
                    platform=str(row['platform']),
                    title=str(row.get('title', '')),
                    title_translated=str(row.get('title_translated', '')) if pd.notna(row.get('title_translated')) else None,
                    category=str(row.get('category', '')) if pd.notna(row.get('category')) else None,
                    status=str(row.get('status', 'active')),
                    monitoring_frequency=int(row.get('monitoring_frequency', 24)),
                    is_active=bool(row.get('is_active', True)),
                    notes=str(row.get('notes', '')) if pd.notna(row.get('notes')) else None
                )

                db.add(new_product)
                success_count += 1

            except Exception as e:
                errors.append(f"第{index+2}行: {str(e)}")
                error_count += 1

        # 提交事务
        await db.commit()

        logger.info(f"批量导入完成 - 成功: {success_count}, 失败: {error_count}")
        return {
            "message": "批量导入完成",
            "success_count": success_count,
            "error_count": error_count,
            "errors": errors[:10]  # 只返回前10个错误
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"批量导入失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量导入失败: {str(e)}")


@router.get("/{product_id}/history", summary="获取商品历史数据")
async def get_product_history(
    product_id: str,
    days: int = Query(30, ge=1, le=365, description="历史天数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取商品历史数据"""
    logger.info(f"获取商品历史数据 - ID: {product_id}, days: {days}, limit: {limit}")

    try:
        # 验证UUID格式
        try:
            product_uuid = UUID(product_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的商品ID格式")

        # 验证商品是否存在
        product_query = select(DBProduct).where(DBProduct.id == product_uuid)
        product_result = await db.execute(product_query)
        product = product_result.scalar_one_or_none()

        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")

        # 计算时间范围
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 查询历史数据
        history_query = select(DBProductHistory).where(
            and_(
                DBProductHistory.product_id == product_uuid,
                DBProductHistory.created_at >= start_date,
                DBProductHistory.created_at <= end_date
            )
        ).order_by(DBProductHistory.created_at.desc()).limit(limit)

        history_result = await db.execute(history_query)
        history_records = history_result.scalars().all()

        # 构建响应数据
        history_data = []
        for record in history_records:
            history_data.append({
                "id": str(record.id),
                "price": float(record.price) if record.price else None,
                "currency": record.currency,
                "stock_quantity": record.stock_quantity,
                "sales_count": record.sales_count,
                "rating": float(record.rating) if record.rating else None,
                "review_count": record.review_count,
                "change_type": record.change_type,
                "change_value": float(record.change_value) if record.change_value else None,
                "data_quality_score": float(record.data_quality_score) if record.data_quality_score else None,
                "created_at": record.created_at
            })

        # 统计信息
        stats = {
            "total_records": len(history_data),
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "price_stats": None,
            "stock_stats": None
        }

        # 计算价格统计
        prices = [h["price"] for h in history_data if h["price"] is not None]
        if prices:
            stats["price_stats"] = {
                "min": min(prices),
                "max": max(prices),
                "avg": sum(prices) / len(prices),
                "latest": prices[0] if prices else None
            }

        # 计算库存统计
        stocks = [h["stock_quantity"] for h in history_data if h["stock_quantity"] is not None]
        if stocks:
            stats["stock_stats"] = {
                "min": min(stocks),
                "max": max(stocks),
                "avg": sum(stocks) / len(stocks),
                "latest": stocks[0] if stocks else None
            }

        logger.info(f"成功获取商品历史数据 - ID: {product_id}, 记录数: {len(history_data)}")
        return {
            "product_id": product_id,
            "product_title": product.title,
            "days": days,
            "history": history_data,
            "stats": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商品历史数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商品历史数据失败: {str(e)}")
