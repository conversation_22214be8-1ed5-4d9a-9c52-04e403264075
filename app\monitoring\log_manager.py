"""
日志管理系统

实现结构化日志记录、日志轮转和管理、日志级别和分类
"""

import logging
import logging.handlers
import json
import os
import gzip
import shutil
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
import threading
import queue
import time

from app.core.logging import get_logger

logger = get_logger(__name__)


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(Enum):
    """日志分类"""
    SYSTEM = "system"
    AUTH = "auth"
    API = "api"
    DATABASE = "database"
    TRANSLATION = "translation"
    MONITORING = "monitoring"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BUSINESS = "business"
    ERROR = "error"


@dataclass
class LogRotationConfig:
    """日志轮转配置"""
    max_file_size_mb: int = 100
    backup_count: int = 10
    rotation_interval: str = "daily"  # daily, weekly, monthly
    compress_old_logs: bool = True
    retention_days: int = 30


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: LogLevel
    category: LogCategory
    message: str
    logger_name: str = ""
    module: str = ""
    function: str = ""
    line_number: int = 0
    thread_id: int = 0
    process_id: int = 0
    user_id: str = ""
    session_id: str = ""
    request_id: str = ""
    extra_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "category": self.category.value,
            "message": self.message,
            "logger_name": self.logger_name,
            "module": self.module,
            "function": self.function,
            "line_number": self.line_number,
            "thread_id": self.thread_id,
            "process_id": self.process_id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": self.request_id,
            "extra_data": self.extra_data
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        try:
            # 基本信息
            log_data = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger_name": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line_number": record.lineno,
                "thread_id": record.thread,
                "process_id": record.process
            }
            
            # 添加异常信息
            if record.exc_info:
                log_data["exception"] = self.formatException(record.exc_info)
            
            # 添加额外字段
            if self.include_extra:
                extra_data = {}
                for key, value in record.__dict__.items():
                    if key not in {
                        'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                        'filename', 'module', 'lineno', 'funcName', 'created',
                        'msecs', 'relativeCreated', 'thread', 'threadName',
                        'processName', 'process', 'getMessage', 'exc_info',
                        'exc_text', 'stack_info'
                    }:
                        extra_data[key] = value
                
                if extra_data:
                    log_data["extra_data"] = extra_data
            
            return json.dumps(log_data, ensure_ascii=False)
            
        except Exception as e:
            # 如果格式化失败，返回基本信息
            return json.dumps({
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": f"日志格式化失败: {str(e)}",
                "original_message": str(record.msg)
            }, ensure_ascii=False)


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs", config: LogRotationConfig = None):
        self.log_dir = log_dir
        self.config = config or LogRotationConfig()
        
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 日志器配置
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        
        # 日志统计
        self.log_stats = {
            "total_logs": 0,
            "by_level": {level.value: 0 for level in LogLevel},
            "by_category": {category.value: 0 for category in LogCategory},
            "by_logger": {},
            "errors_today": 0,
            "warnings_today": 0,
            "last_error": None,
            "last_warning": None
        }
        
        # 异步日志队列
        self.log_queue = queue.Queue(maxsize=10000)
        self.log_thread = None
        self.is_running = False
        
        # 初始化默认日志器
        self._setup_default_loggers()
        
        # 启动异步日志处理
        self.start_async_logging()
    
    def _setup_default_loggers(self):
        """设置默认日志器"""
        try:
            # 为每个分类创建日志器
            for category in LogCategory:
                logger_name = f"moniit.{category.value}"
                log_file = os.path.join(self.log_dir, f"{category.value}.log")
                
                # 创建日志器
                logger_obj = logging.getLogger(logger_name)
                logger_obj.setLevel(logging.DEBUG)
                
                # 创建轮转文件处理器
                handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=self.config.max_file_size_mb * 1024 * 1024,
                    backupCount=self.config.backup_count,
                    encoding='utf-8'
                )
                
                # 设置格式化器
                formatter = StructuredFormatter()
                handler.setFormatter(formatter)
                
                # 添加处理器
                logger_obj.addHandler(handler)
                
                # 存储引用
                self.loggers[category.value] = logger_obj
                self.handlers[category.value] = handler
            
            # 创建主日志器
            main_logger = logging.getLogger("moniit")
            main_logger.setLevel(logging.INFO)
            
            # 主日志文件
            main_handler = logging.handlers.RotatingFileHandler(
                os.path.join(self.log_dir, "main.log"),
                maxBytes=self.config.max_file_size_mb * 1024 * 1024,
                backupCount=self.config.backup_count,
                encoding='utf-8'
            )
            main_handler.setFormatter(StructuredFormatter())
            main_logger.addHandler(main_handler)
            
            self.loggers["main"] = main_logger
            self.handlers["main"] = main_handler
            
            logger.info("默认日志器设置完成")
            
        except Exception as e:
            logger.error(f"设置默认日志器失败: {e}")
    
    def log(self, level: LogLevel, category: LogCategory, message: str,
           user_id: str = "", session_id: str = "", request_id: str = "",
           extra_data: Dict[str, Any] = None, logger_name: str = ""):
        """
        记录日志
        
        Args:
            level: 日志级别
            category: 日志分类
            message: 日志消息
            user_id: 用户ID
            session_id: 会话ID
            request_id: 请求ID
            extra_data: 额外数据
            logger_name: 日志器名称
        """
        try:
            # 创建日志条目
            log_entry = LogEntry(
                timestamp=datetime.now(),
                level=level,
                category=category,
                message=message,
                logger_name=logger_name or f"moniit.{category.value}",
                user_id=user_id,
                session_id=session_id,
                request_id=request_id,
                extra_data=extra_data or {}
            )
            
            # 添加到异步队列
            if not self.log_queue.full():
                self.log_queue.put(log_entry)
            else:
                # 队列满时直接写入
                self._write_log_entry(log_entry)
            
            # 更新统计
            self._update_log_stats(log_entry)
            
        except Exception as e:
            # 记录日志失败时的处理
            print(f"记录日志失败: {e}")
    
    def debug(self, category: LogCategory, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self.log(LogLevel.DEBUG, category, message, **kwargs)
    
    def info(self, category: LogCategory, message: str, **kwargs):
        """记录INFO级别日志"""
        self.log(LogLevel.INFO, category, message, **kwargs)
    
    def warning(self, category: LogCategory, message: str, **kwargs):
        """记录WARNING级别日志"""
        self.log(LogLevel.WARNING, category, message, **kwargs)
    
    def error(self, category: LogCategory, message: str, **kwargs):
        """记录ERROR级别日志"""
        self.log(LogLevel.ERROR, category, message, **kwargs)
    
    def critical(self, category: LogCategory, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self.log(LogLevel.CRITICAL, category, message, **kwargs)
    
    def _write_log_entry(self, log_entry: LogEntry):
        """写入日志条目"""
        try:
            # 获取对应的日志器
            logger_obj = self.loggers.get(log_entry.category.value)
            if not logger_obj:
                logger_obj = self.loggers.get("main")
            
            if logger_obj:
                # 创建日志记录
                record = logging.LogRecord(
                    name=log_entry.logger_name,
                    level=getattr(logging, log_entry.level.value),
                    pathname="",
                    lineno=log_entry.line_number,
                    msg=log_entry.message,
                    args=(),
                    exc_info=None
                )
                
                # 添加额外字段
                record.user_id = log_entry.user_id
                record.session_id = log_entry.session_id
                record.request_id = log_entry.request_id
                record.category = log_entry.category.value
                
                for key, value in log_entry.extra_data.items():
                    setattr(record, key, value)
                
                # 写入日志
                logger_obj.handle(record)
            
        except Exception as e:
            print(f"写入日志条目失败: {e}")
    
    def _update_log_stats(self, log_entry: LogEntry):
        """更新日志统计"""
        try:
            self.log_stats["total_logs"] += 1
            self.log_stats["by_level"][log_entry.level.value] += 1
            self.log_stats["by_category"][log_entry.category.value] += 1
            
            # 按日志器统计
            logger_key = log_entry.logger_name
            self.log_stats["by_logger"][logger_key] = self.log_stats["by_logger"].get(logger_key, 0) + 1
            
            # 今日错误和警告统计
            today = datetime.now().date()
            if log_entry.timestamp.date() == today:
                if log_entry.level == LogLevel.ERROR:
                    self.log_stats["errors_today"] += 1
                    self.log_stats["last_error"] = log_entry.timestamp.isoformat()
                elif log_entry.level == LogLevel.WARNING:
                    self.log_stats["warnings_today"] += 1
                    self.log_stats["last_warning"] = log_entry.timestamp.isoformat()
            
        except Exception as e:
            print(f"更新日志统计失败: {e}")
    
    def start_async_logging(self):
        """启动异步日志处理"""
        try:
            if self.is_running:
                return
            
            self.is_running = True
            self.log_thread = threading.Thread(target=self._async_log_worker, daemon=True)
            self.log_thread.start()
            
            logger.info("异步日志处理已启动")
            
        except Exception as e:
            logger.error(f"启动异步日志处理失败: {e}")
    
    def stop_async_logging(self):
        """停止异步日志处理"""
        try:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 等待队列清空
            while not self.log_queue.empty():
                time.sleep(0.1)
            
            if self.log_thread and self.log_thread.is_alive():
                self.log_thread.join(timeout=5)
            
            logger.info("异步日志处理已停止")
            
        except Exception as e:
            logger.error(f"停止异步日志处理失败: {e}")
    
    def _async_log_worker(self):
        """异步日志工作线程"""
        try:
            while self.is_running:
                try:
                    # 从队列获取日志条目
                    log_entry = self.log_queue.get(timeout=1)
                    self._write_log_entry(log_entry)
                    self.log_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"异步日志处理失败: {e}")
                    
        except Exception as e:
            print(f"异步日志工作线程异常: {e}")
    
    def rotate_logs(self):
        """手动轮转日志"""
        try:
            for handler_name, handler in self.handlers.items():
                if isinstance(handler, logging.handlers.RotatingFileHandler):
                    handler.doRollover()
                    logger.info(f"日志轮转完成: {handler_name}")
            
        except Exception as e:
            logger.error(f"日志轮转失败: {e}")
    
    def compress_old_logs(self):
        """压缩旧日志文件"""
        try:
            if not self.config.compress_old_logs:
                return
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log.1') or filename.endswith('.log.2'):
                    log_file = os.path.join(self.log_dir, filename)
                    compressed_file = log_file + '.gz'
                    
                    if not os.path.exists(compressed_file):
                        with open(log_file, 'rb') as f_in:
                            with gzip.open(compressed_file, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        os.remove(log_file)
                        logger.info(f"压缩日志文件: {filename}")
            
        except Exception as e:
            logger.error(f"压缩旧日志失败: {e}")
    
    def cleanup_old_logs(self):
        """清理过期日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config.retention_days)
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log') or filename.endswith('.gz'):
                    log_file = os.path.join(self.log_dir, filename)
                    file_stat = os.stat(log_file)
                    file_date = datetime.fromtimestamp(file_stat.st_mtime)
                    
                    if file_date < cutoff_date:
                        os.remove(log_file)
                        logger.info(f"删除过期日志: {filename}")
            
        except Exception as e:
            logger.error(f"清理过期日志失败: {e}")
    
    def search_logs(self, category: LogCategory = None, level: LogLevel = None,
                   start_date: datetime = None, end_date: datetime = None,
                   keyword: str = "", limit: int = 1000) -> List[Dict[str, Any]]:
        """
        搜索日志
        
        Args:
            category: 日志分类
            level: 日志级别
            start_date: 开始日期
            end_date: 结束日期
            keyword: 关键词
            limit: 返回条数限制
        
        Returns:
            List[Dict[str, Any]]: 日志条目列表
        """
        try:
            results = []
            
            # 确定要搜索的日志文件
            log_files = []
            if category:
                log_files.append(os.path.join(self.log_dir, f"{category.value}.log"))
            else:
                for cat in LogCategory:
                    log_file = os.path.join(self.log_dir, f"{cat.value}.log")
                    if os.path.exists(log_file):
                        log_files.append(log_file)
            
            # 搜索日志文件
            for log_file in log_files:
                if not os.path.exists(log_file):
                    continue
                
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                log_data = json.loads(line.strip())
                                
                                # 应用过滤条件
                                if level and log_data.get("level") != level.value:
                                    continue
                                
                                if start_date or end_date:
                                    log_time = datetime.fromisoformat(log_data["timestamp"])
                                    if start_date and log_time < start_date:
                                        continue
                                    if end_date and log_time > end_date:
                                        continue
                                
                                if keyword and keyword.lower() not in log_data.get("message", "").lower():
                                    continue
                                
                                results.append(log_data)
                                
                                if len(results) >= limit:
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    if len(results) >= limit:
                        break
                        
                except Exception as e:
                    logger.error(f"搜索日志文件失败 {log_file}: {e}")
                    continue
            
            # 按时间戳排序
            results.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            return []
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        return {
            "stats": self.log_stats.copy(),
            "config": {
                "log_dir": self.log_dir,
                "max_file_size_mb": self.config.max_file_size_mb,
                "backup_count": self.config.backup_count,
                "retention_days": self.config.retention_days,
                "compress_old_logs": self.config.compress_old_logs
            },
            "loggers": list(self.loggers.keys()),
            "queue_size": self.log_queue.qsize(),
            "is_running": self.is_running
        }
    
    def export_logs(self, output_file: str, category: LogCategory = None,
                   start_date: datetime = None, end_date: datetime = None) -> bool:
        """
        导出日志
        
        Args:
            output_file: 输出文件路径
            category: 日志分类
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            bool: 是否成功
        """
        try:
            logs = self.search_logs(
                category=category,
                start_date=start_date,
                end_date=end_date,
                limit=100000
            )
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for log_entry in logs:
                    f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
            logger.info(f"导出日志成功: {output_file}, 共{len(logs)}条")
            return True
            
        except Exception as e:
            logger.error(f"导出日志失败: {e}")
            return False
