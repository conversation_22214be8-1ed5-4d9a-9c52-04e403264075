"""
Task-Middleware集成测试

测试统一数据获取层的各个组件
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from decimal import Decimal

from app.services.task_middleware import (
    TaskMiddlewareClient, 
    PlatformConfigManager, 
    DataNormalizer,
    TaskScheduler
)
from app.services.task_middleware.client import CrawlConfig, TaskPriority, TaskStatus
from app.services.task_middleware.config_manager import Platform, ProductType
from app.services.task_middleware.task_scheduler import ScheduleTask, ScheduleStrategy


class TestTaskMiddlewareClient:
    """Task-Middleware客户端测试"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TaskMiddlewareClient("http://localhost:11238")

    @pytest.fixture
    def sample_crawl_config(self):
        """示例爬取配置"""
        return CrawlConfig(
            urls=["https://detail.1688.com/offer/123456.html"],
            query="提取商品信息：标题、价格、库存、销量",
            priority=TaskPriority.MEDIUM,
            batch_name="test_batch"
        )

    def test_client_initialization(self, client):
        """测试客户端初始化"""
        assert client.base_url == "http://localhost:11238"
        assert client.session is None

    def test_crawl_config_creation(self, sample_crawl_config):
        """测试爬取配置创建"""
        assert len(sample_crawl_config.urls) == 1
        assert sample_crawl_config.query == "提取商品信息：标题、价格、库存、销量"
        assert sample_crawl_config.priority == TaskPriority.MEDIUM
        assert sample_crawl_config.batch_name == "test_batch"

    def test_parse_task_result(self, client):
        """测试任务结果解析"""
        mock_data = {
            "id": "task_123",
            "batch_id": "batch_123",
            "url": "https://example.com",
            "status": "completed",
            "priority": "medium",
            "created_at": "2024-01-01T10:00:00",
            "updated_at": "2024-01-01T10:05:00",
            "completed_at": "2024-01-01T10:05:00",
            "retry_count": 0,
            "max_retries": 3,
            "processing_duration": 30.5,
            "result": {"title": "测试商品"}
        }

        result = client._parse_task_result(mock_data)

        assert result.task_id == "task_123"
        assert result.status == TaskStatus.COMPLETED
        assert result.priority == TaskPriority.MEDIUM
        assert result.processing_duration == 30.5
        assert result.result["title"] == "测试商品"


class TestPlatformConfigManager:
    """平台配置管理器测试"""
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器"""
        return PlatformConfigManager()
    
    def test_get_platform_config(self, config_manager):
        """测试获取平台配置"""
        config = config_manager.get_platform_config(Platform.ALIBABA_1688)
        
        assert config is not None
        assert config.platform == Platform.ALIBABA_1688
        assert config.base_config.name == "1688.com"
        assert config.enabled is True
    
    def test_get_product_type_config(self, config_manager):
        """测试获取商品类型配置"""
        config = config_manager.get_product_type_config(
            Platform.ALIBABA_1688, 
            ProductType.COMPETITOR
        )
        
        assert config is not None
        assert config.priority_boost == 2
        assert config.monitoring_frequency == 1800
        assert "竞品信息" in config.custom_query_template
    
    def test_build_crawl_query(self, config_manager):
        """测试构建爬取查询"""
        query = config_manager.build_crawl_query(
            Platform.ALIBABA_1688,
            ProductType.SUPPLIER,
            custom_fields=["供应商评级", "发货地址"]
        )
        
        assert "供货商商品信息" in query
        assert "供应商评级" in query
        assert "发货地址" in query
    
    def test_get_monitoring_frequency(self, config_manager):
        """测试获取监控频率"""
        # 竞品监控频率更高
        competitor_freq = config_manager.get_monitoring_frequency(
            Platform.ALIBABA_1688, ProductType.COMPETITOR
        )
        
        # 其他商品监控频率较低
        other_freq = config_manager.get_monitoring_frequency(
            Platform.ALIBABA_1688, ProductType.OTHER
        )
        
        assert competitor_freq < other_freq
        assert competitor_freq == 1800  # 30分钟
        assert other_freq == 7200      # 2小时


class TestDataNormalizer:
    """数据标准化处理器测试"""
    
    @pytest.fixture
    def normalizer(self):
        """创建数据标准化处理器"""
        return DataNormalizer()
    
    @pytest.fixture
    def sample_raw_data(self):
        """示例原始数据"""
        return {
            "title": "高品质手机壳 iPhone 15 Pro Max",
            "price": "¥29.90",
            "stock": "库存：1000件",
            "sales": "月销量：5000+",
            "rating": "4.8分",
            "main_image": "https://example.com/image1.jpg",
            "images": [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            "seller": "优质供应商",
            "min_order": "起订量：100件",
            "brand": "Apple"
        }
    
    def test_normalize_crawl_result(self, normalizer, sample_raw_data):
        """测试标准化爬取结果"""
        result = normalizer.normalize_crawl_result(
            sample_raw_data,
            "https://detail.1688.com/offer/123456.html",
            Platform.ALIBABA_1688,
            ProductType.SUPPLIER
        )
        
        assert result.title == "高品质手机壳 iPhone 15 Pro Max"
        assert result.price == Decimal("29.90")
        assert result.stock == 1000
        assert result.sales_count == 5000
        assert result.rating == 4.8
        assert result.main_image == "https://example.com/image1.jpg"
        assert len(result.images) == 2
        assert result.seller_name == "优质供应商"
        assert result.min_order_quantity == 100
        assert result.brand == "Apple"
        assert result.data_quality_score > 0.8
    
    def test_parse_price_formats(self, normalizer):
        """测试价格解析"""
        test_cases = [
            ("¥29.90", Decimal("29.90")),
            ("29.90元", Decimal("29.90")),
            ("$15.99", Decimal("15.99")),
            ("1,299.00", Decimal("1299.00")),
            ("价格：¥199", Decimal("199")),
        ]
        
        for price_str, expected in test_cases:
            result = normalizer._parse_price(price_str)
            assert result == expected, f"Failed for {price_str}"
    
    def test_extract_sales_count_with_units(self, normalizer):
        """测试销量提取（包含中文单位）"""
        test_data = {
            "sales": "月销量：5万+"
        }
        
        result = normalizer._extract_sales_count(test_data)
        assert result == 50000
    
    def test_data_quality_score_calculation(self, normalizer):
        """测试数据质量分数计算"""
        from app.services.task_middleware.data_normalizer import StandardizedProductData
        
        # 高质量数据
        high_quality = StandardizedProductData(
            url="https://example.com",
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.COMPETITOR,
            title="完整商品标题",
            price=Decimal("99.99"),
            stock=100,
            sales_count=1000,
            rating=4.5,
            main_image="https://example.com/image.jpg",
            description="详细描述",
            seller_name="卖家名称"
        )
        
        score = normalizer._calculate_quality_score(high_quality)
        assert score >= 0.9
        
        # 低质量数据
        low_quality = StandardizedProductData(
            url="https://example.com",
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.OTHER,
            title="Unknown Product"
        )
        
        score = normalizer._calculate_quality_score(low_quality)
        assert score <= 0.3


class TestTaskScheduler:
    """任务调度器测试"""
    
    @pytest.fixture
    def mock_client(self):
        """模拟客户端"""
        client = AsyncMock(spec=TaskMiddlewareClient)
        return client
    
    @pytest.fixture
    def config_manager(self):
        """配置管理器"""
        return PlatformConfigManager()
    
    @pytest.fixture
    def scheduler(self, mock_client, config_manager):
        """创建任务调度器"""
        return TaskScheduler(mock_client, config_manager)
    
    @pytest.fixture
    def sample_schedule_task(self):
        """示例调度任务"""
        return ScheduleTask(
            urls=["https://detail.1688.com/offer/123456.html"],
            platform=Platform.ALIBABA_1688,
            product_type=ProductType.COMPETITOR,
            priority=TaskPriority.HIGH,
            batch_name="test_competitor_batch"
        )
    
    @pytest.mark.asyncio
    async def test_schedule_single_task(self, scheduler, mock_client, sample_schedule_task):
        """测试调度单个任务"""
        from app.services.task_middleware.client import BatchTaskResult
        
        # 模拟成功的批量任务结果
        mock_batch_result = BatchTaskResult(
            success=True,
            batch_id="batch_123",
            task_ids=["task_1"],
            total_tasks=1,
            valid_tasks=1,
            invalid_tasks=0,
            invalid_urls=[],
            created_at=datetime.now(),
            message="成功"
        )
        
        mock_client.submit_crawl_task.return_value = mock_batch_result
        
        result = await scheduler.schedule_single_task(sample_schedule_task)
        
        assert result.success is True
        assert result.scheduled_tasks == 1
        assert len(result.batch_results) == 1
        assert result.failed_tasks == 0
        
        # 验证调用参数
        mock_client.submit_crawl_task.assert_called_once()
        call_args = mock_client.submit_crawl_task.call_args[0][0]
        assert isinstance(call_args, CrawlConfig)
        assert call_args.priority == TaskPriority.HIGH
        assert "竞品信息" in call_args.query
    
    @pytest.mark.asyncio
    async def test_schedule_batch_optimize_strategy(self, scheduler, mock_client):
        """测试批量优化策略"""
        from app.services.task_middleware.client import BatchTaskResult
        
        # 创建多个不同平台的任务
        tasks = [
            ScheduleTask(
                urls=[f"https://detail.1688.com/offer/{i}.html" for i in range(10)],
                platform=Platform.ALIBABA_1688,
                product_type=ProductType.COMPETITOR
            ),
            ScheduleTask(
                urls=[f"https://item.taobao.com/{i}.htm" for i in range(5)],
                platform=Platform.TAOBAO,
                product_type=ProductType.SUPPLIER
            )
        ]
        
        # 模拟批量任务结果
        mock_batch_result = BatchTaskResult(
            success=True,
            batch_id="batch_123",
            task_ids=["task_1", "task_2"],
            total_tasks=2,
            valid_tasks=2,
            invalid_tasks=0,
            invalid_urls=[],
            created_at=datetime.now(),
            message="成功"
        )
        
        mock_client.submit_crawl_task.return_value = mock_batch_result
        
        result = await scheduler.schedule_batch_tasks(tasks, ScheduleStrategy.BATCH_OPTIMIZE)
        
        assert result.success is True
        assert result.scheduled_tasks > 0
        
        # 验证按平台分组调用
        assert mock_client.submit_crawl_task.call_count >= 2  # 至少两个平台各一次


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
