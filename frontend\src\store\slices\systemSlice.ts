/**
 * 系统状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SystemHealth, DashboardStats, PriceTrend } from '../../types';
import { systemApi } from '../../services/systemApi';

// 状态类型
interface SystemState {
  health: SystemHealth | null;
  stats: DashboardStats | null;
  priceTrends: PriceTrend[];
  isLoading: boolean;
  error: string | null;
  lastHealthCheck: string | null;
  notifications: any[];
}

// 初始状态
const initialState: SystemState = {
  health: null,
  stats: null,
  priceTrends: [],
  isLoading: false,
  error: null,
  lastHealthCheck: null,
  notifications: [],
};

// 异步actions
export const fetchSystemHealthAsync = createAsyncThunk(
  'system/fetchHealth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await systemApi.getHealth();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取系统健康状态失败');
    }
  }
);

export const fetchDashboardStatsAsync = createAsyncThunk(
  'system/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await systemApi.getDashboardStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取仪表板统计失败');
    }
  }
);

export const fetchPriceTrendsAsync = createAsyncThunk(
  'system/fetchPriceTrends',
  async (params: { productIds?: string[]; days?: number }, { rejectWithValue }) => {
    try {
      const response = await systemApi.getPriceTrends(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取价格趋势失败');
    }
  }
);

export const fetchNotificationsAsync = createAsyncThunk(
  'system/fetchNotifications',
  async (params: { unread_only?: boolean; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await systemApi.getNotifications(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取通知失败');
    }
  }
);

export const markNotificationReadAsync = createAsyncThunk(
  'system/markNotificationRead',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      await systemApi.markNotificationRead(notificationId);
      return notificationId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '标记通知已读失败');
    }
  }
);

export const clearAllNotificationsAsync = createAsyncThunk(
  'system/clearAllNotifications',
  async (_, { rejectWithValue }) => {
    try {
      await systemApi.clearAllNotifications();
      return null;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '清除所有通知失败');
    }
  }
);

// Slice
const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateHealth: (state, action: PayloadAction<SystemHealth>) => {
      state.health = action.payload;
      state.lastHealthCheck = new Date().toISOString();
    },
    addNotification: (state, action: PayloadAction<any>) => {
      state.notifications.unshift(action.payload);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
  },
  extraReducers: (builder) => {
    // 获取系统健康状态
    builder
      .addCase(fetchSystemHealthAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSystemHealthAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.health = action.payload;
        state.lastHealthCheck = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchSystemHealthAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取仪表板统计
    builder
      .addCase(fetchDashboardStatsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardStatsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stats = action.payload;
        state.error = null;
      })
      .addCase(fetchDashboardStatsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取价格趋势
    builder
      .addCase(fetchPriceTrendsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPriceTrendsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.priceTrends = action.payload;
        state.error = null;
      })
      .addCase(fetchPriceTrendsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取通知
    builder
      .addCase(fetchNotificationsAsync.fulfilled, (state, action) => {
        state.notifications = action.payload;
      });

    // 标记通知已读
    builder
      .addCase(markNotificationReadAsync.fulfilled, (state, action) => {
        const notificationId = action.payload;
        const notification = state.notifications.find(n => n.id === notificationId);
        if (notification) {
          notification.read = true;
        }
      });

    // 清除所有通知
    builder
      .addCase(clearAllNotificationsAsync.fulfilled, (state) => {
        state.notifications = [];
      });
  },
});

// 导出actions
export const {
  clearError,
  updateHealth,
  addNotification,
  removeNotification,
  clearNotifications,
} = systemSlice.actions;

// 选择器
export const selectSystemHealth = (state: { system: SystemState }) => state.system.health;
export const selectDashboardStats = (state: { system: SystemState }) => state.system.stats;
export const selectPriceTrends = (state: { system: SystemState }) => state.system.priceTrends;
export const selectSystemLoading = (state: { system: SystemState }) => state.system.isLoading;
export const selectSystemError = (state: { system: SystemState }) => state.system.error;
export const selectLastHealthCheck = (state: { system: SystemState }) => state.system.lastHealthCheck;
export const selectNotifications = (state: { system: SystemState }) => state.system.notifications;
export const selectUnreadNotifications = (state: { system: SystemState }) => 
  state.system.notifications.filter(n => !n.read);

export default systemSlice.reducer;
