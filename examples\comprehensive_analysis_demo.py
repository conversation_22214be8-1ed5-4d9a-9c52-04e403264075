"""
综合分析报告引擎演示

展示多维度数据综合分析、商品评分排名、对比分析等功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.product import (
    Product, ProductType, ProductPrice, ProductSpecs, ProductMetrics
)
from app.services.analytics.comprehensive_analyzer import ComprehensiveAnalyzer
from app.services.analytics.product_ranking_system import (
    ProductRankingSystem, RankingCriteria, RankingCategory
)


async def demo_comprehensive_analysis():
    """演示综合分析功能"""
    print("=== 综合分析报告引擎演示 ===")
    
    comprehensive_analyzer = ComprehensiveAnalyzer()
    
    # 创建测试商品
    test_products = [
        Product(
            url="https://item.taobao.com/item.htm?id=111111",
            title="Apple iPhone 15 Pro Max 256GB 深空黑色",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=8999.00),
            specs=ProductSpecs(brand="Apple"),
            metrics=ProductMetrics(sales_count=25000, rating=4.9)
        ),
        Product(
            url="https://item.jd.com/item.htm?id=222222",
            title="Samsung Galaxy S24 Ultra 512GB 钛金灰",
            platform="jd",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=9999.00),
            specs=ProductSpecs(brand="Samsung"),
            metrics=ProductMetrics(sales_count=18000, rating=4.8)
        ),
        Product(
            url="https://item.taobao.com/item.htm?id=333333",
            title="Huawei Mate 60 Pro 512GB 雅川青",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6999.00),
            specs=ProductSpecs(brand="Huawei"),
            metrics=ProductMetrics(sales_count=22000, rating=4.7)
        ),
        Product(
            url="https://item.jd.com/item.htm?id=444444",
            title="小米14 Ultra 16GB+1TB 黑色",
            platform="jd",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6499.00),
            specs=ProductSpecs(brand="小米"),
            metrics=ProductMetrics(sales_count=15000, rating=4.6)
        ),
        Product(
            url="https://item.taobao.com/item.htm?id=555555",
            title="OPPO Find X7 Ultra 16GB+512GB 海阔天空",
            platform="taobao",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=5999.00),
            specs=ProductSpecs(brand="OPPO"),
            metrics=ProductMetrics(sales_count=12000, rating=4.5)
        )
    ]
    
    print(f"\n1. 生成综合分析报告:")
    
    # 为第一个商品生成详细报告
    target_product = test_products[0]
    competitors = test_products[1:3]  # 选择前两个竞品
    
    print(f"\n   目标商品: {target_product.title}")
    print(f"   竞品数量: {len(competitors)}")
    
    comprehensive_report = await comprehensive_analyzer.generate_comprehensive_report(
        target_product, competitors, "detailed"
    )
    
    print(f"\n   报告ID: {comprehensive_report.report_id}")
    print(f"   报告类型: {comprehensive_report.report_type}")
    print(f"   生成时间: {comprehensive_report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 综合评分
    score = comprehensive_report.comprehensive_score
    print(f"\n2. 综合评分分析:")
    print(f"   总体评分: {score.overall_score:.1f}分")
    print(f"   评分等级: {score.grade}")
    print(f"   评分趋势: {score.score_trend}")
    
    print(f"\n   分项评分:")
    for component in score.components:
        print(f"     {component.category.value}: {component.score:.1f}分 (权重: {component.weight:.2f})")
        print(f"       描述: {component.description}")
        if component.factors:
            print(f"       影响因素: {', '.join(component.factors[:2])}")
        if component.improvement_suggestions:
            print(f"       改进建议: {component.improvement_suggestions[0]}")
    
    print(f"\n   优势:")
    for strength in score.strengths:
        print(f"     • {strength}")
    
    print(f"\n   劣势:")
    for weakness in score.weaknesses:
        print(f"     • {weakness}")
    
    print(f"\n   基准对比:")
    for benchmark, diff in score.benchmark_comparison.items():
        print(f"     vs {benchmark}: {diff:+.1f}分")
    
    # 风险评估
    risk = comprehensive_report.risk_assessment
    print(f"\n3. 风险评估:")
    print(f"   总体风险级别: {risk.overall_risk_level}")
    print(f"   风险评分: {risk.risk_score:.1f}")
    print(f"   风险趋势: {risk.risk_trend}")
    
    print(f"\n   识别的风险 ({len(risk.risks)} 个):")
    for i, risk_item in enumerate(risk.risks, 1):
        print(f"     {i}. {risk_item['risk_type']}: {risk_item['description']}")
        print(f"        风险分数: {risk_item['risk_score']}")
        print(f"        影响: {risk_item['impact']}")
        print(f"        概率: {risk_item['probability']:.1%}")
        if risk_item['factors']:
            print(f"        因素: {', '.join(risk_item['factors'][:2])}")
    
    print(f"\n   缓解策略:")
    for strategy in risk.mitigation_strategies[:3]:
        print(f"     • {strategy}")
    
    print(f"\n   早期预警指标:")
    for indicator in risk.early_warning_indicators[:3]:
        print(f"     • {indicator}")
    
    # 市场机会
    opportunities = comprehensive_report.market_opportunities
    print(f"\n4. 市场机会识别 ({len(opportunities)} 个):")
    
    for i, opportunity in enumerate(opportunities[:5], 1):  # 显示前5个
        print(f"   {i}. {opportunity.opportunity_type.value}")
        print(f"      描述: {opportunity.description}")
        print(f"      潜在影响: {opportunity.potential_impact:.1%}")
        print(f"      信心水平: {opportunity.confidence_level:.1%}")
        print(f"      实现时间: {opportunity.time_to_realize}")
        print(f"      成功概率: {opportunity.success_probability:.1%}")
        print(f"      所需行动: {', '.join(opportunity.required_actions[:2])}")
    
    # 竞品对比
    if comprehensive_report.competitive_comparison:
        comparison = comprehensive_report.competitive_comparison
        print(f"\n5. 竞品对比分析:")
        print(f"   目标商品: {target_product.title[:30]}...")
        print(f"   竞品数量: {len(comparison.competitor_products)}")
        print(f"   市场定位: {comparison.market_positioning}")
        print(f"   竞争优势数: {len(comparison.competitive_advantages)}")
        
        print(f"\n   对比指标:")
        for product_id, metrics in comparison.comparison_metrics.items():
            product_name = next((p.title[:20] for p in [target_product] + competitors if p.id == product_id), "未知商品")
            print(f"     {product_name}...")
            for metric_name, value in metrics.items():
                print(f"       {metric_name}: {value}")
        
        print(f"\n   竞争优势:")
        for advantage in comparison.competitive_advantages:
            print(f"     • {advantage}")
        
        print(f"\n   竞争劣势:")
        for disadvantage in comparison.competitive_disadvantages:
            print(f"     • {disadvantage}")
        
        print(f"\n   战略建议:")
        for recommendation in comparison.strategic_recommendations:
            print(f"     • {recommendation}")
    
    # 关键洞察
    print(f"\n6. 关键洞察 ({len(comprehensive_report.key_insights)} 个):")
    for insight in comprehensive_report.key_insights:
        print(f"   • {insight}")
    
    # 行动建议
    print(f"\n7. 行动建议 ({len(comprehensive_report.action_recommendations)} 个):")
    for recommendation in comprehensive_report.action_recommendations:
        print(f"   • {recommendation}")
    
    # 执行摘要
    print(f"\n8. 执行摘要:")
    print(f"   {comprehensive_report.executive_summary}")
    
    return test_products, comprehensive_analyzer


async def demo_product_ranking():
    """演示商品排名功能"""
    print("\n=== 商品排名系统演示 ===")
    
    ranking_system = ProductRankingSystem()
    
    # 使用之前创建的测试商品
    test_products = [
        Product(
            url=f"https://example.com/product_{i}",
            title=f"测试商品 {i} - 智能手机",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=1000.0 + i * 500),
            metrics=ProductMetrics(sales_count=5000 + i * 1000, rating=4.0 + i * 0.1)
        )
        for i in range(8)
    ]
    
    print(f"\n1. 生成商品排名:")
    print(f"   商品数量: {len(test_products)}")
    
    # 按综合评分排名
    ranking_result = await ranking_system.generate_product_ranking(
        test_products,
        RankingCriteria.OVERALL_SCORE,
        RankingCategory.ALL_PRODUCTS,
        limit=10
    )
    
    print(f"\n   排名标准: {ranking_result.criteria.value}")
    print(f"   排名类别: {ranking_result.category.value}")
    print(f"   参与排名商品数: {ranking_result.total_products}")
    
    print(f"\n   排名结果:")
    for item in ranking_result.rankings[:5]:  # 显示前5名
        print(f"     第{item.rank}名: {item.product_title}")
        print(f"       评分: {item.score:.1f}分 ({item.grade}级)")
        print(f"       趋势: {item.trend}")
        print(f"       关键指标: {dict(list(item.key_metrics.items())[:3])}")
        if item.strengths:
            print(f"       优势: {item.strengths[0]}")
        if item.weaknesses:
            print(f"       劣势: {item.weaknesses[0]}")
    
    print(f"\n   类别统计:")
    for stat_name, value in ranking_result.category_stats.items():
        print(f"     {stat_name}: {value:.2f}")
    
    print(f"\n   排名洞察:")
    for insight in ranking_result.insights:
        print(f"     • {insight}")
    
    # 按不同标准排名
    print(f"\n2. 多标准排名对比:")
    
    criteria_list = [
        RankingCriteria.PRICE_COMPETITIVENESS,
        RankingCriteria.SALES_PERFORMANCE,
        RankingCriteria.CUSTOMER_SATISFACTION
    ]
    
    for criteria in criteria_list:
        ranking = await ranking_system.generate_product_ranking(
            test_products[:5],  # 使用前5个商品
            criteria,
            RankingCategory.ALL_PRODUCTS,
            limit=3
        )
        
        print(f"\n   {criteria.value} 排名:")
        for item in ranking.rankings:
            print(f"     第{item.rank}名: {item.product_title[:25]}... ({item.score:.1f}分)")
    
    return test_products, ranking_system


async def demo_product_comparison():
    """演示商品对比功能"""
    print("\n=== 商品对比分析演示 ===")
    
    ranking_system = ProductRankingSystem()
    
    # 创建对比商品
    comparison_products = [
        Product(
            url="https://item.taobao.com/compare1",
            title="iPhone 15 Pro Max 256GB",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=8999.00),
            metrics=ProductMetrics(sales_count=25000, rating=4.9)
        ),
        Product(
            url="https://item.jd.com/compare2",
            title="Samsung Galaxy S24 Ultra 512GB",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=9999.00),
            metrics=ProductMetrics(sales_count=18000, rating=4.8)
        ),
        Product(
            url="https://item.taobao.com/compare3",
            title="Huawei Mate 60 Pro 512GB",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=6999.00),
            metrics=ProductMetrics(sales_count=22000, rating=4.7)
        )
    ]
    
    print(f"\n1. 商品对比分析:")
    print(f"   对比商品数: {len(comparison_products)}")
    
    for i, product in enumerate(comparison_products, 1):
        print(f"     商品{i}: {product.title}")
        print(f"       价格: ¥{product.price.current_price:,.0f}")
        print(f"       销量: {product.metrics.sales_count:,}")
        print(f"       评分: {product.metrics.rating}")
    
    comparison_result = await ranking_system.compare_products(comparison_products)
    
    print(f"\n   对比结果:")
    print(f"   对比ID: {comparison_result.comparison_id}")
    print(f"   总体优胜者: {comparison_result.overall_winner}")
    
    print(f"\n   对比指标 ({len(comparison_result.comparison_metrics)} 个):")
    for metric in comparison_result.comparison_metrics:
        print(f"     {metric.metric_name}:")
        print(f"       最佳: {metric.best_product} ({metric.product_values[metric.best_product]})")
        print(f"       最差: {metric.worst_product} ({metric.product_values[metric.worst_product]})")
        print(f"       平均值: {metric.average_value:.2f}")
        print(f"       方差: {metric.variance:.2f}")
    
    print(f"\n   详细分析:")
    for product_id, analysis in comparison_result.detailed_analysis.items():
        product_name = next((p.title[:20] for p in comparison_products if p.id == product_id), "未知")
        print(f"     {product_name}...")
        print(f"       综合评分: {analysis['overall_score']:.1f} ({analysis['grade']})")
        print(f"       风险级别: {analysis['risk_level']}")
        print(f"       机会数量: {analysis['opportunities_count']}")
        if analysis['strengths']:
            print(f"       优势: {analysis['strengths'][0]}")
        if analysis['key_insights']:
            print(f"       洞察: {analysis['key_insights'][0]}")
    
    print(f"\n   对比建议:")
    for recommendation in comparison_result.recommendations:
        print(f"     • {recommendation}")
    
    return comparison_products, comparison_result


async def demo_batch_analysis():
    """演示批量分析功能"""
    print("\n=== 批量分析演示 ===")
    
    comprehensive_analyzer = ComprehensiveAnalyzer()
    
    # 创建批量分析商品
    batch_products = [
        Product(
            url=f"https://batch.example.com/product_{i}",
            title=f"批量分析商品 {i}",
            product_type=ProductType.COMPETITOR,
            price=ProductPrice(current_price=500.0 + i * 100),
            metrics=ProductMetrics(sales_count=2000 + i * 300, rating=4.0 + i * 0.05)
        )
        for i in range(6)
    ]
    
    print(f"\n1. 批量生成综合报告:")
    print(f"   批量商品数: {len(batch_products)}")
    
    batch_reports = await comprehensive_analyzer.batch_generate_reports(
        batch_products, "standard"
    )
    
    print(f"   生成报告数: {len(batch_reports)}")
    
    # 统计分析结果
    scores = [report.comprehensive_score.overall_score for report in batch_reports.values()]
    risk_levels = [report.risk_assessment.overall_risk_level for report in batch_reports.values()]
    opportunity_counts = [len(report.market_opportunities) for report in batch_reports.values()]
    
    print(f"\n   批量分析统计:")
    print(f"     平均评分: {sum(scores) / len(scores):.1f}")
    print(f"     最高评分: {max(scores):.1f}")
    print(f"     最低评分: {min(scores):.1f}")
    print(f"     平均机会数: {sum(opportunity_counts) / len(opportunity_counts):.1f}")
    
    # 风险级别分布
    risk_distribution = {}
    for risk_level in risk_levels:
        risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
    
    print(f"\n   风险级别分布:")
    for risk_level, count in risk_distribution.items():
        print(f"     {risk_level}: {count} 个商品")
    
    # 显示表现最好和最差的商品
    best_product_id = max(batch_reports.keys(), key=lambda x: batch_reports[x].comprehensive_score.overall_score)
    worst_product_id = min(batch_reports.keys(), key=lambda x: batch_reports[x].comprehensive_score.overall_score)
    
    best_product = next(p for p in batch_products if p.id == best_product_id)
    worst_product = next(p for p in batch_products if p.id == worst_product_id)
    
    print(f"\n   表现最佳商品:")
    print(f"     {best_product.title}")
    print(f"     评分: {batch_reports[best_product_id].comprehensive_score.overall_score:.1f}")
    print(f"     等级: {batch_reports[best_product_id].comprehensive_score.grade}")
    
    print(f"\n   表现最差商品:")
    print(f"     {worst_product.title}")
    print(f"     评分: {batch_reports[worst_product_id].comprehensive_score.overall_score:.1f}")
    print(f"     等级: {batch_reports[worst_product_id].comprehensive_score.grade}")
    
    return batch_products, batch_reports


async def main():
    """主演示函数"""
    print("🚀 综合分析报告引擎演示")
    print("=" * 60)
    
    # 1. 综合分析演示
    test_products, comprehensive_analyzer = await demo_comprehensive_analysis()
    
    # 2. 商品排名演示
    ranking_products, ranking_system = await demo_product_ranking()
    
    # 3. 商品对比演示
    comparison_products, comparison_result = await demo_product_comparison()
    
    # 4. 批量分析演示
    batch_products, batch_reports = await demo_batch_analysis()
    
    print("\n" + "=" * 60)
    print("✅ 综合分析报告引擎演示完成！")
    
    print(f"\n🎯 核心功能:")
    print(f"- 综合评分：5维度评分体系，7级评分等级，智能权重分配")
    print(f"- 风险评估：6种风险类型，5级风险等级，缓解策略生成")
    print(f"- 机会识别：4种机会类型，潜在影响评估，行动计划制定")
    print(f"- 竞品对比：多维度对比分析，竞争优势识别，战略建议")
    print(f"- 商品排名：6种排名标准，6种排名类别，统计洞察")
    print(f"- 批量分析：并发处理，统计分析，性能优化")
    
    print(f"\n📊 演示统计:")
    print(f"- 测试商品数: {len(test_products) + len(ranking_products) + len(comparison_products) + len(batch_products)}")
    print(f"- 综合报告: 1个详细报告 + {len(batch_reports)}个批量报告")
    print(f"- 排名分析: 4种排名标准，{len(ranking_products)}个商品")
    print(f"- 对比分析: {len(comparison_products)}个商品，{len(comparison_result.comparison_metrics)}个对比指标")
    print(f"- 评分组件: 5个评分维度，权重自动分配")
    print(f"- 风险评估: 6种风险类型，智能风险识别")
    print(f"- 市场机会: 4种机会类型，成功概率评估")
    
    print(f"\n🔧 技术特性:")
    print(f"- 多维度分析：价格竞争力、销量表现、库存健康、客户满意度、市场潜力")
    print(f"- 智能评分：基于商品类型的权重配置，动态评分算法")
    print(f"- 风险管理：全面风险识别，缓解策略生成，早期预警指标")
    print(f"- 机会挖掘：价格机会、市场机会、增长机会、竞争机会")
    print(f"- 竞争分析：市场定位识别，竞争优势分析，战略建议生成")
    print(f"- 排名系统：多标准排名，类别统计，排名洞察")
    print(f"- 对比分析：多指标对比，优胜者识别，详细分析")
    print(f"- 批量处理：并发分析，缓存优化，统计汇总")
    
    print(f"\n🏗️ 架构优势:")
    print(f"- 模块化设计：综合分析器 + 排名系统，职责清晰")
    print(f"- 多算法融合：评分算法 + 风险算法 + 机会算法 + 排名算法")
    print(f"- 缓存优化：报告缓存 + 排名缓存 + 对比缓存")
    print(f"- 并发处理：批量分析 + 异步处理 + 性能优化")
    print(f"- 业务导向：商业洞察 + 行动建议 + 战略指导")
    print(f"- 扩展性强：易于添加新的评分维度和分析方法")


if __name__ == "__main__":
    asyncio.run(main())
