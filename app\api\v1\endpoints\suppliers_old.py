"""
供货商管理API端点

实现供货商的完整CRUD操作、联系人管理和评估对比功能
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from decimal import Decimal
import uuid

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.models.database import Supplier as DBSupplier, ProductCost as DBProductCost, Product as DBProduct
from app.models.schemas import (
    Supplier, SupplierCreate, SupplierUpdate, SupplierList,
    ProductCost, ProductCostList
)

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", summary="获取供货商列表")
async def get_suppliers(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    min_rating: Optional[float] = Query(None, ge=0, le=5, description="最低评分"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取供货商列表"""
    logger.info(f"获取供货商列表 - skip: {skip}, limit: {limit}")
    
    try:
        # 构建查询条件
        query = select(DBSupplier)
        conditions = []
        
        # 激活状态筛选
        if is_active is not None:
            conditions.append(DBSupplier.is_active == is_active)
        
        # 评分筛选
        if min_rating is not None:
            conditions.append(DBSupplier.rating >= min_rating)
        
        # 搜索功能
        if search:
            search_condition = or_(
                DBSupplier.name.ilike(f"%{search}%"),
                DBSupplier.contact_person.ilike(f"%{search}%"),
                DBSupplier.email.ilike(f"%{search}%"),
                DBSupplier.phone.ilike(f"%{search}%")
            )
            conditions.append(search_condition)
        
        # 应用筛选条件
        if conditions:
            query = query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.offset(skip).limit(limit).order_by(DBSupplier.created_at.desc())
        result = await db.execute(query)
        suppliers = result.scalars().all()
        
        # 转换为响应模型
        items = []
        for supplier in suppliers:
            items.append({
                "id": supplier.supplier_id,
                "name": supplier.name,
                "contact_person": supplier.contact_person,
                "phone": supplier.phone,
                "email": supplier.email,
                "address": supplier.address,
                "payment_terms": supplier.payment_terms,
                "delivery_time": supplier.delivery_time,
                "min_order_quantity": supplier.min_order_quantity,
                "is_active": supplier.is_active,
                "rating": float(supplier.rating) if supplier.rating else None,
                "notes": supplier.notes,
                "created_at": supplier.created_at,
                "updated_at": supplier.updated_at
            })
        
        logger.info(f"成功获取供货商列表 - 总数: {total}, 返回: {len(items)}")
        return {
            "items": items,
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"获取供货商列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取供货商列表失败: {str(e)}")


@router.post("/", summary="创建供货商")
async def create_supplier(
    supplier_data: SupplierCreate,
    db: AsyncSession = Depends(get_db_session)
):
    """创建新供货商"""
    logger.info(f"创建供货商 - 名称: {supplier_data.name}")
    
    try:
        # 检查供货商名称是否已存在
        existing_query = select(DBSupplier).where(DBSupplier.name == supplier_data.name)
        existing_result = await db.execute(existing_query)
        existing_supplier = existing_result.scalar_one_or_none()
        
        if existing_supplier:
            raise HTTPException(status_code=400, detail="该供货商名称已存在")
        
        # 创建新供货商
        supplier_id = str(uuid.uuid4())
        new_supplier = DBSupplier(
            supplier_id=supplier_id,
            name=supplier_data.name,
            contact_person=supplier_data.contact_person,
            phone=supplier_data.phone,
            email=supplier_data.email,
            address=supplier_data.address,
            payment_terms=supplier_data.payment_terms,
            delivery_time=supplier_data.delivery_time,
            min_order_quantity=supplier_data.min_order_quantity,
            is_active=supplier_data.is_active,
            rating=supplier_data.rating,
            notes=supplier_data.notes
        )
        
        db.add(new_supplier)
        await db.commit()
        await db.refresh(new_supplier)
        
        logger.info(f"供货商创建成功 - ID: {new_supplier.supplier_id}")
        return {
            "id": new_supplier.supplier_id,
            "name": new_supplier.name,
            "contact_person": new_supplier.contact_person,
            "phone": new_supplier.phone,
            "email": new_supplier.email,
            "address": new_supplier.address,
            "payment_terms": new_supplier.payment_terms,
            "delivery_time": new_supplier.delivery_time,
            "min_order_quantity": new_supplier.min_order_quantity,
            "is_active": new_supplier.is_active,
            "rating": float(new_supplier.rating) if new_supplier.rating else None,
            "notes": new_supplier.notes,
            "created_at": new_supplier.created_at,
            "updated_at": new_supplier.updated_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建供货商失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建供货商失败: {str(e)}")


@router.get("/{supplier_id}", summary="获取供货商详情")
async def get_supplier(
    supplier_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取供货商详情"""
    logger.info(f"获取供货商详情 - ID: {supplier_id}")

    try:
        # 查询供货商详情（使用supplier_id字段）
        query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
        result = await db.execute(query)
        supplier = result.scalar_one_or_none()

        if not supplier:
            raise HTTPException(status_code=404, detail="供货商不存在")

        # 查询供货商的商品成本记录数量
        cost_count_query = select(func.count(DBProductCost.id)).where(
            DBProductCost.supplier_id == supplier.id
        )
        cost_count_result = await db.execute(cost_count_query)
        product_count = cost_count_result.scalar()

        # 构建响应数据
        supplier_detail = {
            "id": supplier.supplier_id,
            "name": supplier.name,
            "contact_person": supplier.contact_person,
            "phone": supplier.phone,
            "email": supplier.email,
            "address": supplier.address,
            "payment_terms": supplier.payment_terms,
            "delivery_time": supplier.delivery_time,
            "min_order_quantity": supplier.min_order_quantity,
            "is_active": supplier.is_active,
            "rating": float(supplier.rating) if supplier.rating else None,
            "notes": supplier.notes,
            "created_at": supplier.created_at,
            "updated_at": supplier.updated_at,
            "product_count": product_count
        }
        
        logger.info(f"成功获取供货商详情 - ID: {supplier_id}")
        return supplier_detail
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取供货商详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取供货商详情失败: {str(e)}")


@router.put("/{supplier_id}", summary="更新供货商")
async def update_supplier(
    supplier_id: str,
    supplier_data: SupplierUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """更新供货商信息"""
    logger.info(f"更新供货商 - ID: {supplier_id}")

    try:
        # 查询供货商是否存在
        query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
        result = await db.execute(query)
        supplier = result.scalar_one_or_none()

        if not supplier:
            raise HTTPException(status_code=404, detail="供货商不存在")

        # 如果更新名称，检查是否重复
        if supplier_data.name and supplier_data.name != supplier.name:
            existing_query = select(DBSupplier).where(
                and_(
                    DBSupplier.name == supplier_data.name,
                    DBSupplier.supplier_id != supplier_id
                )
            )
            existing_result = await db.execute(existing_query)
            existing_supplier = existing_result.scalar_one_or_none()

            if existing_supplier:
                raise HTTPException(status_code=400, detail="该供货商名称已存在")

        # 更新供货商信息（只更新提供的字段）
        update_data = supplier_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            if hasattr(supplier, field):
                setattr(supplier, field, value)

        await db.commit()
        await db.refresh(supplier)

        logger.info(f"供货商更新成功 - ID: {supplier_id}")
        return {
            "id": supplier.supplier_id,
            "name": supplier.name,
            "contact_person": supplier.contact_person,
            "phone": supplier.phone,
            "email": supplier.email,
            "address": supplier.address,
            "payment_terms": supplier.payment_terms,
            "delivery_time": supplier.delivery_time,
            "min_order_quantity": supplier.min_order_quantity,
            "is_active": supplier.is_active,
            "rating": float(supplier.rating) if supplier.rating else None,
            "notes": supplier.notes,
            "created_at": supplier.created_at,
            "updated_at": supplier.updated_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新供货商失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新供货商失败: {str(e)}")


@router.delete("/{supplier_id}", summary="删除供货商")
async def delete_supplier(
    supplier_id: str,
    force: bool = Query(False, description="是否强制删除（物理删除）"),
    db: AsyncSession = Depends(get_db_session)
):
    """删除供货商（默认软删除）"""
    logger.info(f"删除供货商 - ID: {supplier_id}, 强制删除: {force}")

    try:
        # 查询供货商是否存在
        query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
        result = await db.execute(query)
        supplier = result.scalar_one_or_none()

        if not supplier:
            raise HTTPException(status_code=404, detail="供货商不存在")

        # 检查是否有关联的商品成本记录
        cost_query = select(func.count(DBProductCost.id)).where(
            DBProductCost.supplier_id == supplier.id
        )
        cost_result = await db.execute(cost_query)
        cost_count = cost_result.scalar()

        if cost_count > 0 and not force:
            raise HTTPException(
                status_code=400,
                detail=f"该供货商有{cost_count}个关联的商品成本记录，请先处理这些记录或使用强制删除"
            )

        if force:
            # 物理删除：删除供货商及其相关数据
            # 先删除关联的商品成本记录
            if cost_count > 0:
                delete_costs_query = select(DBProductCost).where(
                    DBProductCost.supplier_id == supplier.id
                )
                costs_result = await db.execute(delete_costs_query)
                costs = costs_result.scalars().all()
                for cost in costs:
                    await db.delete(cost)

            await db.delete(supplier)
            await db.commit()
            logger.info(f"供货商物理删除成功 - ID: {supplier_id}")
            message = "供货商已永久删除"
        else:
            # 软删除：更新状态为不激活
            supplier.is_active = False
            await db.commit()
            logger.info(f"供货商软删除成功 - ID: {supplier_id}")
            message = "供货商已停用（可恢复）"

        return {
            "message": message,
            "supplier_id": supplier_id,
            "deleted_permanently": force,
            "affected_products": cost_count
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除供货商失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除供货商失败: {str(e)}")


@router.get("/{supplier_id}/products", summary="获取供货商的商品列表")
async def get_supplier_products(
    supplier_id: str,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取供货商的商品列表"""
    logger.info(f"获取供货商商品列表 - 供货商ID: {supplier_id}")

    try:
        # 验证供货商是否存在
        supplier_query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
        supplier_result = await db.execute(supplier_query)
        supplier = supplier_result.scalar_one_or_none()

        if not supplier:
            raise HTTPException(status_code=404, detail="供货商不存在")

        # 先查询供货商的商品成本记录（不关联商品表，避免关联问题）
        cost_query = select(DBProductCost).where(DBProductCost.supplier_id == supplier.id)

        # 获取总数
        count_query = select(func.count(DBProductCost.id)).where(DBProductCost.supplier_id == supplier.id)
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        cost_query = cost_query.offset(skip).limit(limit).order_by(DBProductCost.created_at.desc())
        cost_result = await db.execute(cost_query)
        costs = cost_result.scalars().all()

        # 构建响应数据
        items = []
        for cost in costs:
            # 尝试查询关联的商品信息
            product_title = f"商品ID: {cost.product_id}"
            product_url = None
            product_platform = "未知"

            try:
                if cost.product_id:
                    product_query = select(DBProduct).where(DBProduct.id == cost.product_id)
                    product_result = await db.execute(product_query)
                    product = product_result.scalar_one_or_none()
                    if product:
                        product_title = product.title or f"商品ID: {cost.product_id}"
                        product_url = product.url
                        product_platform = product.platform or "未知"
            except Exception as e:
                logger.warning(f"查询商品信息失败: {str(e)}")

            items.append({
                "cost_id": cost.id,
                "product_id": cost.product_id,
                "product_title": product_title,
                "product_url": product_url,
                "product_platform": product_platform,
                "unit_cost": float(cost.unit_cost),
                "currency": cost.currency,
                "shipping_cost": float(cost.shipping_cost) if cost.shipping_cost else None,
                "other_costs": float(cost.other_costs) if cost.other_costs else None,
                "total_cost": float(cost.total_cost) if cost.total_cost else None,
                "min_quantity": cost.min_quantity,
                "max_quantity": cost.max_quantity,
                "is_preferred": cost.is_preferred,
                "is_active": cost.is_active,
                "valid_from": cost.valid_from,
                "valid_until": cost.valid_until,
                "created_at": cost.created_at,
                "updated_at": cost.updated_at
            })

        logger.info(f"成功获取供货商商品列表 - 供货商ID: {supplier_id}, 总数: {total}")
        return {
            "supplier_id": supplier_id,
            "supplier_name": supplier.name,
            "items": items,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取供货商商品列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取供货商商品列表失败: {str(e)}")


@router.get("/{supplier_id}/stats", summary="获取供货商统计信息")
async def get_supplier_stats(
    supplier_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取供货商统计信息"""
    logger.info(f"获取供货商统计信息 - ID: {supplier_id}")

    try:
        # 验证供货商是否存在
        supplier_query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
        supplier_result = await db.execute(supplier_query)
        supplier = supplier_result.scalar_one_or_none()

        if not supplier:
            raise HTTPException(status_code=404, detail="供货商不存在")

        # 统计商品数量
        product_count_query = select(func.count(DBProductCost.id)).where(
            DBProductCost.supplier_id == supplier.id
        )
        product_count_result = await db.execute(product_count_query)
        product_count = product_count_result.scalar()

        # 统计首选商品数量
        preferred_count_query = select(func.count(DBProductCost.id)).where(
            and_(
                DBProductCost.supplier_id == supplier.id,
                DBProductCost.is_preferred == True
            )
        )
        preferred_count_result = await db.execute(preferred_count_query)
        preferred_count = preferred_count_result.scalar()

        # 计算平均成本
        avg_cost_query = select(func.avg(DBProductCost.total_cost)).where(
            and_(
                DBProductCost.supplier_id == supplier.id,
                DBProductCost.total_cost.isnot(None)
            )
        )
        avg_cost_result = await db.execute(avg_cost_query)
        avg_cost = avg_cost_result.scalar()

        # 构建统计信息
        stats = {
            "supplier_id": supplier_id,
            "supplier_name": supplier.name,
            "total_products": product_count,
            "preferred_products": preferred_count,
            "average_cost": float(avg_cost) if avg_cost else None,
            "rating": float(supplier.rating) if supplier.rating else None,
            "delivery_time": supplier.delivery_time,
            "min_order_quantity": supplier.min_order_quantity,
            "is_active": supplier.is_active,
            "performance_score": None  # 将在下面计算
        }

        # 计算性能评分（综合算法）
        if supplier.rating and product_count > 0:
            # 基于评分、商品数量、首选比例计算性能评分
            preferred_ratio = preferred_count / product_count if product_count > 0 else 0
            performance_score = (
                float(supplier.rating) * 0.4 +  # 评分权重40%
                min(product_count / 10, 1) * 0.3 +  # 商品数量权重30%（最多10个商品得满分）
                preferred_ratio * 0.3  # 首选比例权重30%
            ) * 100
            stats["performance_score"] = round(performance_score, 2)

        logger.info(f"成功获取供货商统计信息 - ID: {supplier_id}")
        return stats

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取供货商统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取供货商统计信息失败: {str(e)}")


@router.get("/compare", summary="供货商对比分析")
async def compare_suppliers(
    supplier_ids: str = Query(..., description="供货商ID列表，用逗号分隔"),
    product_id: Optional[str] = Query(None, description="指定商品ID进行对比"),
    db: AsyncSession = Depends(get_db_session)
):
    """供货商对比分析"""
    logger.info(f"供货商对比分析 - IDs: {supplier_ids}")

    try:
        # 解析供货商ID列表
        supplier_id_list = [id.strip() for id in supplier_ids.split(",")]
        if len(supplier_id_list) < 2:
            raise HTTPException(status_code=400, detail="至少需要2个供货商进行对比")

        if len(supplier_id_list) > 5:
            raise HTTPException(status_code=400, detail="最多支持5个供货商对比")

        # 查询供货商信息（逐个查询以确保兼容性）
        suppliers = []

        for supplier_id in supplier_id_list:
            # 尝试用supplier_id字段查询
            supplier_query = select(DBSupplier).where(DBSupplier.supplier_id == supplier_id)
            supplier_result = await db.execute(supplier_query)
            supplier = supplier_result.scalar_one_or_none()

            if supplier:
                suppliers.append(supplier)
                logger.info(f"找到供货商 - ID: {supplier_id}, 名称: {supplier.name}")
            else:
                # 如果没找到，记录详细日志
                logger.warning(f"未找到供货商 - 查询ID: {supplier_id}")

                # 尝试查询所有供货商的ID来调试
                all_suppliers_query = select(DBSupplier.id, DBSupplier.supplier_id, DBSupplier.name).limit(5)
                all_result = await db.execute(all_suppliers_query)
                all_suppliers = all_result.all()
                logger.info(f"数据库中的供货商示例: {[(s.id, s.supplier_id, s.name) for s in all_suppliers]}")

        if len(suppliers) < 2:
            raise HTTPException(status_code=404, detail=f"至少需要找到2个有效的供货商进行对比，当前找到{len(suppliers)}个")

        # 构建对比数据
        comparison_data = []

        for supplier in suppliers:
            # 基础信息
            supplier_data = {
                "id": supplier.supplier_id,
                "name": supplier.name,
                "contact_person": supplier.contact_person,
                "phone": supplier.phone,
                "email": supplier.email,
                "rating": float(supplier.rating) if supplier.rating else None,
                "delivery_time": supplier.delivery_time,
                "min_order_quantity": supplier.min_order_quantity,
                "payment_terms": supplier.payment_terms,
                "is_active": supplier.is_active
            }

            # 统计信息
            product_count_query = select(func.count(DBProductCost.id)).where(
                DBProductCost.supplier_id == supplier.id
            )
            product_count_result = await db.execute(product_count_query)
            product_count = product_count_result.scalar()

            preferred_count_query = select(func.count(DBProductCost.id)).where(
                and_(
                    DBProductCost.supplier_id == supplier.id,
                    DBProductCost.is_preferred == True
                )
            )
            preferred_count_result = await db.execute(preferred_count_query)
            preferred_count = preferred_count_result.scalar()

            avg_cost_query = select(func.avg(DBProductCost.total_cost)).where(
                and_(
                    DBProductCost.supplier_id == supplier.id,
                    DBProductCost.total_cost.isnot(None)
                )
            )
            avg_cost_result = await db.execute(avg_cost_query)
            avg_cost = avg_cost_result.scalar()

            supplier_data.update({
                "total_products": product_count,
                "preferred_products": preferred_count,
                "preferred_ratio": preferred_count / product_count if product_count > 0 else 0,
                "average_cost": float(avg_cost) if avg_cost else None
            })

            # 如果指定了商品ID，查询该商品的成本信息
            if product_id:
                try:
                    cost_query = select(DBProductCost).where(
                        and_(
                            DBProductCost.supplier_id == supplier.id,
                            DBProductCost.product_id == int(product_id)
                        )
                    )
                    cost_result = await db.execute(cost_query)
                    cost = cost_result.scalar_one_or_none()

                    if cost:
                        supplier_data["product_cost"] = {
                            "unit_cost": float(cost.unit_cost),
                            "currency": cost.currency,
                            "total_cost": float(cost.total_cost) if cost.total_cost else None,
                            "min_quantity": cost.min_quantity,
                            "is_preferred": cost.is_preferred
                        }
                    else:
                        supplier_data["product_cost"] = None
                except ValueError:
                    pass  # 忽略无效的商品ID

            comparison_data.append(supplier_data)

        # 计算对比指标
        comparison_summary = {
            "total_suppliers": len(comparison_data),
            "best_rating": max([s["rating"] for s in comparison_data if s["rating"]], default=None),
            "fastest_delivery": min([s["delivery_time"] for s in comparison_data if s["delivery_time"]], default=None),
            "lowest_min_order": min([s["min_order_quantity"] for s in comparison_data if s["min_order_quantity"]], default=None),
            "most_products": max([s["total_products"] for s in comparison_data]),
            "highest_preferred_ratio": max([s["preferred_ratio"] for s in comparison_data])
        }

        if product_id:
            # 如果指定了商品，找出最低成本
            product_costs = [s["product_cost"]["total_cost"] for s in comparison_data
                           if s.get("product_cost") and s["product_cost"]["total_cost"]]
            if product_costs:
                comparison_summary["lowest_product_cost"] = min(product_costs)

        logger.info(f"供货商对比分析完成 - 对比数量: {len(comparison_data)}")
        return {
            "suppliers": comparison_data,
            "summary": comparison_summary,
            "product_id": product_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"供货商对比分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"供货商对比分析失败: {str(e)}")


@router.get("/evaluation/ranking", summary="供货商评估排名")
async def get_supplier_ranking(
    limit: int = Query(10, ge=1, le=50, description="返回排名数量"),
    sort_by: str = Query("performance", description="排序依据: performance, rating, products, cost"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取供货商评估排名"""
    logger.info(f"获取供货商评估排名 - 排序: {sort_by}, 数量: {limit}")

    try:
        # 查询所有激活的供货商
        suppliers_query = select(DBSupplier).where(DBSupplier.is_active == True)
        suppliers_result = await db.execute(suppliers_query)
        suppliers = suppliers_result.scalars().all()

        # 计算每个供货商的评估指标
        supplier_evaluations = []

        for supplier in suppliers:
            # 统计信息
            product_count_query = select(func.count(DBProductCost.id)).where(
                DBProductCost.supplier_id == supplier.id
            )
            product_count_result = await db.execute(product_count_query)
            product_count = product_count_result.scalar()

            preferred_count_query = select(func.count(DBProductCost.id)).where(
                and_(
                    DBProductCost.supplier_id == supplier.id,
                    DBProductCost.is_preferred == True
                )
            )
            preferred_count_result = await db.execute(preferred_count_query)
            preferred_count = preferred_count_result.scalar()

            avg_cost_query = select(func.avg(DBProductCost.total_cost)).where(
                and_(
                    DBProductCost.supplier_id == supplier.id,
                    DBProductCost.total_cost.isnot(None)
                )
            )
            avg_cost_result = await db.execute(avg_cost_query)
            avg_cost = avg_cost_result.scalar()

            # 计算综合性能评分
            rating_score = float(supplier.rating) if supplier.rating else 0
            product_score = min(product_count / 10, 1)  # 商品数量评分（最多10个得满分）
            preferred_ratio = preferred_count / product_count if product_count > 0 else 0
            delivery_score = max(0, (30 - (supplier.delivery_time or 30)) / 30) if supplier.delivery_time else 0

            performance_score = (
                rating_score * 0.3 +  # 评分权重30%
                product_score * 0.25 +  # 商品数量权重25%
                preferred_ratio * 0.25 +  # 首选比例权重25%
                delivery_score * 0.2  # 交货时间权重20%
            ) * 100

            supplier_evaluations.append({
                "id": supplier.supplier_id,
                "name": supplier.name,
                "rating": rating_score,
                "total_products": product_count,
                "preferred_products": preferred_count,
                "preferred_ratio": preferred_ratio,
                "average_cost": float(avg_cost) if avg_cost else None,
                "delivery_time": supplier.delivery_time,
                "performance_score": round(performance_score, 2),
                "contact_person": supplier.contact_person,
                "phone": supplier.phone,
                "email": supplier.email
            })

        # 根据指定字段排序
        if sort_by == "performance":
            supplier_evaluations.sort(key=lambda x: x["performance_score"], reverse=True)
        elif sort_by == "rating":
            supplier_evaluations.sort(key=lambda x: x["rating"], reverse=True)
        elif sort_by == "products":
            supplier_evaluations.sort(key=lambda x: x["total_products"], reverse=True)
        elif sort_by == "cost":
            # 按平均成本升序排列（成本越低越好）
            supplier_evaluations.sort(key=lambda x: x["average_cost"] or float('inf'))
        else:
            raise HTTPException(status_code=400, detail="不支持的排序字段")

        # 添加排名
        for i, supplier in enumerate(supplier_evaluations[:limit]):
            supplier["rank"] = i + 1

        logger.info(f"供货商评估排名完成 - 返回数量: {min(len(supplier_evaluations), limit)}")
        return {
            "rankings": supplier_evaluations[:limit],
            "total_suppliers": len(supplier_evaluations),
            "sort_by": sort_by
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取供货商评估排名失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取供货商评估排名失败: {str(e)}")
