"""
系统监控器

创建系统监控脚本、服务状态检查、自动重启机制
"""

import asyncio
import psutil
import subprocess
import time
import json
import os
import signal
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field

from .health_checker import HealthChecker, HealthStatus
from .log_manager import LogManager, LogLevel, LogCategory
from app.core.logging import get_logger

logger = get_logger(__name__)


class ServiceStatus(Enum):
    """服务状态"""
    RUNNING = "running"
    STOPPED = "stopped"
    FAILED = "failed"
    UNKNOWN = "unknown"


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class ResourceMetrics:
    """资源指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: Optional[List[float]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "disk_percent": self.disk_percent,
            "network_bytes_sent": self.network_bytes_sent,
            "network_bytes_recv": self.network_bytes_recv,
            "process_count": self.process_count,
            "load_average": self.load_average
        }


@dataclass
class AlertThreshold:
    """告警阈值"""
    metric_name: str
    warning_threshold: float
    critical_threshold: float
    duration_minutes: int = 5  # 持续时间
    enabled: bool = True


@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    command: str
    working_dir: str = ""
    env_vars: Dict[str, str] = field(default_factory=dict)
    auto_restart: bool = True
    max_restart_attempts: int = 3
    restart_delay_seconds: int = 30
    health_check_url: str = ""
    health_check_interval: int = 60
    process_id: Optional[int] = None
    restart_count: int = 0
    last_restart: Optional[datetime] = None


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config_file: str = "monitor_config.json", data_dir: str = "monitor_data"):
        self.config_file = config_file
        self.data_dir = data_dir
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化组件
        self.health_checker = HealthChecker()
        self.log_manager = LogManager(os.path.join(self.data_dir, "logs"))
        
        # 监控配置
        self.monitor_config = {
            "enabled": True,
            "check_interval_seconds": 60,
            "metrics_retention_hours": 24,
            "alert_cooldown_minutes": 15,
            "auto_restart_services": True,
            "max_restart_attempts": 3,
            "notification_enabled": True
        }
        
        # 告警阈值
        self.alert_thresholds = [
            AlertThreshold("cpu_percent", 80.0, 90.0, 5),
            AlertThreshold("memory_percent", 80.0, 90.0, 5),
            AlertThreshold("disk_percent", 85.0, 95.0, 10),
            AlertThreshold("load_average", 2.0, 4.0, 5)
        ]
        
        # 服务配置
        self.services: Dict[str, ServiceConfig] = {}
        
        # 监控数据
        self.metrics_history: List[ResourceMetrics] = []
        self.alert_history: List[Dict[str, Any]] = []
        self.last_alerts: Dict[str, datetime] = {}
        
        # 监控统计
        self.monitor_stats = {
            "uptime_start": datetime.now(),
            "total_checks": 0,
            "alerts_sent": 0,
            "services_restarted": 0,
            "last_check": None,
            "system_healthy": True
        }
        
        # 运行状态
        self.is_running = False
        self.monitor_task = None
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载监控配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新监控配置
                self.monitor_config.update(config_data.get("monitor_config", {}))
                
                # 加载告警阈值
                thresholds_data = config_data.get("alert_thresholds", [])
                self.alert_thresholds = []
                for threshold_data in thresholds_data:
                    threshold = AlertThreshold(
                        metric_name=threshold_data["metric_name"],
                        warning_threshold=threshold_data["warning_threshold"],
                        critical_threshold=threshold_data["critical_threshold"],
                        duration_minutes=threshold_data.get("duration_minutes", 5),
                        enabled=threshold_data.get("enabled", True)
                    )
                    self.alert_thresholds.append(threshold)
                
                # 加载服务配置
                services_data = config_data.get("services", {})
                for service_name, service_data in services_data.items():
                    service = ServiceConfig(
                        name=service_name,
                        command=service_data["command"],
                        working_dir=service_data.get("working_dir", ""),
                        env_vars=service_data.get("env_vars", {}),
                        auto_restart=service_data.get("auto_restart", True),
                        max_restart_attempts=service_data.get("max_restart_attempts", 3),
                        restart_delay_seconds=service_data.get("restart_delay_seconds", 30),
                        health_check_url=service_data.get("health_check_url", ""),
                        health_check_interval=service_data.get("health_check_interval", 60)
                    )
                    self.services[service_name] = service
                
                logger.info("监控配置加载成功")
            else:
                # 创建默认配置
                self._create_default_config()
                
        except Exception as e:
            logger.error(f"加载监控配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        try:
            default_config = {
                "monitor_config": self.monitor_config,
                "alert_thresholds": [
                    {
                        "metric_name": "cpu_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "memory_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "disk_percent",
                        "warning_threshold": 85.0,
                        "critical_threshold": 95.0,
                        "duration_minutes": 10,
                        "enabled": True
                    }
                ],
                "services": {
                    "moniit_api": {
                        "command": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000",
                        "working_dir": ".",
                        "auto_restart": True,
                        "max_restart_attempts": 3,
                        "health_check_url": "http://localhost:8000/health",
                        "health_check_interval": 60
                    }
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            logger.info("创建默认监控配置")
            
        except Exception as e:
            logger.error(f"创建默认配置失败: {e}")
    
    async def start_monitoring(self):
        """启动系统监控"""
        try:
            if self.is_running:
                logger.warning("系统监控已在运行")
                return
            
            self.is_running = True
            
            # 启动健康检查
            await self.health_checker.start_periodic_checks()
            
            # 启动监控循环
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            
            self.log_manager.info(
                LogCategory.MONITORING,
                "系统监控已启动",
                extra_data={"config": self.monitor_config}
            )
            
            logger.info("系统监控已启动")
            
        except Exception as e:
            logger.error(f"启动系统监控失败: {e}")
            self.log_manager.error(
                LogCategory.MONITORING,
                f"启动系统监控失败: {str(e)}"
            )
    
    async def stop_monitoring(self):
        """停止系统监控"""
        try:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 停止健康检查
            await self.health_checker.stop_periodic_checks()
            
            # 停止监控任务
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 停止日志管理器
            self.log_manager.stop_async_logging()
            
            self.log_manager.info(LogCategory.MONITORING, "系统监控已停止")
            logger.info("系统监控已停止")
            
        except Exception as e:
            logger.error(f"停止系统监控失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_running:
                try:
                    # 收集系统指标
                    metrics = await self._collect_system_metrics()
                    
                    # 存储指标
                    self._store_metrics(metrics)
                    
                    # 检查告警
                    await self._check_alerts(metrics)
                    
                    # 检查服务状态
                    await self._check_services()
                    
                    # 清理旧数据
                    self._cleanup_old_data()
                    
                    # 更新统计
                    self.monitor_stats["total_checks"] += 1
                    self.monitor_stats["last_check"] = datetime.now().isoformat()
                    
                    # 等待下次检查
                    await asyncio.sleep(self.monitor_config["check_interval_seconds"])
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"监控循环异常: {e}")
                    self.log_manager.error(
                        LogCategory.MONITORING,
                        f"监控循环异常: {str(e)}"
                    )
                    await asyncio.sleep(self.monitor_config["check_interval_seconds"])
                    
        except asyncio.CancelledError:
            logger.info("监控循环已取消")
    
    async def _collect_system_metrics(self) -> ResourceMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值
            load_average = None
            if hasattr(os, 'getloadavg'):
                load_average = list(os.getloadavg())
            
            metrics = ResourceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                process_count=process_count,
                load_average=load_average
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            raise
    
    def _store_metrics(self, metrics: ResourceMetrics):
        """存储指标数据"""
        try:
            self.metrics_history.append(metrics)
            
            # 保持数据在指定时间范围内
            cutoff_time = datetime.now() - timedelta(hours=self.monitor_config["metrics_retention_hours"])
            self.metrics_history = [
                m for m in self.metrics_history 
                if m.timestamp > cutoff_time
            ]
            
            # 记录指标日志
            self.log_manager.debug(
                LogCategory.MONITORING,
                "系统指标收集",
                extra_data=metrics.to_dict()
            )
            
        except Exception as e:
            logger.error(f"存储指标数据失败: {e}")
    
    async def _check_alerts(self, metrics: ResourceMetrics):
        """检查告警条件"""
        try:
            for threshold in self.alert_thresholds:
                if not threshold.enabled:
                    continue
                
                # 获取指标值
                metric_value = getattr(metrics, threshold.metric_name, None)
                if metric_value is None:
                    continue
                
                # 特殊处理负载平均值
                if threshold.metric_name == "load_average" and metrics.load_average:
                    metric_value = metrics.load_average[0]  # 1分钟负载
                
                # 检查告警条件
                alert_level = None
                if metric_value >= threshold.critical_threshold:
                    alert_level = AlertLevel.CRITICAL
                elif metric_value >= threshold.warning_threshold:
                    alert_level = AlertLevel.WARNING
                
                if alert_level:
                    await self._send_alert(
                        threshold.metric_name,
                        alert_level,
                        metric_value,
                        threshold
                    )
            
        except Exception as e:
            logger.error(f"检查告警失败: {e}")
    
    async def _send_alert(self, metric_name: str, level: AlertLevel, 
                         value: float, threshold: AlertThreshold):
        """发送告警"""
        try:
            # 检查告警冷却时间
            alert_key = f"{metric_name}_{level.value}"
            last_alert = self.last_alerts.get(alert_key)
            
            if last_alert:
                cooldown = timedelta(minutes=self.monitor_config["alert_cooldown_minutes"])
                if datetime.now() - last_alert < cooldown:
                    return
            
            # 创建告警信息
            alert_info = {
                "timestamp": datetime.now().isoformat(),
                "metric_name": metric_name,
                "level": level.value,
                "current_value": value,
                "threshold": threshold.critical_threshold if level == AlertLevel.CRITICAL else threshold.warning_threshold,
                "message": f"{metric_name}指标{level.value}: 当前值{value:.1f}%, 阈值{threshold.critical_threshold if level == AlertLevel.CRITICAL else threshold.warning_threshold}%"
            }
            
            # 记录告警
            self.alert_history.append(alert_info)
            self.last_alerts[alert_key] = datetime.now()
            self.monitor_stats["alerts_sent"] += 1
            
            # 记录日志
            log_level = LogLevel.CRITICAL if level == AlertLevel.CRITICAL else LogLevel.WARNING
            self.log_manager.log(
                log_level,
                LogCategory.MONITORING,
                alert_info["message"],
                extra_data=alert_info
            )
            
            # 更新系统健康状态
            if level == AlertLevel.CRITICAL:
                self.monitor_stats["system_healthy"] = False
            
            logger.warning(f"系统告警: {alert_info['message']}")
            
        except Exception as e:
            logger.error(f"发送告警失败: {e}")
    
    async def _check_services(self):
        """检查服务状态"""
        try:
            for service_name, service_config in self.services.items():
                try:
                    status = await self._get_service_status(service_config)
                    
                    if status == ServiceStatus.STOPPED or status == ServiceStatus.FAILED:
                        if service_config.auto_restart and self.monitor_config["auto_restart_services"]:
                            await self._restart_service(service_config)
                    
                except Exception as e:
                    logger.error(f"检查服务{service_name}状态失败: {e}")
                    
        except Exception as e:
            logger.error(f"检查服务状态失败: {e}")
    
    async def _get_service_status(self, service_config: ServiceConfig) -> ServiceStatus:
        """获取服务状态"""
        try:
            # 检查进程是否存在
            if service_config.process_id:
                try:
                    process = psutil.Process(service_config.process_id)
                    if process.is_running():
                        return ServiceStatus.RUNNING
                except psutil.NoSuchProcess:
                    service_config.process_id = None
            
            # 通过健康检查URL检查
            if service_config.health_check_url:
                try:
                    # 这里应该实现HTTP健康检查
                    # 简化实现，假设服务正在运行
                    return ServiceStatus.RUNNING
                except Exception:
                    return ServiceStatus.FAILED
            
            return ServiceStatus.UNKNOWN
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return ServiceStatus.UNKNOWN
    
    async def _restart_service(self, service_config: ServiceConfig):
        """重启服务"""
        try:
            # 检查重启次数限制
            if service_config.restart_count >= service_config.max_restart_attempts:
                logger.warning(f"服务{service_config.name}重启次数已达上限")
                return
            
            # 检查重启间隔
            if service_config.last_restart:
                elapsed = datetime.now() - service_config.last_restart
                if elapsed.total_seconds() < service_config.restart_delay_seconds:
                    return
            
            logger.info(f"正在重启服务: {service_config.name}")
            
            # 停止现有进程
            if service_config.process_id:
                try:
                    process = psutil.Process(service_config.process_id)
                    process.terminate()
                    process.wait(timeout=10)
                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    pass
            
            # 启动新进程
            env = os.environ.copy()
            env.update(service_config.env_vars)
            
            process = subprocess.Popen(
                service_config.command,
                shell=True,
                cwd=service_config.working_dir or None,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            service_config.process_id = process.pid
            service_config.restart_count += 1
            service_config.last_restart = datetime.now()
            
            # 更新统计
            self.monitor_stats["services_restarted"] += 1
            
            # 记录日志
            self.log_manager.info(
                LogCategory.MONITORING,
                f"服务重启成功: {service_config.name}",
                extra_data={
                    "service_name": service_config.name,
                    "process_id": service_config.process_id,
                    "restart_count": service_config.restart_count
                }
            )
            
            logger.info(f"服务重启成功: {service_config.name}, PID: {service_config.process_id}")
            
        except Exception as e:
            logger.error(f"重启服务失败: {e}")
            self.log_manager.error(
                LogCategory.MONITORING,
                f"重启服务失败: {service_config.name} - {str(e)}"
            )
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理告警历史
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.alert_history = [
                alert for alert in self.alert_history
                if datetime.fromisoformat(alert["timestamp"]) > cutoff_time
            ]
            
            # 清理告警冷却记录
            cooldown_cutoff = datetime.now() - timedelta(hours=1)
            self.last_alerts = {
                key: timestamp for key, timestamp in self.last_alerts.items()
                if timestamp > cooldown_cutoff
            }
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 获取最新指标
            latest_metrics = self.metrics_history[-1] if self.metrics_history else None
            
            # 获取服务状态
            services_status = {}
            for service_name, service_config in self.services.items():
                services_status[service_name] = {
                    "status": "unknown",  # 简化实现
                    "process_id": service_config.process_id,
                    "restart_count": service_config.restart_count,
                    "last_restart": service_config.last_restart.isoformat() if service_config.last_restart else None
                }
            
            return {
                "system_healthy": self.monitor_stats["system_healthy"],
                "uptime_seconds": (datetime.now() - self.monitor_stats["uptime_start"]).total_seconds(),
                "latest_metrics": latest_metrics.to_dict() if latest_metrics else None,
                "services": services_status,
                "recent_alerts": self.alert_history[-10:],  # 最近10个告警
                "stats": self.monitor_stats.copy()
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取指标历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            filtered_metrics = [
                metrics.to_dict() for metrics in self.metrics_history
                if metrics.timestamp > cutoff_time
            ]
            
            return filtered_metrics
            
        except Exception as e:
            logger.error(f"获取指标历史失败: {e}")
            return []
