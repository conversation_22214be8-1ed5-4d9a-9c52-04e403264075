/**
 * 个人中心页面
 */

import React, { useEffect, useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Space,
  Tabs,
  message,
  Spin,
  Modal
} from 'antd';
import { UserOutlined, UploadOutlined, LockOutlined, SaveOutlined } from '@ant-design/icons';
import type { TabsProps, UploadProps } from 'antd';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  getCurrentUserAsync,
  updateProfileAsync,
  changePasswordAsync,
  selectUser,
  selectAuthLoading
} from '../../store/slices/authSlice';

const ProfilePage: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectUser);
  const loading = useAppSelector(selectAuthLoading);

  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [profileSaving, setProfileSaving] = useState(false);
  const [passwordSaving, setPasswordSaving] = useState(false);

  useEffect(() => {
    // 获取当前用户信息
    dispatch(getCurrentUserAsync());
  }, [dispatch]);

  useEffect(() => {
    // 当用户信息加载完成后，设置表单初始值
    if (currentUser) {
      profileForm.setFieldsValue({
        username: currentUser.username,
        full_name: currentUser.full_name,
        email: currentUser.email,
        phone: currentUser.phone || '',
      });
    }
  }, [currentUser, profileForm]);

  const handleProfileSubmit = async (values: any) => {
    try {
      setProfileSaving(true);
      await dispatch(updateProfileAsync(values)).unwrap();
      message.success('个人信息更新成功');
    } catch (error: any) {
      message.error(`更新失败：${error.message}`);
    } finally {
      setProfileSaving(false);
    }
  };

  const handlePasswordSubmit = async (values: any) => {
    try {
      setPasswordSaving(true);
      await dispatch(changePasswordAsync({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      })).unwrap();
      message.success('密码修改成功');
      passwordForm.resetFields();
    } catch (error: any) {
      message.error(`密码修改失败：${error.message}`);
    } finally {
      setPasswordSaving(false);
    }
  };

  const handleAvatarUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      setUploading(true);
      // 这里应该调用头像上传API
      // const response = await uploadAvatar(file);
      // 模拟上传成功
      setTimeout(() => {
        message.success('头像上传成功');
        onSuccess?.('ok');
        setUploading(false);
      }, 2000);
    } catch (error: any) {
      message.error(`头像上传失败：${error.message}`);
      onError?.(error);
      setUploading(false);
    }
  };

  const profileTab = (
    <Card>
      <div className="text-center mb-4">
        <Avatar
          size={80}
          icon={<UserOutlined />}
        />
        <div className="mt-2">
          <Upload
            accept="image/*"
            showUploadList={false}
            customRequest={handleAvatarUpload}
          >
            <Button
              size="small"
              icon={<UploadOutlined />}
              loading={uploading}
            >
              更换头像
            </Button>
          </Upload>
        </div>
      </div>

      <Form
        form={profileForm}
        layout="vertical"
        onFinish={handleProfileSubmit}
      >
        <Form.Item
          label="用户名"
          name="username"
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="姓名"
          name="full_name"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="邮箱"
          name="email"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="手机号"
          name="phone"
        >
          <Input />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            loading={profileSaving}
          >
            保存修改
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const passwordTab = (
    <Card>
      <Form
        form={passwordForm}
        layout="vertical"
        onFinish={handlePasswordSubmit}
      >
        <Form.Item
          label="当前密码"
          name="oldPassword"
          rules={[{ required: true, message: '请输入当前密码' }]}
        >
          <Input.Password prefix={<LockOutlined />} />
        </Form.Item>

        <Form.Item
          label="新密码"
          name="newPassword"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 8, message: '密码至少8个字符' },
          ]}
        >
          <Input.Password prefix={<LockOutlined />} />
        </Form.Item>

        <Form.Item
          label="确认新密码"
          name="confirmPassword"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password prefix={<LockOutlined />} />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<LockOutlined />}
            loading={passwordSaving}
          >
            修改密码
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const items: TabsProps['items'] = [
    {
      key: 'profile',
      label: '个人信息',
      children: profileTab,
    },
    {
      key: 'password',
      label: '修改密码',
      children: passwordTab,
    },
  ];

  if (loading && !currentUser) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <h2 className="mb-4">个人中心</h2>
      <Tabs items={items} />
    </div>
  );
};

export default ProfilePage;
