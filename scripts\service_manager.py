#!/usr/bin/env python3
"""
服务管理脚本

提供服务启动、停止、重启、状态检查等功能
"""

import os
import sys
import subprocess
import psutil
import time
import json
import argparse
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class ServiceManager:
    """服务管理器"""
    
    def __init__(self, config_file: str = "services.json"):
        self.config_file = config_file
        self.services = {}
        self.load_config()
    
    def load_config(self):
        """加载服务配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.services = json.load(f)
            else:
                self.create_default_config()
                
        except Exception as e:
            print(f"❌ 加载服务配置失败: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认服务配置"""
        try:
            default_services = {
                "api": {
                    "name": "Moniit API服务",
                    "command": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000",
                    "working_dir": ".",
                    "env": {},
                    "auto_restart": True,
                    "health_check": "http://localhost:8000/health",
                    "description": "主API服务"
                },
                "worker": {
                    "name": "后台任务处理器",
                    "command": "python -m app.worker",
                    "working_dir": ".",
                    "env": {},
                    "auto_restart": True,
                    "health_check": "",
                    "description": "处理后台任务"
                },
                "scheduler": {
                    "name": "定时任务调度器",
                    "command": "python -m app.scheduler",
                    "working_dir": ".",
                    "env": {},
                    "auto_restart": True,
                    "health_check": "",
                    "description": "执行定时任务"
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_services, f, ensure_ascii=False, indent=2)
            
            self.services = default_services
            print(f"✅ 创建默认服务配置: {self.config_file}")
            
        except Exception as e:
            print(f"❌ 创建默认配置失败: {e}")
    
    def start_service(self, service_name: str) -> bool:
        """启动服务"""
        try:
            if service_name not in self.services:
                print(f"❌ 服务不存在: {service_name}")
                return False
            
            service_config = self.services[service_name]
            
            # 检查服务是否已在运行
            if self.is_service_running(service_name):
                print(f"⚠️  服务已在运行: {service_config['name']}")
                return True
            
            print(f"🚀 启动服务: {service_config['name']}")
            
            # 准备环境变量
            env = os.environ.copy()
            env.update(service_config.get('env', {}))
            
            # 启动进程
            process = subprocess.Popen(
                service_config['command'],
                shell=True,
                cwd=service_config.get('working_dir', '.'),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True
            )
            
            # 保存进程ID
            pid_file = f"{service_name}.pid"
            with open(pid_file, 'w') as f:
                f.write(str(process.pid))
            
            # 等待一下确认启动成功
            time.sleep(2)
            
            if process.poll() is None:
                print(f"✅ 服务启动成功: {service_config['name']} (PID: {process.pid})")
                return True
            else:
                print(f"❌ 服务启动失败: {service_config['name']}")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务失败: {e}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止服务"""
        try:
            if service_name not in self.services:
                print(f"❌ 服务不存在: {service_name}")
                return False
            
            service_config = self.services[service_name]
            pid_file = f"{service_name}.pid"
            
            if not os.path.exists(pid_file):
                print(f"⚠️  服务未运行: {service_config['name']}")
                return True
            
            # 读取进程ID
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            print(f"🛑 停止服务: {service_config['name']} (PID: {pid})")
            
            try:
                process = psutil.Process(pid)
                
                # 优雅停止
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    # 强制杀死
                    print("⚠️  优雅停止超时，强制终止进程")
                    process.kill()
                    process.wait(timeout=5)
                
                print(f"✅ 服务已停止: {service_config['name']}")
                
            except psutil.NoSuchProcess:
                print(f"⚠️  进程不存在: {service_config['name']}")
            
            # 删除PID文件
            if os.path.exists(pid_file):
                os.remove(pid_file)
            
            return True
            
        except Exception as e:
            print(f"❌ 停止服务失败: {e}")
            return False
    
    def restart_service(self, service_name: str) -> bool:
        """重启服务"""
        try:
            print(f"🔄 重启服务: {service_name}")
            
            # 先停止服务
            self.stop_service(service_name)
            
            # 等待一下
            time.sleep(2)
            
            # 再启动服务
            return self.start_service(service_name)
            
        except Exception as e:
            print(f"❌ 重启服务失败: {e}")
            return False
    
    def is_service_running(self, service_name: str) -> bool:
        """检查服务是否运行"""
        try:
            pid_file = f"{service_name}.pid"
            
            if not os.path.exists(pid_file):
                return False
            
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            try:
                process = psutil.Process(pid)
                return process.is_running()
            except psutil.NoSuchProcess:
                # 清理无效的PID文件
                os.remove(pid_file)
                return False
                
        except Exception:
            return False
    
    def get_service_status(self, service_name: str) -> Dict:
        """获取服务状态"""
        try:
            if service_name not in self.services:
                return {"error": "服务不存在"}
            
            service_config = self.services[service_name]
            pid_file = f"{service_name}.pid"
            
            status = {
                "name": service_config['name'],
                "service_id": service_name,
                "description": service_config.get('description', ''),
                "running": False,
                "pid": None,
                "uptime": None,
                "memory_mb": None,
                "cpu_percent": None
            }
            
            if os.path.exists(pid_file):
                with open(pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                try:
                    process = psutil.Process(pid)
                    if process.is_running():
                        status.update({
                            "running": True,
                            "pid": pid,
                            "uptime": time.time() - process.create_time(),
                            "memory_mb": process.memory_info().rss / 1024 / 1024,
                            "cpu_percent": process.cpu_percent()
                        })
                except psutil.NoSuchProcess:
                    # 清理无效的PID文件
                    os.remove(pid_file)
            
            return status
            
        except Exception as e:
            return {"error": str(e)}
    
    def list_services(self) -> List[Dict]:
        """列出所有服务"""
        try:
            services_status = []
            
            for service_name in self.services:
                status = self.get_service_status(service_name)
                services_status.append(status)
            
            return services_status
            
        except Exception as e:
            print(f"❌ 获取服务列表失败: {e}")
            return []
    
    def start_all(self) -> bool:
        """启动所有服务"""
        try:
            print("🚀 启动所有服务...")
            success_count = 0
            
            for service_name in self.services:
                if self.start_service(service_name):
                    success_count += 1
                time.sleep(1)  # 避免同时启动太多服务
            
            total_services = len(self.services)
            print(f"📊 启动完成: {success_count}/{total_services} 个服务成功启动")
            
            return success_count == total_services
            
        except Exception as e:
            print(f"❌ 启动所有服务失败: {e}")
            return False
    
    def stop_all(self) -> bool:
        """停止所有服务"""
        try:
            print("🛑 停止所有服务...")
            success_count = 0
            
            for service_name in self.services:
                if self.stop_service(service_name):
                    success_count += 1
            
            total_services = len(self.services)
            print(f"📊 停止完成: {success_count}/{total_services} 个服务成功停止")
            
            return success_count == total_services
            
        except Exception as e:
            print(f"❌ 停止所有服务失败: {e}")
            return False
    
    def show_status(self):
        """显示所有服务状态"""
        try:
            services_status = self.list_services()
            
            if not services_status:
                print("📭 没有配置的服务")
                return
            
            print("📊 服务状态:")
            print("-" * 80)
            print(f"{'服务名':<20} {'状态':<8} {'PID':<8} {'运行时间':<12} {'内存(MB)':<10} {'CPU%':<8}")
            print("-" * 80)
            
            for status in services_status:
                if 'error' in status:
                    print(f"{status.get('service_id', 'N/A'):<20} {'错误':<8} {status['error']}")
                    continue
                
                running_status = "✅ 运行" if status['running'] else "❌ 停止"
                pid = str(status['pid']) if status['pid'] else "-"
                
                uptime = "-"
                if status['uptime']:
                    hours = int(status['uptime'] // 3600)
                    minutes = int((status['uptime'] % 3600) // 60)
                    uptime = f"{hours}h{minutes}m"
                
                memory = f"{status['memory_mb']:.1f}" if status['memory_mb'] else "-"
                cpu = f"{status['cpu_percent']:.1f}" if status['cpu_percent'] else "-"
                
                print(f"{status['name']:<20} {running_status:<8} {pid:<8} {uptime:<12} {memory:<10} {cpu:<8}")
            
            # 统计信息
            running_count = sum(1 for s in services_status if s.get('running', False))
            total_count = len(services_status)
            
            print("-" * 80)
            print(f"📈 统计: {running_count}/{total_count} 个服务正在运行")
            
        except Exception as e:
            print(f"❌ 显示服务状态失败: {e}")
    
    def show_logs(self, service_name: str, lines: int = 50):
        """显示服务日志"""
        try:
            if service_name not in self.services:
                print(f"❌ 服务不存在: {service_name}")
                return
            
            log_file = f"{service_name}.log"
            
            if not os.path.exists(log_file):
                print(f"📭 日志文件不存在: {log_file}")
                return
            
            print(f"📋 服务日志: {self.services[service_name]['name']} (最近 {lines} 行)")
            print("-" * 80)
            
            # 读取日志文件的最后几行
            with open(log_file, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
            
            # 显示最后几行
            for line in log_lines[-lines:]:
                print(line.rstrip())
            
        except Exception as e:
            print(f"❌ 显示服务日志失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Moniit 服务管理工具")
    parser.add_argument('--config', '-c', default='services.json', help='服务配置文件')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动服务
    start_parser = subparsers.add_parser('start', help='启动服务')
    start_parser.add_argument('service', nargs='?', help='服务名称 (不指定则启动所有服务)')
    
    # 停止服务
    stop_parser = subparsers.add_parser('stop', help='停止服务')
    stop_parser.add_argument('service', nargs='?', help='服务名称 (不指定则停止所有服务)')
    
    # 重启服务
    restart_parser = subparsers.add_parser('restart', help='重启服务')
    restart_parser.add_argument('service', help='服务名称')
    
    # 查看状态
    status_parser = subparsers.add_parser('status', help='查看服务状态')
    
    # 查看日志
    logs_parser = subparsers.add_parser('logs', help='查看服务日志')
    logs_parser.add_argument('service', help='服务名称')
    logs_parser.add_argument('--lines', type=int, default=50, help='显示行数')
    
    # 列出服务
    list_parser = subparsers.add_parser('list', help='列出所有服务')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = ServiceManager(args.config)
    
    try:
        if args.command == 'start':
            if args.service:
                manager.start_service(args.service)
            else:
                manager.start_all()
        elif args.command == 'stop':
            if args.service:
                manager.stop_service(args.service)
            else:
                manager.stop_all()
        elif args.command == 'restart':
            manager.restart_service(args.service)
        elif args.command == 'status':
            manager.show_status()
        elif args.command == 'logs':
            manager.show_logs(args.service, args.lines)
        elif args.command == 'list':
            manager.show_status()
        else:
            print(f"❌ 未知命令: {args.command}")
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n⏹️  操作已取消")
    except Exception as e:
        print(f"❌ 执行失败: {e}")


if __name__ == "__main__":
    main()
