#!/usr/bin/env python3
"""
测试数据库连接脚本
"""

import asyncio
import asyncpg
import os


async def test_connection():
    """测试数据库连接"""
    db_url = "postgresql://moniit:moniit123@localhost:5432/moniit"
    print(f"尝试连接: {db_url}")
    
    try:
        conn = await asyncpg.connect(db_url)
        print("✅ 数据库连接成功!")
        
        # 测试查询
        result = await conn.fetchval("SELECT version()")
        print(f"数据库版本: {result}")
        
        # 检查测试表
        tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_name LIKE 'test_%'
        """)
        
        print(f"测试表数量: {len(tables)}")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 检查数据
        if tables:
            for table in tables:
                table_name = table['table_name']
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"  {table_name}: {count} 条记录")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_connection())
    exit(0 if success else 1)
