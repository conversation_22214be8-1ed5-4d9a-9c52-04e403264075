"""
应用配置模块
"""

import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
from pydantic import Field
from pydantic_settings import BaseSettings
from datetime import datetime


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    url: str = Field(env="DATABASE_URL")
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False
    pool_pre_ping: bool = True
    pool_recycle: int = 3600


class RedisSettings(BaseSettings):
    """Redis配置"""
    url: str = Field(env="REDIS_URL")
    max_connections: int = 10
    socket_timeout: int = 5
    socket_connect_timeout: int = 5


class CelerySettings(BaseSettings):
    """Celery配置"""
    broker_url: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    result_backend: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    task_serializer: str = "json"
    accept_content: list = ["json"]
    result_serializer: str = "json"
    timezone: str = "UTC"
    enable_utc: bool = True


class TaskMiddlewareSettings(BaseSettings):
    """TaskMiddleware API配置"""
    base_url: str = Field(env="TASK_MIDDLEWARE_URL")
    api_key: str = Field(default="", env="TASK_MIDDLEWARE_API_KEY")
    timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 5


class TranslationSettings(BaseSettings):
    """翻译服务配置"""
    default_provider: str = "openai"
    openai_api_key: str = Field(default="", env="OPENAI_API_KEY")
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-3.5-turbo"
    claude_api_key: str = Field(default="", env="CLAUDE_API_KEY")
    claude_base_url: str = "https://api.anthropic.com"
    claude_model: str = "claude-3-haiku-20240307"


class SecuritySettings(BaseSettings):
    """安全配置"""
    secret_key: str = Field(env="SECRET_KEY")
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    password_min_length: int = 8


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/app.log"
    max_size: str = "10MB"
    backup_count: int = 5
    console_output: bool = True


class NotificationSettings(BaseSettings):
    """通知配置"""
    email_enabled: bool = True
    smtp_host: str = Field(default="", env="SMTP_HOST")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_user: str = Field(default="", env="SMTP_USER")
    smtp_password: str = Field(default="", env="SMTP_PASSWORD")
    from_address: str = Field(default="", env="FROM_EMAIL")
    use_tls: bool = True


class AppSettings(BaseSettings):
    """应用主配置"""
    name: str = "电商商品监控系统"
    version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="APP_HOST")
    port: int = Field(default=8000, env="APP_PORT")
    reload: bool = False
    environment: str = Field(default="production", env="ENVIRONMENT")
    
    # 子配置
    database: DatabaseSettings
    redis: RedisSettings
    celery: CelerySettings
    task_middleware: TaskMiddlewareSettings
    translation: TranslationSettings
    security: SecuritySettings
    logging: LoggingSettings
    notification: NotificationSettings
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"  # 忽略额外的环境变量
    }


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/app.yaml"):
        self.config_file = Path(config_file)
        self.config_cache: Dict[str, Any] = {}
        self.last_reload: Optional[datetime] = None
        self._app_settings: Optional[AppSettings] = None
    
    def load_yaml_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            if not self.config_file.exists():
                return {}
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            
            self.config_cache = config
            self.last_reload = datetime.utcnow()
            
            return config
            
        except Exception as e:
            print(f"配置加载失败: {e}")
            return self.config_cache or {}
    
    def get_app_settings(self) -> AppSettings:
        """获取应用设置"""
        if self._app_settings is None or self._should_reload():
            # 加载YAML配置
            yaml_config = self.load_yaml_config()
            
            # 创建配置实例
            try:
                # 构建配置字典
                config_dict = {}
                
                # 数据库配置
                db_config = yaml_config.get("database", {})
                config_dict["database"] = DatabaseSettings(
                    url=os.getenv("DATABASE_URL", db_config.get("url", "postgresql+asyncpg://moniit:moniit123@localhost:5433/moniit")),
                    pool_size=db_config.get("pool_size", 10),
                    max_overflow=db_config.get("max_overflow", 20),
                    echo=db_config.get("echo", False),
                    pool_pre_ping=db_config.get("pool_pre_ping", True),
                    pool_recycle=db_config.get("pool_recycle", 3600)
                )
                
                # Redis配置
                redis_config = yaml_config.get("redis", {})
                config_dict["redis"] = RedisSettings(
                    url=os.getenv("REDIS_URL", redis_config.get("url", "")),
                    max_connections=redis_config.get("max_connections", 10),
                    socket_timeout=redis_config.get("socket_timeout", 5),
                    socket_connect_timeout=redis_config.get("socket_connect_timeout", 5)
                )
                
                # Celery配置
                celery_config = yaml_config.get("celery", {})
                config_dict["celery"] = CelerySettings(
                    broker_url=os.getenv("CELERY_BROKER_URL", celery_config.get("broker_url", "")),
                    result_backend=os.getenv("CELERY_RESULT_BACKEND", celery_config.get("result_backend", "")),
                    task_serializer=celery_config.get("task_serializer", "json"),
                    accept_content=celery_config.get("accept_content", ["json"]),
                    result_serializer=celery_config.get("result_serializer", "json"),
                    timezone=celery_config.get("timezone", "UTC"),
                    enable_utc=celery_config.get("enable_utc", True)
                )
                
                # TaskMiddleware配置
                tm_config = yaml_config.get("task_middleware", {})
                config_dict["task_middleware"] = TaskMiddlewareSettings(
                    base_url=os.getenv("TASK_MIDDLEWARE_URL", tm_config.get("base_url", "")),
                    api_key=os.getenv("TASK_MIDDLEWARE_API_KEY", tm_config.get("api_key", "")),
                    timeout=tm_config.get("timeout", 30),
                    max_retries=tm_config.get("max_retries", 3),
                    retry_delay=tm_config.get("retry_delay", 5)
                )
                
                # 翻译服务配置
                trans_config = yaml_config.get("translation", {})
                config_dict["translation"] = TranslationSettings(
                    default_provider=trans_config.get("default_provider", "openai"),
                    openai_api_key=os.getenv("OPENAI_API_KEY", trans_config.get("openai", {}).get("api_key", "")),
                    openai_base_url=trans_config.get("openai", {}).get("base_url", "https://api.openai.com/v1"),
                    openai_model=trans_config.get("openai", {}).get("model", "gpt-3.5-turbo"),
                    claude_api_key=os.getenv("CLAUDE_API_KEY", trans_config.get("claude", {}).get("api_key", "")),
                    claude_base_url=trans_config.get("claude", {}).get("base_url", "https://api.anthropic.com"),
                    claude_model=trans_config.get("claude", {}).get("model", "claude-3-haiku-20240307")
                )
                
                # 安全配置
                security_config = yaml_config.get("security", {})
                config_dict["security"] = SecuritySettings(
                    secret_key=os.getenv("SECRET_KEY", security_config.get("secret_key", "")),
                    access_token_expire_minutes=security_config.get("access_token_expire_minutes", 30),
                    algorithm=security_config.get("algorithm", "HS256"),
                    password_min_length=security_config.get("password_min_length", 8)
                )
                
                # 日志配置
                log_config = yaml_config.get("logging", {})
                config_dict["logging"] = LoggingSettings(
                    level=os.getenv("LOG_LEVEL", log_config.get("level", "INFO")),
                    format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
                    file=log_config.get("file", "logs/app.log"),
                    max_size=log_config.get("max_size", "10MB"),
                    backup_count=log_config.get("backup_count", 5),
                    console_output=log_config.get("console_output", True)
                )
                
                # 通知配置
                notif_config = yaml_config.get("notification", {}).get("email", {})
                config_dict["notification"] = NotificationSettings(
                    email_enabled=notif_config.get("enabled", True),
                    smtp_host=os.getenv("SMTP_HOST", notif_config.get("smtp_host", "")),
                    smtp_port=int(os.getenv("SMTP_PORT", notif_config.get("smtp_port", 587))),
                    smtp_user=os.getenv("SMTP_USER", notif_config.get("smtp_user", "")),
                    smtp_password=os.getenv("SMTP_PASSWORD", notif_config.get("smtp_password", "")),
                    from_address=os.getenv("FROM_EMAIL", notif_config.get("from_address", "")),
                    use_tls=notif_config.get("use_tls", True)
                )
                
                # 应用主配置
                app_config = yaml_config.get("app", {})
                self._app_settings = AppSettings(
                    name=app_config.get("name", "电商商品监控系统"),
                    version=app_config.get("version", "1.0.0"),
                    debug=os.getenv("DEBUG", "false").lower() == "true",
                    host=os.getenv("APP_HOST", app_config.get("host", "0.0.0.0")),
                    port=int(os.getenv("APP_PORT", app_config.get("port", 8000))),
                    reload=app_config.get("reload", False),
                    environment=os.getenv("ENVIRONMENT", app_config.get("environment", "production")),
                    **config_dict
                )
                
            except Exception as e:
                print(f"配置解析失败: {e}")
                # 使用默认配置
                self._app_settings = self._get_default_settings()
        
        return self._app_settings
    
    def _should_reload(self) -> bool:
        """检查是否需要重新加载配置"""
        if not self.last_reload:
            return True
        
        # 每5分钟检查一次配置文件是否更新
        if (datetime.utcnow() - self.last_reload).total_seconds() > 300:
            try:
                if self.config_file.exists():
                    file_mtime = datetime.fromtimestamp(self.config_file.stat().st_mtime)
                    return file_mtime > self.last_reload
            except:
                pass
        
        return False
    
    def _get_default_settings(self) -> AppSettings:
        """获取默认配置"""
        # 从环境变量获取数据库URL，如果没有则使用默认值
        database_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://moniit:moniit123@localhost:5433/moniit")
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6380/0")
        secret_key = os.getenv("SECRET_KEY", "default-secret-key-change-in-production")

        return AppSettings(
            database=DatabaseSettings(url=database_url),
            redis=RedisSettings(url=redis_url),
            celery=CelerySettings(),
            task_middleware=TaskMiddlewareSettings(base_url="http://localhost:3000"),
            translation=TranslationSettings(),
            security=SecuritySettings(secret_key=secret_key),
            logging=LoggingSettings(),
            notification=NotificationSettings()
        )
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        if not self.config_cache or self._should_reload():
            self.load_yaml_config()
        
        keys = key.split('.')
        value = self.config_cache
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value


# 全局配置管理器
config_manager = ConfigManager()


def get_settings() -> AppSettings:
    """获取应用设置"""
    return config_manager.get_app_settings()
