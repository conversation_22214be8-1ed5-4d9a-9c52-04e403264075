#!/bin/bash

# Moniit 系统优化脚本
# 优化系统性能、数据库、缓存等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Moniit 系统优化脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --database       优化数据库"
    echo "  -r, --redis          优化Redis缓存"
    echo "  -c, --containers     优化Docker容器"
    echo "  -s, --system         优化系统设置"
    echo "  -a, --all            执行所有优化操作"
    echo "  -f, --force          强制执行（不询问确认）"
    echo "  -n, --dry-run        预览模式（不实际执行）"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -a                执行所有优化操作"
    echo "  $0 -d                仅优化数据库"
    echo "  $0 -n -a             预览所有优化操作"
}

# 确认操作
confirm_action() {
    local action=$1
    
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    echo ""
    log_warning "即将执行: $action"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 1
    fi
    
    return 0
}

# 检查数据库连接
check_database() {
    if ! docker exec moniit-timescaledb-dev pg_isready -U moniit -d moniit &>/dev/null; then
        log_error "TimescaleDB数据库连接失败"
        return 1
    fi
    return 0
}

# 检查Redis连接
check_redis() {
    if ! docker exec moniit-redis redis-cli ping &>/dev/null; then
        log_error "Redis连接失败"
        return 1
    fi
    return 0
}

# 优化数据库
optimize_database() {
    log_info "优化数据库..."
    
    if ! check_database; then
        return 1
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行数据库优化操作"
        log_info "[预览] - 更新表统计信息"
        log_info "[预览] - 重建索引"
        log_info "[预览] - 执行VACUUM FULL"
        log_info "[预览] - 优化数据库配置"
        return 0
    fi
    
    # 获取优化前的数据库信息
    local db_size_before=$(docker exec moniit-timescaledb-dev psql -U moniit -d moniit -t -c "SELECT pg_size_pretty(pg_database_size('moniit'));" 2>/dev/null | xargs)
    log_info "优化前TimescaleDB数据库大小: $db_size_before"
    
    # 更新表统计信息
    log_info "更新表统计信息..."
    docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "ANALYZE;" &>/dev/null
    log_success "表统计信息更新完成"

    # 重建索引
    log_info "重建索引..."
    local tables=("users" "products" "monitor_tasks" "system_logs")

    for table in "${tables[@]}"; do
        log_info "重建表 $table 的索引..."
        docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "REINDEX TABLE $table;" &>/dev/null || true
    done
    log_success "索引重建完成"

    # TimescaleDB特定优化
    log_info "执行TimescaleDB优化操作..."
    docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "
        -- 刷新连续聚合视图
        CALL refresh_continuous_aggregate('price_records_hourly', NULL, NULL);
        CALL refresh_continuous_aggregate('price_records_daily', NULL, NULL);
        CALL refresh_continuous_aggregate('price_records_weekly', NULL, NULL);
        CALL refresh_continuous_aggregate('system_logs_hourly', NULL, NULL);

        -- 执行压缩
        SELECT compress_chunk(chunk) FROM show_chunks('price_records') AS chunk
        WHERE NOT is_compressed(chunk) AND chunk < now() - INTERVAL '7 days';

        -- 常规VACUUM
        VACUUM ANALYZE;
    " &>/dev/null
    log_success "TimescaleDB优化完成"
    
    # 优化TimescaleDB配置
    log_info "优化TimescaleDB配置..."

    # 设置合适的内存参数和TimescaleDB特定配置
    docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "
        ALTER SYSTEM SET shared_buffers = '256MB';
        ALTER SYSTEM SET effective_cache_size = '1GB';
        ALTER SYSTEM SET maintenance_work_mem = '64MB';
        ALTER SYSTEM SET checkpoint_completion_target = 0.9;
        ALTER SYSTEM SET wal_buffers = '16MB';
        ALTER SYSTEM SET default_statistics_target = 100;

        -- TimescaleDB特定配置
        ALTER SYSTEM SET timescaledb.max_background_workers = 8;
        ALTER SYSTEM SET max_worker_processes = 16;
        ALTER SYSTEM SET timescaledb.telemetry_level = 'off';
    " &>/dev/null || true

    log_info "TimescaleDB配置优化完成（需要重启数据库容器生效）"

    # 获取优化后的数据库信息
    local db_size_after=$(docker exec moniit-timescaledb-dev psql -U moniit -d moniit -t -c "SELECT pg_size_pretty(pg_database_size('moniit'));" 2>/dev/null | xargs)
    log_info "优化后TimescaleDB数据库大小: $db_size_after"
    
    # 显示TimescaleDB统计信息
    log_info "TimescaleDB统计信息:"
    docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "
        -- 显示超表信息
        SELECT hypertable_name, num_chunks, compressed_chunks, uncompressed_heap_size, compressed_heap_size
        FROM timescaledb_information.hypertables h
        LEFT JOIN timescaledb_information.compression_settings cs ON h.hypertable_name = cs.hypertable_name;

        -- 显示连续聚合视图信息
        SELECT view_name, materialized_only, refresh_lag, refresh_interval
        FROM timescaledb_information.continuous_aggregates;

        -- 显示表统计信息
        SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
        FROM pg_stat_user_tables
        WHERE schemaname = 'public'
        ORDER BY tablename;
    " 2>/dev/null || true

    log_success "TimescaleDB优化完成"
}

# 优化Redis缓存
optimize_redis() {
    log_info "优化Redis缓存..."
    
    if ! check_redis; then
        return 1
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行Redis优化操作"
        log_info "[预览] - 清理过期键"
        log_info "[预览] - 优化内存使用"
        log_info "[预览] - 调整配置参数"
        return 0
    fi
    
    # 获取Redis信息
    local redis_info=$(docker exec moniit-redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
    log_info "当前Redis内存使用: $redis_info"
    
    # 清理过期键
    log_info "清理过期键..."
    local expired_keys=$(docker exec moniit-redis redis-cli --scan --pattern "*" | wc -l)
    docker exec moniit-redis redis-cli FLUSHEXPIRED &>/dev/null || true
    log_info "Redis中共有 $expired_keys 个键"
    
    # 执行内存优化
    log_info "执行内存优化..."
    docker exec moniit-redis redis-cli MEMORY PURGE &>/dev/null || true
    
    # 优化Redis配置
    log_info "优化Redis配置..."
    docker exec moniit-redis redis-cli CONFIG SET maxmemory-policy allkeys-lru &>/dev/null || true
    docker exec moniit-redis redis-cli CONFIG SET save "900 1 300 10 60 10000" &>/dev/null || true
    docker exec moniit-redis redis-cli CONFIG SET tcp-keepalive 300 &>/dev/null || true
    
    # 获取优化后的信息
    local redis_info_after=$(docker exec moniit-redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
    log_info "优化后Redis内存使用: $redis_info_after"
    
    # 显示Redis统计信息
    log_info "Redis统计信息:"
    docker exec moniit-redis redis-cli info stats | grep -E "(keyspace_hits|keyspace_misses|expired_keys|evicted_keys)" || true
    
    log_success "Redis缓存优化完成"
}

# 优化Docker容器
optimize_containers() {
    log_info "优化Docker容器..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行Docker容器优化操作"
        log_info "[预览] - 调整容器资源限制"
        log_info "[预览] - 优化容器网络"
        log_info "[预览] - 清理未使用的资源"
        return 0
    fi
    
    # 显示当前容器资源使用情况
    log_info "当前容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null || true
    
    # 优化容器日志配置
    log_info "优化容器日志配置..."
    
    local containers=$(docker ps --format "{{.Names}}" | grep "^moniit-" || true)
    for container in $containers; do
        # 限制日志文件大小
        docker update --log-opt max-size=10m --log-opt max-file=3 "$container" &>/dev/null || true
        log_info "已优化容器 $container 的日志配置"
    done
    
    # 清理Docker系统
    log_info "清理Docker系统资源..."
    docker system prune -f &>/dev/null || true
    
    # 显示优化后的Docker资源使用情况
    log_info "Docker资源使用情况:"
    docker system df 2>/dev/null || true
    
    log_success "Docker容器优化完成"
}

# 优化系统设置
optimize_system() {
    log_info "优化系统设置..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行系统优化操作"
        log_info "[预览] - 调整文件描述符限制"
        log_info "[预览] - 优化网络参数"
        log_info "[预览] - 清理系统缓存"
        return 0
    fi
    
    # 检查系统资源
    log_info "当前系统资源使用情况:"
    echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' || echo "N/A")"
    echo "内存: $(free -h | grep Mem | awk '{print $3"/"$2}')"
    echo "磁盘: $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5")"}')"
    echo "负载: $(uptime | awk -F'load average:' '{print $2}' | xargs)"
    
    # 清理系统缓存
    log_info "清理系统缓存..."
    sync
    echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
    log_info "系统缓存清理完成"
    
    # 优化文件描述符限制
    log_info "检查文件描述符限制..."
    local current_limit=$(ulimit -n)
    log_info "当前文件描述符限制: $current_limit"
    
    if [ "$current_limit" -lt 65536 ]; then
        log_warning "建议增加文件描述符限制到65536"
        log_info "可以在 /etc/security/limits.conf 中添加："
        log_info "* soft nofile 65536"
        log_info "* hard nofile 65536"
    fi
    
    # 检查交换空间
    log_info "检查交换空间..."
    local swap_info=$(free -h | grep Swap)
    if echo "$swap_info" | grep -q "0B.*0B"; then
        log_warning "系统未配置交换空间，建议配置适当的交换空间"
    else
        log_info "交换空间: $(echo "$swap_info" | awk '{print $3"/"$2}')"
    fi
    
    # 显示系统优化建议
    log_info "系统优化建议:"
    echo "1. 定期重启服务以释放内存"
    echo "2. 监控磁盘空间使用情况"
    echo "3. 定期清理日志文件"
    echo "4. 监控数据库性能"
    echo "5. 配置适当的备份策略"
    
    log_success "系统优化完成"
}

# 生成优化报告
generate_report() {
    log_info "生成优化报告..."
    
    local report_file="./logs/optimization_report_$(date +%Y%m%d_%H%M%S).txt"
    mkdir -p "$(dirname "$report_file")"
    
    {
        echo "Moniit 系统优化报告"
        echo "==================="
        echo "生成时间: $(date)"
        echo ""
        
        echo "系统信息:"
        echo "--------"
        echo "主机名: $(hostname)"
        echo "系统: $(uname -s)"
        echo "内核: $(uname -r)"
        echo "运行时间: $(uptime -p 2>/dev/null || uptime)"
        echo ""
        
        echo "资源使用:"
        echo "--------"
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' || echo "N/A")"
        echo "内存: $(free -h | grep Mem | awk '{print $3"/"$2}')"
        echo "磁盘: $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5")"}')"
        echo "负载: $(uptime | awk -F'load average:' '{print $2}' | xargs)"
        echo ""
        
        echo "Docker信息:"
        echo "----------"
        docker system df 2>/dev/null || echo "Docker信息获取失败"
        echo ""
        
        echo "数据库信息:"
        echo "----------"
        if check_database; then
            docker exec moniit-postgres psql -U moniit -d moniit -t -c "SELECT pg_size_pretty(pg_database_size('moniit'));" 2>/dev/null | xargs || echo "数据库大小获取失败"
        else
            echo "数据库连接失败"
        fi
        echo ""
        
        echo "Redis信息:"
        echo "---------"
        if check_redis; then
            docker exec moniit-redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r' || echo "Redis信息获取失败"
        else
            echo "Redis连接失败"
        fi
        echo ""
        
        echo "优化建议:"
        echo "--------"
        echo "1. 定期执行数据库优化"
        echo "2. 监控Redis内存使用"
        echo "3. 清理旧日志和备份文件"
        echo "4. 监控系统资源使用"
        echo "5. 定期重启服务"
        
    } > "$report_file"
    
    log_success "优化报告已生成: $report_file"
}

# 主函数
main() {
    # 默认参数
    local optimize_database=false
    local optimize_redis=false
    local optimize_containers=false
    local optimize_system=false
    local optimize_all=false
    FORCE=false
    DRY_RUN=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--database)
                optimize_database=true
                shift
                ;;
            -r|--redis)
                optimize_redis=true
                shift
                ;;
            -c|--containers)
                optimize_containers=true
                shift
                ;;
            -s|--system)
                optimize_system=true
                shift
                ;;
            -a|--all)
                optimize_all=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何优化选项，显示帮助
    if [ "$optimize_all" = false ] && [ "$optimize_database" = false ] && [ "$optimize_redis" = false ] && [ "$optimize_containers" = false ] && [ "$optimize_system" = false ]; then
        log_error "请指定要执行的优化操作"
        show_help
        exit 1
    fi
    
    log_info "开始 Moniit 系统优化"
    if [ "$DRY_RUN" = true ]; then
        log_warning "预览模式：不会实际执行优化操作"
    fi
    
    # 执行优化操作
    if [ "$optimize_all" = true ] || [ "$optimize_database" = true ]; then
        if confirm_action "优化数据库"; then
            optimize_database
        fi
    fi
    
    if [ "$optimize_all" = true ] || [ "$optimize_redis" = true ]; then
        if confirm_action "优化Redis缓存"; then
            optimize_redis
        fi
    fi
    
    if [ "$optimize_all" = true ] || [ "$optimize_containers" = true ]; then
        if confirm_action "优化Docker容器"; then
            optimize_containers
        fi
    fi
    
    if [ "$optimize_all" = true ] || [ "$optimize_system" = true ]; then
        if confirm_action "优化系统设置"; then
            optimize_system
        fi
    fi
    
    # 生成优化报告
    if [ "$DRY_RUN" = false ]; then
        generate_report
    fi
    
    log_success "Moniit 系统优化完成!"
}

# 执行主函数
main "$@"
