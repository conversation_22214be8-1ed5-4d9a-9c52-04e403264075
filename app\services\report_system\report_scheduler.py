"""
报表调度器

提供定期报表自动生成和推送功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from app.core.logging import get_logger
from .report_engine import ReportEngine, ReportConfig, ReportPeriod, ReportFormat

logger = get_logger(__name__)


class ScheduleStatus(Enum):
    """调度状态"""
    PENDING = "pending"         # 等待中
    RUNNING = "running"         # 运行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"     # 已取消


@dataclass
class ScheduleTask:
    """调度任务"""
    task_id: str
    report_id: str
    schedule_time: datetime
    status: ScheduleStatus = ScheduleStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class ScheduleRule:
    """调度规则"""
    rule_id: str
    report_id: str
    period: ReportPeriod
    enabled: bool = True
    next_run_time: Optional[datetime] = None
    last_run_time: Optional[datetime] = None
    run_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)


class ReportScheduler:
    """报表调度器"""
    
    def __init__(self, report_engine: ReportEngine):
        self.report_engine = report_engine
        
        # 调度存储
        self.schedule_tasks: List[ScheduleTask] = []
        self.schedule_rules: Dict[str, ScheduleRule] = {}
        
        # 调度配置
        self.scheduler_config = {
            "check_interval": 60,      # 检查间隔（秒）
            "max_concurrent": 5,       # 最大并发任务数
            "task_timeout": 1800,      # 任务超时时间（秒）
            "cleanup_days": 30,        # 清理天数
            "retry_interval": 300      # 重试间隔（秒）
        }
        
        # 运行状态
        self.is_running = False
        self.current_tasks: Dict[str, asyncio.Task] = {}
        
        # 初始化默认调度规则
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """初始化默认调度规则"""
        # 获取所有自动生成的报表配置
        for config in self.report_engine.get_report_configs():
            if config.auto_generate:
                rule = ScheduleRule(
                    rule_id=f"auto_{config.report_id}",
                    report_id=config.report_id,
                    period=config.report_period,
                    next_run_time=self._calculate_next_run_time(config.report_period)
                )
                self.schedule_rules[rule.rule_id] = rule
    
    def _calculate_next_run_time(self, period: ReportPeriod, 
                                base_time: Optional[datetime] = None) -> datetime:
        """计算下次运行时间"""
        if not base_time:
            base_time = datetime.now()
        
        if period == ReportPeriod.DAILY:
            # 每天早上8点
            next_time = base_time.replace(hour=8, minute=0, second=0, microsecond=0)
            if next_time <= base_time:
                next_time += timedelta(days=1)
        elif period == ReportPeriod.WEEKLY:
            # 每周一早上8点
            days_until_monday = (7 - base_time.weekday()) % 7
            if days_until_monday == 0 and base_time.hour >= 8:
                days_until_monday = 7
            next_time = (base_time + timedelta(days=days_until_monday)).replace(
                hour=8, minute=0, second=0, microsecond=0)
        elif period == ReportPeriod.MONTHLY:
            # 每月1号早上8点
            if base_time.day == 1 and base_time.hour < 8:
                next_time = base_time.replace(day=1, hour=8, minute=0, second=0, microsecond=0)
            else:
                if base_time.month == 12:
                    next_time = base_time.replace(year=base_time.year + 1, month=1, day=1,
                                                hour=8, minute=0, second=0, microsecond=0)
                else:
                    next_time = base_time.replace(month=base_time.month + 1, day=1,
                                                hour=8, minute=0, second=0, microsecond=0)
        elif period == ReportPeriod.QUARTERLY:
            # 每季度第一天早上8点
            current_quarter = (base_time.month - 1) // 3 + 1
            next_quarter_month = current_quarter * 3 + 1
            if next_quarter_month > 12:
                next_time = base_time.replace(year=base_time.year + 1, month=1, day=1,
                                            hour=8, minute=0, second=0, microsecond=0)
            else:
                next_time = base_time.replace(month=next_quarter_month, day=1,
                                            hour=8, minute=0, second=0, microsecond=0)
        elif period == ReportPeriod.YEARLY:
            # 每年1月1号早上8点
            next_time = base_time.replace(year=base_time.year + 1, month=1, day=1,
                                        hour=8, minute=0, second=0, microsecond=0)
        else:
            # 默认1小时后
            next_time = base_time + timedelta(hours=1)
        
        return next_time
    
    async def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return
        
        self.is_running = True
        logger.info("报表调度器已启动")
        
        try:
            while self.is_running:
                await self._check_and_execute_tasks()
                await asyncio.sleep(self.scheduler_config["check_interval"])
                
        except Exception as e:
            logger.error(f"调度器运行异常: {e}")
        finally:
            self.is_running = False
            logger.info("报表调度器已停止")
    
    async def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        
        # 等待当前任务完成
        if self.current_tasks:
            logger.info(f"等待 {len(self.current_tasks)} 个任务完成...")
            await asyncio.gather(*self.current_tasks.values(), return_exceptions=True)
        
        logger.info("报表调度器已停止")
    
    async def _check_and_execute_tasks(self):
        """检查并执行任务"""
        try:
            current_time = datetime.now()
            
            # 检查调度规则
            for rule in self.schedule_rules.values():
                if (rule.enabled and rule.next_run_time and 
                    rule.next_run_time <= current_time):
                    await self._create_schedule_task(rule)
            
            # 执行待处理的任务
            pending_tasks = [t for t in self.schedule_tasks 
                           if t.status == ScheduleStatus.PENDING and t.schedule_time <= current_time]
            
            # 限制并发数
            available_slots = self.scheduler_config["max_concurrent"] - len(self.current_tasks)
            tasks_to_execute = pending_tasks[:available_slots]
            
            for task in tasks_to_execute:
                await self._execute_task(task)
            
            # 清理完成的任务
            await self._cleanup_completed_tasks()
            
        except Exception as e:
            logger.error(f"检查和执行任务失败: {e}")
    
    async def _create_schedule_task(self, rule: ScheduleRule):
        """创建调度任务"""
        try:
            task = ScheduleTask(
                task_id=f"{rule.rule_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                report_id=rule.report_id,
                schedule_time=datetime.now()
            )
            
            self.schedule_tasks.append(task)
            
            # 更新规则的下次运行时间
            rule.next_run_time = self._calculate_next_run_time(rule.period, datetime.now())
            rule.run_count += 1
            
            logger.info(f"创建调度任务: {task.task_id}")
            
        except Exception as e:
            logger.error(f"创建调度任务失败: {rule.rule_id}, {e}")
    
    async def _execute_task(self, task: ScheduleTask):
        """执行任务"""
        try:
            task.status = ScheduleStatus.RUNNING
            task.started_at = datetime.now()
            
            # 创建异步任务
            async_task = asyncio.create_task(self._run_report_generation(task))
            self.current_tasks[task.task_id] = async_task
            
            logger.info(f"开始执行任务: {task.task_id}")
            
        except Exception as e:
            logger.error(f"执行任务失败: {task.task_id}, {e}")
            task.status = ScheduleStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
    
    async def _run_report_generation(self, task: ScheduleTask):
        """运行报表生成"""
        try:
            # 生成报表
            result = await self.report_engine.generate_report(
                report_id=task.report_id,
                format=ReportFormat.HTML
            )
            
            if result.status == "completed":
                task.status = ScheduleStatus.COMPLETED
                task.result_id = result.report_id
                
                # 更新规则统计
                rule = self.schedule_rules.get(f"auto_{task.report_id}")
                if rule:
                    rule.success_count += 1
                    rule.last_run_time = datetime.now()
                
                logger.info(f"任务执行成功: {task.task_id}")
            else:
                raise Exception(result.error_message or "报表生成失败")
                
        except Exception as e:
            logger.error(f"任务执行失败: {task.task_id}, {e}")
            task.status = ScheduleStatus.FAILED
            task.error_message = str(e)
            
            # 更新规则统计
            rule = self.schedule_rules.get(f"auto_{task.report_id}")
            if rule:
                rule.failure_count += 1
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = ScheduleStatus.PENDING
                task.schedule_time = datetime.now() + timedelta(
                    seconds=self.scheduler_config["retry_interval"]
                )
                logger.info(f"任务将重试: {task.task_id}, 重试次数: {task.retry_count}")
        
        finally:
            task.completed_at = datetime.now()
            # 从当前任务中移除
            if task.task_id in self.current_tasks:
                del self.current_tasks[task.task_id]
    
    async def _cleanup_completed_tasks(self):
        """清理完成的任务"""
        try:
            cleanup_time = datetime.now() - timedelta(days=self.scheduler_config["cleanup_days"])
            
            # 保留最近的任务
            self.schedule_tasks = [
                task for task in self.schedule_tasks
                if (task.completed_at is None or task.completed_at > cleanup_time)
            ]
            
        except Exception as e:
            logger.error(f"清理任务失败: {e}")
    
    def add_schedule_rule(self, rule: ScheduleRule) -> bool:
        """添加调度规则"""
        try:
            if rule.rule_id in self.schedule_rules:
                logger.warning(f"调度规则已存在: {rule.rule_id}")
                return False
            
            # 计算下次运行时间
            if not rule.next_run_time:
                rule.next_run_time = self._calculate_next_run_time(rule.period)
            
            self.schedule_rules[rule.rule_id] = rule
            logger.info(f"调度规则已添加: {rule.rule_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加调度规则失败: {rule.rule_id}, {e}")
            return False
    
    def update_schedule_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """更新调度规则"""
        try:
            if rule_id not in self.schedule_rules:
                logger.error(f"调度规则不存在: {rule_id}")
                return False
            
            rule = self.schedule_rules[rule_id]
            
            # 更新规则属性
            for key, value in updates.items():
                if hasattr(rule, key):
                    if key == "period":
                        rule.period = ReportPeriod(value)
                        # 重新计算下次运行时间
                        rule.next_run_time = self._calculate_next_run_time(rule.period)
                    else:
                        setattr(rule, key, value)
            
            logger.info(f"调度规则已更新: {rule_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新调度规则失败: {rule_id}, {e}")
            return False
    
    def delete_schedule_rule(self, rule_id: str) -> bool:
        """删除调度规则"""
        try:
            if rule_id in self.schedule_rules:
                del self.schedule_rules[rule_id]
                logger.info(f"调度规则已删除: {rule_id}")
                return True
            else:
                logger.error(f"调度规则不存在: {rule_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除调度规则失败: {rule_id}, {e}")
            return False
    
    def get_schedule_rules(self) -> List[ScheduleRule]:
        """获取所有调度规则"""
        return list(self.schedule_rules.values())
    
    def get_schedule_tasks(self, limit: Optional[int] = None) -> List[ScheduleTask]:
        """获取调度任务"""
        tasks = sorted(self.schedule_tasks, key=lambda x: x.created_at, reverse=True)
        
        if limit:
            tasks = tasks[:limit]
        
        return tasks
    
    def get_scheduler_statistics(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        try:
            total_tasks = len(self.schedule_tasks)
            completed_tasks = len([t for t in self.schedule_tasks if t.status == ScheduleStatus.COMPLETED])
            failed_tasks = len([t for t in self.schedule_tasks if t.status == ScheduleStatus.FAILED])
            running_tasks = len([t for t in self.schedule_tasks if t.status == ScheduleStatus.RUNNING])
            
            # 按状态统计
            status_distribution = {}
            for task in self.schedule_tasks:
                status = task.status.value
                status_distribution[status] = status_distribution.get(status, 0) + 1
            
            # 成功率
            success_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            return {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "running_tasks": running_tasks,
                "pending_tasks": total_tasks - completed_tasks - failed_tasks - running_tasks,
                "success_rate": success_rate,
                "total_rules": len(self.schedule_rules),
                "enabled_rules": len([r for r in self.schedule_rules.values() if r.enabled]),
                "current_concurrent": len(self.current_tasks),
                "max_concurrent": self.scheduler_config["max_concurrent"],
                "is_running": self.is_running,
                "status_distribution": status_distribution,
                "available_periods": [p.value for p in ReportPeriod]
            }
            
        except Exception as e:
            logger.error(f"获取调度器统计失败: {e}")
            return {
                "total_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "running_tasks": 0,
                "pending_tasks": 0,
                "success_rate": 0,
                "total_rules": 0,
                "enabled_rules": 0,
                "current_concurrent": 0,
                "max_concurrent": self.scheduler_config["max_concurrent"],
                "is_running": self.is_running,
                "status_distribution": {},
                "available_periods": [p.value for p in ReportPeriod]
            }
