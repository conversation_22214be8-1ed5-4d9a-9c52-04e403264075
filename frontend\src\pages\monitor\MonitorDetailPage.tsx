/**
 * 监控任务详情页面
 */

import React, { useEffect } from 'react';
import { Card, Descriptions, Tag, Button, Space, Spin, Alert, message } from 'antd';
import { ArrowLeftOutlined, EditOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchTaskByIdAsync,
  pauseTaskAsync,
  resumeTaskAsync,
  selectCurrentTask,
  selectMonitorLoading
} from '../../store/slices/monitorSlice';

const MonitorDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();

  const task = useAppSelector(selectCurrentTask);
  const loading = useAppSelector(selectMonitorLoading);

  useEffect(() => {
    if (id) {
      dispatch(fetchTaskByIdAsync(id))
        .unwrap()
        .catch((error) => {
          message.error(`获取任务详情失败：${error.message}`);
        });
    }
  }, [dispatch, id]);

  const handleTaskAction = async (action: 'pause' | 'resume') => {
    if (!id) return;

    try {
      if (action === 'pause') {
        await dispatch(pauseTaskAsync(id)).unwrap();
        message.success('任务已暂停');
      } else {
        await dispatch(resumeTaskAsync(id)).unwrap();
        message.success('任务已恢复');
      }
      // 重新获取任务详情
      dispatch(fetchTaskByIdAsync(id));
    } catch (error: any) {
      message.error(`操作失败：${error.message}`);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!task) {
    return (
      <div>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/monitor')}
          className="mb-4"
        >
          返回监控列表
        </Button>
        <Alert
          message="任务不存在"
          description="未找到指定的监控任务，请检查任务ID是否正确。"
          type="error"
          showIcon
        />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      active: 'green',
      paused: 'orange',
      failed: 'red',
      pending: 'blue',
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      active: '运行中',
      paused: '已暂停',
      failed: '失败',
      pending: '等待中',
    };
    return labels[status] || status;
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex align-items-center">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/monitor')}
            className="mr-2"
          >
            返回
          </Button>
          <h2 className="mb-0">监控任务详情</h2>
        </div>
        <Space>
          {task.status === 'active' ? (
            <Button
              icon={<PauseCircleOutlined />}
              onClick={() => handleTaskAction('pause')}
            >
              暂停任务
            </Button>
          ) : (
            <Button
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction('resume')}
            >
              启动任务
            </Button>
          )}
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => navigate(`/monitor/${id}/edit`)}
          >
            编辑任务
          </Button>
        </Space>
      </div>

      <Card title="基本信息" className="mb-4">
        <Descriptions column={2} bordered>
          <Descriptions.Item label="任务ID">{task.task_id}</Descriptions.Item>
          <Descriptions.Item label="任务名称">{task.name}</Descriptions.Item>
          <Descriptions.Item label="关联商品ID">{task.product_id || '未关联'}</Descriptions.Item>
          <Descriptions.Item label="监控URL" span={2}>
            {task.url ? (
              <a href={task.url} target="_blank" rel="noopener noreferrer">
                {task.url}
              </a>
            ) : '未设置'}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={getStatusColor(task.status)}>{getStatusLabel(task.status)}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="监控间隔">{task.interval_minutes}分钟</Descriptions.Item>
          <Descriptions.Item label="CSS选择器">{task.selector || '未设置'}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {task.created_at ? new Date(task.created_at).toLocaleString() : '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {task.updated_at ? new Date(task.updated_at).toLocaleString() : '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="最后运行时间">
            {task.last_run ? new Date(task.last_run).toLocaleString() : '从未运行'}
          </Descriptions.Item>
          <Descriptions.Item label="下次运行时间">
            {task.next_run ? new Date(task.next_run).toLocaleString() : '未安排'}
          </Descriptions.Item>
        </Descriptions>
      </Card>


    </div>
  );
};

export default MonitorDetailPage;
