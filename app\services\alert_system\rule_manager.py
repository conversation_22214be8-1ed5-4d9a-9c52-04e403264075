"""
预警规则管理器

提供预警规则的创建、管理、验证等功能
"""

import json
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from app.core.logging import get_logger
from app.models.product import ProductType
from .alert_engine import AlertRule, AlertType, AlertCategory, AlertLevel

logger = get_logger(__name__)


class RuleConditionType(Enum):
    """规则条件类型"""
    THRESHOLD = "threshold"           # 阈值条件
    PERCENTAGE_CHANGE = "percentage_change"  # 百分比变化
    TIME_WINDOW = "time_window"       # 时间窗口
    COMPARISON = "comparison"         # 比较条件
    PATTERN = "pattern"               # 模式匹配


@dataclass
class RuleCondition:
    """规则条件"""
    condition_type: RuleConditionType
    field_name: str
    operator: str  # >, <, >=, <=, ==, !=, contains, matches
    value: Any
    description: str = ""


@dataclass
class RuleTemplate:
    """规则模板"""
    template_id: str
    template_name: str
    alert_type: AlertType
    alert_category: AlertCategory
    default_level: AlertLevel
    applicable_product_types: List[ProductType]
    condition_templates: List[RuleCondition]
    description: str
    example_conditions: Dict[str, Any]


class RuleManager:
    """预警规则管理器"""
    
    def __init__(self):
        # 规则存储
        self.rule_templates: Dict[str, RuleTemplate] = {}
        self.custom_rules: Dict[str, AlertRule] = {}
        
        # 规则验证配置
        self.validation_config = {
            "max_conditions_per_rule": 10,
            "supported_operators": {
                "numeric": [">", "<", ">=", "<=", "==", "!="],
                "string": ["==", "!=", "contains", "startswith", "endswith"],
                "boolean": ["==", "!="]
            },
            "required_fields": ["rule_name", "alert_type", "alert_category", "conditions"]
        }
        
        # 初始化规则模板
        self._initialize_rule_templates()
    
    def _initialize_rule_templates(self):
        """初始化规则模板"""
        # 价格异常模板
        price_anomaly_template = RuleTemplate(
            template_id="price_anomaly_template",
            template_name="价格异常预警模板",
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            default_level=AlertLevel.HIGH,
            applicable_product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.PERCENTAGE_CHANGE,
                    field_name="price_change_percentage",
                    operator="<=",
                    value=-0.10,
                    description="价格下降超过10%"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.TIME_WINDOW,
                    field_name="time_window_hours",
                    operator="<=",
                    value=24,
                    description="在24小时内"
                )
            ],
            description="监控商品价格异常变化",
            example_conditions={
                "price_drop_percentage": 0.10,
                "time_window_hours": 24
            }
        )
        
        # 销量变化模板
        sales_change_template = RuleTemplate(
            template_id="sales_change_template",
            template_name="销量变化预警模板",
            alert_type=AlertType.SALES_DECLINE,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            default_level=AlertLevel.MEDIUM,
            applicable_product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.PERCENTAGE_CHANGE,
                    field_name="sales_change_percentage",
                    operator=">=",
                    value=0.50,
                    description="销量变化超过50%"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.TIME_WINDOW,
                    field_name="time_window_days",
                    operator="<=",
                    value=7,
                    description="在7天内"
                )
            ],
            description="监控销量异常变化",
            example_conditions={
                "sales_growth_percentage": 0.50,
                "time_window_days": 7
            }
        )
        
        # 库存不足模板
        inventory_shortage_template = RuleTemplate(
            template_id="inventory_shortage_template",
            template_name="库存不足预警模板",
            alert_type=AlertType.INVENTORY_SHORTAGE,
            alert_category=AlertCategory.INVENTORY_ALERT,
            default_level=AlertLevel.HIGH,
            applicable_product_types=[ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.THRESHOLD,
                    field_name="stock_level_threshold",
                    operator="<=",
                    value=100,
                    description="库存低于100件"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.THRESHOLD,
                    field_name="days_of_supply",
                    operator="<=",
                    value=7,
                    description="供应天数少于7天"
                )
            ],
            description="监控库存不足情况",
            example_conditions={
                "stock_level_threshold": 100,
                "days_of_supply": 7
            }
        )
        
        # 利润率下降模板
        profit_margin_template = RuleTemplate(
            template_id="profit_margin_template",
            template_name="利润率预警模板",
            alert_type=AlertType.PROFIT_MARGIN_DROP,
            alert_category=AlertCategory.PROFIT_ALERT,
            default_level=AlertLevel.HIGH,
            applicable_product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.THRESHOLD,
                    field_name="profit_margin_threshold",
                    operator="<=",
                    value=0.0,
                    description="利润率低于0%"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.PERCENTAGE_CHANGE,
                    field_name="margin_drop_percentage",
                    operator=">=",
                    value=0.20,
                    description="利润率下降超过20%"
                )
            ],
            description="监控利润率异常情况",
            example_conditions={
                "profit_margin_threshold": 0.0,
                "margin_drop_percentage": 0.20
            }
        )
        
        # 供应商问题模板
        supplier_issue_template = RuleTemplate(
            template_id="supplier_issue_template",
            template_name="供应商问题预警模板",
            alert_type=AlertType.SUPPLIER_ISSUE,
            alert_category=AlertCategory.SUPPLIER_ALERT,
            default_level=AlertLevel.HIGH,
            applicable_product_types=[ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.PERCENTAGE_CHANGE,
                    field_name="cost_increase_percentage",
                    operator=">=",
                    value=0.15,
                    description="成本上涨超过15%"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.TIME_WINDOW,
                    field_name="time_window_days",
                    operator="<=",
                    value=30,
                    description="在30天内"
                )
            ],
            description="监控供应商相关问题",
            example_conditions={
                "cost_increase_percentage": 0.15,
                "time_window_days": 30
            }
        )
        
        # 市场机会模板
        market_opportunity_template = RuleTemplate(
            template_id="market_opportunity_template",
            template_name="市场机会预警模板",
            alert_type=AlertType.MARKET_OPPORTUNITY,
            alert_category=AlertCategory.MARKET_ALERT,
            default_level=AlertLevel.MEDIUM,
            applicable_product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
            condition_templates=[
                RuleCondition(
                    condition_type=RuleConditionType.THRESHOLD,
                    field_name="profit_margin_threshold",
                    operator=">=",
                    value=0.40,
                    description="利润率超过40%"
                ),
                RuleCondition(
                    condition_type=RuleConditionType.THRESHOLD,
                    field_name="market_demand_growth",
                    operator=">=",
                    value=0.30,
                    description="市场需求增长超过30%"
                )
            ],
            description="识别市场机会",
            example_conditions={
                "profit_margin_threshold": 0.40,
                "market_demand_growth": 0.30
            }
        )
        
        # 注册所有模板
        templates = [
            price_anomaly_template,
            sales_change_template,
            inventory_shortage_template,
            profit_margin_template,
            supplier_issue_template,
            market_opportunity_template
        ]
        
        for template in templates:
            self.rule_templates[template.template_id] = template
    
    def create_rule_from_template(self, template_id: str, rule_name: str,
                                 custom_conditions: Optional[Dict[str, Any]] = None,
                                 product_types: Optional[List[ProductType]] = None,
                                 alert_level: Optional[AlertLevel] = None) -> Optional[AlertRule]:
        """
        从模板创建规则
        
        Args:
            template_id: 模板ID
            rule_name: 规则名称
            custom_conditions: 自定义条件
            product_types: 适用商品类型
            alert_level: 预警级别
        
        Returns:
            Optional[AlertRule]: 创建的规则
        """
        try:
            if template_id not in self.rule_templates:
                logger.error(f"模板不存在: {template_id}")
                return None
            
            template = self.rule_templates[template_id]
            
            # 使用模板的默认条件或自定义条件
            conditions = custom_conditions or template.example_conditions
            
            # 使用指定的商品类型或模板默认类型
            applicable_types = product_types or template.applicable_product_types
            
            # 使用指定的预警级别或模板默认级别
            level = alert_level or template.default_level
            
            # 生成规则ID
            rule_id = f"{template_id}_{rule_name.lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            rule = AlertRule(
                rule_id=rule_id,
                rule_name=rule_name,
                alert_type=template.alert_type,
                alert_category=template.alert_category,
                product_types=applicable_types,
                conditions=conditions,
                alert_level=level,
                description=f"基于模板 {template.template_name} 创建的规则"
            )
            
            # 验证规则
            if self.validate_rule(rule):
                logger.info(f"从模板创建规则成功: {rule_id}")
                return rule
            else:
                logger.error(f"规则验证失败: {rule_id}")
                return None
                
        except Exception as e:
            logger.error(f"从模板创建规则失败: {template_id}, {e}")
            return None

    def validate_rule(self, rule: AlertRule) -> bool:
        """
        验证规则有效性

        Args:
            rule: 待验证的规则

        Returns:
            bool: 是否有效
        """
        try:
            # 检查必需字段
            if not rule.rule_name or not rule.rule_name.strip():
                logger.error("规则名称不能为空")
                return False

            if not rule.conditions:
                logger.error("规则条件不能为空")
                return False

            # 检查条件数量
            if len(rule.conditions) > self.validation_config["max_conditions_per_rule"]:
                logger.error(f"规则条件过多，最多支持 {self.validation_config['max_conditions_per_rule']} 个")
                return False

            # 验证条件格式
            for key, value in rule.conditions.items():
                if not self._validate_condition_value(key, value):
                    logger.error(f"条件值无效: {key} = {value}")
                    return False

            # 检查商品类型
            if not rule.product_types:
                logger.error("必须指定适用的商品类型")
                return False

            logger.info(f"规则验证通过: {rule.rule_id}")
            return True

        except Exception as e:
            logger.error(f"规则验证异常: {rule.rule_id}, {e}")
            return False

    def _validate_condition_value(self, key: str, value: Any) -> bool:
        """验证条件值"""
        try:
            # 百分比条件验证
            if "percentage" in key.lower():
                if not isinstance(value, (int, float)):
                    return False
                # 百分比通常在-1到10之间（-100%到1000%）
                return -1.0 <= value <= 10.0

            # 时间窗口验证
            if "time_window" in key.lower():
                if not isinstance(value, (int, float)):
                    return False
                return 0 < value <= 365 * 24  # 最多一年

            # 阈值验证
            if "threshold" in key.lower():
                if not isinstance(value, (int, float)):
                    return False
                return value >= 0  # 阈值通常为非负数

            # 其他数值验证
            if isinstance(value, (int, float)):
                return not (value != value)  # 检查NaN

            # 字符串验证
            if isinstance(value, str):
                return len(value.strip()) > 0

            # 布尔值验证
            if isinstance(value, bool):
                return True

            return True

        except Exception:
            return False

    def create_custom_rule(self, rule_data: Dict[str, Any]) -> Optional[AlertRule]:
        """
        创建自定义规则

        Args:
            rule_data: 规则数据

        Returns:
            Optional[AlertRule]: 创建的规则
        """
        try:
            # 检查必需字段
            for field in self.validation_config["required_fields"]:
                if field not in rule_data:
                    logger.error(f"缺少必需字段: {field}")
                    return None

            # 生成规则ID
            rule_id = f"custom_{rule_data['rule_name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 解析枚举值
            try:
                alert_type = AlertType(rule_data["alert_type"])
                alert_category = AlertCategory(rule_data["alert_category"])
                alert_level = AlertLevel(rule_data.get("alert_level", "medium"))
                product_types = [ProductType(pt) for pt in rule_data.get("product_types", ["other"])]
            except ValueError as e:
                logger.error(f"枚举值无效: {e}")
                return None

            rule = AlertRule(
                rule_id=rule_id,
                rule_name=rule_data["rule_name"],
                alert_type=alert_type,
                alert_category=alert_category,
                product_types=product_types,
                conditions=rule_data["conditions"],
                alert_level=alert_level,
                enabled=rule_data.get("enabled", True),
                description=rule_data.get("description", "")
            )

            # 验证规则
            if self.validate_rule(rule):
                self.custom_rules[rule_id] = rule
                logger.info(f"自定义规则创建成功: {rule_id}")
                return rule
            else:
                logger.error(f"自定义规则验证失败: {rule_id}")
                return None

        except Exception as e:
            logger.error(f"创建自定义规则失败: {e}")
            return None

    def get_rule(self, rule_id: str) -> Optional[AlertRule]:
        """
        获取规则

        Args:
            rule_id: 规则ID

        Returns:
            Optional[AlertRule]: 规则对象
        """
        return self.custom_rules.get(rule_id)

    def get_all_rules(self) -> List[AlertRule]:
        """获取所有自定义规则"""
        return list(self.custom_rules.values())

    def get_rules_by_category(self, category: AlertCategory) -> List[AlertRule]:
        """
        按分类获取规则

        Args:
            category: 预警分类

        Returns:
            List[AlertRule]: 规则列表
        """
        return [rule for rule in self.custom_rules.values() if rule.alert_category == category]

    def get_templates(self) -> List[RuleTemplate]:
        """获取所有规则模板"""
        return list(self.rule_templates.values())

    def get_template(self, template_id: str) -> Optional[RuleTemplate]:
        """
        获取规则模板

        Args:
            template_id: 模板ID

        Returns:
            Optional[RuleTemplate]: 模板对象
        """
        return self.rule_templates.get(template_id)

    def update_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新规则

        Args:
            rule_id: 规则ID
            updates: 更新内容

        Returns:
            bool: 是否成功更新
        """
        try:
            if rule_id not in self.custom_rules:
                logger.error(f"规则不存在: {rule_id}")
                return False

            rule = self.custom_rules[rule_id]

            # 更新规则属性
            for key, value in updates.items():
                if hasattr(rule, key):
                    # 处理枚举类型
                    if key == "alert_type":
                        rule.alert_type = AlertType(value)
                    elif key == "alert_category":
                        rule.alert_category = AlertCategory(value)
                    elif key == "alert_level":
                        rule.alert_level = AlertLevel(value)
                    elif key == "product_types":
                        rule.product_types = [ProductType(pt) for pt in value]
                    else:
                        setattr(rule, key, value)

            logger.info(f"规则已更新: {rule_id}")
            return True

        except Exception as e:
            logger.error(f"更新规则失败: {rule_id}, {e}")
            return False

    def delete_rule(self, rule_id: str) -> bool:
        """
        删除规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 是否成功删除
        """
        try:
            if rule_id in self.custom_rules:
                del self.custom_rules[rule_id]
                logger.info(f"规则删除成功: {rule_id}")
                return True
            else:
                logger.error(f"规则不存在: {rule_id}")
                return False

        except Exception as e:
            logger.error(f"删除规则失败: {rule_id}, {e}")
            return False

    def get_rule_statistics(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        try:
            total_rules = len(self.custom_rules)
            enabled_rules = len([r for r in self.custom_rules.values() if r.enabled])

            # 按类型统计
            type_distribution = {}
            for rule in self.custom_rules.values():
                alert_type = rule.alert_type.value
                type_distribution[alert_type] = type_distribution.get(alert_type, 0) + 1

            # 按分类统计
            category_distribution = {}
            for rule in self.custom_rules.values():
                category = rule.alert_category.value
                category_distribution[category] = category_distribution.get(category, 0) + 1

            # 按级别统计
            level_distribution = {}
            for rule in self.custom_rules.values():
                level = rule.alert_level.value
                level_distribution[level] = level_distribution.get(level, 0) + 1

            return {
                "total_rules": total_rules,
                "enabled_rules": enabled_rules,
                "disabled_rules": total_rules - enabled_rules,
                "total_templates": len(self.rule_templates),
                "type_distribution": type_distribution,
                "category_distribution": category_distribution,
                "level_distribution": level_distribution,
                "available_types": [t.value for t in AlertType],
                "available_categories": [c.value for c in AlertCategory],
                "available_levels": [l.value for l in AlertLevel]
            }

        except Exception as e:
            logger.error(f"获取规则统计失败: {e}")
            return {
                "total_rules": 0,
                "enabled_rules": 0,
                "disabled_rules": 0,
                "total_templates": 0,
                "type_distribution": {},
                "category_distribution": {},
                "level_distribution": {},
                "available_types": [t.value for t in AlertType],
                "available_categories": [c.value for c in AlertCategory],
                "available_levels": [l.value for l in AlertLevel]
            }
