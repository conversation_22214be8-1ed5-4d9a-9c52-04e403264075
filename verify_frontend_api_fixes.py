#!/usr/bin/env python3
"""
前端API修复验证脚本

验证前端API调用与后端API端点的匹配情况
"""

import asyncio
import httpx
import json
from typing import Dict, List, Tuple

# 后端服务地址
BACKEND_URL = "http://localhost:8000"

class APIVerifier:
    """API验证器"""
    
    def __init__(self):
        self.results = {
            "passed": [],
            "failed": [],
            "warnings": []
        }
    
    async def verify_api_endpoints(self):
        """验证API端点"""
        print("🔍 开始验证前端API修复结果...")
        print("=" * 60)
        
        # 验证各个模块的API端点
        await self.verify_auth_apis()
        await self.verify_product_apis()
        await self.verify_supplier_apis()
        await self.verify_monitor_apis()
        await self.verify_analytics_apis()
        await self.verify_system_apis()
        
        # 输出验证结果
        self.print_results()
    
    async def verify_auth_apis(self):
        """验证认证API"""
        print("🔐 验证认证API...")
        
        endpoints = [
            ("POST", "/api/v1/auth/login", "用户登录"),
            ("POST", "/api/v1/auth/register", "用户注册"),
            ("POST", "/api/v1/auth/logout", "用户登出"),
            ("POST", "/api/v1/auth/refresh", "令牌刷新"),
            ("GET", "/api/v1/auth/me", "获取用户信息"),
        ]
        
        await self.check_endpoints("认证API", endpoints)
    
    async def verify_product_apis(self):
        """验证商品API"""
        print("📦 验证商品API...")
        
        endpoints = [
            ("GET", "/api/v1/products/", "获取商品列表"),
            ("POST", "/api/v1/products/", "创建商品"),
            ("GET", "/api/v1/products/{id}", "获取商品详情"),
            ("PUT", "/api/v1/products/{id}", "更新商品"),
            ("DELETE", "/api/v1/products/{id}", "删除商品"),
            ("POST", "/api/v1/products/import", "批量导入商品"),
        ]
        
        await self.check_endpoints("商品API", endpoints)
    
    async def verify_supplier_apis(self):
        """验证供货商API"""
        print("🏪 验证供货商API...")
        
        endpoints = [
            ("GET", "/api/v1/suppliers/", "获取供货商列表"),
            ("POST", "/api/v1/suppliers/", "创建供货商"),
            ("GET", "/api/v1/suppliers/{id}", "获取供货商详情"),
            ("PUT", "/api/v1/suppliers/{id}", "更新供货商"),
            ("DELETE", "/api/v1/suppliers/{id}", "删除供货商"),
        ]
        
        await self.check_endpoints("供货商API", endpoints)
    
    async def verify_monitor_apis(self):
        """验证监控API"""
        print("📊 验证监控API...")
        
        endpoints = [
            ("GET", "/api/v1/monitor/tasks", "获取监控任务列表"),
            ("POST", "/api/v1/monitor/tasks", "创建监控任务"),
            ("GET", "/api/v1/monitor/tasks/{id}", "获取任务详情"),
            ("PUT", "/api/v1/monitor/tasks/{id}", "更新任务"),
            ("DELETE", "/api/v1/monitor/tasks/{id}", "删除任务"),
            ("POST", "/api/v1/monitor/tasks/{id}/start", "启动任务"),
            ("POST", "/api/v1/monitor/tasks/{id}/pause", "暂停任务"),
            ("GET", "/api/v1/monitor/tasks/{id}/logs", "获取任务日志"),
        ]
        
        await self.check_endpoints("监控API", endpoints)
    
    async def verify_analytics_apis(self):
        """验证数据分析API"""
        print("📈 验证数据分析API...")
        
        endpoints = [
            ("GET", "/api/v1/analytics/statistics", "获取统计数据"),
            ("GET", "/api/v1/analytics/price-trends/{product_id}", "获取价格趋势"),
            ("POST", "/api/v1/analytics/reports/generate", "生成报表"),
            ("GET", "/api/v1/analytics/search", "数据搜索"),
        ]
        
        await self.check_endpoints("数据分析API", endpoints)
    
    async def verify_system_apis(self):
        """验证系统API"""
        print("⚙️ 验证系统API...")
        
        endpoints = [
            ("GET", "/health", "健康检查"),
            ("GET", "/api/v1/system/dashboard/stats", "仪表板统计"),
            ("GET", "/api/v1/system/info", "系统信息"),
            ("GET", "/api/v1/system/config", "获取系统配置"),
            ("PUT", "/api/v1/system/config/{key}", "更新系统配置"),
            ("GET", "/api/v1/system/users", "获取用户列表"),
            ("POST", "/api/v1/system/users", "创建用户"),
            ("GET", "/api/v1/system/logs", "获取系统日志"),
            ("GET", "/api/v1/system/metrics", "获取系统指标"),
        ]
        
        await self.check_endpoints("系统API", endpoints)
    
    async def check_endpoints(self, module_name: str, endpoints: List[Tuple[str, str, str]]):
        """检查API端点"""
        async with httpx.AsyncClient(base_url=BACKEND_URL, timeout=10.0) as client:
            for method, path, description in endpoints:
                try:
                    # 替换路径参数为测试值
                    test_path = path.replace("{id}", "test-id").replace("{product_id}", "test-product-id").replace("{key}", "test-key")
                    
                    # 发送请求
                    if method == "GET":
                        response = await client.get(test_path)
                    elif method == "POST":
                        response = await client.post(test_path, json={})
                    elif method == "PUT":
                        response = await client.put(test_path, json={})
                    elif method == "DELETE":
                        response = await client.delete(test_path)
                    else:
                        continue
                    
                    # 检查响应
                    if response.status_code == 404:
                        self.results["failed"].append(f"❌ {module_name} - {description}: 端点不存在 ({method} {path})")
                    elif response.status_code in [200, 201, 422, 401, 403]:  # 422是验证错误，401/403是认证错误，都表示端点存在
                        self.results["passed"].append(f"✅ {module_name} - {description}: 端点存在 ({method} {path})")
                    else:
                        self.results["warnings"].append(f"⚠️ {module_name} - {description}: 状态码 {response.status_code} ({method} {path})")
                
                except httpx.ConnectError:
                    self.results["failed"].append(f"❌ {module_name} - {description}: 无法连接到服务器 ({method} {path})")
                except Exception as e:
                    self.results["warnings"].append(f"⚠️ {module_name} - {description}: 检查异常 {str(e)} ({method} {path})")
    
    def print_results(self):
        """输出验证结果"""
        print("\n" + "=" * 60)
        print("🎯 API验证结果汇总")
        print("=" * 60)
        
        print(f"\n✅ 通过验证: {len(self.results['passed'])} 个")
        for result in self.results["passed"]:
            print(f"  {result}")
        
        print(f"\n❌ 验证失败: {len(self.results['failed'])} 个")
        for result in self.results["failed"]:
            print(f"  {result}")
        
        print(f"\n⚠️ 警告信息: {len(self.results['warnings'])} 个")
        for result in self.results["warnings"]:
            print(f"  {result}")
        
        # 计算成功率
        total = len(self.results["passed"]) + len(self.results["failed"]) + len(self.results["warnings"])
        success_rate = (len(self.results["passed"]) / total * 100) if total > 0 else 0
        
        print(f"\n📊 验证统计:")
        print(f"  总计: {total} 个API端点")
        print(f"  成功: {len(self.results['passed'])} 个")
        print(f"  失败: {len(self.results['failed'])} 个")
        print(f"  警告: {len(self.results['warnings'])} 个")
        print(f"  成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 API修复验证通过！前端与后端API匹配度良好。")
        elif success_rate >= 70:
            print("\n⚠️ API修复基本完成，但仍有部分问题需要解决。")
        else:
            print("\n❌ API修复不完整，需要进一步调整。")

async def main():
    """主函数"""
    verifier = APIVerifier()
    await verifier.verify_api_endpoints()

if __name__ == "__main__":
    asyncio.run(main())
