# 电商商品监控系统 Makefile

.PHONY: help install dev-up dev-down prod-up prod-down build test clean logs shell migrate

# 默认目标
help:
	@echo "电商商品监控系统 - 可用命令:"
	@echo ""
	@echo "开发环境:"
	@echo "  install     - 安装Python依赖"
	@echo "  dev-up      - 启动开发环境"
	@echo "  dev-down    - 停止开发环境"
	@echo "  dev-logs    - 查看开发环境日志"
	@echo ""
	@echo "生产环境:"
	@echo "  prod-up     - 启动生产环境"
	@echo "  prod-down   - 停止生产环境"
	@echo "  prod-logs   - 查看生产环境日志"
	@echo "  build       - 构建Docker镜像"
	@echo ""
	@echo "数据库:"
	@echo "  migrate     - 运行数据库迁移"
	@echo "  db-shell    - 连接数据库"
	@echo ""
	@echo "开发工具:"
	@echo "  test        - 运行测试"
	@echo "  format      - 格式化代码"
	@echo "  lint        - 代码检查"
	@echo "  clean       - 清理临时文件"
	@echo "  shell       - 进入应用容器"

# 安装依赖
install:
	pip install -r requirements.txt

# 开发环境
dev-up:
	@echo "启动开发环境（支持热重载）..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "开发环境已启动"
	@echo "API服务: http://localhost:8000"
	@echo "数据库: localhost:5433"
	@echo "Redis: localhost:6380"
	@echo "Flower: http://localhost:5556"

dev-down:
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f

dev-logs-app:
	docker-compose -f docker-compose.dev.yml logs -f app

dev-restart:
	docker-compose -f docker-compose.dev.yml restart app

dev-shell:
	docker-compose -f docker-compose.dev.yml exec app bash

dev-build:
	docker-compose -f docker-compose.dev.yml build --no-cache

dev-start:
	@echo "使用快速启动脚本..."
	chmod +x scripts/dev-start.sh
	./scripts/dev-start.sh

# 生产环境
prod-up:
	docker-compose up -d
	@echo "生产环境已启动"
	@echo "应用: http://localhost:8000"
	@echo "API文档: http://localhost:8000/docs"
	@echo "Flower: http://localhost:5555"

prod-down:
	docker-compose down

prod-logs:
	docker-compose logs -f

build:
	docker-compose build

# 数据库操作
migrate:
	alembic upgrade head

db-shell:
	docker-compose exec db psql -U postgres -d ecommerce_monitor

# 开发工具
test:
	pytest -v --cov=app --cov-report=html

test-dev:
	docker-compose -f docker-compose.dev.yml exec app pytest -v --cov=app --cov-report=html

test-config:
	docker-compose -f docker-compose.dev.yml exec app pytest tests/test_config.py -v

test-cache:
	docker-compose -f docker-compose.dev.yml exec app pytest tests/test_cache.py -v

test-env:
	@echo "运行开发环境完整测试..."
	chmod +x scripts/test-dev-env.sh
	./scripts/test-dev-env.sh

format:
	black app/
	isort app/

format-dev:
	docker-compose -f docker-compose.dev.yml exec app black app/
	docker-compose -f docker-compose.dev.yml exec app isort app/

lint:
	flake8 app/
	black --check app/
	isort --check-only app/

lint-dev:
	docker-compose -f docker-compose.dev.yml exec app flake8 app/
	docker-compose -f docker-compose.dev.yml exec app black --check app/
	docker-compose -f docker-compose.dev.yml exec app isort --check-only app/

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage

clean-docker:
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f

shell:
	docker-compose exec app /bin/bash

# 快速启动本地开发
dev: dev-up
	@echo "等待服务启动..."
	@sleep 10
	@echo "运行数据库迁移..."
	@make migrate
	@echo "启动开发服务器..."
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 完整部署
deploy: build prod-up
	@echo "等待服务启动..."
	@sleep 30
	@echo "检查服务状态..."
	@docker-compose ps
	@echo "部署完成!"

# 备份数据库
backup:
	@echo "备份数据库..."
	@mkdir -p backups
	@docker-compose exec -T db pg_dump -U postgres ecommerce_monitor > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "备份完成: backups/backup_$(shell date +%Y%m%d_%H%M%S).sql"

# 恢复数据库
restore:
	@echo "请指定备份文件: make restore-file BACKUP_FILE=backups/backup_xxx.sql"

restore-file:
	@if [ -z "$(BACKUP_FILE)" ]; then echo "请指定BACKUP_FILE参数"; exit 1; fi
	@echo "恢复数据库从: $(BACKUP_FILE)"
	@docker-compose exec -T db psql -U postgres -d ecommerce_monitor < $(BACKUP_FILE)
	@echo "恢复完成"

# 查看系统状态
status:
	@echo "=== Docker容器状态 ==="
	@docker-compose ps
	@echo ""
	@echo "=== 应用健康检查 ==="
	@curl -s http://localhost:8000/health | python -m json.tool || echo "应用未启动"
	@echo ""
	@echo "=== 磁盘使用情况 ==="
	@df -h
	@echo ""
	@echo "=== 内存使用情况 ==="
	@free -h
