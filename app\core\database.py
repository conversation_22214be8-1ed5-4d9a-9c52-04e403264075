"""
数据库连接和管理模块
"""

import logging
from typing import Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    AsyncEngine,
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import NullPool
from sqlalchemy import text

logger = logging.getLogger(__name__)


class Base(DeclarativeBase):
    """数据库模型基类"""
    pass


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine: Optional[AsyncEngine] = None
        self.session_factory: Optional[async_sessionmaker] = None
    
    async def init(self, database_url: str, **kwargs):
        """初始化数据库连接"""
        try:
            # 创建异步引擎
            self.engine = create_async_engine(
                database_url,
                echo=kwargs.get("echo", False),
                pool_size=kwargs.get("pool_size", 10),
                max_overflow=kwargs.get("max_overflow", 20),
                pool_pre_ping=kwargs.get("pool_pre_ping", True),
                pool_recycle=kwargs.get("pool_recycle", 3600),
                poolclass=NullPool if "sqlite" in database_url else None,
            )
            
            # 创建会话工厂
            self.session_factory = async_sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 测试连接
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭数据库连接"""
        if self.engine:
            await self.engine.dispose()
            self.engine = None
            self.session_factory = None
            logger.info("数据库连接已关闭")
    
    def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        if not self.session_factory:
            raise RuntimeError("数据库未初始化")

        return self.session_factory()
    
    async def execute(self, query: str, params: dict = None):
        """执行SQL查询"""
        async with self.get_session() as session:
            result = await session.execute(text(query), params or {})
            await session.commit()
            return result
    
    async def fetch_all(self, query: str, params: dict = None):
        """获取所有查询结果"""
        async with self.get_session() as session:
            result = await session.execute(text(query), params or {})
            return result.fetchall()
    
    async def fetch_one(self, query: str, params: dict = None):
        """获取单个查询结果"""
        async with self.get_session() as session:
            result = await session.execute(text(query), params or {})
            return result.fetchone()
    
    async def create_tables(self):
        """创建数据表"""
        if not self.engine:
            raise RuntimeError("数据库未初始化")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据表创建完成")
    
    async def drop_tables(self):
        """删除数据表"""
        if not self.engine:
            raise RuntimeError("数据库未初始化")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("数据表删除完成")
    
    async def init_timescale_extension(self):
        """初始化TimescaleDB扩展"""
        try:
            await self.execute("CREATE EXTENSION IF NOT EXISTS timescaledb;")
            logger.info("TimescaleDB扩展初始化成功")
        except Exception as e:
            logger.warning(f"TimescaleDB扩展初始化失败: {e}")
    
    async def create_hypertable(self, table_name: str, time_column: str, 
                               chunk_time_interval: str = "1 day"):
        """创建TimescaleDB超表"""
        try:
            query = f"""
            SELECT create_hypertable('{table_name}', '{time_column}', 
                                   chunk_time_interval => INTERVAL '{chunk_time_interval}',
                                   if_not_exists => TRUE);
            """
            await self.execute(query)
            logger.info(f"超表 {table_name} 创建成功")
        except Exception as e:
            logger.error(f"超表 {table_name} 创建失败: {e}")
    
    async def get_table_info(self, table_name: str):
        """获取表信息"""
        query = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = :table_name
        ORDER BY ordinal_position;
        """
        return await self.fetch_all(query, {"table_name": table_name})
    
    async def get_database_size(self):
        """获取数据库大小"""
        query = "SELECT pg_size_pretty(pg_database_size(current_database()));"
        result = await self.fetch_one(query)
        return result[0] if result else "Unknown"
    
    async def get_connection_count(self):
        """获取连接数"""
        query = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
        result = await self.fetch_one(query)
        return result[0] if result else 0


# 全局数据库管理器
db_manager = DatabaseManager()


async def init_database(database_url: str, **kwargs):
    """初始化数据库"""
    await db_manager.init(database_url, **kwargs)
    
    # 初始化TimescaleDB扩展
    await db_manager.init_timescale_extension()


async def close_database():
    """关闭数据库连接"""
    await db_manager.close()


async def get_database() -> DatabaseManager:
    """获取数据库管理器"""
    if not db_manager.engine:
        raise RuntimeError("数据库未初始化")
    return db_manager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（依赖注入用）"""
    async with db_manager.get_session() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# 数据库操作基类
class BaseRepository:
    """数据库操作基类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def save(self, obj):
        """保存对象"""
        self.session.add(obj)
        await self.session.commit()
        await self.session.refresh(obj)
        return obj
    
    async def delete(self, obj):
        """删除对象"""
        await self.session.delete(obj)
        await self.session.commit()
    
    async def commit(self):
        """提交事务"""
        await self.session.commit()
    
    async def rollback(self):
        """回滚事务"""
        await self.session.rollback()
    
    async def refresh(self, obj):
        """刷新对象"""
        await self.session.refresh(obj)
        return obj
