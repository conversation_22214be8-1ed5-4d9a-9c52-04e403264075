"""
销量异常检测和预警系统

提供实时销量异常检测、预警通知和自动响应功能
"""

import asyncio
import statistics
import math
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.advanced_sales_analyzer import (
    AdvancedSalesAnalyzer, SalesAnomaly, SalesAnomalyType, SalesAlertLevel
)

logger = get_logger(__name__)


class DetectionMethod(Enum):
    """检测方法"""
    STATISTICAL = "statistical"        # 统计方法
    MACHINE_LEARNING = "ml"           # 机器学习方法
    RULE_BASED = "rule_based"         # 基于规则
    HYBRID = "hybrid"                 # 混合方法


class AlertChannel(Enum):
    """预警渠道"""
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    DASHBOARD = "dashboard"
    LOG = "log"


@dataclass
class DetectionConfig:
    """检测配置"""
    product_id: str
    enabled: bool = True
    detection_methods: List[DetectionMethod] = field(default_factory=lambda: [DetectionMethod.STATISTICAL])
    sensitivity: float = 0.5  # 0-1，越高越敏感
    alert_channels: List[AlertChannel] = field(default_factory=lambda: [AlertChannel.LOG])
    alert_cooldown_minutes: int = 60  # 预警冷却时间
    custom_thresholds: Dict[str, float] = field(default_factory=dict)
    notification_callbacks: List[Callable] = field(default_factory=list)


@dataclass
class AlertRecord:
    """预警记录"""
    alert_id: str
    product_id: str
    anomaly: SalesAnomaly
    triggered_at: datetime
    channels_sent: List[AlertChannel]
    acknowledged: bool = False
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolution_notes: str = ""


@dataclass
class DetectionStats:
    """检测统计"""
    total_detections: int = 0
    true_positives: int = 0
    false_positives: int = 0
    missed_anomalies: int = 0
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


class SalesAnomalyDetector:
    """销量异常检测和预警系统"""
    
    def __init__(self):
        self.advanced_analyzer = AdvancedSalesAnalyzer()
        self.detection_configs: Dict[str, DetectionConfig] = {}
        self.alert_records: List[AlertRecord] = []
        self.detection_stats: Dict[str, DetectionStats] = {}
        self.running = False
        self.detection_interval = 300  # 5分钟检测一次
        
        # 默认检测配置
        self.default_config = DetectionConfig(
            product_id="default",
            sensitivity=0.5,
            detection_methods=[DetectionMethod.STATISTICAL, DetectionMethod.RULE_BASED]
        )
    
    async def start_monitoring(self):
        """启动监控"""
        if self.running:
            logger.warning("异常检测器已在运行")
            return
        
        self.running = True
        logger.info("启动销量异常检测监控")
        
        # 启动后台检测任务
        asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("停止销量异常检测监控")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                await self._run_detection_cycle()
                await asyncio.sleep(self.detection_interval)
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再继续
    
    async def _run_detection_cycle(self):
        """运行检测周期"""
        logger.debug("开始异常检测周期")
        
        # 获取需要检测的商品
        products_to_check = await self._get_products_for_detection()
        
        # 并行检测
        detection_tasks = [
            self._detect_product_anomalies(product_id)
            for product_id in products_to_check
        ]
        
        results = await asyncio.gather(*detection_tasks, return_exceptions=True)
        
        # 处理检测结果
        total_anomalies = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"商品 {products_to_check[i]} 检测失败: {result}")
            elif isinstance(result, list):
                total_anomalies += len(result)
        
        logger.debug(f"检测周期完成，发现 {total_anomalies} 个异常")
    
    async def _get_products_for_detection(self) -> List[str]:
        """获取需要检测的商品列表"""
        # 返回已配置的商品ID列表
        return list(self.detection_configs.keys())
    
    async def _detect_product_anomalies(self, product_id: str) -> List[SalesAnomaly]:
        """检测单个商品的异常"""
        try:
            config = self.detection_configs.get(product_id, self.default_config)
            
            if not config.enabled:
                return []
            
            # 创建临时商品对象（实际应从数据库获取）
            product = Product(
                url=f"temp://{product_id}",
                title=f"Product {product_id}",
                product_type=ProductType.COMPETITOR
            )
            product.id = product_id
            
            anomalies = []
            
            # 根据配置的检测方法进行检测
            for method in config.detection_methods:
                method_anomalies = await self._detect_by_method(product, method, config)
                anomalies.extend(method_anomalies)
            
            # 去重和过滤
            unique_anomalies = self._deduplicate_anomalies(anomalies)
            filtered_anomalies = self._filter_by_sensitivity(unique_anomalies, config.sensitivity)
            
            # 发送预警
            for anomaly in filtered_anomalies:
                await self._send_alert(product_id, anomaly, config)
            
            # 更新统计
            self._update_detection_stats(product_id, filtered_anomalies)
            
            return filtered_anomalies
            
        except Exception as e:
            logger.error(f"商品 {product_id} 异常检测失败: {e}")
            return []
    
    async def _detect_by_method(self, product: Product, method: DetectionMethod,
                              config: DetectionConfig) -> List[SalesAnomaly]:
        """根据指定方法检测异常"""
        if method == DetectionMethod.STATISTICAL:
            return await self._statistical_detection(product, config)
        elif method == DetectionMethod.RULE_BASED:
            return await self._rule_based_detection(product, config)
        elif method == DetectionMethod.MACHINE_LEARNING:
            return await self._ml_detection(product, config)
        elif method == DetectionMethod.HYBRID:
            return await self._hybrid_detection(product, config)
        else:
            return []
    
    async def _statistical_detection(self, product: Product, 
                                   config: DetectionConfig) -> List[SalesAnomaly]:
        """统计方法检测"""
        # 使用高级分析器的异常检测功能
        return await self.advanced_analyzer.detect_sales_anomalies(product, days=30)
    
    async def _rule_based_detection(self, product: Product,
                                  config: DetectionConfig) -> List[SalesAnomaly]:
        """基于规则的检测"""
        anomalies = []
        
        # 获取销量数据
        if product.id not in self.advanced_analyzer.base_analyzer.sales_history:
            return anomalies
        
        sales_data = await self.advanced_analyzer.base_analyzer._get_sales_data(product.id, 7)
        if len(sales_data) < 3:
            return anomalies
        
        sales_values = [point.sales_count for point in sales_data]
        timestamps = [point.timestamp for point in sales_data]
        
        # 规则1：连续下降
        if self._check_consecutive_decline(sales_values):
            anomaly = SalesAnomaly(
                anomaly_type=SalesAnomalyType.GRADUAL_DECLINE,
                alert_level=SalesAlertLevel.MEDIUM,
                detected_at=datetime.now(),
                description="检测到连续销量下降",
                severity_score=0.6,
                affected_period=(timestamps[0], timestamps[-1]),
                baseline_value=sales_values[0],
                anomaly_value=sales_values[-1],
                deviation_percent=((sales_values[0] - sales_values[-1]) / sales_values[0] * 100 
                                 if sales_values[0] > 0 else 0),
                recommendations=["调查销量下降原因", "制定应对策略"]
            )
            anomalies.append(anomaly)
        
        # 规则2：零销量
        if self._check_zero_sales(sales_values):
            anomaly = SalesAnomaly(
                anomaly_type=SalesAnomalyType.SUDDEN_DROP,
                alert_level=SalesAlertLevel.CRITICAL,
                detected_at=datetime.now(),
                description="检测到零销量异常",
                severity_score=1.0,
                affected_period=(timestamps[-1], timestamps[-1]),
                baseline_value=statistics.mean(sales_values[:-1]) if len(sales_values) > 1 else 0,
                anomaly_value=0,
                deviation_percent=100,
                recommendations=["立即检查产品状态", "确认数据准确性", "联系相关负责人"]
            )
            anomalies.append(anomaly)
        
        return anomalies
    
    async def _ml_detection(self, product: Product,
                          config: DetectionConfig) -> List[SalesAnomaly]:
        """机器学习方法检测（简化实现）"""
        # 这里应该实现机器学习异常检测算法
        # 如孤立森林、LSTM自编码器等
        # 简化实现，返回空列表
        return []
    
    async def _hybrid_detection(self, product: Product,
                              config: DetectionConfig) -> List[SalesAnomaly]:
        """混合方法检测"""
        # 结合多种方法的结果
        statistical_anomalies = await self._statistical_detection(product, config)
        rule_based_anomalies = await self._rule_based_detection(product, config)
        
        # 合并结果
        all_anomalies = statistical_anomalies + rule_based_anomalies
        
        # 可以在这里实现更复杂的融合逻辑
        return self._deduplicate_anomalies(all_anomalies)
    
    def _check_consecutive_decline(self, sales_values: List[int], min_periods: int = 3) -> bool:
        """检查连续下降"""
        if len(sales_values) < min_periods:
            return False
        
        decline_count = 0
        for i in range(1, len(sales_values)):
            if sales_values[i] < sales_values[i-1]:
                decline_count += 1
            else:
                decline_count = 0
            
            if decline_count >= min_periods - 1:
                return True
        
        return False
    
    def _check_zero_sales(self, sales_values: List[int]) -> bool:
        """检查零销量"""
        return len(sales_values) > 0 and sales_values[-1] == 0
    
    def _deduplicate_anomalies(self, anomalies: List[SalesAnomaly]) -> List[SalesAnomaly]:
        """去重异常"""
        # 简化的去重逻辑：基于异常类型和时间窗口
        unique_anomalies = []
        seen_types = set()
        
        for anomaly in anomalies:
            key = (anomaly.anomaly_type, anomaly.detected_at.date())
            if key not in seen_types:
                unique_anomalies.append(anomaly)
                seen_types.add(key)
        
        return unique_anomalies
    
    def _filter_by_sensitivity(self, anomalies: List[SalesAnomaly], 
                             sensitivity: float) -> List[SalesAnomaly]:
        """根据敏感度过滤异常"""
        # 敏感度越高，保留的异常越多
        threshold = 1.0 - sensitivity  # 敏感度0.8对应阈值0.2
        
        filtered = []
        for anomaly in anomalies:
            if anomaly.severity_score >= threshold:
                filtered.append(anomaly)
        
        return filtered
    
    async def _send_alert(self, product_id: str, anomaly: SalesAnomaly,
                        config: DetectionConfig):
        """发送预警"""
        # 检查冷却时间
        if self._is_in_cooldown(product_id, anomaly.anomaly_type, config.alert_cooldown_minutes):
            return
        
        # 生成预警记录
        alert_record = AlertRecord(
            alert_id=f"alert_{product_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            product_id=product_id,
            anomaly=anomaly,
            triggered_at=datetime.now(),
            channels_sent=[]
        )
        
        # 发送到各个渠道
        for channel in config.alert_channels:
            try:
                await self._send_to_channel(alert_record, channel)
                alert_record.channels_sent.append(channel)
            except Exception as e:
                logger.error(f"发送预警到 {channel.value} 失败: {e}")
        
        # 执行回调函数
        for callback in config.notification_callbacks:
            try:
                await callback(alert_record)
            except Exception as e:
                logger.error(f"执行预警回调失败: {e}")
        
        # 保存预警记录
        self.alert_records.append(alert_record)
        
        logger.info(f"发送预警: {product_id} - {anomaly.description}")
    
    def _is_in_cooldown(self, product_id: str, anomaly_type: SalesAnomalyType,
                       cooldown_minutes: int) -> bool:
        """检查是否在冷却时间内"""
        cutoff_time = datetime.now() - timedelta(minutes=cooldown_minutes)
        
        for record in self.alert_records:
            if (record.product_id == product_id and 
                record.anomaly.anomaly_type == anomaly_type and
                record.triggered_at > cutoff_time):
                return True
        
        return False
    
    async def _send_to_channel(self, alert_record: AlertRecord, channel: AlertChannel):
        """发送到指定渠道"""
        if channel == AlertChannel.LOG:
            logger.warning(f"销量异常预警: {alert_record.anomaly.description}")
        elif channel == AlertChannel.EMAIL:
            # 实际实现中应该发送邮件
            logger.info(f"模拟发送邮件预警: {alert_record.alert_id}")
        elif channel == AlertChannel.SMS:
            # 实际实现中应该发送短信
            logger.info(f"模拟发送短信预警: {alert_record.alert_id}")
        elif channel == AlertChannel.WEBHOOK:
            # 实际实现中应该调用webhook
            logger.info(f"模拟调用webhook: {alert_record.alert_id}")
        elif channel == AlertChannel.DASHBOARD:
            # 实际实现中应该推送到仪表板
            logger.info(f"模拟推送到仪表板: {alert_record.alert_id}")
    
    def _update_detection_stats(self, product_id: str, anomalies: List[SalesAnomaly]):
        """更新检测统计"""
        if product_id not in self.detection_stats:
            self.detection_stats[product_id] = DetectionStats()
        
        stats = self.detection_stats[product_id]
        stats.total_detections += len(anomalies)
        stats.last_updated = datetime.now()
        
        # 简化的准确率计算（实际需要人工标注）
        # 这里假设所有检测都是正确的
        stats.true_positives += len(anomalies)
        if stats.total_detections > 0:
            stats.precision = stats.true_positives / stats.total_detections
            stats.accuracy = stats.precision  # 简化计算
    
    def configure_product_detection(self, product_id: str, config: DetectionConfig):
        """配置商品检测"""
        config.product_id = product_id
        self.detection_configs[product_id] = config
        logger.info(f"配置商品异常检测: {product_id}")
    
    def get_detection_config(self, product_id: str) -> Optional[DetectionConfig]:
        """获取检测配置"""
        return self.detection_configs.get(product_id)
    
    def get_alert_records(self, product_id: Optional[str] = None,
                         limit: int = 100) -> List[AlertRecord]:
        """获取预警记录"""
        records = self.alert_records
        
        if product_id:
            records = [r for r in records if r.product_id == product_id]
        
        # 按时间倒序排列
        records.sort(key=lambda x: x.triggered_at, reverse=True)
        
        return records[:limit]
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "system"):
        """确认预警"""
        for record in self.alert_records:
            if record.alert_id == alert_id:
                record.acknowledged = True
                record.acknowledged_at = datetime.now()
                record.acknowledged_by = acknowledged_by
                logger.info(f"预警已确认: {alert_id}")
                return True
        
        return False
    
    def resolve_alert(self, alert_id: str, resolution_notes: str = ""):
        """解决预警"""
        for record in self.alert_records:
            if record.alert_id == alert_id:
                record.resolved = True
                record.resolved_at = datetime.now()
                record.resolution_notes = resolution_notes
                logger.info(f"预警已解决: {alert_id}")
                return True
        
        return False
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        total_products = len(self.detection_configs)
        total_alerts = len(self.alert_records)
        active_alerts = len([r for r in self.alert_records if not r.resolved])
        
        # 按异常类型统计
        anomaly_type_stats = {}
        for record in self.alert_records:
            anomaly_type = record.anomaly.anomaly_type.value
            anomaly_type_stats[anomaly_type] = anomaly_type_stats.get(anomaly_type, 0) + 1
        
        # 按预警级别统计
        alert_level_stats = {}
        for record in self.alert_records:
            alert_level = record.anomaly.alert_level.value
            alert_level_stats[alert_level] = alert_level_stats.get(alert_level, 0) + 1
        
        return {
            "total_products": total_products,
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "resolved_alerts": total_alerts - active_alerts,
            "anomaly_type_distribution": anomaly_type_stats,
            "alert_level_distribution": alert_level_stats,
            "detection_stats": {pid: {
                "total_detections": stats.total_detections,
                "accuracy": stats.accuracy,
                "precision": stats.precision,
                "last_updated": stats.last_updated.isoformat()
            } for pid, stats in self.detection_stats.items()},
            "running": self.running,
            "detection_interval": self.detection_interval
        }
