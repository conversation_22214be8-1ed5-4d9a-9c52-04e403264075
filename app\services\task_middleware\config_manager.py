"""
平台配置管理器

管理多平台爬取配置，支持配置模板和继承机制
区分竞品、供货商商品、其他商品的爬取策略
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import yaml
import json
from pathlib import Path

from app.core.logging import get_logger

logger = get_logger(__name__)


class ProductType(str, Enum):
    """商品类型枚举"""
    COMPETITOR = "competitor"  # 竞品
    SUPPLIER = "supplier"      # 供货商商品
    OTHER = "other"           # 其他商品


class Platform(str, Enum):
    """支持的平台枚举"""
    ALIBABA_1688 = "1688"
    TAOBAO = "taobao"
    TMALL = "tmall"
    JD = "jd"
    PINDUODUO = "pdd"
    AMAZON = "amazon"
    EBAY = "ebay"


@dataclass
class SelectorConfig:
    """选择器配置"""
    title: str
    price: str
    stock: Optional[str] = None
    sales_count: Optional[str] = None
    rating: Optional[str] = None
    images: Optional[str] = None
    description: Optional[str] = None
    seller_info: Optional[str] = None
    min_order: Optional[str] = None  # 最小起订量（供货商商品特有）
    
    
@dataclass
class RateLimitConfig:
    """速率限制配置"""
    requests_per_minute: int = 30
    requests_per_hour: Optional[int] = None
    concurrent_requests: int = 3
    delay_between_requests: float = 2.0


@dataclass
class PlatformBaseConfig:
    """平台基础配置"""
    name: str
    base_url: str
    selectors: SelectorConfig
    rate_limit: RateLimitConfig
    headers: Optional[Dict[str, str]] = None
    cookies: Optional[Dict[str, str]] = None
    proxy_required: bool = False
    javascript_required: bool = True
    
    
@dataclass
class ProductTypeConfig:
    """商品类型特定配置"""
    additional_selectors: Optional[Dict[str, str]] = None
    custom_query_template: Optional[str] = None
    priority_boost: int = 0  # 优先级提升
    monitoring_frequency: int = 3600  # 监控频率（秒）
    

@dataclass
class PlatformConfig:
    """完整平台配置"""
    platform: Platform
    base_config: PlatformBaseConfig
    product_types: Dict[ProductType, ProductTypeConfig]
    enabled: bool = True
    

class PlatformConfigManager:
    """平台配置管理器"""
    
    def __init__(self, config_dir: str = "config/platforms"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self._configs: Dict[Platform, PlatformConfig] = {}
        self._load_default_configs()
        
    def _load_default_configs(self):
        """加载默认配置"""
        # 1688平台配置
        alibaba_1688_config = PlatformConfig(
            platform=Platform.ALIBABA_1688,
            base_config=PlatformBaseConfig(
                name="1688.com",
                base_url="https://www.1688.com",
                selectors=SelectorConfig(
                    title=".d-title",
                    price=".price-now",
                    stock=".amount-text",
                    sales_count=".sales-info",
                    rating=".rating-score",
                    images=".main-image img",
                    description=".description-content",
                    seller_info=".seller-name"
                ),
                rate_limit=RateLimitConfig(
                    requests_per_minute=30,
                    concurrent_requests=3,
                    delay_between_requests=2.0
                ),
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                },
                javascript_required=True
            ),
            product_types={
                ProductType.COMPETITOR: ProductTypeConfig(
                    additional_selectors={
                        "brand": ".brand-name",
                        "model": ".model-info"
                    },
                    custom_query_template="提取竞品信息：标题、价格、销量、品牌、型号",
                    priority_boost=2,
                    monitoring_frequency=1800  # 30分钟
                ),
                ProductType.SUPPLIER: ProductTypeConfig(
                    additional_selectors={
                        "min_order": ".min-order",
                        "wholesale_price": ".wholesale-price",
                        "supplier_rating": ".supplier-rating"
                    },
                    custom_query_template="提取供货商商品信息：标题、价格、最小起订量、批发价格、供应商评级",
                    priority_boost=1,
                    monitoring_frequency=3600  # 1小时
                ),
                ProductType.OTHER: ProductTypeConfig(
                    custom_query_template="提取基础商品信息：标题、价格、库存、销量",
                    monitoring_frequency=7200  # 2小时
                )
            }
        )
        
        # 淘宝平台配置
        taobao_config = PlatformConfig(
            platform=Platform.TAOBAO,
            base_config=PlatformBaseConfig(
                name="淘宝网",
                base_url="https://www.taobao.com",
                selectors=SelectorConfig(
                    title=".tb-main-title",
                    price=".tb-price",
                    stock=".tb-amount",
                    sales_count=".tb-sell-counter",
                    rating=".tb-rate",
                    images=".tb-pic img"
                ),
                rate_limit=RateLimitConfig(
                    requests_per_minute=20,
                    concurrent_requests=2,
                    delay_between_requests=3.0
                ),
                javascript_required=True,
                proxy_required=True
            ),
            product_types={
                ProductType.COMPETITOR: ProductTypeConfig(
                    priority_boost=2,
                    monitoring_frequency=1800
                ),
                ProductType.SUPPLIER: ProductTypeConfig(
                    priority_boost=1,
                    monitoring_frequency=3600
                ),
                ProductType.OTHER: ProductTypeConfig(
                    monitoring_frequency=7200
                )
            }
        )
        
        self._configs[Platform.ALIBABA_1688] = alibaba_1688_config
        self._configs[Platform.TAOBAO] = taobao_config
        
        logger.info(f"加载了 {len(self._configs)} 个默认平台配置")
        
    def get_platform_config(self, platform: Platform) -> Optional[PlatformConfig]:
        """
        获取平台配置
        
        Args:
            platform: 平台类型
            
        Returns:
            Optional[PlatformConfig]: 平台配置
        """
        return self._configs.get(platform)
        
    def get_product_type_config(self, platform: Platform, product_type: ProductType) -> Optional[ProductTypeConfig]:
        """
        获取特定商品类型的配置
        
        Args:
            platform: 平台类型
            product_type: 商品类型
            
        Returns:
            Optional[ProductTypeConfig]: 商品类型配置
        """
        platform_config = self.get_platform_config(platform)
        if platform_config:
            return platform_config.product_types.get(product_type)
        return None
        
    def build_crawl_query(self, platform: Platform, product_type: ProductType, 
                         custom_fields: Optional[List[str]] = None) -> str:
        """
        构建爬取查询指令
        
        Args:
            platform: 平台类型
            product_type: 商品类型
            custom_fields: 自定义字段列表
            
        Returns:
            str: LLM查询指令
        """
        product_config = self.get_product_type_config(platform, product_type)
        
        if product_config and product_config.custom_query_template:
            query = product_config.custom_query_template
        else:
            # 默认查询模板
            query = "提取商品信息：标题、价格、库存、销量、评分"
            
        # 添加自定义字段
        if custom_fields:
            query += f"，以及：{', '.join(custom_fields)}"
            
        return query
        
    def get_monitoring_frequency(self, platform: Platform, product_type: ProductType) -> int:
        """
        获取监控频率
        
        Args:
            platform: 平台类型
            product_type: 商品类型
            
        Returns:
            int: 监控频率（秒）
        """
        product_config = self.get_product_type_config(platform, product_type)
        if product_config:
            return product_config.monitoring_frequency
        return 3600  # 默认1小时
        
    def get_priority_boost(self, platform: Platform, product_type: ProductType) -> int:
        """
        获取优先级提升值
        
        Args:
            platform: 平台类型
            product_type: 商品类型
            
        Returns:
            int: 优先级提升值
        """
        product_config = self.get_product_type_config(platform, product_type)
        if product_config:
            return product_config.priority_boost
        return 0
        
    def save_config(self, platform: Platform, config: PlatformConfig):
        """
        保存平台配置
        
        Args:
            platform: 平台类型
            config: 平台配置
        """
        self._configs[platform] = config
        
        # 保存到文件
        config_file = self.config_dir / f"{platform.value}.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(asdict(config), f, default_flow_style=False, allow_unicode=True)
            
        logger.info(f"保存平台配置: {platform.value}")
        
    def load_config_from_file(self, platform: Platform) -> Optional[PlatformConfig]:
        """
        从文件加载平台配置

        Args:
            platform: 平台类型

        Returns:
            Optional[PlatformConfig]: 平台配置
        """
        config_file = self.config_dir / f"{platform.value}.yaml"

        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    config = self._dict_to_platform_config(data)
                    if config:
                        self._configs[platform] = config
                        logger.info(f"从文件加载平台配置: {platform.value}")
                        return config
            except Exception as e:
                logger.error(f"加载平台配置文件失败: {e}")

        return None

    def _dict_to_platform_config(self, data: Dict[str, Any]) -> Optional[PlatformConfig]:
        """
        将字典转换为PlatformConfig对象

        Args:
            data: 配置字典

        Returns:
            Optional[PlatformConfig]: 平台配置对象
        """
        try:
            # 转换基础配置
            base_config_data = data['base_config']
            selectors = SelectorConfig(**base_config_data['selectors'])
            rate_limit = RateLimitConfig(**base_config_data['rate_limit'])

            base_config = PlatformBaseConfig(
                name=base_config_data['name'],
                base_url=base_config_data['base_url'],
                selectors=selectors,
                rate_limit=rate_limit,
                headers=base_config_data.get('headers'),
                cookies=base_config_data.get('cookies'),
                proxy_required=base_config_data.get('proxy_required', False),
                javascript_required=base_config_data.get('javascript_required', True)
            )

            # 转换商品类型配置
            product_types = {}
            for ptype_str, ptype_data in data['product_types'].items():
                product_type = ProductType(ptype_str)
                product_config = ProductTypeConfig(
                    additional_selectors=ptype_data.get('additional_selectors'),
                    custom_query_template=ptype_data.get('custom_query_template'),
                    priority_boost=ptype_data.get('priority_boost', 0),
                    monitoring_frequency=ptype_data.get('monitoring_frequency', 3600)
                )
                product_types[product_type] = product_config

            return PlatformConfig(
                platform=Platform(data['platform']),
                base_config=base_config,
                product_types=product_types,
                enabled=data.get('enabled', True)
            )

        except Exception as e:
            logger.error(f"配置字典转换失败: {e}")
            return None
        
    def list_platforms(self) -> List[Platform]:
        """
        列出所有可用平台
        
        Returns:
            List[Platform]: 平台列表
        """
        return list(self._configs.keys())
        
    def is_platform_enabled(self, platform: Platform) -> bool:
        """
        检查平台是否启用
        
        Args:
            platform: 平台类型
            
        Returns:
            bool: 是否启用
        """
        config = self.get_platform_config(platform)
        return config.enabled if config else False
