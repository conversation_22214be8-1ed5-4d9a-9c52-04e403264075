/**
 * 快捷键帮助组件
 */

import React, { useState } from 'react';
import { Modal, Table, Button, Tag, FloatButton } from 'antd';
import { QuestionCircleOutlined, KeyOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface ShortcutItem {
  keys: string;
  description: string;
  category?: string;
}

interface ShortcutHelpProps {
  shortcuts?: ShortcutItem[];
}

const ShortcutHelp: React.FC<ShortcutHelpProps> = ({ shortcuts = [] }) => {
  const [visible, setVisible] = useState(false);

  // 默认快捷键
  const defaultShortcuts: ShortcutItem[] = [
    { keys: 'Ctrl + F', description: '搜索', category: '通用' },
    { keys: 'Ctrl + R', description: '刷新页面', category: '通用' },
    { keys: 'Ctrl + N', description: '新建', category: '通用' },
    { keys: 'Ctrl + S', description: '保存', category: '通用' },
    { keys: 'Delete', description: '删除选中项', category: '通用' },
    { keys: 'Ctrl + A', description: '全选', category: '通用' },
    { keys: 'ESC', description: '取消/关闭', category: '通用' },
    { keys: 'Enter', description: '确认', category: '通用' },
    { keys: 'F1', description: '显示帮助', category: '通用' },
    { keys: '↑ ↓', description: '上下导航', category: '导航' },
    { keys: 'Tab', description: '切换焦点', category: '导航' },
    { keys: 'Shift + Tab', description: '反向切换焦点', category: '导航' },
    { keys: 'Ctrl + 1-9', description: '快速切换标签页', category: '导航' },
  ];

  const allShortcuts = [...defaultShortcuts, ...shortcuts];

  const columns: ColumnsType<ShortcutItem> = [
    {
      title: '快捷键',
      dataIndex: 'keys',
      key: 'keys',
      width: 150,
      render: (keys: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {keys}
        </Tag>
      ),
    },
    {
      title: '功能描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag color="default">{category || '其他'}</Tag>
      ),
    },
  ];

  // 按分类分组
  const groupedShortcuts = allShortcuts.reduce((groups, shortcut) => {
    const category = shortcut.category || '其他';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(shortcut);
    return groups;
  }, {} as Record<string, ShortcutItem[]>);

  return (
    <>
      <FloatButton
        icon={<KeyOutlined />}
        tooltip="快捷键帮助"
        onClick={() => setVisible(true)}
        style={{ right: 24, bottom: 80 }}
      />

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <KeyOutlined />
            快捷键帮助
          </div>
        }
        open={visible}
        onCancel={() => setVisible(false)}
        footer={
          <Button onClick={() => setVisible(false)}>
            关闭
          </Button>
        }
        width={700}
      >
        <div style={{ marginBottom: 16 }}>
          <p style={{ color: '#666', margin: 0 }}>
            以下是系统支持的快捷键，可以帮助您更高效地使用系统。
          </p>
        </div>

        {Object.keys(groupedShortcuts).map(category => (
          <div key={category} style={{ marginBottom: 24 }}>
            <h4 style={{ 
              borderBottom: '1px solid #f0f0f0', 
              paddingBottom: 8,
              marginBottom: 12,
              color: '#1890ff'
            }}>
              {category}
            </h4>
            <Table
              columns={columns.filter(col => col.key !== 'category')}
              dataSource={groupedShortcuts[category]}
              pagination={false}
              size="small"
              rowKey={(record, index) => `${category}-${index}`}
              showHeader={false}
            />
          </div>
        ))}

        <div style={{ 
          marginTop: 24, 
          padding: 16, 
          background: '#f6f8fa', 
          borderRadius: 6,
          fontSize: 12,
          color: '#666'
        }}>
          <p style={{ margin: 0, marginBottom: 8 }}>
            <strong>提示：</strong>
          </p>
          <ul style={{ margin: 0, paddingLeft: 16 }}>
            <li>在输入框中使用快捷键时，某些快捷键可能不生效</li>
            <li>Mac 用户请将 Ctrl 替换为 Cmd</li>
            <li>部分快捷键可能因浏览器设置而被覆盖</li>
          </ul>
        </div>
      </Modal>
    </>
  );
};

export default ShortcutHelp;
