/**
 * 分类分析图表组件
 */

import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin, Empty } from 'antd';
import { analyticsApi } from '../../services/analyticsApi';

interface CategoryAnalysisChartProps {
  startDate?: string;
  endDate?: string;
  height?: number;
}

interface CategoryData {
  category: string;
  avg_price: number;
  product_count: number;
  price_trend: number;
}

const CategoryAnalysisChart: React.FC<CategoryAnalysisChartProps> = ({
  startDate,
  endDate,
  height = 400,
}) => {
  const [data, setData] = useState<CategoryData[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, [startDate, endDate]);

  const loadData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getCategoryAnalysis({
        start_date: startDate,
        end_date: endDate,
      });
      setData(response.data);
    } catch (error) {
      console.error('Failed to load category analysis data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const getOption = () => {
    if (!data || data.length === 0) {
      return {};
    }

    const categories = data.map(item => item.category);
    const avgPrices = data.map(item => item.avg_price);
    const productCounts = data.map(item => item.product_count);
    const priceTrends = data.map(item => item.price_trend);

    return {
      title: {
        text: '商品分类分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: (params: any) => {
          let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
          params.forEach((param: any, index: number) => {
            let value = param.value;
            if (param.seriesName === '平均价格') {
              value = `¥${value.toFixed(2)}`;
            } else if (param.seriesName === '价格趋势') {
              value = `${value > 0 ? '+' : ''}${(value * 100).toFixed(1)}%`;
            }
            result += `
              <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; ${param.seriesType === 'line' ? 'border-radius: 50%;' : 'border-radius: 2px;'} margin-right: 8px;"></span>
                <span style="margin-right: 8px;">${param.seriesName}:</span>
                <span style="font-weight: bold;">${value}</span>
              </div>
            `;
          });
          return result;
        },
      },
      legend: {
        data: ['商品数量', '平均价格', '价格趋势'],
        bottom: 10,
      },
      xAxis: [
        {
          type: 'category',
          data: categories,
          axisPointer: {
            type: 'shadow',
          },
          axisLabel: {
            color: '#666',
            interval: 0,
            rotate: categories.length > 6 ? 45 : 0,
          },
          axisLine: {
            lineStyle: {
              color: '#d9d9d9',
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '数量',
          position: 'left',
          nameTextStyle: {
            color: '#666',
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#1890ff',
            },
          },
          axisLabel: {
            color: '#666',
            formatter: '{value}',
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
        {
          type: 'value',
          name: '价格 (¥)',
          position: 'right',
          nameTextStyle: {
            color: '#666',
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#52c41a',
            },
          },
          axisLabel: {
            color: '#666',
            formatter: '¥{value}',
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: '商品数量',
          type: 'bar',
          yAxisIndex: 0,
          data: productCounts,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [2, 2, 0, 0],
          },
          barWidth: '30%',
        },
        {
          name: '平均价格',
          type: 'line',
          yAxisIndex: 1,
          data: avgPrices,
          lineStyle: {
            color: '#52c41a',
            width: 2,
          },
          itemStyle: {
            color: '#52c41a',
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true,
        },
        {
          name: '价格趋势',
          type: 'line',
          yAxisIndex: 1,
          data: priceTrends.map(trend => (trend * 100).toFixed(1)),
          lineStyle: {
            color: '#faad14',
            width: 2,
            type: 'dashed',
          },
          itemStyle: {
            color: '#faad14',
          },
          symbol: 'diamond',
          symbolSize: 6,
          smooth: true,
        },
      ],
    };
  };

  if (loading) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="暂无数据" />
      </div>
    );
  }

  return (
    <ReactECharts
      option={getOption()}
      style={{ height }}
      opts={{ renderer: 'canvas' }}
    />
  );
};

export default CategoryAnalysisChart;
