"""
JWT令牌处理器

实现JWT令牌的生成、验证、刷新等功能
"""

from jose import jwt, JWTError
import secrets
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, Tuple
import json

from app.core.logging import get_logger

logger = get_logger(__name__)


class TokenType(Enum):
    """令牌类型"""
    ACCESS = "access"
    REFRESH = "refresh"
    RESET = "reset"
    VERIFY = "verify"


class JWTHandler:
    """JWT令牌处理器"""
    
    def __init__(self, secret_key: str = None):
        # JWT配置
        self.secret_key = secret_key or self._generate_secret_key()
        self.algorithm = "HS256"
        
        # 令牌过期时间配置
        self.token_expiry = {
            TokenType.ACCESS: timedelta(hours=1),      # 访问令牌1小时
            TokenType.REFRESH: timedelta(days=7),      # 刷新令牌7天
            TokenType.RESET: timedelta(hours=1),       # 重置令牌1小时
            TokenType.VERIFY: timedelta(hours=24)      # 验证令牌24小时
        }
        
        # 令牌黑名单（用于撤销令牌）
        self.token_blacklist = set()
        
        # 令牌统计
        self.token_stats = {
            "generated": 0,
            "verified": 0,
            "expired": 0,
            "invalid": 0,
            "blacklisted": 0
        }
    
    def _generate_secret_key(self) -> str:
        """生成随机密钥"""
        return secrets.token_urlsafe(64)
    
    def generate_token(self, payload: Dict[str, Any], token_type: TokenType = TokenType.ACCESS,
                      expires_delta: Optional[timedelta] = None) -> str:
        """
        生成JWT令牌
        
        Args:
            payload: 令牌载荷
            token_type: 令牌类型
            expires_delta: 自定义过期时间
        
        Returns:
            str: JWT令牌
        """
        try:
            # 设置过期时间
            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                expire = datetime.utcnow() + self.token_expiry[token_type]
            
            # 构建令牌载荷
            token_payload = {
                **payload,
                "exp": expire,
                "iat": datetime.utcnow(),
                "type": token_type.value,
                "jti": secrets.token_urlsafe(16)  # JWT ID，用于撤销
            }
            
            # 生成令牌
            token = jwt.encode(
                token_payload,
                self.secret_key,
                algorithm=self.algorithm
            )
            
            self.token_stats["generated"] += 1
            logger.debug(f"生成{token_type.value}令牌成功")
            
            return token
            
        except Exception as e:
            logger.error(f"生成JWT令牌失败: {e}")
            raise
    
    def verify_token(self, token: str, token_type: TokenType = None) -> Optional[Dict[str, Any]]:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌
            token_type: 期望的令牌类型
        
        Returns:
            Optional[Dict[str, Any]]: 令牌载荷，验证失败返回None
        """
        try:
            # 检查令牌是否在黑名单中
            if token in self.token_blacklist:
                self.token_stats["blacklisted"] += 1
                logger.warning("令牌已被撤销")
                return None
            
            # 解码令牌
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            # 检查令牌类型
            if token_type and payload.get("type") != token_type.value:
                self.token_stats["invalid"] += 1
                logger.warning(f"令牌类型不匹配: 期望{token_type.value}, 实际{payload.get('type')}")
                return None
            
            self.token_stats["verified"] += 1
            logger.debug("令牌验证成功")
            
            return payload
            
        except JWTError as e:
            if "expired" in str(e).lower():
                self.token_stats["expired"] += 1
                logger.warning("令牌已过期")
            else:
                self.token_stats["invalid"] += 1
                logger.warning(f"无效令牌: {e}")
            return None
        except Exception as e:
            logger.error(f"验证JWT令牌失败: {e}")
            return None
    
    def refresh_token(self, refresh_token: str) -> Optional[Tuple[str, str]]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
        
        Returns:
            Optional[Tuple[str, str]]: (新访问令牌, 新刷新令牌)，失败返回None
        """
        try:
            # 验证刷新令牌
            payload = self.verify_token(refresh_token, TokenType.REFRESH)
            if not payload:
                return None
            
            # 提取用户信息
            user_payload = {
                "user_id": payload.get("user_id"),
                "username": payload.get("username"),
                "role": payload.get("role"),
                "permissions": payload.get("permissions", [])
            }
            
            # 生成新的访问令牌和刷新令牌
            new_access_token = self.generate_token(user_payload, TokenType.ACCESS)
            new_refresh_token = self.generate_token(user_payload, TokenType.REFRESH)
            
            # 将旧的刷新令牌加入黑名单
            self.revoke_token(refresh_token)
            
            logger.info(f"刷新令牌成功: 用户{payload.get('username')}")
            
            return new_access_token, new_refresh_token
            
        except Exception as e:
            logger.error(f"刷新令牌失败: {e}")
            return None
    
    def revoke_token(self, token: str):
        """
        撤销令牌
        
        Args:
            token: 要撤销的令牌
        """
        try:
            self.token_blacklist.add(token)
            logger.info("令牌已撤销")
            
        except Exception as e:
            logger.error(f"撤销令牌失败: {e}")
    
    def revoke_all_user_tokens(self, user_id: str):
        """
        撤销用户的所有令牌（通过更新用户密钥实现）
        
        Args:
            user_id: 用户ID
        """
        try:
            # 在实际实现中，可以通过更新用户的密钥版本来撤销所有令牌
            # 这里简化处理，记录日志
            logger.info(f"撤销用户{user_id}的所有令牌")
            
        except Exception as e:
            logger.error(f"撤销用户令牌失败: {e}")
    
    def decode_token_without_verification(self, token: str) -> Optional[Dict[str, Any]]:
        """
        不验证签名解码令牌（用于获取过期令牌信息）
        
        Args:
            token: JWT令牌
        
        Returns:
            Optional[Dict[str, Any]]: 令牌载荷
        """
        try:
            payload = jwt.decode(
                token,
                options={"verify_signature": False}
            )
            return payload
            
        except Exception as e:
            logger.error(f"解码令牌失败: {e}")
            return None
    
    def get_token_info(self, token: str) -> Dict[str, Any]:
        """
        获取令牌信息
        
        Args:
            token: JWT令牌
        
        Returns:
            Dict[str, Any]: 令牌信息
        """
        try:
            payload = self.decode_token_without_verification(token)
            if not payload:
                return {"valid": False, "error": "无法解码令牌"}
            
            now = datetime.utcnow()
            exp = datetime.fromtimestamp(payload.get("exp", 0))
            iat = datetime.fromtimestamp(payload.get("iat", 0))
            
            is_expired = now > exp
            is_blacklisted = token in self.token_blacklist
            
            return {
                "valid": not is_expired and not is_blacklisted,
                "expired": is_expired,
                "blacklisted": is_blacklisted,
                "type": payload.get("type"),
                "user_id": payload.get("user_id"),
                "username": payload.get("username"),
                "role": payload.get("role"),
                "issued_at": iat.isoformat(),
                "expires_at": exp.isoformat(),
                "remaining_seconds": max(0, (exp - now).total_seconds()),
                "jti": payload.get("jti")
            }
            
        except Exception as e:
            logger.error(f"获取令牌信息失败: {e}")
            return {"valid": False, "error": str(e)}
    
    def cleanup_blacklist(self):
        """清理过期的黑名单令牌"""
        try:
            # 在实际实现中，应该定期清理过期的黑名单令牌
            # 这里简化处理
            logger.info("清理令牌黑名单")
            
        except Exception as e:
            logger.error(f"清理黑名单失败: {e}")
    
    def get_token_statistics(self) -> Dict[str, Any]:
        """获取令牌统计信息"""
        return {
            "stats": self.token_stats.copy(),
            "blacklist_size": len(self.token_blacklist),
            "config": {
                "algorithm": self.algorithm,
                "access_token_expiry_hours": self.token_expiry[TokenType.ACCESS].total_seconds() / 3600,
                "refresh_token_expiry_days": self.token_expiry[TokenType.REFRESH].days
            }
        }
    
    def update_token_expiry(self, token_type: TokenType, expiry: timedelta):
        """
        更新令牌过期时间
        
        Args:
            token_type: 令牌类型
            expiry: 过期时间
        """
        try:
            self.token_expiry[token_type] = expiry
            logger.info(f"更新{token_type.value}令牌过期时间: {expiry}")
            
        except Exception as e:
            logger.error(f"更新令牌过期时间失败: {e}")
    
    def generate_token_pair(self, user_payload: Dict[str, Any]) -> Tuple[str, str]:
        """
        生成访问令牌和刷新令牌对
        
        Args:
            user_payload: 用户载荷
        
        Returns:
            Tuple[str, str]: (访问令牌, 刷新令牌)
        """
        try:
            access_token = self.generate_token(user_payload, TokenType.ACCESS)
            refresh_token = self.generate_token(user_payload, TokenType.REFRESH)
            
            return access_token, refresh_token

        except Exception as e:
            logger.error(f"生成令牌对失败: {e}")
            raise
