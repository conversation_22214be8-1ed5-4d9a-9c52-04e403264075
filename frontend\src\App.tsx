/**
 * 主应用组件
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { store } from './store';
import { useAppDispatch, useAppSelector } from './store';
import { getCurrentUserAsync, selectIsAuthenticated, selectAuthLoading, clearAuth } from './store/slices/authSlice';
import { selectTheme } from './store/slices/uiSlice';

import Layout from './components/Layout';
import LoginPage from './pages/auth/LoginPage';
import LoadingPage from './components/LoadingPage';
import NotFoundPage from './pages/NotFoundPage';

// 页面组件
import DashboardPage from './pages/DashboardPage';
import ProductListPage from './pages/products/ProductListPage';
import ProductDetailPage from './pages/products/ProductDetailPage';
import ProductEditPage from './pages/products/ProductEditPage';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';
import FeedbackButton from './components/Feedback/FeedbackButton';
import ShortcutHelp from './components/ShortcutHelp/ShortcutHelp';
import MonitorListPage from './pages/monitor/MonitorListPage';
import MonitorDetailPage from './pages/monitor/MonitorDetailPage';
import AnalyticsPage from './pages/analytics/AnalyticsPage';
import SupplierListPage from './pages/suppliers/SupplierListPage';
import SystemSettingsPage from './pages/system/SystemSettingsPage';
import UserManagementPage from './pages/system/UserManagementPage';
import ProfilePage from './pages/auth/ProfilePage';

// 设置dayjs中文
dayjs.locale('zh-cn');

// 主题配置
const getThemeConfig = (theme: 'light' | 'dark') => ({
  algorithm: theme === 'dark' ? undefined : undefined, // 暂时使用默认主题
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 6,
  },
});

// 应用内容组件
const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authLoading = useAppSelector(selectAuthLoading);
  const theme = useAppSelector(selectTheme);

  useEffect(() => {
    // 应用启动时检查用户认证状态
    const token = localStorage.getItem('access_token');
    if (token && !isAuthenticated) {
      // 简单检查token格式是否正确
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        // 如果token已过期，直接清除
        if (payload.exp && payload.exp < currentTime) {
          console.log('Token已过期，清除认证状态');
          dispatch(clearAuth());
          return;
        }

        // Token看起来有效，尝试验证
        dispatch(getCurrentUserAsync());
      } catch (error) {
        // Token格式无效，清除认证状态
        console.log('Token格式无效，清除认证状态');
        dispatch(clearAuth());
      }
    }
  }, [dispatch, isAuthenticated]);

  // 显示加载页面
  if (authLoading) {
    return <LoadingPage />;
  }

  return (
    <ConfigProvider 
      locale={zhCN} 
      theme={getThemeConfig(theme)}
    >
      <AntdApp>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route 
              path="/login" 
              element={
                isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
              } 
            />

            {/* 受保护的路由 */}
            <Route
              path="/*"
              element={
                isAuthenticated ? (
                  <Layout>
                    <Routes>
                      {/* 仪表板 */}
                      <Route path="/" element={<DashboardPage />} />
                      <Route path="/dashboard" element={<DashboardPage />} />

                      {/* 商品管理 */}
                      <Route path="/products" element={<ProductListPage />} />
                      <Route path="/products/new" element={<ProductEditPage />} />
                      <Route path="/products/:id/edit" element={<ProductEditPage />} />
                      <Route path="/products/:id" element={<ProductDetailPage />} />

                      {/* 监控管理 */}
                      <Route path="/monitor" element={<MonitorListPage />} />
                      <Route path="/monitor/:id" element={<MonitorDetailPage />} />

                      {/* 数据分析 */}
                      <Route path="/analytics" element={<AnalyticsPage />} />

                      {/* 供货商管理 */}
                      <Route path="/suppliers" element={<SupplierListPage />} />

                      {/* 系统管理 */}
                      <Route path="/system/settings" element={<SystemSettingsPage />} />
                      <Route path="/system/users" element={<UserManagementPage />} />

                      {/* 用户中心 */}
                      <Route path="/profile" element={<ProfilePage />} />

                      {/* 404页面 */}
                      <Route path="/404" element={<NotFoundPage />} />
                      <Route path="*" element={<Navigate to="/404" replace />} />
                    </Routes>
                  </Layout>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
          </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ErrorBoundary>
        <AppContent />
        <FeedbackButton />
        <ShortcutHelp />
      </ErrorBoundary>
    </Provider>
  );
};

export default App;
