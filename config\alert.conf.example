# Moniit 系统告警配置文件示例
# 复制此文件为 alert.conf 并根据实际情况修改配置

# 邮件配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_TLS="true"
EMAIL_FROM="<EMAIL>"
EMAIL_TO="<EMAIL>,<EMAIL>"

# Webhook配置
WEBHOOK_URL="https://your-webhook-endpoint.com/alerts"
WEBHOOK_TOKEN="your-webhook-token"

# Slack配置
SLACK_WEBHOOK="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# 钉钉配置
DINGTALK_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"

# 企业微信配置
WECHAT_WEBHOOK="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"

# 短信配置（可选）
SMS_API_URL=""
SMS_API_KEY=""
SMS_PHONE_NUMBERS=""

# 告警级别配置
ALERT_LEVELS="info,warning,error,critical"

# 告警频率限制（分钟）
ALERT_RATE_LIMIT="5"

# 告警模板配置
ALERT_TEMPLATE_SUBJECT="[{{level}}] Moniit系统告警: {{subject}}"
ALERT_TEMPLATE_BODY="时间: {{timestamp}}\n级别: {{level}}\n主题: {{subject}}\n\n详细信息:\n{{message}}\n\n---\n此告警由Moniit监控系统自动发送"
