#!/usr/bin/env python3
"""
API功能验证脚本

验证我们实现的所有API功能是否正常工作
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_products_api():
    """测试商品管理API"""
    print("🔍 测试商品管理API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        try:
            # 测试获取商品列表
            response = await client.get("/api/v1/products/")
            print(f"  ✅ 获取商品列表: {response.status_code}")
            
            # 测试创建商品
            product_data = {
                "url": "https://example.com/test-product",
                "platform": "test_platform",
                "title": "测试商品",
                "category": "electronics",
                "status": "active",
                "monitoring_frequency": 24,
                "is_active": True
            }
            
            response = await client.post("/api/v1/products/", json=product_data)
            if response.status_code == 200:
                product_id = response.json()["id"]
                print(f"  ✅ 创建商品: {response.status_code} (ID: {product_id[:8]}...)")
                
                # 测试获取商品详情
                response = await client.get(f"/api/v1/products/{product_id}")
                print(f"  ✅ 获取商品详情: {response.status_code}")
                
                # 测试更新商品
                update_data = {"title": "更新后的测试商品"}
                response = await client.put(f"/api/v1/products/{product_id}", json=update_data)
                print(f"  ✅ 更新商品: {response.status_code}")
                
                # 测试获取历史数据
                response = await client.get(f"/api/v1/products/{product_id}/history")
                print(f"  ✅ 获取历史数据: {response.status_code}")
                
                # 测试删除商品
                response = await client.delete(f"/api/v1/products/{product_id}")
                print(f"  ✅ 删除商品: {response.status_code}")
            else:
                print(f"  ❌ 创建商品失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 商品API测试失败: {str(e)}")


async def test_monitor_api():
    """测试监控管理API"""
    print("🔍 测试监控管理API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        try:
            # 测试获取监控任务列表
            response = await client.get("/api/v1/monitor/tasks")
            print(f"  ✅ 获取监控任务列表: {response.status_code}")
            
            # 先创建一个商品用于监控任务
            product_data = {
                "url": "https://example.com/monitor-test-product",
                "platform": "test_platform",
                "title": "监控测试商品",
                "category": "electronics",
                "status": "active",
                "monitoring_frequency": 24,
                "is_active": True
            }
            
            product_response = await client.post("/api/v1/products/", json=product_data)
            if product_response.status_code == 200:
                product_id = product_response.json()["id"]
                
                # 测试创建监控任务
                task_data = {
                    "name": "测试监控任务",
                    "description": "API验证测试任务",
                    "product_ids": [product_id],
                    "schedule_type": "interval",
                    "schedule_config": {"interval_hours": 2},
                    "priority": "normal",
                    "is_active": True
                }
                
                response = await client.post("/api/v1/monitor/tasks", json=task_data)
                if response.status_code == 200:
                    task_id = response.json()["id"]
                    print(f"  ✅ 创建监控任务: {response.status_code} (ID: {task_id[:8]}...)")
                    
                    # 测试获取任务详情
                    response = await client.get(f"/api/v1/monitor/tasks/{task_id}")
                    print(f"  ✅ 获取任务详情: {response.status_code}")
                    
                    # 测试启动任务
                    response = await client.post(f"/api/v1/monitor/tasks/{task_id}/start")
                    print(f"  ✅ 启动任务: {response.status_code}")
                    
                    # 测试获取任务状态
                    response = await client.get(f"/api/v1/monitor/tasks/{task_id}/status")
                    print(f"  ✅ 获取任务状态: {response.status_code}")
                    
                    # 测试获取任务日志
                    response = await client.get(f"/api/v1/monitor/tasks/{task_id}/logs")
                    print(f"  ✅ 获取任务日志: {response.status_code}")
                    
                    # 测试停止任务
                    response = await client.post(f"/api/v1/monitor/tasks/{task_id}/stop")
                    print(f"  ✅ 停止任务: {response.status_code}")
                    
                    # 测试删除任务
                    response = await client.delete(f"/api/v1/monitor/tasks/{task_id}")
                    print(f"  ✅ 删除任务: {response.status_code}")
                else:
                    print(f"  ❌ 创建监控任务失败: {response.status_code}")
                
                # 清理测试商品
                await client.delete(f"/api/v1/products/{product_id}")
            else:
                print(f"  ❌ 创建测试商品失败: {product_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 监控API测试失败: {str(e)}")


async def test_analytics_api():
    """测试数据分析API"""
    print("🔍 测试数据分析API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        try:
            # 测试获取统计数据
            response = await client.get("/api/v1/analytics/statistics")
            print(f"  ✅ 获取统计数据: {response.status_code}")
            
            # 测试数据搜索
            response = await client.get("/api/v1/analytics/search")
            print(f"  ✅ 数据搜索: {response.status_code}")
            
            # 测试生成报表
            report_data = {
                "platforms": ["test_platform"],
                "categories": ["electronics"]
            }
            response = await client.post("/api/v1/analytics/reports/generate", json=report_data)
            print(f"  ✅ 生成报表: {response.status_code}")
            
            # 创建测试商品和历史数据来测试价格趋势
            product_data = {
                "url": "https://example.com/analytics-test-product",
                "platform": "test_platform",
                "title": "分析测试商品",
                "category": "electronics",
                "status": "active",
                "monitoring_frequency": 24,
                "is_active": True
            }
            
            product_response = await client.post("/api/v1/products/", json=product_data)
            if product_response.status_code == 200:
                product_id = product_response.json()["id"]
                
                # 测试价格趋势分析
                response = await client.get(f"/api/v1/analytics/price-trends/{product_id}")
                print(f"  ✅ 价格趋势分析: {response.status_code}")
                
                # 清理测试商品
                await client.delete(f"/api/v1/products/{product_id}")
            else:
                print(f"  ❌ 创建分析测试商品失败: {product_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 分析API测试失败: {str(e)}")


async def test_system_api():
    """测试系统管理API"""
    print("🔍 测试系统管理API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        try:
            # 测试获取系统信息
            response = await client.get("/api/v1/system/info")
            print(f"  ✅ 获取系统信息: {response.status_code}")
            
            # 测试系统健康检查
            response = await client.get("/api/v1/system/health")
            print(f"  ✅ 系统健康检查: {response.status_code}")
            
            # 测试获取仪表板统计
            response = await client.get("/api/v1/system/dashboard/stats")
            print(f"  ✅ 仪表板统计: {response.status_code}")
            
            # 测试获取系统配置
            response = await client.get("/api/v1/system/config")
            print(f"  ✅ 获取系统配置: {response.status_code}")
            
            # 测试更新系统配置
            config_data = {
                "key": "test.config",
                "value": "test_value",
                "description": "测试配置",
                "category": "test"
            }
            response = await client.put("/api/v1/system/config/test.config", json=config_data)
            print(f"  ✅ 更新系统配置: {response.status_code}")
            
            # 测试获取用户列表
            response = await client.get("/api/v1/system/users")
            print(f"  ✅ 获取用户列表: {response.status_code}")
            
            # 测试创建用户
            user_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "full_name": "测试用户",
                "role": "user",
                "is_active": True
            }
            response = await client.post("/api/v1/system/users", json=user_data)
            if response.status_code == 200:
                user_id = response.json()["id"]
                print(f"  ✅ 创建用户: {response.status_code} (ID: {user_id[:8]}...)")
                
                # 测试获取用户详情
                response = await client.get(f"/api/v1/system/users/{user_id}")
                print(f"  ✅ 获取用户详情: {response.status_code}")
                
                # 测试更新用户
                update_data = {"full_name": "更新后的测试用户"}
                response = await client.put(f"/api/v1/system/users/{user_id}", json=update_data)
                print(f"  ✅ 更新用户: {response.status_code}")
                
                # 测试删除用户
                response = await client.delete(f"/api/v1/system/users/{user_id}")
                print(f"  ✅ 删除用户: {response.status_code}")
            else:
                print(f"  ❌ 创建用户失败: {response.status_code}")
            
            # 测试获取权限列表
            response = await client.get("/api/v1/system/permissions")
            print(f"  ✅ 获取权限列表: {response.status_code}")
            
            # 测试获取操作日志
            response = await client.get("/api/v1/system/logs/operations")
            print(f"  ✅ 获取操作日志: {response.status_code}")
            
        except Exception as e:
            print(f"  ❌ 系统API测试失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 开始API功能验证...")
    print("=" * 50)
    
    try:
        # 测试服务器是否运行
        async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
            response = await client.get("/health")
            if response.status_code != 200:
                print("❌ 服务器未运行或健康检查失败")
                print("请先启动服务器: python -m uvicorn app.main:app --reload")
                return
            print("✅ 服务器运行正常")
            print("=" * 50)
        
        # 依次测试各个API模块
        await test_products_api()
        print()
        await test_monitor_api()
        print()
        await test_analytics_api()
        print()
        await test_system_api()
        
        print("=" * 50)
        print("🎉 API功能验证完成!")
        
    except httpx.ConnectError:
        print("❌ 无法连接到服务器")
        print("请先启动服务器: python -m uvicorn app.main:app --reload")
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
