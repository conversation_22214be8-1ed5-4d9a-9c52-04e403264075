"""
分类体系管理器

负责商品分类体系的管理、层级结构维护和分类规则配置
"""

import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductCategory, ProductTag, ProductType

logger = get_logger(__name__)


class CategoryType(Enum):
    """分类类型"""
    INDUSTRY = "industry"        # 行业分类
    FUNCTION = "function"        # 功能分类
    BRAND = "brand"             # 品牌分类
    PRICE_RANGE = "price_range" # 价格区间分类
    QUALITY = "quality"         # 质量分类
    CUSTOM = "custom"           # 自定义分类


@dataclass
class CategoryRule:
    """分类规则"""
    id: str
    name: str
    category_type: CategoryType
    target_category_id: str
    conditions: Dict[str, Any]
    priority: int = 1
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class CategoryHierarchy:
    """分类层级结构"""
    root_categories: List[ProductCategory]
    category_tree: Dict[str, List[str]]  # parent_id -> [child_ids]
    category_map: Dict[str, ProductCategory]  # id -> category


class CategoryManager:
    """分类体系管理器"""
    
    def __init__(self):
        self.categories: Dict[str, ProductCategory] = {}
        self.tags: Dict[str, ProductTag] = {}
        self.classification_rules: List[CategoryRule] = []
        self.hierarchy = self._build_default_hierarchy()
        
    def _build_default_hierarchy(self) -> CategoryHierarchy:
        """构建默认分类层级"""
        # 创建根分类
        root_categories = [
            ProductCategory(
                id="cat_electronics",
                name="电子数码",
                level=1,
                description="手机、电脑、数码配件等电子产品"
            ),
            ProductCategory(
                id="cat_fashion",
                name="服装鞋包",
                level=1,
                description="服装、鞋子、包包等时尚用品"
            ),
            ProductCategory(
                id="cat_home",
                name="家居用品",
                level=1,
                description="家具、家纺、厨具等家居产品"
            ),
            ProductCategory(
                id="cat_beauty",
                name="美妆护肤",
                level=1,
                description="化妆品、护肤品、个人护理用品"
            ),
            ProductCategory(
                id="cat_food",
                name="食品饮料",
                level=1,
                description="食品、饮料、保健品等"
            ),
            ProductCategory(
                id="cat_sports",
                name="运动户外",
                level=1,
                description="运动器材、户外用品、健身产品"
            ),
            ProductCategory(
                id="cat_other",
                name="其他商品",
                level=1,
                description="其他未分类商品"
            )
        ]
        
        # 创建二级分类
        sub_categories = [
            # 电子数码子分类
            ProductCategory(
                id="cat_mobile",
                name="手机通讯",
                parent_id="cat_electronics",
                level=2,
                description="手机、平板、通讯设备"
            ),
            ProductCategory(
                id="cat_computer",
                name="电脑办公",
                parent_id="cat_electronics",
                level=2,
                description="电脑、笔记本、办公设备"
            ),
            ProductCategory(
                id="cat_accessories",
                name="数码配件",
                parent_id="cat_electronics",
                level=2,
                description="手机壳、充电器、耳机等配件"
            ),
            
            # 服装鞋包子分类
            ProductCategory(
                id="cat_clothing",
                name="服装",
                parent_id="cat_fashion",
                level=2,
                description="男装、女装、童装"
            ),
            ProductCategory(
                id="cat_shoes",
                name="鞋靴",
                parent_id="cat_fashion",
                level=2,
                description="运动鞋、皮鞋、靴子"
            ),
            ProductCategory(
                id="cat_bags",
                name="箱包",
                parent_id="cat_fashion",
                level=2,
                description="背包、手提包、行李箱"
            ),
        ]
        
        # 合并所有分类
        all_categories = root_categories + sub_categories
        
        # 构建分类映射
        category_map = {cat.id: cat for cat in all_categories}
        
        # 构建层级树
        category_tree = {}
        for category in all_categories:
            if category.parent_id:
                if category.parent_id not in category_tree:
                    category_tree[category.parent_id] = []
                category_tree[category.parent_id].append(category.id)
        
        # 保存到实例变量
        for category in all_categories:
            self.categories[category.id] = category
        
        return CategoryHierarchy(
            root_categories=root_categories,
            category_tree=category_tree,
            category_map=category_map
        )
    
    async def classify_product_advanced(self, product: Product) -> List[ProductCategory]:
        """
        高级商品分类
        
        Args:
            product: 商品对象
        
        Returns:
            List[ProductCategory]: 分类列表
        """
        try:
            logger.info(f"开始高级商品分类: {product.id}")
            
            categories = []
            
            # 1. 基于标题的关键词匹配
            title_categories = await self._classify_by_title(product.title)
            categories.extend(title_categories)
            
            # 2. 基于品牌的分类
            if product.specs and product.specs.brand:
                brand_categories = await self._classify_by_brand(product.specs.brand)
                categories.extend(brand_categories)
            
            # 3. 基于价格区间的分类
            if product.price and product.price.current_price:
                price_categories = await self._classify_by_price(product.price.current_price)
                categories.extend(price_categories)
            
            # 4. 基于平台的分类
            platform_categories = await self._classify_by_platform(product.platform)
            categories.extend(platform_categories)
            
            # 5. 应用自定义分类规则
            rule_categories = await self._apply_classification_rules(product)
            categories.extend(rule_categories)
            
            # 去重并排序
            unique_categories = self._deduplicate_categories(categories)
            
            logger.info(f"商品分类完成: {product.id}, 分类数: {len(unique_categories)}")
            return unique_categories
            
        except Exception as e:
            logger.error(f"高级商品分类失败: {e}")
            return [self.categories["cat_other"]]  # 返回默认分类
    
    async def _classify_by_title(self, title: str) -> List[ProductCategory]:
        """基于标题的关键词分类"""
        categories = []
        title_lower = title.lower()
        
        # 电子数码关键词
        electronics_keywords = [
            "手机", "iphone", "华为", "小米", "oppo", "vivo", "三星",
            "电脑", "笔记本", "平板", "ipad", "充电器", "耳机", "音响",
            "手机壳", "保护套", "数据线", "移动电源"
        ]
        
        # 服装鞋包关键词
        fashion_keywords = [
            "衣服", "裤子", "裙子", "外套", "t恤", "衬衫",
            "鞋子", "运动鞋", "皮鞋", "靴子", "凉鞋",
            "包包", "背包", "手提包", "钱包", "行李箱"
        ]
        
        # 家居用品关键词
        home_keywords = [
            "家具", "沙发", "床", "桌子", "椅子",
            "厨具", "锅", "碗", "杯子", "餐具",
            "家纺", "被子", "枕头", "床单", "窗帘"
        ]
        
        # 美妆护肤关键词
        beauty_keywords = [
            "化妆品", "口红", "粉底", "眼影", "睫毛膏",
            "护肤品", "面膜", "洗面奶", "爽肤水", "精华",
            "香水", "洗发水", "护发素"
        ]
        
        # 食品饮料关键词
        food_keywords = [
            "食品", "零食", "饼干", "糖果", "巧克力",
            "饮料", "茶叶", "咖啡", "果汁", "牛奶",
            "保健品", "维生素", "蛋白粉"
        ]
        
        # 运动户外关键词
        sports_keywords = [
            "运动", "健身", "瑜伽", "跑步", "游泳",
            "球类", "篮球", "足球", "羽毛球", "乒乓球",
            "户外", "登山", "露营", "钓鱼"
        ]
        
        # 匹配关键词
        keyword_mapping = {
            "cat_electronics": electronics_keywords,
            "cat_fashion": fashion_keywords,
            "cat_home": home_keywords,
            "cat_beauty": beauty_keywords,
            "cat_food": food_keywords,
            "cat_sports": sports_keywords,
        }
        
        for category_id, keywords in keyword_mapping.items():
            for keyword in keywords:
                if keyword in title_lower:
                    if category_id in self.categories:
                        categories.append(self.categories[category_id])
                    break
        
        # 更细粒度的子分类匹配
        if any(kw in title_lower for kw in ["手机壳", "充电器", "耳机", "数据线", "保护套"]):
            categories.append(self.categories["cat_accessories"])
        elif any(kw in title_lower for kw in ["手机", "iphone", "华为", "小米", "oppo", "vivo"]):
            categories.append(self.categories["cat_mobile"])
        elif any(kw in title_lower for kw in ["电脑", "笔记本", "平板", "ipad"]):
            categories.append(self.categories["cat_computer"])
        elif any(kw in title_lower for kw in ["衣服", "裤子", "裙子", "外套", "t恤"]):
            categories.append(self.categories["cat_clothing"])
        elif any(kw in title_lower for kw in ["鞋子", "运动鞋", "皮鞋", "靴子"]):
            categories.append(self.categories["cat_shoes"])
        elif any(kw in title_lower for kw in ["包包", "背包", "手提包", "行李箱"]):
            categories.append(self.categories["cat_bags"])
        
        return categories
    
    async def _classify_by_brand(self, brand: str) -> List[ProductCategory]:
        """基于品牌的分类"""
        categories = []
        brand_lower = brand.lower()
        
        # 电子品牌
        electronics_brands = [
            "apple", "苹果", "huawei", "华为", "xiaomi", "小米",
            "oppo", "vivo", "samsung", "三星", "sony", "索尼",
            "lenovo", "联想", "dell", "戴尔", "hp", "惠普"
        ]
        
        # 时尚品牌
        fashion_brands = [
            "nike", "耐克", "adidas", "阿迪达斯", "uniqlo", "优衣库",
            "zara", "h&m", "gucci", "古驰", "lv", "路易威登"
        ]
        
        if any(b in brand_lower for b in electronics_brands):
            categories.append(self.categories["cat_electronics"])
        elif any(b in brand_lower for b in fashion_brands):
            categories.append(self.categories["cat_fashion"])
        
        return categories
    
    async def _classify_by_price(self, price: float) -> List[ProductCategory]:
        """基于价格区间的分类"""
        categories = []
        
        # 这里可以根据价格区间添加特殊标签，而不是分类
        # 实际实现中可能需要价格相关的分类逻辑
        
        return categories
    
    async def _classify_by_platform(self, platform: str) -> List[ProductCategory]:
        """基于平台的分类"""
        categories = []
        
        # 1688平台通常是供货商商品，可能偏向工业品
        if platform == "1688":
            # 可以添加平台特定的分类逻辑
            pass
        
        return categories
    
    async def _apply_classification_rules(self, product: Product) -> List[ProductCategory]:
        """应用自定义分类规则"""
        categories = []
        
        for rule in self.classification_rules:
            if not rule.enabled:
                continue
                
            if await self._product_matches_rule(product, rule):
                if rule.target_category_id in self.categories:
                    categories.append(self.categories[rule.target_category_id])
        
        return categories
    
    async def _product_matches_rule(self, product: Product, rule: CategoryRule) -> bool:
        """检查商品是否匹配分类规则"""
        try:
            conditions = rule.conditions
            
            # 检查标题条件
            if "title_contains" in conditions:
                keywords = conditions["title_contains"]
                if not any(kw.lower() in product.title.lower() for kw in keywords):
                    return False
            
            # 检查价格条件
            if "price_range" in conditions and product.price:
                min_price, max_price = conditions["price_range"]
                if not (min_price <= product.price.current_price <= max_price):
                    return False
            
            # 检查品牌条件
            if "brand_in" in conditions and product.specs and product.specs.brand:
                allowed_brands = conditions["brand_in"]
                if product.specs.brand not in allowed_brands:
                    return False
            
            # 检查平台条件
            if "platform_in" in conditions:
                allowed_platforms = conditions["platform_in"]
                if product.platform not in allowed_platforms:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查分类规则失败: {e}")
            return False
    
    def _deduplicate_categories(self, categories: List[ProductCategory]) -> List[ProductCategory]:
        """去重分类列表"""
        seen_ids = set()
        unique_categories = []
        
        for category in categories:
            if category.id not in seen_ids:
                seen_ids.add(category.id)
                unique_categories.append(category)
        
        # 按层级排序
        unique_categories.sort(key=lambda x: (x.level, x.name))
        return unique_categories
    
    async def create_category(self, category: ProductCategory) -> bool:
        """创建新分类"""
        try:
            if category.id in self.categories:
                logger.warning(f"分类已存在: {category.id}")
                return False
            
            self.categories[category.id] = category
            
            # 更新层级结构
            if category.parent_id:
                if category.parent_id not in self.hierarchy.category_tree:
                    self.hierarchy.category_tree[category.parent_id] = []
                self.hierarchy.category_tree[category.parent_id].append(category.id)
            else:
                self.hierarchy.root_categories.append(category)
            
            self.hierarchy.category_map[category.id] = category
            
            logger.info(f"创建分类成功: {category.id} - {category.name}")
            return True
            
        except Exception as e:
            logger.error(f"创建分类失败: {e}")
            return False
    
    async def update_category(self, category_id: str, updates: Dict[str, Any]) -> bool:
        """更新分类信息"""
        try:
            if category_id not in self.categories:
                logger.warning(f"分类不存在: {category_id}")
                return False
            
            category = self.categories[category_id]
            
            # 更新字段
            if "name" in updates:
                category.name = updates["name"]
            if "description" in updates:
                category.description = updates["description"]
            if "parent_id" in updates:
                # 处理父分类变更
                old_parent = category.parent_id
                new_parent = updates["parent_id"]
                
                if old_parent != new_parent:
                    # 从旧父分类中移除
                    if old_parent and old_parent in self.hierarchy.category_tree:
                        if category_id in self.hierarchy.category_tree[old_parent]:
                            self.hierarchy.category_tree[old_parent].remove(category_id)
                    
                    # 添加到新父分类
                    if new_parent:
                        if new_parent not in self.hierarchy.category_tree:
                            self.hierarchy.category_tree[new_parent] = []
                        self.hierarchy.category_tree[new_parent].append(category_id)
                    
                    category.parent_id = new_parent
            
            logger.info(f"更新分类成功: {category_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新分类失败: {e}")
            return False
    
    async def delete_category(self, category_id: str) -> bool:
        """删除分类"""
        try:
            if category_id not in self.categories:
                logger.warning(f"分类不存在: {category_id}")
                return False
            
            category = self.categories[category_id]
            
            # 检查是否有子分类
            if category_id in self.hierarchy.category_tree:
                child_count = len(self.hierarchy.category_tree[category_id])
                if child_count > 0:
                    logger.warning(f"分类有 {child_count} 个子分类，无法删除: {category_id}")
                    return False
            
            # 从父分类中移除
            if category.parent_id and category.parent_id in self.hierarchy.category_tree:
                if category_id in self.hierarchy.category_tree[category.parent_id]:
                    self.hierarchy.category_tree[category.parent_id].remove(category_id)
            
            # 从根分类中移除
            self.hierarchy.root_categories = [
                cat for cat in self.hierarchy.root_categories 
                if cat.id != category_id
            ]
            
            # 删除分类
            del self.categories[category_id]
            del self.hierarchy.category_map[category_id]
            
            logger.info(f"删除分类成功: {category_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除分类失败: {e}")
            return False
    
    def get_category_tree(self) -> Dict[str, Any]:
        """获取分类树结构"""
        def build_tree_node(category: ProductCategory) -> Dict[str, Any]:
            node = {
                "id": category.id,
                "name": category.name,
                "level": category.level,
                "description": category.description,
                "children": []
            }
            
            # 添加子分类
            if category.id in self.hierarchy.category_tree:
                for child_id in self.hierarchy.category_tree[category.id]:
                    if child_id in self.categories:
                        child_category = self.categories[child_id]
                        child_node = build_tree_node(child_category)
                        node["children"].append(child_node)
            
            return node
        
        tree = {
            "root": {
                "id": "root",
                "name": "商品分类",
                "level": 0,
                "children": []
            }
        }
        
        # 构建根分类节点
        for root_category in self.hierarchy.root_categories:
            root_node = build_tree_node(root_category)
            tree["root"]["children"].append(root_node)
        
        return tree
    
    def get_category_path(self, category_id: str) -> List[str]:
        """获取分类路径"""
        path = []
        current_id = category_id
        
        while current_id and current_id in self.categories:
            category = self.categories[current_id]
            path.insert(0, category.name)
            current_id = category.parent_id
        
        return path
    
    def add_classification_rule(self, rule: CategoryRule):
        """添加分类规则"""
        self.classification_rules.append(rule)
        # 按优先级排序
        self.classification_rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"添加分类规则: {rule.name}")
    
    def remove_classification_rule(self, rule_id: str) -> bool:
        """移除分类规则"""
        original_count = len(self.classification_rules)
        self.classification_rules = [r for r in self.classification_rules if r.id != rule_id]
        
        if len(self.classification_rules) < original_count:
            logger.info(f"移除分类规则: {rule_id}")
            return True
        
        return False
    
    def get_classification_rules(self) -> List[Dict[str, Any]]:
        """获取分类规则列表"""
        return [
            {
                "id": rule.id,
                "name": rule.name,
                "category_type": rule.category_type.value,
                "target_category_id": rule.target_category_id,
                "conditions": rule.conditions,
                "priority": rule.priority,
                "enabled": rule.enabled,
                "created_at": rule.created_at.isoformat()
            }
            for rule in self.classification_rules
        ]
