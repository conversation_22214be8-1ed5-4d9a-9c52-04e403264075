"""
归档管理器

负责商品的归档、版本管理和历史记录
"""

import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductStatus, ProductChangeRecord

logger = get_logger(__name__)


class ArchiveReason(Enum):
    """归档原因"""
    MANUAL = "manual"                    # 手动归档
    INACTIVE = "inactive"               # 长期不活跃
    DUPLICATE = "duplicate"             # 重复商品
    INVALID = "invalid"                 # 无效商品
    POLICY_VIOLATION = "policy_violation" # 违反政策
    QUALITY_ISSUE = "quality_issue"     # 质量问题
    EXPIRED = "expired"                 # 过期商品


@dataclass
class ArchiveRule:
    """归档规则"""
    name: str
    reason: ArchiveReason
    conditions: Dict[str, Any]
    enabled: bool = True
    priority: int = 1


@dataclass
class ArchiveOperation:
    """归档操作记录"""
    id: str
    product_id: str
    operation_type: str  # archive, restore, delete
    reason: ArchiveReason
    operator: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)


class ArchiveManager:
    """归档管理器"""
    
    def __init__(self):
        self.archive_rules = self._load_default_rules()
        self.archive_operations: List[ArchiveOperation] = []
        
    def _load_default_rules(self) -> List[ArchiveRule]:
        """加载默认归档规则"""
        return [
            # 长期不活跃商品
            ArchiveRule(
                name="inactive_products",
                reason=ArchiveReason.INACTIVE,
                conditions={
                    "days_since_last_crawl": 30,
                    "status": [ProductStatus.PAUSED, ProductStatus.INACTIVE]
                },
                priority=1
            ),
            
            # 数据质量差的商品
            ArchiveRule(
                name="poor_quality_products",
                reason=ArchiveReason.QUALITY_ISSUE,
                conditions={
                    "data_quality_score": {"max": 0.3},
                    "days_since_created": 7  # 创建7天后仍然质量差
                },
                priority=2
            ),
            
            # 无效URL的商品
            ArchiveRule(
                name="invalid_url_products",
                reason=ArchiveReason.INVALID,
                conditions={
                    "url_accessible": False,
                    "consecutive_failures": 5
                },
                priority=3
            ),
        ]
    
    async def archive_product(self, product: Product, reason: ArchiveReason,
                            operator: Optional[str] = None, 
                            notes: Optional[str] = None) -> bool:
        """
        归档商品
        
        Args:
            product: 商品对象
            reason: 归档原因
            operator: 操作者
            notes: 备注
        
        Returns:
            bool: 是否归档成功
        """
        try:
            logger.info(f"开始归档商品: {product.id}, 原因: {reason.value}")
            
            # 检查商品状态
            if product.status == ProductStatus.ARCHIVED:
                logger.warning(f"商品已归档: {product.id}")
                return False
            
            # 记录状态变更
            old_status = product.status
            product.status = ProductStatus.ARCHIVED
            
            # 添加变更记录
            product.add_change_record(
                change_type="status_change",
                old_value=old_status.value,
                new_value=ProductStatus.ARCHIVED.value,
                reason=f"归档: {reason.value}"
            )
            
            # 记录归档操作
            operation = ArchiveOperation(
                id=f"archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{product.id[:8]}",
                product_id=product.id,
                operation_type="archive",
                reason=reason,
                operator=operator,
                notes=notes
            )
            self.archive_operations.append(operation)
            
            logger.info(f"商品归档成功: {product.id}")
            return True
            
        except Exception as e:
            logger.error(f"归档商品失败: {e}")
            return False
    
    async def restore_product(self, product: Product, 
                            operator: Optional[str] = None,
                            notes: Optional[str] = None) -> bool:
        """
        恢复归档商品
        
        Args:
            product: 商品对象
            operator: 操作者
            notes: 备注
        
        Returns:
            bool: 是否恢复成功
        """
        try:
            logger.info(f"开始恢复商品: {product.id}")
            
            # 检查商品状态
            if product.status != ProductStatus.ARCHIVED:
                logger.warning(f"商品未归档，无需恢复: {product.id}")
                return False
            
            # 恢复为活跃状态
            old_status = product.status
            product.status = ProductStatus.ACTIVE
            
            # 添加变更记录
            product.add_change_record(
                change_type="status_change",
                old_value=old_status.value,
                new_value=ProductStatus.ACTIVE.value,
                reason="恢复归档"
            )
            
            # 记录恢复操作
            operation = ArchiveOperation(
                id=f"restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{product.id[:8]}",
                product_id=product.id,
                operation_type="restore",
                reason=ArchiveReason.MANUAL,
                operator=operator,
                notes=notes
            )
            self.archive_operations.append(operation)
            
            logger.info(f"商品恢复成功: {product.id}")
            return True
            
        except Exception as e:
            logger.error(f"恢复商品失败: {e}")
            return False
    
    async def delete_product(self, product: Product,
                           operator: Optional[str] = None,
                           notes: Optional[str] = None) -> bool:
        """
        删除商品（软删除）
        
        Args:
            product: 商品对象
            operator: 操作者
            notes: 备注
        
        Returns:
            bool: 是否删除成功
        """
        try:
            logger.info(f"开始删除商品: {product.id}")
            
            # 记录状态变更
            old_status = product.status
            product.status = ProductStatus.DELETED
            
            # 添加变更记录
            product.add_change_record(
                change_type="status_change",
                old_value=old_status.value,
                new_value=ProductStatus.DELETED.value,
                reason="删除商品"
            )
            
            # 记录删除操作
            operation = ArchiveOperation(
                id=f"delete_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{product.id[:8]}",
                product_id=product.id,
                operation_type="delete",
                reason=ArchiveReason.MANUAL,
                operator=operator,
                notes=notes
            )
            self.archive_operations.append(operation)
            
            logger.info(f"商品删除成功: {product.id}")
            return True
            
        except Exception as e:
            logger.error(f"删除商品失败: {e}")
            return False
    
    async def auto_archive_products(self, products: List[Product]) -> Dict[str, Any]:
        """
        自动归档商品
        
        Args:
            products: 商品列表
        
        Returns:
            Dict[str, Any]: 归档结果统计
        """
        logger.info(f"开始自动归档检查: {len(products)} 个商品")
        
        archived_count = 0
        archive_results = {}
        
        for rule in self.archive_rules:
            if not rule.enabled:
                continue
                
            logger.info(f"应用归档规则: {rule.name}")
            
            # 找到符合规则的商品
            matching_products = await self._find_matching_products(products, rule)
            
            # 归档符合条件的商品
            rule_archived = 0
            for product in matching_products:
                if await self.archive_product(product, rule.reason, operator="system"):
                    rule_archived += 1
                    archived_count += 1
            
            archive_results[rule.name] = {
                "rule": rule.name,
                "reason": rule.reason.value,
                "matched_products": len(matching_products),
                "archived_count": rule_archived
            }
            
            logger.info(f"规则 {rule.name} 归档了 {rule_archived} 个商品")
        
        result = {
            "total_checked": len(products),
            "total_archived": archived_count,
            "rules_applied": len([r for r in self.archive_rules if r.enabled]),
            "rule_results": archive_results,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"自动归档完成: 检查 {len(products)} 个商品，归档 {archived_count} 个")
        return result
    
    async def _find_matching_products(self, products: List[Product], 
                                    rule: ArchiveRule) -> List[Product]:
        """找到符合归档规则的商品"""
        matching_products = []
        
        for product in products:
            if await self._product_matches_rule(product, rule):
                matching_products.append(product)
        
        return matching_products
    
    async def _product_matches_rule(self, product: Product, rule: ArchiveRule) -> bool:
        """检查商品是否符合归档规则"""
        try:
            conditions = rule.conditions
            
            # 检查状态条件
            if "status" in conditions:
                allowed_statuses = conditions["status"]
                if product.status not in allowed_statuses:
                    return False
            
            # 检查最后爬取时间
            if "days_since_last_crawl" in conditions:
                max_days = conditions["days_since_last_crawl"]
                if product.last_crawled_at:
                    days_diff = (datetime.now() - product.last_crawled_at).days
                    if days_diff < max_days:
                        return False
                else:
                    # 如果从未爬取，检查创建时间
                    days_since_created = (datetime.now() - product.created_at).days
                    if days_since_created < max_days:
                        return False
            
            # 检查创建时间
            if "days_since_created" in conditions:
                min_days = conditions["days_since_created"]
                days_since_created = (datetime.now() - product.created_at).days
                if days_since_created < min_days:
                    return False
            
            # 检查数据质量分数
            if "data_quality_score" in conditions:
                quality_condition = conditions["data_quality_score"]
                if "max" in quality_condition:
                    if product.data_quality_score > quality_condition["max"]:
                        return False
                if "min" in quality_condition:
                    if product.data_quality_score < quality_condition["min"]:
                        return False
            
            # 检查URL可访问性（这里简化处理）
            if "url_accessible" in conditions:
                expected_accessible = conditions["url_accessible"]
                # 简化实现：假设URL不可访问的商品有特定标记
                # 实际实现中需要检查爬取失败记录
                if not expected_accessible:
                    # 检查是否有连续失败记录
                    consecutive_failures = conditions.get("consecutive_failures", 1)
                    # 这里简化为检查数据质量分数很低
                    if product.data_quality_score > 0.1:
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查归档规则失败: {e}")
            return False
    
    async def get_archive_statistics(self, products: List[Product]) -> Dict[str, Any]:
        """获取归档统计信息"""
        stats = {
            "total_products": len(products),
            "status_breakdown": {},
            "archive_operations": {
                "total": len(self.archive_operations),
                "by_reason": {},
                "by_operator": {},
                "recent_operations": []
            },
            "quality_distribution": {
                "excellent": 0,
                "good": 0,
                "fair": 0,
                "poor": 0,
                "bad": 0
            }
        }
        
        # 统计商品状态分布
        for product in products:
            status = product.status.value
            stats["status_breakdown"][status] = stats["status_breakdown"].get(status, 0) + 1
            
            # 统计质量分布
            quality_level = product.data_quality.value
            if quality_level in stats["quality_distribution"]:
                stats["quality_distribution"][quality_level] += 1
        
        # 统计归档操作
        for operation in self.archive_operations:
            # 按原因统计
            reason = operation.reason.value
            stats["archive_operations"]["by_reason"][reason] = \
                stats["archive_operations"]["by_reason"].get(reason, 0) + 1
            
            # 按操作者统计
            operator = operation.operator or "system"
            stats["archive_operations"]["by_operator"][operator] = \
                stats["archive_operations"]["by_operator"].get(operator, 0) + 1
        
        # 最近的归档操作
        recent_ops = sorted(self.archive_operations, 
                          key=lambda x: x.created_at, reverse=True)[:10]
        stats["archive_operations"]["recent_operations"] = [
            {
                "id": op.id,
                "product_id": op.product_id,
                "operation_type": op.operation_type,
                "reason": op.reason.value,
                "operator": op.operator,
                "created_at": op.created_at.isoformat()
            }
            for op in recent_ops
        ]
        
        return stats
    
    async def cleanup_old_operations(self, days_to_keep: int = 90):
        """清理旧的归档操作记录"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        old_count = len(self.archive_operations)
        self.archive_operations = [
            op for op in self.archive_operations 
            if op.created_at > cutoff_date
        ]
        
        cleaned_count = old_count - len(self.archive_operations)
        logger.info(f"清理了 {cleaned_count} 条旧的归档操作记录")
        
        return cleaned_count
    
    def add_archive_rule(self, rule: ArchiveRule):
        """添加归档规则"""
        self.archive_rules.append(rule)
        logger.info(f"添加归档规则: {rule.name}")
    
    def remove_archive_rule(self, rule_name: str) -> bool:
        """移除归档规则"""
        original_count = len(self.archive_rules)
        self.archive_rules = [r for r in self.archive_rules if r.name != rule_name]
        
        if len(self.archive_rules) < original_count:
            logger.info(f"移除归档规则: {rule_name}")
            return True
        
        return False
    
    def get_archive_rules(self) -> List[Dict[str, Any]]:
        """获取归档规则列表"""
        return [
            {
                "name": rule.name,
                "reason": rule.reason.value,
                "conditions": rule.conditions,
                "enabled": rule.enabled,
                "priority": rule.priority
            }
            for rule in self.archive_rules
        ]
