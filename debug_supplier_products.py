#!/usr/bin/env python3
"""
调试供货商商品列表API的脚本
"""

import asyncio
import httpx
import json


async def debug_supplier_products():
    """调试供货商商品列表API"""
    print("🔍 调试供货商商品列表API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002", timeout=30.0) as client:
        try:
            # 1. 先获取供货商列表
            print("  📋 获取供货商列表...")
            response = await client.get("/api/v1/suppliers/")
            if response.status_code == 200:
                data = response.json()
                suppliers = data.get('items', [])
                if suppliers:
                    supplier_id = suppliers[0]['id']
                    print(f"    ✅ 找到供货商 - ID: {supplier_id}")
                    
                    # 2. 尝试获取该供货商的商品列表
                    print("  📦 尝试获取供货商商品列表...")
                    response = await client.get(f"/api/v1/suppliers/{supplier_id}/products")
                    print(f"    状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"    ✅ 获取成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"    ❌ 获取失败")
                        try:
                            error_data = response.json()
                            print(f"    错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                        except:
                            print(f"    错误内容: {response.text}")
                else:
                    print("    ❌ 没有找到供货商")
            else:
                print(f"    ❌ 获取供货商列表失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 调试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(debug_supplier_products())
