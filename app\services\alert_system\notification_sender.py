"""
通知发送器

提供多渠道通知推送、通知模板、个性化配置等功能
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from app.core.logging import get_logger
from .alert_engine import Alert, AlertLevel, AlertType, AlertCategory

logger = get_logger(__name__)


class NotificationChannel(Enum):
    """通知渠道"""
    EMAIL = "email"           # 邮件
    WECHAT = "wechat"        # 微信
    DINGTALK = "dingtalk"    # 钉钉
    SMS = "sms"              # 短信
    WEBHOOK = "webhook"      # Webhook
    IN_APP = "in_app"        # 应用内通知


class NotificationStatus(Enum):
    """通知状态"""
    PENDING = "pending"       # 待发送
    SENT = "sent"            # 已发送
    DELIVERED = "delivered"   # 已送达
    FAILED = "failed"        # 发送失败
    RETRY = "retry"          # 重试中


class NotificationPriority(Enum):
    """通知优先级"""
    IMMEDIATE = "immediate"   # 立即发送
    HIGH = "high"            # 高优先级
    NORMAL = "normal"        # 普通优先级
    LOW = "low"              # 低优先级


@dataclass
class NotificationTemplate:
    """通知模板"""
    template_id: str
    template_name: str
    alert_type: AlertType
    alert_category: AlertCategory
    channels: List[NotificationChannel]
    subject_template: str
    content_template: str
    html_template: Optional[str] = None
    variables: List[str] = field(default_factory=list)
    enabled: bool = True


@dataclass
class NotificationConfig:
    """通知配置"""
    user_id: str
    enabled_channels: List[NotificationChannel]
    alert_level_filters: List[AlertLevel]  # 接收的预警级别
    alert_type_filters: List[AlertType]    # 接收的预警类型
    quiet_hours: Optional[Dict[str, str]] = None  # 免打扰时间
    max_notifications_per_hour: int = 10
    email_address: Optional[str] = None
    phone_number: Optional[str] = None
    wechat_id: Optional[str] = None
    dingtalk_webhook: Optional[str] = None


@dataclass
class NotificationRecord:
    """通知记录"""
    notification_id: str
    alert_id: str
    user_id: str
    channel: NotificationChannel
    template_id: str
    subject: str
    content: str
    status: NotificationStatus
    priority: NotificationPriority
    created_at: datetime = field(default_factory=datetime.now)
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    failed_reason: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


class NotificationSender:
    """通知发送器"""
    
    def __init__(self):
        # 通知存储
        self.notification_records: List[NotificationRecord] = []
        self.notification_templates: Dict[str, NotificationTemplate] = {}
        self.user_configs: Dict[str, NotificationConfig] = {}
        
        # 发送配置
        self.sender_config = {
            "email": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "from_address": "<EMAIL>"
            },
            "retry_intervals": [60, 300, 900],  # 重试间隔（秒）
            "batch_size": 50,                   # 批量发送大小
            "rate_limit": {
                "email": 100,      # 每小时邮件数量限制
                "sms": 50,         # 每小时短信数量限制
                "wechat": 200      # 每小时微信数量限制
            }
        }
        
        # 初始化默认模板
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """初始化默认通知模板"""
        # 价格异常模板
        price_anomaly_template = NotificationTemplate(
            template_id="price_anomaly_template",
            template_name="价格异常通知模板",
            alert_type=AlertType.PRICE_ANOMALY,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            subject_template="【价格预警】{product_title} 价格异常",
            content_template="""
商品价格异常预警

商品名称：{product_title}
当前价格：¥{current_price}
变化幅度：{change_percentage}%
预警级别：{alert_level}
预警时间：{alert_time}

详细描述：{description}

建议措施：
{recommendations}

请及时关注并采取相应措施。
            """.strip(),
            variables=["product_title", "current_price", "change_percentage", "alert_level", "alert_time", "description", "recommendations"]
        )
        
        # 销量变化模板
        sales_change_template = NotificationTemplate(
            template_id="sales_change_template",
            template_name="销量变化通知模板",
            alert_type=AlertType.SALES_DECLINE,
            alert_category=AlertCategory.COMPETITOR_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.WECHAT],
            subject_template="【销量预警】{product_title} 销量异常",
            content_template="""
销量变化预警

商品名称：{product_title}
当前销量：{current_sales}
变化幅度：{change_percentage}%
预警级别：{alert_level}
预警时间：{alert_time}

详细描述：{description}

建议措施：
{recommendations}

请关注市场动态，及时调整策略。
            """.strip(),
            variables=["product_title", "current_sales", "change_percentage", "alert_level", "alert_time", "description", "recommendations"]
        )
        
        # 库存不足模板
        inventory_shortage_template = NotificationTemplate(
            template_id="inventory_shortage_template",
            template_name="库存不足通知模板",
            alert_type=AlertType.INVENTORY_SHORTAGE,
            alert_category=AlertCategory.INVENTORY_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.DINGTALK],
            subject_template="【库存预警】{product_title} 库存不足",
            content_template="""
库存不足紧急预警

商品名称：{product_title}
当前库存：{current_stock} 件
预计可供应：{supply_days} 天
预警级别：{alert_level}
预警时间：{alert_time}

详细描述：{description}

紧急措施：
{recommendations}

请立即联系供应商补充库存！
            """.strip(),
            variables=["product_title", "current_stock", "supply_days", "alert_level", "alert_time", "description", "recommendations"]
        )
        
        # 利润预警模板
        profit_alert_template = NotificationTemplate(
            template_id="profit_alert_template",
            template_name="利润预警通知模板",
            alert_type=AlertType.PROFIT_MARGIN_DROP,
            alert_category=AlertCategory.PROFIT_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            subject_template="【利润预警】{product_title} 利润异常",
            content_template="""
利润异常预警

商品名称：{product_title}
当前利润率：{current_margin}%
预警级别：{alert_level}
预警时间：{alert_time}

详细描述：{description}

优化建议：
{recommendations}

请及时调整定价或成本策略。
            """.strip(),
            variables=["product_title", "current_margin", "alert_level", "alert_time", "description", "recommendations"]
        )
        
        # 供应商问题模板
        supplier_issue_template = NotificationTemplate(
            template_id="supplier_issue_template",
            template_name="供应商问题通知模板",
            alert_type=AlertType.SUPPLIER_ISSUE,
            alert_category=AlertCategory.SUPPLIER_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.DINGTALK],
            subject_template="【供应商预警】{product_title} 供应商问题",
            content_template="""
供应商问题预警

商品名称：{product_title}
供应商：{supplier_name}
问题类型：{issue_type}
预警级别：{alert_level}
预警时间：{alert_time}

详细描述：{description}

处理建议：
{recommendations}

请及时与供应商沟通解决。
            """.strip(),
            variables=["product_title", "supplier_name", "issue_type", "alert_level", "alert_time", "description", "recommendations"]
        )
        
        # 市场机会模板
        market_opportunity_template = NotificationTemplate(
            template_id="market_opportunity_template",
            template_name="市场机会通知模板",
            alert_type=AlertType.MARKET_OPPORTUNITY,
            alert_category=AlertCategory.MARKET_ALERT,
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            subject_template="【市场机会】{product_title} 发现商机",
            content_template="""
市场机会提醒

商品名称：{product_title}
机会类型：{opportunity_type}
潜在收益：{potential_impact}%
预警时间：{alert_time}

详细描述：{description}

行动建议：
{recommendations}

建议抓住机会，及时行动！
            """.strip(),
            variables=["product_title", "opportunity_type", "potential_impact", "alert_time", "description", "recommendations"]
        )
        
        # 注册所有模板
        templates = [
            price_anomaly_template,
            sales_change_template,
            inventory_shortage_template,
            profit_alert_template,
            supplier_issue_template,
            market_opportunity_template
        ]
        
        for template in templates:
            self.notification_templates[template.template_id] = template
    
    async def send_alert_notification(self, alert: Alert, user_ids: List[str]) -> List[NotificationRecord]:
        """
        发送预警通知
        
        Args:
            alert: 预警信息
            user_ids: 用户ID列表
        
        Returns:
            List[NotificationRecord]: 通知记录列表
        """
        try:
            logger.info(f"开始发送预警通知: {alert.alert_id}, 用户数: {len(user_ids)}")
            
            notification_records = []
            
            for user_id in user_ids:
                # 获取用户配置
                user_config = self.user_configs.get(user_id)
                if not user_config:
                    logger.warning(f"用户配置不存在: {user_id}")
                    continue
                
                # 检查用户是否接收此类预警
                if not self._should_send_notification(alert, user_config):
                    logger.info(f"用户过滤预警: {user_id}, {alert.alert_type.value}")
                    continue
                
                # 为每个启用的渠道创建通知
                for channel in user_config.enabled_channels:
                    try:
                        notification = await self._create_notification(alert, user_id, channel)
                        if notification:
                            notification_records.append(notification)
                    except Exception as e:
                        logger.error(f"创建通知失败: {user_id}, {channel.value}, {e}")
            
            # 批量发送通知
            if notification_records:
                await self._batch_send_notifications(notification_records)
            
            logger.info(f"预警通知发送完成: {len(notification_records)} 个通知")
            return notification_records
            
        except Exception as e:
            logger.error(f"发送预警通知失败: {alert.alert_id}, {e}")
            return []

    def _should_send_notification(self, alert: Alert, user_config: NotificationConfig) -> bool:
        """检查是否应该发送通知"""
        # 检查预警级别过滤
        if alert.alert_level not in user_config.alert_level_filters:
            return False

        # 检查预警类型过滤
        if alert.alert_type not in user_config.alert_type_filters:
            return False

        # 检查免打扰时间
        if user_config.quiet_hours:
            current_time = datetime.now().time()
            start_time = datetime.strptime(user_config.quiet_hours.get("start", "00:00"), "%H:%M").time()
            end_time = datetime.strptime(user_config.quiet_hours.get("end", "00:00"), "%H:%M").time()

            if start_time <= end_time:
                if start_time <= current_time <= end_time:
                    return False
            else:  # 跨天的情况
                if current_time >= start_time or current_time <= end_time:
                    return False

        # 检查通知频率限制
        recent_notifications = [
            n for n in self.notification_records
            if (n.user_id == user_config.user_id and
                datetime.now() - n.created_at <= timedelta(hours=1))
        ]

        if len(recent_notifications) >= user_config.max_notifications_per_hour:
            return False

        return True

    async def _create_notification(self, alert: Alert, user_id: str,
                                 channel: NotificationChannel) -> Optional[NotificationRecord]:
        """创建通知记录"""
        try:
            # 查找合适的模板
            template = self._find_template(alert.alert_type, alert.alert_category, channel)
            if not template:
                logger.warning(f"未找到合适的模板: {alert.alert_type.value}, {channel.value}")
                return None

            # 准备模板变量
            template_vars = self._prepare_template_variables(alert)

            # 渲染模板
            subject = self._render_template(template.subject_template, template_vars)
            content = self._render_template(template.content_template, template_vars)

            # 确定优先级
            priority = self._determine_notification_priority(alert.alert_level)

            # 生成通知ID
            notification_id = f"notification_{alert.alert_id}_{user_id}_{channel.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            notification = NotificationRecord(
                notification_id=notification_id,
                alert_id=alert.alert_id,
                user_id=user_id,
                channel=channel,
                template_id=template.template_id,
                subject=subject,
                content=content,
                status=NotificationStatus.PENDING,
                priority=priority
            )

            self.notification_records.append(notification)
            return notification

        except Exception as e:
            logger.error(f"创建通知失败: {alert.alert_id}, {user_id}, {channel.value}, {e}")
            return None

    def _find_template(self, alert_type: AlertType, alert_category: AlertCategory,
                      channel: NotificationChannel) -> Optional[NotificationTemplate]:
        """查找合适的模板"""
        # 优先查找完全匹配的模板
        for template in self.notification_templates.values():
            if (template.alert_type == alert_type and
                template.alert_category == alert_category and
                channel in template.channels and
                template.enabled):
                return template

        # 查找类型匹配的模板
        for template in self.notification_templates.values():
            if (template.alert_type == alert_type and
                channel in template.channels and
                template.enabled):
                return template

        return None

    def _prepare_template_variables(self, alert: Alert) -> Dict[str, str]:
        """准备模板变量"""
        variables = {
            "alert_id": alert.alert_id,
            "product_title": alert.title,
            "alert_level": alert.alert_level.value.upper(),
            "alert_time": alert.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "description": alert.description,
            "recommendations": "\n".join([f"• {rec}" for rec in alert.recommendations])
        }

        # 添加特定指标
        if alert.current_value is not None:
            if alert.alert_type == AlertType.PRICE_ANOMALY:
                variables["current_price"] = f"{alert.current_value:.2f}"
            elif alert.alert_type == AlertType.SALES_DECLINE:
                variables["current_sales"] = str(int(alert.current_value))
            elif alert.alert_type == AlertType.INVENTORY_SHORTAGE:
                variables["current_stock"] = str(int(alert.current_value))
            elif alert.alert_type == AlertType.PROFIT_MARGIN_DROP:
                variables["current_margin"] = f"{alert.current_value * 100:.2f}"

        # 添加变化百分比
        if alert.change_percentage is not None:
            variables["change_percentage"] = f"{alert.change_percentage:+.1f}"

        # 添加受影响的指标
        for key, value in alert.affected_metrics.items():
            if isinstance(value, (int, float)):
                if "days" in key.lower():
                    variables[key] = f"{value:.1f}"
                elif "percentage" in key.lower() or "rate" in key.lower():
                    variables[key] = f"{value * 100:.2f}" if value < 1 else f"{value:.2f}"
                else:
                    variables[key] = f"{value:.2f}"
            else:
                variables[key] = str(value)

        return variables

    def _render_template(self, template: str, variables: Dict[str, str]) -> str:
        """渲染模板"""
        try:
            rendered = template
            for key, value in variables.items():
                placeholder = "{" + key + "}"
                rendered = rendered.replace(placeholder, str(value))
            return rendered
        except Exception as e:
            logger.error(f"模板渲染失败: {e}")
            return template

    def _determine_notification_priority(self, alert_level: AlertLevel) -> NotificationPriority:
        """确定通知优先级"""
        priority_mapping = {
            AlertLevel.CRITICAL: NotificationPriority.IMMEDIATE,
            AlertLevel.HIGH: NotificationPriority.HIGH,
            AlertLevel.MEDIUM: NotificationPriority.NORMAL,
            AlertLevel.LOW: NotificationPriority.LOW,
            AlertLevel.INFO: NotificationPriority.LOW
        }
        return priority_mapping.get(alert_level, NotificationPriority.NORMAL)

    async def _batch_send_notifications(self, notifications: List[NotificationRecord]):
        """批量发送通知"""
        try:
            # 按渠道分组
            channel_groups = {}
            for notification in notifications:
                channel = notification.channel
                if channel not in channel_groups:
                    channel_groups[channel] = []
                channel_groups[channel].append(notification)

            # 并发发送不同渠道的通知
            tasks = []
            for channel, channel_notifications in channel_groups.items():
                task = self._send_channel_notifications(channel, channel_notifications)
                tasks.append(task)

            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"批量发送通知失败: {e}")

    async def _send_channel_notifications(self, channel: NotificationChannel,
                                        notifications: List[NotificationRecord]):
        """发送特定渠道的通知"""
        try:
            batch_size = self.sender_config["batch_size"]

            for i in range(0, len(notifications), batch_size):
                batch = notifications[i:i + batch_size]

                if channel == NotificationChannel.EMAIL:
                    await self._send_email_batch(batch)
                elif channel == NotificationChannel.SMS:
                    await self._send_sms_batch(batch)
                elif channel == NotificationChannel.WECHAT:
                    await self._send_wechat_batch(batch)
                elif channel == NotificationChannel.DINGTALK:
                    await self._send_dingtalk_batch(batch)
                elif channel == NotificationChannel.WEBHOOK:
                    await self._send_webhook_batch(batch)
                elif channel == NotificationChannel.IN_APP:
                    await self._send_in_app_batch(batch)

                # 避免发送过快
                await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"发送渠道通知失败: {channel.value}, {e}")

    async def _send_email_batch(self, notifications: List[NotificationRecord]):
        """发送邮件批次"""
        for notification in notifications:
            try:
                # 获取用户邮箱
                user_config = self.user_configs.get(notification.user_id)
                if not user_config or not user_config.email_address:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "用户邮箱地址未配置"
                    continue

                # 发送邮件（这里简化处理，实际应该使用真实的SMTP配置）
                success = await self._send_email(
                    to_address=user_config.email_address,
                    subject=notification.subject,
                    content=notification.content
                )

                if success:
                    notification.status = NotificationStatus.SENT
                    notification.sent_at = datetime.now()
                else:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "邮件发送失败"

            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"发送邮件失败: {notification.notification_id}, {e}")

    async def _send_email(self, to_address: str, subject: str, content: str) -> bool:
        """发送单个邮件"""
        try:
            # 这里是邮件发送的模拟实现
            # 实际应用中需要配置真实的SMTP服务器
            logger.info(f"模拟发送邮件到: {to_address}, 主题: {subject}")

            # 模拟发送延迟
            await asyncio.sleep(0.1)

            # 模拟90%的成功率
            import random
            return random.random() > 0.1

        except Exception as e:
            logger.error(f"邮件发送异常: {to_address}, {e}")
            return False

    async def _send_sms_batch(self, notifications: List[NotificationRecord]):
        """发送短信批次"""
        for notification in notifications:
            try:
                user_config = self.user_configs.get(notification.user_id)
                if not user_config or not user_config.phone_number:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "用户手机号未配置"
                    continue

                # 短信内容需要简化
                sms_content = f"【预警通知】{notification.subject}\n{notification.content[:100]}..."

                success = await self._send_sms(user_config.phone_number, sms_content)

                if success:
                    notification.status = NotificationStatus.SENT
                    notification.sent_at = datetime.now()
                else:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "短信发送失败"

            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"发送短信失败: {notification.notification_id}, {e}")

    async def _send_sms(self, phone_number: str, content: str) -> bool:
        """发送单个短信"""
        try:
            logger.info(f"模拟发送短信到: {phone_number}")
            await asyncio.sleep(0.1)
            import random
            return random.random() > 0.05  # 95%成功率
        except Exception as e:
            logger.error(f"短信发送异常: {phone_number}, {e}")
            return False

    async def _send_wechat_batch(self, notifications: List[NotificationRecord]):
        """发送微信批次"""
        for notification in notifications:
            try:
                user_config = self.user_configs.get(notification.user_id)
                if not user_config or not user_config.wechat_id:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "用户微信ID未配置"
                    continue

                success = await self._send_wechat(user_config.wechat_id, notification.subject, notification.content)

                if success:
                    notification.status = NotificationStatus.SENT
                    notification.sent_at = datetime.now()
                else:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "微信发送失败"

            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"发送微信失败: {notification.notification_id}, {e}")

    async def _send_wechat(self, wechat_id: str, subject: str, content: str) -> bool:
        """发送微信消息"""
        try:
            logger.info(f"模拟发送微信到: {wechat_id}, 主题: {subject}")
            await asyncio.sleep(0.1)
            import random
            return random.random() > 0.02  # 98%成功率
        except Exception as e:
            logger.error(f"微信发送异常: {wechat_id}, {e}")
            return False

    async def _send_dingtalk_batch(self, notifications: List[NotificationRecord]):
        """发送钉钉批次"""
        for notification in notifications:
            try:
                user_config = self.user_configs.get(notification.user_id)
                if not user_config or not user_config.dingtalk_webhook:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "用户钉钉Webhook未配置"
                    continue

                success = await self._send_dingtalk(user_config.dingtalk_webhook, notification.subject, notification.content)

                if success:
                    notification.status = NotificationStatus.SENT
                    notification.sent_at = datetime.now()
                else:
                    notification.status = NotificationStatus.FAILED
                    notification.failed_reason = "钉钉发送失败"

            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"发送钉钉失败: {notification.notification_id}, {e}")

    async def _send_dingtalk(self, webhook_url: str, subject: str, content: str) -> bool:
        """发送钉钉消息"""
        try:
            logger.info(f"模拟发送钉钉消息: {subject}")
            await asyncio.sleep(0.1)
            import random
            return random.random() > 0.03  # 97%成功率
        except Exception as e:
            logger.error(f"钉钉发送异常: {webhook_url}, {e}")
            return False

    async def _send_webhook_batch(self, notifications: List[NotificationRecord]):
        """发送Webhook批次"""
        for notification in notifications:
            try:
                # Webhook发送逻辑
                logger.info(f"模拟发送Webhook: {notification.notification_id}")
                notification.status = NotificationStatus.SENT
                notification.sent_at = datetime.now()
            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"发送Webhook失败: {notification.notification_id}, {e}")

    async def _send_in_app_batch(self, notifications: List[NotificationRecord]):
        """发送应用内通知批次"""
        for notification in notifications:
            try:
                # 应用内通知直接标记为已发送
                logger.info(f"应用内通知: {notification.notification_id}")
                notification.status = NotificationStatus.SENT
                notification.sent_at = datetime.now()
            except Exception as e:
                notification.status = NotificationStatus.FAILED
                notification.failed_reason = str(e)
                logger.error(f"应用内通知失败: {notification.notification_id}, {e}")

    def add_user_config(self, user_config: NotificationConfig) -> bool:
        """
        添加用户通知配置

        Args:
            user_config: 用户配置

        Returns:
            bool: 是否成功添加
        """
        try:
            self.user_configs[user_config.user_id] = user_config
            logger.info(f"用户通知配置已添加: {user_config.user_id}")
            return True
        except Exception as e:
            logger.error(f"添加用户配置失败: {user_config.user_id}, {e}")
            return False

    def update_user_config(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新用户通知配置

        Args:
            user_id: 用户ID
            updates: 更新内容

        Returns:
            bool: 是否成功更新
        """
        try:
            if user_id not in self.user_configs:
                logger.error(f"用户配置不存在: {user_id}")
                return False

            config = self.user_configs[user_id]

            # 更新配置属性
            for key, value in updates.items():
                if hasattr(config, key):
                    # 处理枚举类型
                    if key == "enabled_channels":
                        config.enabled_channels = [NotificationChannel(ch) for ch in value]
                    elif key == "alert_level_filters":
                        config.alert_level_filters = [AlertLevel(level) for level in value]
                    elif key == "alert_type_filters":
                        config.alert_type_filters = [AlertType(alert_type) for alert_type in value]
                    else:
                        setattr(config, key, value)

            logger.info(f"用户配置已更新: {user_id}")
            return True

        except Exception as e:
            logger.error(f"更新用户配置失败: {user_id}, {e}")
            return False

    def get_user_config(self, user_id: str) -> Optional[NotificationConfig]:
        """获取用户通知配置"""
        return self.user_configs.get(user_id)

    def get_notification_records(self, user_id: Optional[str] = None,
                               status: Optional[NotificationStatus] = None,
                               channel: Optional[NotificationChannel] = None,
                               limit: Optional[int] = None) -> List[NotificationRecord]:
        """
        获取通知记录

        Args:
            user_id: 用户ID过滤
            status: 状态过滤
            channel: 渠道过滤
            limit: 数量限制

        Returns:
            List[NotificationRecord]: 通知记录列表
        """
        filtered_records = self.notification_records.copy()

        if user_id:
            filtered_records = [r for r in filtered_records if r.user_id == user_id]

        if status:
            filtered_records = [r for r in filtered_records if r.status == status]

        if channel:
            filtered_records = [r for r in filtered_records if r.channel == channel]

        # 按创建时间倒序排序
        filtered_records.sort(key=lambda x: x.created_at, reverse=True)

        if limit:
            filtered_records = filtered_records[:limit]

        return filtered_records

    def get_notification_statistics(self) -> Dict[str, Any]:
        """获取通知统计信息"""
        try:
            total_notifications = len(self.notification_records)

            # 按状态统计
            status_distribution = {}
            for record in self.notification_records:
                status = record.status.value
                status_distribution[status] = status_distribution.get(status, 0) + 1

            # 按渠道统计
            channel_distribution = {}
            for record in self.notification_records:
                channel = record.channel.value
                channel_distribution[channel] = channel_distribution.get(channel, 0) + 1

            # 按优先级统计
            priority_distribution = {}
            for record in self.notification_records:
                priority = record.priority.value
                priority_distribution[priority] = priority_distribution.get(priority, 0) + 1

            # 成功率统计
            sent_count = status_distribution.get("sent", 0)
            success_rate = (sent_count / total_notifications * 100) if total_notifications > 0 else 0

            # 最近24小时统计
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_notifications = [r for r in self.notification_records if r.created_at > recent_cutoff]

            return {
                "total_notifications": total_notifications,
                "total_users": len(self.user_configs),
                "total_templates": len(self.notification_templates),
                "success_rate": success_rate,
                "recent_24h_count": len(recent_notifications),
                "status_distribution": status_distribution,
                "channel_distribution": channel_distribution,
                "priority_distribution": priority_distribution,
                "available_channels": [ch.value for ch in NotificationChannel],
                "available_statuses": [st.value for st in NotificationStatus],
                "available_priorities": [pr.value for pr in NotificationPriority]
            }

        except Exception as e:
            logger.error(f"获取通知统计失败: {e}")
            return {
                "total_notifications": 0,
                "total_users": 0,
                "total_templates": 0,
                "success_rate": 0,
                "recent_24h_count": 0,
                "status_distribution": {},
                "channel_distribution": {},
                "priority_distribution": {},
                "available_channels": [ch.value for ch in NotificationChannel],
                "available_statuses": [st.value for st in NotificationStatus],
                "available_priorities": [pr.value for pr in NotificationPriority]
            }
