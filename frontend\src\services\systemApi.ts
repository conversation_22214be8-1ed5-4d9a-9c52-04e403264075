/**
 * 系统相关API
 */

import { api } from './api';
import { SystemHealth, DashboardStats, PriceTrend, ApiResponse } from '../types';

// 价格趋势查询参数
interface PriceTrendParams {
  productIds?: string[];
  days?: number;
}

// 通知查询参数
interface NotificationParams {
  unread_only?: boolean;
  limit?: number;
}

export const systemApi = {
  // 获取系统健康状态
  getHealth: (): Promise<ApiResponse<SystemHealth>> => {
    return api.get('/health');
  },

  // 获取仪表板统计数据
  getDashboardStats: (): Promise<ApiResponse<DashboardStats>> => {
    return api.get('/api/v1/system/dashboard/stats');
  },

  // 获取价格趋势数据
  getPriceTrends: (params?: PriceTrendParams): Promise<ApiResponse<PriceTrend[]>> => {
    return api.get('/api/v1/analytics/price-trends', { params });
  },

  // 获取系统信息
  getSystemInfo: (): Promise<ApiResponse<any>> => {
    return api.get('/api/v1/system/info');
  },

  // 获取系统配置
  getSystemConfig: (): Promise<ApiResponse<any>> => {
    return api.get('/api/v1/system/config');
  },

  // 更新系统配置 - 调整为匹配后端端点
  updateSystemConfig: (configKey: string, config: any): Promise<ApiResponse<any>> => {
    return api.put(`/api/v1/system/config/${configKey}`, config);
  },

  // 获取系统日志
  getSystemLogs: (params?: { level?: string; limit?: number; offset?: number }): Promise<ApiResponse<any[]>> => {
    return api.get('/system/logs', { params });
  },

  // 获取通知列表
  getNotifications: (params?: NotificationParams): Promise<ApiResponse<any[]>> => {
    return api.get('/notifications', { params });
  },

  // 标记通知为已读
  markNotificationRead: (notificationId: string): Promise<ApiResponse<null>> => {
    return api.patch(`/notifications/${notificationId}/read`);
  },

  // 清除所有通知
  clearAllNotifications: (): Promise<ApiResponse<null>> => {
    return api.delete('/notifications');
  },

  // 获取用户列表（管理员功能）
  getUsers: (params?: { page?: number; page_size?: number }): Promise<ApiResponse<any>> => {
    return api.get('/admin/users', { params });
  },

  // 创建用户（管理员功能）
  createUser: (userData: any): Promise<ApiResponse<any>> => {
    return api.post('/admin/users', userData);
  },

  // 更新用户（管理员功能）
  updateUser: (userId: string, userData: any): Promise<ApiResponse<any>> => {
    return api.put(`/admin/users/${userId}`, userData);
  },

  // 删除用户（管理员功能）
  deleteUser: (userId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/admin/users/${userId}`);
  },

  // 重置用户密码（管理员功能）
  resetUserPassword: (userId: string): Promise<ApiResponse<{ temporary_password: string }>> => {
    return api.post(`/admin/users/${userId}/reset-password`);
  },

  // 获取审计日志
  getAuditLogs: (params?: { 
    user_id?: string; 
    action?: string; 
    resource_type?: string;
    date_from?: string;
    date_to?: string;
    page?: number;
    page_size?: number;
  }): Promise<ApiResponse<any>> => {
    return api.get('/admin/audit-logs', { params });
  },

  // 获取系统统计
  getSystemStats: (): Promise<ApiResponse<{
    users: { total: number; active: number; };
    products: { total: number; active: number; };
    monitors: { total: number; active: number; };
    storage: { used: number; total: number; };
  }>> => {
    return api.get('/admin/stats');
  },

  // 执行系统维护任务
  runMaintenanceTask: (taskType: string): Promise<ApiResponse<any>> => {
    return api.post('/admin/maintenance', { task_type: taskType });
  },

  // 备份系统数据
  backupSystem: (): Promise<ApiResponse<{ backup_id: string; filename: string }>> => {
    return api.post('/admin/backup');
  },

  // 获取备份列表
  getBackups: (): Promise<ApiResponse<any[]>> => {
    return api.get('/admin/backups');
  },

  // 恢复系统数据
  restoreSystem: (backupId: string): Promise<ApiResponse<null>> => {
    return api.post(`/admin/backups/${backupId}/restore`);
  },

  // 删除备份
  deleteBackup: (backupId: string): Promise<ApiResponse<null>> => {
    return api.delete(`/admin/backups/${backupId}`);
  },
};
