"""
数据标准化处理器

将不同平台的爬取结果标准化为统一格式
支持数据清洗、验证和格式转换
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import re
from decimal import Decimal, InvalidOperation
from urllib.parse import urlparse

from app.core.logging import get_logger
from .config_manager import Platform, ProductType

logger = get_logger(__name__)


@dataclass
class StandardizedProductData:
    """标准化商品数据"""
    # 基础信息
    url: str
    platform: Platform
    product_type: ProductType
    title: str
    price: Optional[Decimal] = None
    currency: str = "CNY"
    
    # 库存和销量
    stock: Optional[int] = None
    sales_count: Optional[int] = None
    
    # 评价信息
    rating: Optional[float] = None
    review_count: Optional[int] = None
    
    # 图片和描述
    main_image: Optional[str] = None
    images: List[str] = None
    description: Optional[str] = None
    
    # 卖家信息
    seller_name: Optional[str] = None
    seller_rating: Optional[float] = None
    
    # 供货商特有字段
    min_order_quantity: Optional[int] = None
    wholesale_price: Optional[Decimal] = None
    
    # 竞品特有字段
    brand: Optional[str] = None
    model: Optional[str] = None
    
    # 元数据
    crawled_at: datetime = None
    raw_data: Dict[str, Any] = None
    data_quality_score: float = 0.0
    
    def __post_init__(self):
        if self.images is None:
            self.images = []
        if self.crawled_at is None:
            self.crawled_at = datetime.now()
        if self.raw_data is None:
            self.raw_data = {}


class DataNormalizer:
    """数据标准化处理器"""
    
    def __init__(self):
        self.price_patterns = {
            'cny': [
                r'¥\s*(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d{2})?)\s*元',
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)'
            ],
            'usd': [
                r'\$\s*(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d{2})?)\s*USD'
            ]
        }
        
    def normalize_crawl_result(self, raw_data: Dict[str, Any], url: str, 
                             platform: Platform, product_type: ProductType) -> StandardizedProductData:
        """
        标准化爬取结果
        
        Args:
            raw_data: 原始爬取数据
            url: 商品URL
            platform: 平台类型
            product_type: 商品类型
            
        Returns:
            StandardizedProductData: 标准化后的商品数据
        """
        try:
            logger.debug(f"开始标准化数据: {url}")
            
            # 创建标准化数据对象
            normalized = StandardizedProductData(
                url=url,
                platform=platform,
                product_type=product_type,
                title=self._extract_title(raw_data),
                raw_data=raw_data
            )
            
            # 提取和标准化各个字段
            normalized.price = self._extract_price(raw_data)
            normalized.stock = self._extract_stock(raw_data)
            normalized.sales_count = self._extract_sales_count(raw_data)
            normalized.rating = self._extract_rating(raw_data)
            normalized.review_count = self._extract_review_count(raw_data)
            
            # 提取图片信息
            normalized.main_image = self._extract_main_image(raw_data)
            normalized.images = self._extract_images(raw_data)
            
            # 提取描述和卖家信息
            normalized.description = self._extract_description(raw_data)
            normalized.seller_name = self._extract_seller_name(raw_data)
            normalized.seller_rating = self._extract_seller_rating(raw_data)
            
            # 根据商品类型提取特定字段
            if product_type == ProductType.SUPPLIER:
                normalized.min_order_quantity = self._extract_min_order(raw_data)
                normalized.wholesale_price = self._extract_wholesale_price(raw_data)
            elif product_type == ProductType.COMPETITOR:
                normalized.brand = self._extract_brand(raw_data)
                normalized.model = self._extract_model(raw_data)

            # 对于所有类型都尝试提取品牌信息（如果存在）
            if not normalized.brand:
                normalized.brand = self._extract_brand(raw_data)
            
            # 计算数据质量分数
            normalized.data_quality_score = self._calculate_quality_score(normalized)
            
            logger.debug(f"数据标准化完成: {url}, 质量分数: {normalized.data_quality_score}")
            return normalized
            
        except Exception as e:
            logger.error(f"数据标准化失败: {url} - {e}")
            # 返回基础数据，避免完全失败
            return StandardizedProductData(
                url=url,
                platform=platform,
                product_type=product_type,
                title=str(raw_data.get('title', 'Unknown')),
                raw_data=raw_data,
                data_quality_score=0.1
            )
    
    def _extract_title(self, data: Dict[str, Any]) -> str:
        """提取商品标题"""
        title_fields = ['title', 'name', 'product_name', 'item_title']
        
        for field in title_fields:
            if field in data and data[field]:
                title = str(data[field]).strip()
                if title:
                    return self._clean_text(title)
        
        return "Unknown Product"
    
    def _extract_price(self, data: Dict[str, Any]) -> Optional[Decimal]:
        """提取价格"""
        price_fields = ['price', 'current_price', 'sale_price', 'unit_price']
        
        for field in price_fields:
            if field in data and data[field]:
                price_str = str(data[field])
                price = self._parse_price(price_str)
                if price:
                    return price
        
        return None
    
    def _parse_price(self, price_str: str) -> Optional[Decimal]:
        """解析价格字符串"""
        if not price_str:
            return None
            
        # 清理价格字符串
        price_str = price_str.strip().replace(',', '').replace(' ', '')
        
        # 尝试不同的价格模式
        for currency, patterns in self.price_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, price_str)
                if match:
                    try:
                        return Decimal(match.group(1))
                    except (InvalidOperation, IndexError):
                        continue
        
        # 尝试直接解析数字
        try:
            # 提取数字部分
            numbers = re.findall(r'\d+(?:\.\d{2})?', price_str)
            if numbers:
                return Decimal(numbers[0])
        except InvalidOperation:
            pass
            
        return None
    
    def _extract_stock(self, data: Dict[str, Any]) -> Optional[int]:
        """提取库存数量"""
        stock_fields = ['stock', 'inventory', 'available', 'quantity']
        
        for field in stock_fields:
            if field in data and data[field] is not None:
                try:
                    stock_str = str(data[field])
                    # 提取数字
                    numbers = re.findall(r'\d+', stock_str)
                    if numbers:
                        return int(numbers[0])
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_sales_count(self, data: Dict[str, Any]) -> Optional[int]:
        """提取销量"""
        sales_fields = ['sales', 'sold', 'sales_count', 'monthly_sales']
        
        for field in sales_fields:
            if field in data and data[field] is not None:
                try:
                    sales_str = str(data[field])
                    # 处理中文数字单位
                    sales_str = sales_str.replace('万', '0000').replace('千', '000')
                    # 提取数字
                    numbers = re.findall(r'\d+', sales_str)
                    if numbers:
                        return int(numbers[0])
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_rating(self, data: Dict[str, Any]) -> Optional[float]:
        """提取评分"""
        rating_fields = ['rating', 'score', 'star_rating', 'review_score']
        
        for field in rating_fields:
            if field in data and data[field] is not None:
                try:
                    rating_str = str(data[field])
                    # 提取数字（支持小数）
                    match = re.search(r'(\d+(?:\.\d+)?)', rating_str)
                    if match:
                        rating = float(match.group(1))
                        # 确保评分在合理范围内
                        if 0 <= rating <= 5:
                            return rating
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_review_count(self, data: Dict[str, Any]) -> Optional[int]:
        """提取评价数量"""
        review_fields = ['reviews', 'review_count', 'comment_count', 'feedback_count']
        
        for field in review_fields:
            if field in data and data[field] is not None:
                try:
                    review_str = str(data[field])
                    # 处理中文数字单位
                    review_str = review_str.replace('万', '0000').replace('千', '000')
                    # 提取数字
                    numbers = re.findall(r'\d+', review_str)
                    if numbers:
                        return int(numbers[0])
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_main_image(self, data: Dict[str, Any]) -> Optional[str]:
        """提取主图片"""
        image_fields = ['main_image', 'primary_image', 'image', 'thumbnail']
        
        for field in image_fields:
            if field in data and data[field]:
                image_url = str(data[field]).strip()
                if self._is_valid_url(image_url):
                    return image_url
        
        return None
    
    def _extract_images(self, data: Dict[str, Any]) -> List[str]:
        """提取所有图片"""
        images = []
        image_fields = ['images', 'image_list', 'photos', 'gallery']
        
        for field in image_fields:
            if field in data and data[field]:
                if isinstance(data[field], list):
                    for img in data[field]:
                        img_url = str(img).strip()
                        if self._is_valid_url(img_url):
                            images.append(img_url)
                elif isinstance(data[field], str):
                    img_url = str(data[field]).strip()
                    if self._is_valid_url(img_url):
                        images.append(img_url)
        
        return list(set(images))  # 去重
    
    def _extract_description(self, data: Dict[str, Any]) -> Optional[str]:
        """提取商品描述"""
        desc_fields = ['description', 'detail', 'content', 'summary']
        
        for field in desc_fields:
            if field in data and data[field]:
                desc = str(data[field]).strip()
                if desc:
                    return self._clean_text(desc)
        
        return None
    
    def _extract_seller_name(self, data: Dict[str, Any]) -> Optional[str]:
        """提取卖家名称"""
        seller_fields = ['seller', 'shop_name', 'store_name', 'vendor']
        
        for field in seller_fields:
            if field in data and data[field]:
                seller = str(data[field]).strip()
                if seller:
                    return self._clean_text(seller)
        
        return None
    
    def _extract_seller_rating(self, data: Dict[str, Any]) -> Optional[float]:
        """提取卖家评分"""
        seller_rating_fields = ['seller_rating', 'shop_rating', 'store_score']
        
        for field in seller_rating_fields:
            if field in data and data[field] is not None:
                try:
                    rating_str = str(data[field])
                    match = re.search(r'(\d+(?:\.\d+)?)', rating_str)
                    if match:
                        rating = float(match.group(1))
                        if 0 <= rating <= 5:
                            return rating
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_min_order(self, data: Dict[str, Any]) -> Optional[int]:
        """提取最小起订量（供货商商品）"""
        min_order_fields = ['min_order', 'minimum_order', 'moq', 'min_quantity']
        
        for field in min_order_fields:
            if field in data and data[field] is not None:
                try:
                    order_str = str(data[field])
                    numbers = re.findall(r'\d+', order_str)
                    if numbers:
                        return int(numbers[0])
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _extract_wholesale_price(self, data: Dict[str, Any]) -> Optional[Decimal]:
        """提取批发价格（供货商商品）"""
        wholesale_fields = ['wholesale_price', 'bulk_price', 'trade_price']
        
        for field in wholesale_fields:
            if field in data and data[field]:
                price_str = str(data[field])
                price = self._parse_price(price_str)
                if price:
                    return price
        
        return None
    
    def _extract_brand(self, data: Dict[str, Any]) -> Optional[str]:
        """提取品牌（竞品）"""
        brand_fields = ['brand', 'brand_name', 'manufacturer']
        
        for field in brand_fields:
            if field in data and data[field]:
                brand = str(data[field]).strip()
                if brand:
                    return self._clean_text(brand)
        
        return None
    
    def _extract_model(self, data: Dict[str, Any]) -> Optional[str]:
        """提取型号（竞品）"""
        model_fields = ['model', 'model_number', 'sku', 'part_number']
        
        for field in model_fields:
            if field in data and data[field]:
                model = str(data[field]).strip()
                if model:
                    return self._clean_text(model)
        
        return None
    
    def _calculate_quality_score(self, data: StandardizedProductData) -> float:
        """计算数据质量分数"""
        score = 0.0
        max_score = 10.0
        
        # 基础字段权重
        if data.title and data.title != "Unknown Product":
            score += 2.0
        if data.price is not None:
            score += 2.0
        if data.main_image:
            score += 1.0
        if data.description:
            score += 1.0
        if data.seller_name:
            score += 0.5
        
        # 可选字段权重
        if data.stock is not None:
            score += 1.0
        if data.sales_count is not None:
            score += 1.0
        if data.rating is not None:
            score += 1.0
        if data.images:
            score += 0.5
        
        return min(score / max_score, 1.0)
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        return text
    
    def _is_valid_url(self, url: str) -> bool:
        """验证URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
