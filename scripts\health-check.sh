#!/bin/bash

# Moniit 系统健康检查脚本
# 快速检查系统关键组件状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Moniit 系统健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -v, --verbose        详细输出"
    echo "  -q, --quiet          静默模式"
    echo "  -j, --json           JSON格式输出"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                   执行健康检查"
    echo "  $0 -v                详细模式检查"
    echo "  $0 -j                JSON格式输出"
}

# 检查Docker服务
check_docker() {
    if ! command -v docker &> /dev/null; then
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        return 1
    fi
    
    return 0
}

# 检查容器状态
check_containers() {
    local containers=(
        "moniit-backend"
        "moniit-frontend" 
        "moniit-redis"
    )
    
    local running=0
    local total=${#containers[@]}
    
    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "^${container}"; then
            ((running++))
        fi
    done
    
    if [ $running -eq $total ]; then
        return 0
    else
        return 1
    fi
}

# 检查HTTP服务
check_http_services() {
    local services=(
        "http://localhost:8000/health"
        "http://localhost:3000"
    )
    
    for service in "${services[@]}"; do
        if ! curl -f -s --max-time 5 "$service" &> /dev/null; then
            return 1
        fi
    done
    
    return 0
}

# 检查数据库
check_database() {
    if ! docker exec moniit-timescaledb-dev pg_isready -U moniit -d moniit &> /dev/null; then
        return 1
    fi
    return 0
}

# 检查Redis
check_redis() {
    if ! docker exec moniit-redis redis-cli ping &> /dev/null; then
        return 1
    fi
    return 0
}

# 获取系统资源信息
get_system_resources() {
    local cpu_usage="N/A"
    local memory_usage="N/A"
    local disk_usage="N/A"
    
    # CPU使用率
    if command -v top &> /dev/null; then
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' | awk -F'us' '{print $1}' | tr -d ' ' || echo "N/A")
    fi
    
    # 内存使用率
    if command -v free &> /dev/null; then
        local memory_info=$(free | grep Mem)
        local total_mem=$(echo $memory_info | awk '{print $2}')
        local used_mem=$(echo $memory_info | awk '{print $3}')
        if [ "$total_mem" -gt 0 ]; then
            memory_usage=$(echo "scale=1; $used_mem * 100 / $total_mem" | bc 2>/dev/null || echo "N/A")
        fi
    fi
    
    # 磁盘使用率
    if command -v df &> /dev/null; then
        disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//' || echo "N/A")
    fi
    
    echo "$cpu_usage,$memory_usage,$disk_usage"
}

# 执行健康检查
run_health_check() {
    local verbose=$1
    local quiet=$2
    local json_output=$3
    
    local results=()
    local overall_status="healthy"
    
    # 检查Docker
    if check_docker; then
        results+=("docker:healthy")
        [ "$verbose" = true ] && [ "$quiet" = false ] && log_success "Docker服务正常"
    else
        results+=("docker:unhealthy")
        overall_status="unhealthy"
        [ "$quiet" = false ] && log_error "Docker服务异常"
    fi
    
    # 检查容器
    if check_containers; then
        results+=("containers:healthy")
        [ "$verbose" = true ] && [ "$quiet" = false ] && log_success "容器状态正常"
    else
        results+=("containers:unhealthy")
        overall_status="unhealthy"
        [ "$quiet" = false ] && log_error "容器状态异常"
    fi
    
    # 检查HTTP服务
    if check_http_services; then
        results+=("http:healthy")
        [ "$verbose" = true ] && [ "$quiet" = false ] && log_success "HTTP服务正常"
    else
        results+=("http:unhealthy")
        overall_status="unhealthy"
        [ "$quiet" = false ] && log_error "HTTP服务异常"
    fi
    
    # 检查数据库
    if check_database; then
        results+=("database:healthy")
        [ "$verbose" = true ] && [ "$quiet" = false ] && log_success "数据库连接正常"
    else
        results+=("database:unhealthy")
        overall_status="unhealthy"
        [ "$quiet" = false ] && log_error "数据库连接异常"
    fi
    
    # 检查Redis
    if check_redis; then
        results+=("redis:healthy")
        [ "$verbose" = true ] && [ "$quiet" = false ] && log_success "Redis连接正常"
    else
        results+=("redis:unhealthy")
        overall_status="unhealthy"
        [ "$quiet" = false ] && log_error "Redis连接异常"
    fi
    
    # 获取系统资源
    local resources=$(get_system_resources)
    local cpu=$(echo "$resources" | cut -d, -f1)
    local memory=$(echo "$resources" | cut -d, -f2)
    local disk=$(echo "$resources" | cut -d, -f3)
    
    # 输出结果
    if [ "$json_output" = true ]; then
        echo "{"
        echo "  \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\","
        echo "  \"status\": \"$overall_status\","
        echo "  \"checks\": {"
        for result in "${results[@]}"; do
            local service=$(echo "$result" | cut -d: -f1)
            local status=$(echo "$result" | cut -d: -f2)
            echo "    \"$service\": \"$status\","
        done | sed '$ s/,$//'
        echo "  },"
        echo "  \"resources\": {"
        echo "    \"cpu_usage\": \"$cpu%\","
        echo "    \"memory_usage\": \"$memory%\","
        echo "    \"disk_usage\": \"$disk%\""
        echo "  }"
        echo "}"
    else
        if [ "$quiet" = false ]; then
            echo ""
            echo "Moniit 系统健康检查结果"
            echo "======================"
            echo "时间: $(date)"
            echo "状态: $overall_status"
            echo ""
            echo "组件状态:"
            for result in "${results[@]}"; do
                local service=$(echo "$result" | cut -d: -f1)
                local status=$(echo "$result" | cut -d: -f2)
                if [ "$status" = "healthy" ]; then
                    echo "  ✅ $service: 正常"
                else
                    echo "  ❌ $service: 异常"
                fi
            done
            echo ""
            echo "系统资源:"
            echo "  CPU: $cpu%"
            echo "  内存: $memory%"
            echo "  磁盘: $disk%"
            echo ""
        fi
    fi
    
    # 返回状态码
    if [ "$overall_status" = "healthy" ]; then
        return 0
    else
        return 1
    fi
}

# 主函数
main() {
    local verbose=false
    local quiet=false
    local json_output=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                verbose=true
                shift
                ;;
            -q|--quiet)
                quiet=true
                shift
                ;;
            -j|--json)
                json_output=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行健康检查
    run_health_check "$verbose" "$quiet" "$json_output"
}

# 执行主函数
main "$@"
