"""
测试专用应用配置
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from unittest.mock import AsyncMock

from app.api.v1 import api_router


def create_test_app() -> FastAPI:
    """创建测试专用的FastAPI应用"""
    app = FastAPI(
        title="电商商品监控系统 - 测试版",
        version="1.0.0",
        description="测试专用应用，禁用了中间件和数据库依赖"
    )
    
    # 添加CORS中间件（简化版）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 包含API路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 添加根路径
    @app.get("/")
    async def root():
        return {"message": "电商商品监控系统测试版"}
    
    # 添加健康检查
    @app.get("/health")
    async def health():
        return {"status": "ok"}
    
    return app


def create_mock_db_session():
    """创建模拟数据库会话"""
    session = AsyncMock()
    
    # 模拟查询结果
    session.execute.return_value.fetchall.return_value = []
    session.execute.return_value.fetchone.return_value = None
    session.execute.return_value.scalar.return_value = 0
    
    # 模拟提交和回滚
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    
    return session


async def mock_db_session_generator():
    """模拟数据库会话生成器"""
    session = create_mock_db_session()
    try:
        yield session
    finally:
        await session.close()
