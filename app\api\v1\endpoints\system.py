"""
系统管理API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import uuid4
import json

from app.core.database import get_db_session
from app.core.cache import get_cache_manager
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/info", summary="获取系统信息")
async def get_system_info():
    """获取系统基本信息"""
    logger.info("获取系统信息")
    
    from app.core.config import get_settings
    settings = get_settings()
    
    return {
        "name": settings.name,
        "version": settings.version,
        "environment": settings.environment,
        "debug": settings.debug
    }


@router.get("/health", summary="系统健康检查")
async def get_system_health():
    """获取系统健康状态"""
    logger.info("获取系统健康状态")

    try:
        # 简单的健康检查
        from app.core.config import get_settings
        settings = get_settings()

        # 基本健康状态
        health_status = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "services": {
                "api": "healthy",
                "database": "healthy",
                "cache": "healthy"
            }
        }

        return health_status

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "error": str(e)
        }


@router.get("/dashboard/stats", summary="获取仪表板统计数据")
async def get_dashboard_stats():
    """获取仪表板统计数据"""
    logger.info("获取仪表板统计数据")

    try:
        from datetime import datetime

        # 模拟仪表板统计数据
        dashboard_stats = {
            "total_products": 156,
            "active_monitors": 89,
            "total_price_records": 12543,
            "system_health": "healthy",
            "recent_alerts": 3,
            "today_stats": {
                "new_products": 5,
                "price_updates": 234,
                "alerts_generated": 8,
                "system_uptime": "99.8%"
            },
            "performance_metrics": {
                "avg_response_time": 0.125,
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 34.5
            },
            "last_updated": datetime.now().isoformat()
        }

        return dashboard_stats

    except Exception as e:
        logger.error(f"获取仪表板统计失败: {e}")
        return {
            "total_products": 0,
            "active_monitors": 0,
            "total_price_records": 0,
            "system_health": "error",
            "recent_alerts": 0,
            "error": str(e)
        }


@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check(
    db: AsyncSession = Depends(get_db_session)
):
    """详细的系统健康检查"""
    logger.info("执行详细健康检查")

    health_status = {
        "overall": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "services": {},
        "metrics": {}
    }
    
    # 检查数据库
    try:
        from app.core.database import get_database
        db_manager = await get_database()
        await db_manager.execute("SELECT 1")
        
        # 获取数据库统计
        db_size = await db_manager.get_database_size()
        connection_count = await db_manager.get_connection_count()
        
        health_status["services"]["database"] = {
            "status": "healthy",
            "size": db_size,
            "connections": connection_count
        }
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        health_status["services"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["overall"] = "unhealthy"
    
    # 检查缓存
    try:
        cache_manager = await get_cache_manager()
        cache_info = await cache_manager.get_info()
        
        health_status["services"]["cache"] = {
            "status": "healthy" if cache_info["redis"]["connected"] else "unhealthy",
            "stats": cache_info["stats"],
            "redis_info": cache_info["redis"]
        }
        
        if not cache_info["redis"]["connected"]:
            health_status["overall"] = "degraded"
            
    except Exception as e:
        logger.error(f"缓存健康检查失败: {e}")
        health_status["services"]["cache"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["overall"] = "degraded"
    
    # 检查系统资源
    try:
        import psutil
        import shutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        
        # 磁盘使用率
        disk = shutil.disk_usage("/")
        disk_percent = (disk.used / disk.total) * 100
        
        health_status["metrics"] = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "disk_percent": round(disk_percent, 2),
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # 检查资源警告
        if cpu_percent > 80 or memory.percent > 85 or disk_percent > 90:
            if health_status["overall"] == "healthy":
                health_status["overall"] = "warning"
                
    except Exception as e:
        logger.error(f"系统资源检查失败: {e}")
        health_status["metrics"]["error"] = str(e)
    
    return health_status


@router.get("/cache/stats", summary="获取缓存统计")
async def get_cache_stats():
    """获取缓存统计信息"""
    logger.info("获取缓存统计")
    
    try:
        cache_manager = await get_cache_manager()
        return await cache_manager.get_info()
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        return {"error": str(e)}


@router.post("/cache/clear", summary="清理缓存")
async def clear_cache(
    pattern: str = "*"
):
    """清理缓存"""
    logger.info(f"清理缓存 - 模式: {pattern}")
    
    try:
        cache_manager = await get_cache_manager()
        cleared_count = await cache_manager.clear_pattern(pattern)
        
        return {
            "message": "缓存清理成功",
            "cleared_count": cleared_count,
            "pattern": pattern
        }
    except Exception as e:
        logger.error(f"缓存清理失败: {e}")
        return {"error": str(e)}


@router.get("/logs", summary="获取系统日志")
async def get_system_logs(
    lines: int = Query(100, ge=1, le=10000, description="返回的日志行数"),
    level: str = Query("INFO", description="日志级别筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    start_time: Optional[str] = Query(None, description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: Optional[str] = Query(None, description="结束时间 (YYYY-MM-DD HH:MM:SS)")
):
    """获取系统日志"""
    logger.info(f"获取系统日志 - 行数: {lines}, 级别: {level}")

    try:
        import os
        import re
        from datetime import datetime
        from pathlib import Path

        # 定义日志文件路径
        log_files = []
        possible_log_paths = [
            "logs/app.log",
            "logs/moniit.log",
            "app.log",
            "moniit.log",
            "/var/log/moniit/app.log",
            "/tmp/moniit.log"
        ]

        # 查找存在的日志文件
        for log_path in possible_log_paths:
            if os.path.exists(log_path):
                log_files.append(log_path)

        # 如果没有找到日志文件，返回空结果
        if not log_files:
            logger.warning("未找到系统日志文件")
            return {
                "logs": [],
                "total_lines": 0,
                "level": level,
                "message": "未找到系统日志文件"
            }

        # 读取日志内容
        all_logs = []
        total_lines = 0

        # 日志级别优先级映射
        level_priority = {
            "DEBUG": 0,
            "INFO": 1,
            "WARNING": 2,
            "ERROR": 3,
            "CRITICAL": 4
        }

        min_level_priority = level_priority.get(level.upper(), 1)

        # 时间解析
        start_datetime = None
        end_datetime = None
        if start_time:
            try:
                start_datetime = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                raise HTTPException(status_code=400, detail="开始时间格式错误，应为 YYYY-MM-DD HH:MM:SS")

        if end_time:
            try:
                end_datetime = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                raise HTTPException(status_code=400, detail="结束时间格式错误，应为 YYYY-MM-DD HH:MM:SS")

        # 读取每个日志文件
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    file_lines = f.readlines()
                    total_lines += len(file_lines)

                    # 解析日志行
                    for line in file_lines[-lines*2:]:  # 读取更多行以便筛选
                        line = line.strip()
                        if not line:
                            continue

                        # 解析日志格式：时间戳 - 级别 - 模块 - 消息
                        log_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ - (\w+) - ([\w\.]+) - (.+)'
                        match = re.match(log_pattern, line)

                        if match:
                            timestamp_str, log_level, module, message = match.groups()

                            # 时间筛选
                            if start_datetime or end_datetime:
                                try:
                                    log_datetime = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                                    if start_datetime and log_datetime < start_datetime:
                                        continue
                                    if end_datetime and log_datetime > end_datetime:
                                        continue
                                except ValueError:
                                    continue

                            # 级别筛选
                            log_level_priority = level_priority.get(log_level.upper(), 0)
                            if log_level_priority < min_level_priority:
                                continue

                            # 关键词搜索
                            if search and search.lower() not in message.lower():
                                continue

                            all_logs.append({
                                "timestamp": timestamp_str,
                                "level": log_level,
                                "module": module,
                                "message": message,
                                "file": os.path.basename(log_file)
                            })
                        else:
                            # 如果不匹配标准格式，作为普通日志行处理
                            if search and search.lower() not in line.lower():
                                continue

                            all_logs.append({
                                "timestamp": None,
                                "level": "UNKNOWN",
                                "module": "system",
                                "message": line,
                                "file": os.path.basename(log_file)
                            })

                        # 限制返回数量
                        if len(all_logs) >= lines:
                            break

                    if len(all_logs) >= lines:
                        break

            except Exception as e:
                logger.error(f"读取日志文件失败 {log_file}: {str(e)}")
                continue

        # 按时间戳排序（最新的在前）
        all_logs.sort(key=lambda x: x["timestamp"] or "0000-00-00 00:00:00", reverse=True)

        # 限制返回数量
        result_logs = all_logs[:lines]

        logger.info(f"成功获取系统日志 - 返回: {len(result_logs)} 条")
        return {
            "logs": result_logs,
            "total_lines": total_lines,
            "returned_lines": len(result_logs),
            "level": level,
            "log_files": [os.path.basename(f) for f in log_files],
            "filters": {
                "level": level,
                "search": search,
                "start_time": start_time,
                "end_time": end_time
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统日志失败: {str(e)}")


@router.get("/metrics", summary="获取系统指标")
async def get_system_metrics():
    """获取系统性能指标"""
    logger.info("获取系统指标")
    
    try:
        import psutil
        import time
        
        # 系统负载
        load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
        
        # 网络统计
        net_io = psutil.net_io_counters()
        
        # 进程信息
        process_count = len(psutil.pids())
        
        return {
            "timestamp": time.time(),
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "load_avg": load_avg
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "network": {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            },
            "processes": {
                "count": process_count
            }
        }
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return {"error": str(e)}


# 数据模型定义
class SystemConfig(BaseModel):
    """系统配置模型"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="配置描述")
    category: str = Field("general", description="配置分类")


class UserCreate(BaseModel):
    """创建用户模型"""
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    role: str = Field("user", description="角色")
    is_active: bool = Field(True, description="是否激活")


class UserUpdate(BaseModel):
    """更新用户模型"""
    email: Optional[str] = Field(None, description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    role: Optional[str] = Field(None, description="角色")
    is_active: Optional[bool] = Field(None, description="是否激活")


class OperationLog(BaseModel):
    """操作日志模型"""
    id: str
    user_id: Optional[str]
    action: str
    resource: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime


# 模拟存储
SYSTEM_CONFIGS: Dict[str, Dict[str, Any]] = {
    "app.name": {
        "value": "电商商品监控系统",
        "description": "应用名称",
        "category": "general"
    },
    "app.version": {
        "value": "1.0.0",
        "description": "应用版本",
        "category": "general"
    },
    "monitoring.default_frequency": {
        "value": 24,
        "description": "默认监控频率（小时）",
        "category": "monitoring"
    },
    "alerts.email_enabled": {
        "value": True,
        "description": "是否启用邮件告警",
        "category": "alerts"
    }
}

MOCK_USERS: Dict[str, Dict[str, Any]] = {
    "admin": {
        "id": "admin-001",
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "系统管理员",
        "role": "admin",
        "is_active": True,
        "created_at": datetime.now(),
        "last_login": datetime.now()
    }
}

OPERATION_LOGS: List[Dict[str, Any]] = []


@router.get("/config", summary="获取系统配置")
async def get_system_config(
    category: Optional[str] = Query(None, description="配置分类筛选"),
    key: Optional[str] = Query(None, description="配置键筛选")
):
    """获取系统配置"""
    logger.info(f"获取系统配置 - 分类: {category}, 键: {key}")

    try:
        configs = {}

        for config_key, config_data in SYSTEM_CONFIGS.items():
            # 分类筛选
            if category and config_data.get("category") != category:
                continue

            # 键筛选
            if key and key not in config_key:
                continue

            configs[config_key] = config_data

        logger.info(f"成功获取系统配置 - 数量: {len(configs)}")
        return {
            "configs": configs,
            "total": len(configs)
        }

    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


@router.put("/config/{config_key}", summary="更新系统配置")
async def update_system_config(
    config_key: str,
    config: SystemConfig
):
    """更新系统配置"""
    logger.info(f"更新系统配置 - 键: {config_key}")

    try:
        SYSTEM_CONFIGS[config_key] = {
            "value": config.value,
            "description": config.description,
            "category": config.category
        }

        # 记录操作日志
        log_entry = {
            "id": str(uuid4()),
            "user_id": "admin-001",  # 实际应该从认证中获取
            "action": "update_config",
            "resource": f"config:{config_key}",
            "details": {
                "old_value": SYSTEM_CONFIGS.get(config_key, {}).get("value"),
                "new_value": config.value
            },
            "timestamp": datetime.now()
        }
        OPERATION_LOGS.append(log_entry)

        logger.info(f"系统配置更新成功 - 键: {config_key}")
        return {
            "message": "配置更新成功",
            "key": config_key,
            "value": config.value
        }

    except Exception as e:
        logger.error(f"更新系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新系统配置失败: {str(e)}")


@router.get("/users", summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    role: Optional[str] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选")
):
    """获取用户列表"""
    logger.info(f"获取用户列表 - skip: {skip}, limit: {limit}")

    try:
        # 筛选用户
        filtered_users = []
        for user_data in MOCK_USERS.values():
            # 角色筛选
            if role and user_data.get("role") != role:
                continue

            # 激活状态筛选
            if is_active is not None and user_data.get("is_active") != is_active:
                continue

            filtered_users.append(user_data)

        # 分页
        total = len(filtered_users)
        users = filtered_users[skip:skip + limit]

        logger.info(f"成功获取用户列表 - 总数: {total}, 返回: {len(users)}")
        return {
            "items": users,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@router.post("/users", summary="创建用户")
async def create_user(user_data: UserCreate):
    """创建新用户"""
    logger.info(f"创建用户 - 用户名: {user_data.username}")

    try:
        # 检查用户名是否已存在
        if user_data.username in MOCK_USERS:
            raise HTTPException(status_code=400, detail="用户名已存在")

        # 创建新用户
        user_id = str(uuid4())
        new_user = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "full_name": user_data.full_name,
            "role": user_data.role,
            "is_active": user_data.is_active,
            "created_at": datetime.now(),
            "last_login": None
        }

        MOCK_USERS[user_data.username] = new_user

        # 记录操作日志
        log_entry = {
            "id": str(uuid4()),
            "user_id": "admin-001",
            "action": "create_user",
            "resource": f"user:{user_id}",
            "details": {
                "username": user_data.username,
                "email": user_data.email,
                "role": user_data.role
            },
            "timestamp": datetime.now()
        }
        OPERATION_LOGS.append(log_entry)

        logger.info(f"用户创建成功 - ID: {user_id}")
        return new_user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")


@router.get("/users/{user_id}", summary="获取用户详情")
async def get_user(user_id: str):
    """获取用户详情"""
    logger.info(f"获取用户详情 - ID: {user_id}")

    try:
        # 查找用户
        user = None
        for user_data in MOCK_USERS.values():
            if user_data["id"] == user_id:
                user = user_data
                break

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        logger.info(f"成功获取用户详情 - ID: {user_id}")
        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")


@router.put("/users/{user_id}", summary="更新用户")
async def update_user(user_id: str, user_data: UserUpdate):
    """更新用户信息"""
    logger.info(f"更新用户 - ID: {user_id}")

    try:
        # 查找用户
        user = None
        username = None
        for uname, udata in MOCK_USERS.items():
            if udata["id"] == user_id:
                user = udata
                username = uname
                break

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 更新用户信息
        update_data = user_data.model_dump(exclude_unset=True)
        old_data = user.copy()

        for field, value in update_data.items():
            if hasattr(user, field) or field in user:
                user[field] = value

        user["updated_at"] = datetime.now()

        # 记录操作日志
        log_entry = {
            "id": str(uuid4()),
            "user_id": "admin-001",
            "action": "update_user",
            "resource": f"user:{user_id}",
            "details": {
                "old_data": old_data,
                "new_data": update_data
            },
            "timestamp": datetime.now()
        }
        OPERATION_LOGS.append(log_entry)

        logger.info(f"用户更新成功 - ID: {user_id}")
        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新用户失败: {str(e)}")


@router.delete("/users/{user_id}", summary="删除用户")
async def delete_user(user_id: str):
    """删除用户"""
    logger.info(f"删除用户 - ID: {user_id}")

    try:
        # 查找并删除用户
        user = None
        username = None
        for uname, udata in MOCK_USERS.items():
            if udata["id"] == user_id:
                user = udata
                username = uname
                break

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 不能删除管理员用户
        if user.get("role") == "admin":
            raise HTTPException(status_code=400, detail="不能删除管理员用户")

        del MOCK_USERS[username]

        # 记录操作日志
        log_entry = {
            "id": str(uuid4()),
            "user_id": "admin-001",
            "action": "delete_user",
            "resource": f"user:{user_id}",
            "details": {
                "username": user["username"],
                "email": user["email"]
            },
            "timestamp": datetime.now()
        }
        OPERATION_LOGS.append(log_entry)

        logger.info(f"用户删除成功 - ID: {user_id}")
        return {
            "message": "用户删除成功",
            "user_id": user_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")


@router.get("/permissions", summary="获取权限列表")
async def get_permissions():
    """获取系统权限列表"""
    logger.info("获取权限列表")

    try:
        permissions = {
            "admin": {
                "name": "管理员",
                "permissions": [
                    "system.config.read",
                    "system.config.write",
                    "user.read",
                    "user.write",
                    "user.delete",
                    "product.read",
                    "product.write",
                    "product.delete",
                    "monitor.read",
                    "monitor.write",
                    "analytics.read"
                ]
            },
            "user": {
                "name": "普通用户",
                "permissions": [
                    "product.read",
                    "monitor.read",
                    "analytics.read"
                ]
            },
            "viewer": {
                "name": "查看者",
                "permissions": [
                    "product.read",
                    "analytics.read"
                ]
            }
        }

        logger.info("成功获取权限列表")
        return {
            "roles": permissions,
            "total": len(permissions)
        }

    except Exception as e:
        logger.error(f"获取权限列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取权限列表失败: {str(e)}")


@router.get("/logs/operations", summary="获取操作日志")
async def get_operation_logs(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    action: Optional[str] = Query(None, description="操作类型筛选"),
    user_id: Optional[str] = Query(None, description="用户ID筛选"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间")
):
    """获取操作日志"""
    logger.info(f"获取操作日志 - skip: {skip}, limit: {limit}")

    try:
        # 筛选日志
        filtered_logs = []
        for log_entry in OPERATION_LOGS:
            # 操作类型筛选
            if action and log_entry.get("action") != action:
                continue

            # 用户ID筛选
            if user_id and log_entry.get("user_id") != user_id:
                continue

            # 时间范围筛选
            log_time = log_entry.get("timestamp")
            if start_date and log_time < start_date:
                continue
            if end_date and log_time > end_date:
                continue

            filtered_logs.append(log_entry)

        # 按时间倒序排列
        filtered_logs.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)

        # 分页
        total = len(filtered_logs)
        logs = filtered_logs[skip:skip + limit]

        logger.info(f"成功获取操作日志 - 总数: {total}, 返回: {len(logs)}")
        return {
            "items": logs,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"获取操作日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取操作日志失败: {str(e)}")
