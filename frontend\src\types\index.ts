/**
 * 通用类型定义
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code?: number;
}

// 分页参数
export interface PaginationParams {
  page: number;
  page_size: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  pages: number;
}

// 用户类型
export interface User {
  user_id: string;
  username: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  last_login?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  OPERATOR = 'operator',
  VIEWER = 'viewer',
  GUEST = 'guest'
}

// 商品类型
export interface Product {
  product_id: string;
  name: string;
  brand?: string;
  model?: string;
  category?: string;
  description?: string;
  image_url?: string;
  source_url?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

// 价格记录类型
export interface PriceRecord {
  record_id: string;
  product_id: string;
  price: number;
  currency: string;
  source: string;
  recorded_at: string;
  is_available: boolean;
  discount_info?: string;
}

// 监控任务类型
export interface MonitorTask {
  task_id: string;
  product_id: string;
  name: string;
  url: string;
  selector: string;
  interval_minutes: number;
  is_active: boolean;
  last_run?: string;
  next_run?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  ACTIVE = 'active',
  SUCCESS = 'success',
  FAILED = 'failed',
  PAUSED = 'paused'
}

// 供货商类型
export interface Supplier {
  supplier_id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  website?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 利润分析类型
export interface ProfitAnalysis {
  product_id: string;
  current_price: number;
  cost_price: number;
  profit_margin: number;
  profit_amount: number;
  recommended_price: number;
  analysis_date: string;
}

// 系统健康状态
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  timestamp: string;
  components: Record<string, ComponentHealth>;
  summary: {
    healthy: number;
    warning: number;
    critical: number;
    unknown: number;
  };
  uptime_seconds: number;
}

export interface ComponentHealth {
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  response_time_ms: number;
  details: Record<string, any>;
  last_check: string;
}

// 表单类型
export interface ProductForm {
  name: string;
  brand?: string;
  model?: string;
  category?: string;
  description?: string;
  image_url?: string;
  source_url?: string;
}

export interface MonitorTaskForm {
  product_id: string;
  name: string;
  url: string;
  selector: string;
  interval_minutes: number;
}

export interface SupplierForm {
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  website?: string;
}

// 搜索和过滤类型
export interface SearchParams {
  keyword?: string;
  category?: string;
  brand?: string;
  is_active?: boolean;
  date_from?: string;
  date_to?: string;
}

export interface MonitorSearchParams extends SearchParams {
  status?: TaskStatus;
  product_id?: string;
  platform?: string;
  search?: string;
}

// 统计数据类型
export interface DashboardStats {
  total_products: number;
  active_monitors: number;
  total_price_records: number;
  system_health: 'healthy' | 'warning' | 'critical';
  recent_alerts: number;
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
}

// 价格趋势数据
export interface PriceTrend {
  product_id: string;
  product_name: string;
  data_points: PriceTrendPoint[];
}

export interface PriceTrendPoint {
  date: string;
  price: number;
  source: string;
}

// 导出类型
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  date_range?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  title: string;
  icon?: string;
  children?: RouteConfig[];
  requireAuth?: boolean;
  roles?: UserRole[];
}
