"""
智能预警引擎

提供基于商品分类的智能预警、多维度预警规则体系、预警优先级和分级处理等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from app.core.logging import get_logger
from app.models.product import Product, ProductType
from app.services.analytics.comprehensive_analyzer import ComprehensiveAnalyzer
from app.services.profit_analysis.cost_manager import CostManager
from app.services.profit_analysis.profit_calculator import ProfitCalculator

logger = get_logger(__name__)


class AlertLevel(Enum):
    """预警级别"""
    CRITICAL = "critical"       # 紧急
    HIGH = "high"              # 重要
    MEDIUM = "medium"          # 一般
    LOW = "low"                # 低级
    INFO = "info"              # 信息


class AlertType(Enum):
    """预警类型"""
    PRICE_ANOMALY = "price_anomaly"           # 价格异常
    SALES_DECLINE = "sales_decline"           # 销量下滑
    INVENTORY_SHORTAGE = "inventory_shortage"  # 库存不足
    PROFIT_MARGIN_DROP = "profit_margin_drop" # 利润空间下降
    COMPETITOR_THREAT = "competitor_threat"    # 竞品威胁
    SUPPLIER_ISSUE = "supplier_issue"         # 供应商问题
    MARKET_OPPORTUNITY = "market_opportunity" # 市场机会
    QUALITY_CONCERN = "quality_concern"       # 质量问题


class AlertCategory(Enum):
    """预警分类"""
    COMPETITOR_ALERT = "competitor_alert"     # 竞品预警
    SUPPLIER_ALERT = "supplier_alert"         # 供应商预警
    INVENTORY_ALERT = "inventory_alert"       # 库存预警
    PROFIT_ALERT = "profit_alert"             # 利润预警
    MARKET_ALERT = "market_alert"             # 市场预警


class AlertStatus(Enum):
    """预警状态"""
    ACTIVE = "active"           # 活跃
    ACKNOWLEDGED = "acknowledged" # 已确认
    RESOLVED = "resolved"       # 已解决
    DISMISSED = "dismissed"     # 已忽略
    EXPIRED = "expired"         # 已过期


@dataclass
class AlertRule:
    """预警规则"""
    rule_id: str
    rule_name: str
    alert_type: AlertType
    alert_category: AlertCategory
    product_types: List[ProductType]  # 适用的商品类型
    conditions: Dict[str, Any]        # 触发条件
    alert_level: AlertLevel
    enabled: bool = True
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class Alert:
    """预警信息"""
    alert_id: str
    rule_id: str
    product_id: str
    alert_type: AlertType
    alert_category: AlertCategory
    alert_level: AlertLevel
    title: str
    description: str
    current_value: Any
    threshold_value: Any
    change_percentage: Optional[float] = None
    affected_metrics: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    status: AlertStatus = AlertStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.now)
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


@dataclass
class AlertSummary:
    """预警汇总"""
    total_alerts: int
    active_alerts: int
    critical_alerts: int
    high_alerts: int
    medium_alerts: int
    low_alerts: int
    category_distribution: Dict[AlertCategory, int]
    type_distribution: Dict[AlertType, int]
    recent_alerts: List[Alert]
    top_affected_products: List[str]


class AlertEngine:
    """智能预警引擎"""
    
    def __init__(self, comprehensive_analyzer: ComprehensiveAnalyzer,
                 cost_manager: CostManager, profit_calculator: ProfitCalculator):
        self.comprehensive_analyzer = comprehensive_analyzer
        self.cost_manager = cost_manager
        self.profit_calculator = profit_calculator
        
        # 预警存储
        self.alerts: List[Alert] = []
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 预警配置
        self.alert_config = {
            "max_alerts_per_product": 10,      # 每个商品最大预警数
            "alert_expiry_hours": 72,          # 预警过期时间（小时）
            "duplicate_alert_window": 24,      # 重复预警检测窗口（小时）
            "batch_processing_size": 100,      # 批量处理大小
        }
        
        # 智能过滤配置
        self.filter_config = {
            "min_alert_interval": 3600,       # 最小预警间隔（秒）
            "alert_fatigue_threshold": 20,    # 预警疲劳阈值
            "priority_boost_factors": {       # 优先级提升因子
                ProductType.COMPETITOR: 1.5,
                ProductType.SUPPLIER: 1.3,
                ProductType.OTHER: 1.0
            }
        }
        
        # 初始化默认规则
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """初始化默认预警规则"""
        # 竞品预警规则
        competitor_rules = [
            AlertRule(
                rule_id="competitor_price_drop",
                rule_name="竞品价格大幅下降",
                alert_type=AlertType.PRICE_ANOMALY,
                alert_category=AlertCategory.COMPETITOR_ALERT,
                product_types=[ProductType.COMPETITOR],
                conditions={
                    "price_drop_percentage": 0.10,  # 价格下降10%
                    "time_window_hours": 24
                },
                alert_level=AlertLevel.HIGH,
                description="竞品价格下降超过10%，可能影响市场竞争力"
            ),
            AlertRule(
                rule_id="competitor_sales_surge",
                rule_name="竞品销量激增",
                alert_type=AlertType.SALES_DECLINE,
                alert_category=AlertCategory.COMPETITOR_ALERT,
                product_types=[ProductType.COMPETITOR],
                conditions={
                    "sales_growth_percentage": 0.50,  # 销量增长50%
                    "time_window_days": 7
                },
                alert_level=AlertLevel.CRITICAL,
                description="竞品销量增长超过50%，需要关注市场动态"
            ),
            AlertRule(
                rule_id="new_competitor_detected",
                rule_name="发现新竞品",
                alert_type=AlertType.COMPETITOR_THREAT,
                alert_category=AlertCategory.COMPETITOR_ALERT,
                product_types=[ProductType.COMPETITOR],
                conditions={
                    "similarity_threshold": 0.80,  # 相似度80%以上
                    "price_difference_max": 0.20   # 价格差异20%以内
                },
                alert_level=AlertLevel.MEDIUM,
                description="发现新的竞争对手商品"
            )
        ]
        
        # 供应商预警规则
        supplier_rules = [
            AlertRule(
                rule_id="supplier_cost_increase",
                rule_name="供应商成本上涨",
                alert_type=AlertType.SUPPLIER_ISSUE,
                alert_category=AlertCategory.SUPPLIER_ALERT,
                product_types=[ProductType.SUPPLIER],
                conditions={
                    "cost_increase_percentage": 0.15,  # 成本上涨15%
                    "time_window_days": 30
                },
                alert_level=AlertLevel.HIGH,
                description="供应商成本上涨超过15%，影响利润空间"
            ),
            AlertRule(
                rule_id="supplier_stock_shortage",
                rule_name="供应商库存不足",
                alert_type=AlertType.INVENTORY_SHORTAGE,
                alert_category=AlertCategory.SUPPLIER_ALERT,
                product_types=[ProductType.SUPPLIER],
                conditions={
                    "stock_level_threshold": 100,     # 库存低于100
                    "days_of_supply": 7               # 供应天数少于7天
                },
                alert_level=AlertLevel.CRITICAL,
                description="供应商库存严重不足，可能影响供应"
            )
        ]
        
        # 利润预警规则
        profit_rules = [
            AlertRule(
                rule_id="profit_margin_drop",
                rule_name="利润率大幅下降",
                alert_type=AlertType.PROFIT_MARGIN_DROP,
                alert_category=AlertCategory.PROFIT_ALERT,
                product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
                conditions={
                    "margin_drop_percentage": 0.20,   # 利润率下降20%
                    "time_window_days": 14
                },
                alert_level=AlertLevel.HIGH,
                description="利润率下降超过20%，需要优化成本或调整价格"
            ),
            AlertRule(
                rule_id="negative_profit_margin",
                rule_name="利润率为负",
                alert_type=AlertType.PROFIT_MARGIN_DROP,
                alert_category=AlertCategory.PROFIT_ALERT,
                product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
                conditions={
                    "profit_margin_threshold": 0.0    # 利润率低于0
                },
                alert_level=AlertLevel.CRITICAL,
                description="商品利润率为负，亏损销售"
            )
        ]
        
        # 市场机会预警规则
        market_rules = [
            AlertRule(
                rule_id="high_profit_opportunity",
                rule_name="高利润机会",
                alert_type=AlertType.MARKET_OPPORTUNITY,
                alert_category=AlertCategory.MARKET_ALERT,
                product_types=[ProductType.COMPETITOR, ProductType.SUPPLIER],
                conditions={
                    "profit_margin_threshold": 0.40,  # 利润率超过40%
                    "market_demand_growth": 0.30      # 市场需求增长30%
                },
                alert_level=AlertLevel.MEDIUM,
                description="发现高利润机会，建议加大投入"
            )
        ]
        
        # 注册所有规则
        all_rules = competitor_rules + supplier_rules + profit_rules + market_rules
        for rule in all_rules:
            self.alert_rules[rule.rule_id] = rule
    
    async def process_products(self, products: List[Product]) -> List[Alert]:
        """
        处理商品列表，生成预警
        
        Args:
            products: 商品列表
        
        Returns:
            List[Alert]: 生成的预警列表
        """
        try:
            logger.info(f"开始处理商品预警: {len(products)} 个商品")
            
            new_alerts = []
            
            # 批量处理商品
            batch_size = self.alert_config["batch_processing_size"]
            for i in range(0, len(products), batch_size):
                batch = products[i:i + batch_size]
                batch_alerts = await self._process_product_batch(batch)
                new_alerts.extend(batch_alerts)
            
            # 智能过滤预警
            filtered_alerts = self._apply_intelligent_filtering(new_alerts)
            
            # 添加到预警列表
            self.alerts.extend(filtered_alerts)
            
            # 清理过期预警
            self._cleanup_expired_alerts()
            
            logger.info(f"预警处理完成: 生成 {len(filtered_alerts)} 个新预警")
            return filtered_alerts
            
        except Exception as e:
            logger.error(f"商品预警处理失败: {e}")
            return []
    
    async def _process_product_batch(self, products: List[Product]) -> List[Alert]:
        """处理商品批次"""
        batch_alerts = []
        
        for product in products:
            try:
                product_alerts = await self._evaluate_product_alerts(product)
                batch_alerts.extend(product_alerts)
            except Exception as e:
                logger.error(f"商品预警评估失败: {product.id}, {e}")
        
        return batch_alerts
    
    async def _evaluate_product_alerts(self, product: Product) -> List[Alert]:
        """评估单个商品的预警"""
        product_alerts = []
        
        # 获取适用的规则
        applicable_rules = self._get_applicable_rules(product)
        
        for rule in applicable_rules:
            try:
                alert = await self._evaluate_rule(product, rule)
                if alert:
                    product_alerts.append(alert)
            except Exception as e:
                logger.error(f"规则评估失败: {rule.rule_id}, {product.id}, {e}")
        
        return product_alerts
    
    def _get_applicable_rules(self, product: Product) -> List[AlertRule]:
        """获取适用于商品的规则"""
        applicable_rules = []
        
        for rule in self.alert_rules.values():
            if (rule.enabled and 
                product.product_type in rule.product_types):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    async def _evaluate_rule(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估单个规则"""
        try:
            # 根据规则类型进行不同的评估
            if rule.alert_type == AlertType.PRICE_ANOMALY:
                return await self._evaluate_price_anomaly(product, rule)
            elif rule.alert_type == AlertType.SALES_DECLINE:
                return await self._evaluate_sales_decline(product, rule)
            elif rule.alert_type == AlertType.INVENTORY_SHORTAGE:
                return await self._evaluate_inventory_shortage(product, rule)
            elif rule.alert_type == AlertType.PROFIT_MARGIN_DROP:
                return await self._evaluate_profit_margin_drop(product, rule)
            elif rule.alert_type == AlertType.COMPETITOR_THREAT:
                return await self._evaluate_competitor_threat(product, rule)
            elif rule.alert_type == AlertType.SUPPLIER_ISSUE:
                return await self._evaluate_supplier_issue(product, rule)
            elif rule.alert_type == AlertType.MARKET_OPPORTUNITY:
                return await self._evaluate_market_opportunity(product, rule)
            else:
                return None
                
        except Exception as e:
            logger.error(f"规则评估异常: {rule.rule_id}, {e}")
            return None
    
    async def _evaluate_price_anomaly(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估价格异常"""
        if not product.price or not product.price.current_price:
            return None
        
        conditions = rule.conditions
        price_drop_threshold = conditions.get("price_drop_percentage", 0.10)
        time_window_hours = conditions.get("time_window_hours", 24)
        
        # 这里简化处理，实际应该获取历史价格数据
        # 假设有历史价格数据进行比较
        current_price = product.price.current_price
        
        # 模拟价格变化检测（实际应该从数据库获取历史数据）
        if hasattr(product.price, 'previous_price') and product.price.previous_price:
            previous_price = product.price.previous_price
            price_change = (current_price - previous_price) / previous_price
            
            if price_change <= -price_drop_threshold:  # 价格下降超过阈值
                return Alert(
                    alert_id=f"price_anomaly_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    product_id=product.id,
                    alert_type=rule.alert_type,
                    alert_category=rule.alert_category,
                    alert_level=rule.alert_level,
                    title=f"价格异常预警 - {product.title[:30]}...",
                    description=f"商品价格下降 {abs(price_change):.1%}，超过预警阈值 {price_drop_threshold:.1%}",
                    current_value=current_price,
                    threshold_value=previous_price * (1 - price_drop_threshold),
                    change_percentage=price_change * 100,
                    affected_metrics={"current_price": current_price, "previous_price": previous_price},
                    recommendations=[
                        "分析价格下降原因",
                        "评估对竞争力的影响",
                        "考虑调整定价策略"
                    ],
                    expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                )
        
        return None

    async def _evaluate_sales_decline(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估销量下滑"""
        if not product.metrics or not product.metrics.sales_count:
            return None

        conditions = rule.conditions
        sales_growth_threshold = conditions.get("sales_growth_percentage", 0.50)
        time_window_days = conditions.get("time_window_days", 7)

        current_sales = product.metrics.sales_count

        # 模拟销量变化检测（实际应该从历史数据获取）
        if hasattr(product.metrics, 'previous_sales') and product.metrics.previous_sales:
            previous_sales = product.metrics.previous_sales
            if previous_sales > 0:
                sales_change = (current_sales - previous_sales) / previous_sales

                # 对于竞品，销量激增是预警；对于自己的商品，销量下滑是预警
                if (product.product_type == ProductType.COMPETITOR and
                    sales_change >= sales_growth_threshold):
                    return Alert(
                        alert_id=f"sales_surge_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        rule_id=rule.rule_id,
                        product_id=product.id,
                        alert_type=rule.alert_type,
                        alert_category=rule.alert_category,
                        alert_level=rule.alert_level,
                        title=f"竞品销量激增预警 - {product.title[:30]}...",
                        description=f"竞品销量增长 {sales_change:.1%}，超过预警阈值 {sales_growth_threshold:.1%}",
                        current_value=current_sales,
                        threshold_value=previous_sales * (1 + sales_growth_threshold),
                        change_percentage=sales_change * 100,
                        affected_metrics={"current_sales": current_sales, "previous_sales": previous_sales},
                        recommendations=[
                            "分析竞品销量增长原因",
                            "评估对市场份额的影响",
                            "制定应对策略"
                        ],
                        expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                    )
                elif (product.product_type in [ProductType.SUPPLIER, ProductType.OTHER] and
                      sales_change <= -0.20):  # 自己商品销量下滑20%
                    return Alert(
                        alert_id=f"sales_decline_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        rule_id=rule.rule_id,
                        product_id=product.id,
                        alert_type=rule.alert_type,
                        alert_category=rule.alert_category,
                        alert_level=AlertLevel.HIGH,
                        title=f"销量下滑预警 - {product.title[:30]}...",
                        description=f"商品销量下降 {abs(sales_change):.1%}，需要关注",
                        current_value=current_sales,
                        threshold_value=previous_sales * 0.8,
                        change_percentage=sales_change * 100,
                        affected_metrics={"current_sales": current_sales, "previous_sales": previous_sales},
                        recommendations=[
                            "分析销量下滑原因",
                            "检查市场竞争情况",
                            "优化营销策略"
                        ],
                        expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                    )

        return None

    async def _evaluate_inventory_shortage(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估库存不足"""
        if not product.metrics:
            return None

        conditions = rule.conditions
        stock_threshold = conditions.get("stock_level_threshold", 100)
        days_of_supply = conditions.get("days_of_supply", 7)

        # 获取库存数量，如果没有则返回None
        current_stock = getattr(product.metrics, 'stock_quantity', None)
        if current_stock is None:
            return None

        daily_sales = product.metrics.sales_count / 30 if product.metrics.sales_count else 0  # 假设月销量

        if current_stock <= stock_threshold:
            # 计算可供应天数
            supply_days = current_stock / daily_sales if daily_sales > 0 else float('inf')

            if supply_days <= days_of_supply:
                alert_level = AlertLevel.CRITICAL if supply_days <= 3 else AlertLevel.HIGH

                return Alert(
                    alert_id=f"inventory_shortage_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    product_id=product.id,
                    alert_type=rule.alert_type,
                    alert_category=rule.alert_category,
                    alert_level=alert_level,
                    title=f"库存不足预警 - {product.title[:30]}...",
                    description=f"当前库存 {current_stock} 件，预计可供应 {supply_days:.1f} 天",
                    current_value=current_stock,
                    threshold_value=stock_threshold,
                    affected_metrics={
                        "current_stock": current_stock,
                        "daily_sales": daily_sales,
                        "supply_days": supply_days
                    },
                    recommendations=[
                        "紧急补充库存",
                        "联系供应商加急供货",
                        "调整销售策略"
                    ],
                    expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                )

        return None

    async def _evaluate_profit_margin_drop(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估利润率下降"""
        try:
            # 使用利润计算器获取利润信息
            profit_calc_result = self.profit_calculator.calculate_profit(product, "default_supplier")

            # 检查是否是协程对象
            if hasattr(profit_calc_result, '__await__'):
                profit_calc = await profit_calc_result
            else:
                profit_calc = profit_calc_result

            if not profit_calc:
                return None

            conditions = rule.conditions
            margin_drop_threshold = conditions.get("margin_drop_percentage", 0.20)
            margin_threshold = conditions.get("profit_margin_threshold", 0.0)

            current_margin = profit_calc.profit_margin

            # 检查利润率是否为负
            if current_margin <= margin_threshold:
                return Alert(
                    alert_id=f"negative_margin_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    product_id=product.id,
                    alert_type=rule.alert_type,
                    alert_category=rule.alert_category,
                    alert_level=AlertLevel.CRITICAL,
                    title=f"利润率为负预警 - {product.title[:30]}...",
                    description=f"当前利润率 {current_margin:.2%}，商品亏损销售",
                    current_value=current_margin,
                    threshold_value=margin_threshold,
                    affected_metrics={
                        "profit_margin": current_margin,
                        "selling_price": profit_calc.selling_price,
                        "total_cost": profit_calc.total_cost
                    },
                    recommendations=[
                        "立即停止销售或调整价格",
                        "重新评估成本结构",
                        "寻找更优供应商"
                    ],
                    expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                )

            # 检查利润率下降（需要历史数据）
            # 这里简化处理，实际应该比较历史利润率

        except Exception as e:
            logger.error(f"利润率评估失败: {product.id}, {e}")

        return None

    async def _evaluate_competitor_threat(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估竞品威胁"""
        if product.product_type != ProductType.COMPETITOR:
            return None

        conditions = rule.conditions
        similarity_threshold = conditions.get("similarity_threshold", 0.80)
        price_difference_max = conditions.get("price_difference_max", 0.20)

        # 这里简化处理，实际应该进行商品相似度分析
        # 假设发现了新的竞品
        threat_score = 0.85  # 模拟威胁评分

        if threat_score >= similarity_threshold:
            return Alert(
                alert_id=f"competitor_threat_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                product_id=product.id,
                alert_type=rule.alert_type,
                alert_category=rule.alert_category,
                alert_level=rule.alert_level,
                title=f"新竞品威胁预警 - {product.title[:30]}...",
                description=f"发现高相似度竞品，威胁评分 {threat_score:.2%}",
                current_value=threat_score,
                threshold_value=similarity_threshold,
                affected_metrics={"threat_score": threat_score},
                recommendations=[
                    "深入分析竞品特点",
                    "评估价格竞争力",
                    "制定差异化策略"
                ],
                expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
            )

        return None

    async def _evaluate_supplier_issue(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估供应商问题"""
        if product.product_type != ProductType.SUPPLIER:
            return None

        try:
            # 获取供应商成本信息
            cost_alerts_result = self.cost_manager.get_cost_alerts(unresolved_only=True)

            # 检查是否是协程对象
            if hasattr(cost_alerts_result, '__await__'):
                cost_alerts = await cost_alerts_result
            else:
                cost_alerts = cost_alerts_result

            if not cost_alerts:
                return None

            # 检查是否有相关的成本预警
            for cost_alert in cost_alerts:
                if cost_alert.product_id == product.id:
                    conditions = rule.conditions
                    cost_increase_threshold = conditions.get("cost_increase_percentage", 0.15)

                    if (cost_alert.alert_type == "cost_increase" and
                        abs(cost_alert.change_percent) >= cost_increase_threshold * 100):

                        return Alert(
                            alert_id=f"supplier_cost_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                            rule_id=rule.rule_id,
                            product_id=product.id,
                            alert_type=rule.alert_type,
                            alert_category=rule.alert_category,
                            alert_level=rule.alert_level,
                            title=f"供应商成本上涨预警 - {product.title[:30]}...",
                            description=f"供应商成本上涨 {cost_alert.change_percent:.1f}%，影响利润空间",
                            current_value=cost_alert.current_cost,
                            threshold_value=cost_alert.threshold_value,
                            change_percentage=cost_alert.change_percent,
                            affected_metrics={
                                "current_cost": cost_alert.current_cost,
                                "previous_cost": cost_alert.previous_cost
                            },
                            recommendations=[
                                "与供应商协商价格",
                                "寻找替代供应商",
                                "调整产品定价"
                            ],
                            expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                        )

        except Exception as e:
            logger.error(f"供应商问题评估失败: {product.id}, {e}")

        return None

    async def _evaluate_market_opportunity(self, product: Product, rule: AlertRule) -> Optional[Alert]:
        """评估市场机会"""
        try:
            # 使用综合分析器获取市场机会
            report_result = self.comprehensive_analyzer.generate_comprehensive_report(product)

            # 检查是否是协程对象
            if hasattr(report_result, '__await__'):
                comprehensive_report = await report_result
            else:
                comprehensive_report = report_result
            if not comprehensive_report or not comprehensive_report.market_opportunities:
                return None

            conditions = rule.conditions
            profit_margin_threshold = conditions.get("profit_margin_threshold", 0.40)

            # 检查是否有高利润机会
            high_profit_opportunities = [
                op for op in comprehensive_report.market_opportunities
                if op.potential_impact >= profit_margin_threshold
            ]

            if high_profit_opportunities:
                best_opportunity = max(high_profit_opportunities, key=lambda x: x.potential_impact)

                return Alert(
                    alert_id=f"market_opportunity_{product.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    product_id=product.id,
                    alert_type=rule.alert_type,
                    alert_category=rule.alert_category,
                    alert_level=rule.alert_level,
                    title=f"市场机会预警 - {product.title[:30]}...",
                    description=f"发现高利润机会，潜在影响 {best_opportunity.potential_impact:.1%}",
                    current_value=best_opportunity.potential_impact,
                    threshold_value=profit_margin_threshold,
                    affected_metrics={
                        "opportunity_type": best_opportunity.opportunity_type.value,
                        "confidence_level": best_opportunity.confidence_level
                    },
                    recommendations=best_opportunity.required_actions[:3],
                    expires_at=datetime.now() + timedelta(hours=self.alert_config["alert_expiry_hours"])
                )

        except Exception as e:
            logger.error(f"市场机会评估失败: {product.id}, {e}")

        return None

    def _apply_intelligent_filtering(self, alerts: List[Alert]) -> List[Alert]:
        """应用智能过滤，避免预警疲劳"""
        if not alerts:
            return alerts

        filtered_alerts = []

        # 按商品分组
        product_alerts = {}
        for alert in alerts:
            if alert.product_id not in product_alerts:
                product_alerts[alert.product_id] = []
            product_alerts[alert.product_id].append(alert)

        for product_id, product_alert_list in product_alerts.items():
            # 1. 去重：移除重复预警
            unique_alerts = self._remove_duplicate_alerts(product_alert_list)

            # 2. 限制数量：每个商品最多保留指定数量的预警
            max_alerts = self.alert_config["max_alerts_per_product"]
            if len(unique_alerts) > max_alerts:
                # 按优先级和时间排序，保留最重要的
                unique_alerts.sort(key=lambda x: (
                    self._get_alert_priority_score(x),
                    x.created_at
                ), reverse=True)
                unique_alerts = unique_alerts[:max_alerts]

            # 3. 优先级提升：根据商品类型调整优先级
            for alert in unique_alerts:
                alert = self._adjust_alert_priority(alert)

            filtered_alerts.extend(unique_alerts)

        # 4. 全局过滤：避免预警疲劳
        filtered_alerts = self._apply_fatigue_filtering(filtered_alerts)

        return filtered_alerts

    def _remove_duplicate_alerts(self, alerts: List[Alert]) -> List[Alert]:
        """移除重复预警"""
        unique_alerts = []
        seen_combinations = set()

        for alert in alerts:
            # 创建唯一标识
            key = (alert.product_id, alert.alert_type.value, alert.alert_category.value)

            if key not in seen_combinations:
                seen_combinations.add(key)
                unique_alerts.append(alert)
            else:
                # 如果是重复预警，检查是否需要更新
                existing_alert = next(a for a in unique_alerts
                                    if (a.product_id, a.alert_type.value, a.alert_category.value) == key)

                # 如果新预警级别更高，替换现有预警
                if self._get_alert_priority_score(alert) > self._get_alert_priority_score(existing_alert):
                    unique_alerts.remove(existing_alert)
                    unique_alerts.append(alert)

        return unique_alerts

    def _get_alert_priority_score(self, alert: Alert) -> int:
        """获取预警优先级评分"""
        level_scores = {
            AlertLevel.CRITICAL: 100,
            AlertLevel.HIGH: 80,
            AlertLevel.MEDIUM: 60,
            AlertLevel.LOW: 40,
            AlertLevel.INFO: 20
        }

        base_score = level_scores.get(alert.alert_level, 0)

        # 根据预警类型调整评分
        type_multipliers = {
            AlertType.PROFIT_MARGIN_DROP: 1.5,
            AlertType.INVENTORY_SHORTAGE: 1.4,
            AlertType.COMPETITOR_THREAT: 1.3,
            AlertType.SUPPLIER_ISSUE: 1.2,
            AlertType.PRICE_ANOMALY: 1.1,
            AlertType.SALES_DECLINE: 1.0,
            AlertType.MARKET_OPPORTUNITY: 0.9,
            AlertType.QUALITY_CONCERN: 1.3
        }

        multiplier = type_multipliers.get(alert.alert_type, 1.0)
        return int(base_score * multiplier)

    def _adjust_alert_priority(self, alert: Alert) -> Alert:
        """根据商品类型调整预警优先级"""
        # 获取商品类型的优先级提升因子
        product_type = ProductType.OTHER  # 默认值，实际应该从商品信息获取
        boost_factor = self.filter_config["priority_boost_factors"].get(product_type, 1.0)

        if boost_factor > 1.0:
            # 提升预警级别
            current_level = alert.alert_level
            if current_level == AlertLevel.MEDIUM and boost_factor >= 1.3:
                alert.alert_level = AlertLevel.HIGH
            elif current_level == AlertLevel.HIGH and boost_factor >= 1.5:
                alert.alert_level = AlertLevel.CRITICAL

        return alert

    def _apply_fatigue_filtering(self, alerts: List[Alert]) -> List[Alert]:
        """应用预警疲劳过滤"""
        # 检查预警频率
        now = datetime.now()
        min_interval = timedelta(seconds=self.filter_config["min_alert_interval"])

        # 获取最近的预警
        recent_alerts = [
            a for a in self.alerts
            if now - a.created_at <= timedelta(hours=24)
        ]

        # 如果最近预警过多，只保留最高优先级的预警
        fatigue_threshold = self.filter_config["alert_fatigue_threshold"]
        if len(recent_alerts) >= fatigue_threshold:
            # 只保留CRITICAL和HIGH级别的预警
            alerts = [a for a in alerts if a.alert_level in [AlertLevel.CRITICAL, AlertLevel.HIGH]]

        return alerts

    def _cleanup_expired_alerts(self):
        """清理过期预警"""
        now = datetime.now()

        # 移除过期的预警
        active_alerts = []
        for alert in self.alerts:
            if alert.expires_at and now > alert.expires_at:
                alert.status = AlertStatus.EXPIRED
            elif alert.status == AlertStatus.ACTIVE:
                active_alerts.append(alert)

        # 保留活跃预警和最近的历史预警
        recent_cutoff = now - timedelta(days=7)
        self.alerts = active_alerts + [
            a for a in self.alerts
            if a.status != AlertStatus.ACTIVE and a.created_at > recent_cutoff
        ]

    async def get_alerts(self, status: Optional[AlertStatus] = None,
                        alert_level: Optional[AlertLevel] = None,
                        alert_category: Optional[AlertCategory] = None,
                        product_id: Optional[str] = None,
                        limit: Optional[int] = None) -> List[Alert]:
        """
        获取预警列表

        Args:
            status: 预警状态过滤
            alert_level: 预警级别过滤
            alert_category: 预警分类过滤
            product_id: 商品ID过滤
            limit: 返回数量限制

        Returns:
            List[Alert]: 过滤后的预警列表
        """
        filtered_alerts = self.alerts.copy()

        # 应用过滤条件
        if status:
            filtered_alerts = [a for a in filtered_alerts if a.status == status]

        if alert_level:
            filtered_alerts = [a for a in filtered_alerts if a.alert_level == alert_level]

        if alert_category:
            filtered_alerts = [a for a in filtered_alerts if a.alert_category == alert_category]

        if product_id:
            filtered_alerts = [a for a in filtered_alerts if a.product_id == product_id]

        # 按创建时间倒序排序
        filtered_alerts.sort(key=lambda x: x.created_at, reverse=True)

        # 应用数量限制
        if limit:
            filtered_alerts = filtered_alerts[:limit]

        return filtered_alerts

    async def acknowledge_alert(self, alert_id: str) -> bool:
        """
        确认预警

        Args:
            alert_id: 预警ID

        Returns:
            bool: 是否成功确认
        """
        try:
            alert = next((a for a in self.alerts if a.alert_id == alert_id), None)
            if alert and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.ACKNOWLEDGED
                alert.acknowledged_at = datetime.now()
                logger.info(f"预警已确认: {alert_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"确认预警失败: {alert_id}, {e}")
            return False

    async def resolve_alert(self, alert_id: str) -> bool:
        """
        解决预警

        Args:
            alert_id: 预警ID

        Returns:
            bool: 是否成功解决
        """
        try:
            alert = next((a for a in self.alerts if a.alert_id == alert_id), None)
            if alert and alert.status in [AlertStatus.ACTIVE, AlertStatus.ACKNOWLEDGED]:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = datetime.now()
                logger.info(f"预警已解决: {alert_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"解决预警失败: {alert_id}, {e}")
            return False

    async def dismiss_alert(self, alert_id: str) -> bool:
        """
        忽略预警

        Args:
            alert_id: 预警ID

        Returns:
            bool: 是否成功忽略
        """
        try:
            alert = next((a for a in self.alerts if a.alert_id == alert_id), None)
            if alert and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.DISMISSED
                logger.info(f"预警已忽略: {alert_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"忽略预警失败: {alert_id}, {e}")
            return False

    async def get_alert_summary(self) -> AlertSummary:
        """
        获取预警汇总信息

        Returns:
            AlertSummary: 预警汇总
        """
        try:
            # 统计各种预警数量
            total_alerts = len(self.alerts)
            active_alerts = len([a for a in self.alerts if a.status == AlertStatus.ACTIVE])

            # 按级别统计
            level_counts = {level: 0 for level in AlertLevel}
            for alert in self.alerts:
                if alert.status == AlertStatus.ACTIVE:
                    level_counts[alert.alert_level] += 1

            # 按分类统计
            category_distribution = {}
            for alert in self.alerts:
                if alert.status == AlertStatus.ACTIVE:
                    category = alert.alert_category
                    category_distribution[category] = category_distribution.get(category, 0) + 1

            # 按类型统计
            type_distribution = {}
            for alert in self.alerts:
                if alert.status == AlertStatus.ACTIVE:
                    alert_type = alert.alert_type
                    type_distribution[alert_type] = type_distribution.get(alert_type, 0) + 1

            # 最近预警
            recent_alerts = sorted(
                [a for a in self.alerts if a.status == AlertStatus.ACTIVE],
                key=lambda x: x.created_at,
                reverse=True
            )[:10]

            # 受影响最多的商品
            product_alert_counts = {}
            for alert in self.alerts:
                if alert.status == AlertStatus.ACTIVE:
                    product_id = alert.product_id
                    product_alert_counts[product_id] = product_alert_counts.get(product_id, 0) + 1

            top_affected_products = sorted(
                product_alert_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]

            return AlertSummary(
                total_alerts=total_alerts,
                active_alerts=active_alerts,
                critical_alerts=level_counts[AlertLevel.CRITICAL],
                high_alerts=level_counts[AlertLevel.HIGH],
                medium_alerts=level_counts[AlertLevel.MEDIUM],
                low_alerts=level_counts[AlertLevel.LOW],
                category_distribution=category_distribution,
                type_distribution=type_distribution,
                recent_alerts=recent_alerts,
                top_affected_products=[product_id for product_id, _ in top_affected_products]
            )

        except Exception as e:
            logger.error(f"获取预警汇总失败: {e}")
            return AlertSummary(
                total_alerts=0, active_alerts=0, critical_alerts=0,
                high_alerts=0, medium_alerts=0, low_alerts=0,
                category_distribution={}, type_distribution={},
                recent_alerts=[], top_affected_products=[]
            )

    def add_custom_rule(self, rule: AlertRule) -> bool:
        """
        添加自定义预警规则

        Args:
            rule: 预警规则

        Returns:
            bool: 是否成功添加
        """
        try:
            if rule.rule_id in self.alert_rules:
                logger.warning(f"规则已存在: {rule.rule_id}")
                return False

            self.alert_rules[rule.rule_id] = rule
            logger.info(f"自定义规则已添加: {rule.rule_id}")
            return True

        except Exception as e:
            logger.error(f"添加自定义规则失败: {rule.rule_id}, {e}")
            return False

    def update_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新预警规则

        Args:
            rule_id: 规则ID
            updates: 更新内容

        Returns:
            bool: 是否成功更新
        """
        try:
            if rule_id not in self.alert_rules:
                logger.warning(f"规则不存在: {rule_id}")
                return False

            rule = self.alert_rules[rule_id]

            # 更新规则属性
            for key, value in updates.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)

            logger.info(f"规则已更新: {rule_id}")
            return True

        except Exception as e:
            logger.error(f"更新规则失败: {rule_id}, {e}")
            return False

    def get_rule_statistics(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        try:
            total_rules = len(self.alert_rules)
            enabled_rules = len([r for r in self.alert_rules.values() if r.enabled])

            # 按类型统计
            type_distribution = {}
            for rule in self.alert_rules.values():
                alert_type = rule.alert_type.value
                type_distribution[alert_type] = type_distribution.get(alert_type, 0) + 1

            # 按分类统计
            category_distribution = {}
            for rule in self.alert_rules.values():
                category = rule.alert_category.value
                category_distribution[category] = category_distribution.get(category, 0) + 1

            # 按级别统计
            level_distribution = {}
            for rule in self.alert_rules.values():
                level = rule.alert_level.value
                level_distribution[level] = level_distribution.get(level, 0) + 1

            return {
                "total_rules": total_rules,
                "enabled_rules": enabled_rules,
                "disabled_rules": total_rules - enabled_rules,
                "type_distribution": type_distribution,
                "category_distribution": category_distribution,
                "level_distribution": level_distribution,
                "alert_types": [t.value for t in AlertType],
                "alert_categories": [c.value for c in AlertCategory],
                "alert_levels": [l.value for l in AlertLevel]
            }

        except Exception as e:
            logger.error(f"获取规则统计失败: {e}")
            return {
                "total_rules": 0,
                "enabled_rules": 0,
                "disabled_rules": 0,
                "type_distribution": {},
                "category_distribution": {},
                "level_distribution": {},
                "alert_types": [t.value for t in AlertType],
                "alert_categories": [c.value for c in AlertCategory],
                "alert_levels": [l.value for l in AlertLevel]
            }
