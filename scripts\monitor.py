#!/usr/bin/env python3
"""
系统监控脚本

提供系统监控、服务状态检查、自动重启等功能的命令行工具
"""

import asyncio
import argparse
import json
import sys
import os
import signal
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.monitoring.system_monitor import SystemMonitor
from app.monitoring.health_checker import HealthChecker
from app.monitoring.log_manager import LogManager, LogLevel, LogCategory


class MonitorScript:
    """监控脚本主类"""
    
    def __init__(self):
        self.monitor = None
        self.running = False
    
    async def start_monitoring(self, config_file: str = None):
        """启动监控"""
        try:
            print("🚀 启动系统监控...")
            
            # 创建监控器
            self.monitor = SystemMonitor(config_file or "monitor_config.json")
            
            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 启动监控
            await self.monitor.start_monitoring()
            
            print("✅ 系统监控已启动")
            print("按 Ctrl+C 停止监控")
            
            self.running = True
            
            # 保持运行
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️  收到停止信号")
        except Exception as e:
            print(f"❌ 启动监控失败: {e}")
        finally:
            if self.monitor:
                await self.monitor.stop_monitoring()
            print("🛑 系统监控已停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}")
        self.running = False
    
    async def check_health(self, components: str = None):
        """执行健康检查"""
        try:
            print("🔍 执行系统健康检查...")
            
            health_checker = HealthChecker()
            
            # 解析组件列表
            component_list = None
            if components:
                component_list = [c.strip() for c in components.split(',')]
            
            # 执行检查
            result = await health_checker.check_health(component_list)
            
            # 显示结果
            print(f"\n📊 健康检查结果 ({result['timestamp']})")
            print(f"总体状态: {self._get_status_emoji(result['status'])} {result['status'].upper()}")
            print(f"运行时间: {result.get('uptime_seconds', 0):.0f} 秒")
            
            print("\n📋 组件状态:")
            for name, component in result['components'].items():
                status_emoji = self._get_status_emoji(component['status'])
                print(f"  {status_emoji} {name}: {component['status']} ({component['response_time_ms']:.1f}ms)")
                if component['message']:
                    print(f"    💬 {component['message']}")
            
            # 显示摘要
            summary = result.get('summary', {})
            print(f"\n📈 状态摘要:")
            print(f"  ✅ 健康: {summary.get('healthy', 0)}")
            print(f"  ⚠️  警告: {summary.get('warning', 0)}")
            print(f"  ❌ 严重: {summary.get('critical', 0)}")
            print(f"  ❓ 未知: {summary.get('unknown', 0)}")
            
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
    
    def _get_status_emoji(self, status: str) -> str:
        """获取状态表情符号"""
        emoji_map = {
            "healthy": "✅",
            "warning": "⚠️",
            "critical": "❌",
            "unknown": "❓"
        }
        return emoji_map.get(status.lower(), "❓")
    
    async def show_status(self, config_file: str = None):
        """显示系统状态"""
        try:
            print("📊 获取系统状态...")
            
            monitor = SystemMonitor(config_file or "monitor_config.json")
            status = monitor.get_system_status()
            
            print(f"\n🖥️  系统状态")
            print(f"系统健康: {'✅ 健康' if status['system_healthy'] else '❌ 异常'}")
            print(f"运行时间: {status['uptime_seconds']:.0f} 秒")
            
            # 显示最新指标
            if status['latest_metrics']:
                metrics = status['latest_metrics']
                print(f"\n📈 最新指标 ({metrics['timestamp']})")
                print(f"  CPU: {metrics['cpu_percent']:.1f}%")
                print(f"  内存: {metrics['memory_percent']:.1f}%")
                print(f"  磁盘: {metrics['disk_percent']:.1f}%")
                print(f"  进程数: {metrics['process_count']}")
                if metrics.get('load_average'):
                    print(f"  负载: {metrics['load_average'][0]:.2f}")
            
            # 显示服务状态
            if status['services']:
                print(f"\n🔧 服务状态:")
                for service_name, service_info in status['services'].items():
                    status_emoji = "✅" if service_info['status'] == 'running' else "❌"
                    print(f"  {status_emoji} {service_name}: {service_info['status']}")
                    if service_info['process_id']:
                        print(f"    PID: {service_info['process_id']}")
                    if service_info['restart_count'] > 0:
                        print(f"    重启次数: {service_info['restart_count']}")
            
            # 显示最近告警
            if status['recent_alerts']:
                print(f"\n🚨 最近告警:")
                for alert in status['recent_alerts'][-5:]:  # 显示最近5个
                    level_emoji = "⚠️" if alert['level'] == 'warning' else "❌"
                    timestamp = datetime.fromisoformat(alert['timestamp']).strftime('%H:%M:%S')
                    print(f"  {level_emoji} [{timestamp}] {alert['message']}")
            
            # 显示统计信息
            stats = status['stats']
            print(f"\n📊 统计信息:")
            print(f"  总检查次数: {stats['total_checks']}")
            print(f"  告警发送: {stats['alerts_sent']}")
            print(f"  服务重启: {stats['services_restarted']}")
            if stats['last_check']:
                last_check = datetime.fromisoformat(stats['last_check']).strftime('%Y-%m-%d %H:%M:%S')
                print(f"  最后检查: {last_check}")
            
        except Exception as e:
            print(f"❌ 获取系统状态失败: {e}")
    
    async def show_metrics(self, hours: int = 1, config_file: str = None):
        """显示指标历史"""
        try:
            print(f"📈 获取最近 {hours} 小时的指标数据...")
            
            monitor = SystemMonitor(config_file or "monitor_config.json")
            metrics = monitor.get_metrics_history(hours)
            
            if not metrics:
                print("📭 没有找到指标数据")
                return
            
            print(f"\n📊 指标历史 (共 {len(metrics)} 条记录)")
            print("时间\t\t\tCPU%\t内存%\t磁盘%\t进程数")
            print("-" * 60)
            
            for metric in metrics[-20:]:  # 显示最近20条
                timestamp = datetime.fromisoformat(metric['timestamp']).strftime('%H:%M:%S')
                print(f"{timestamp}\t\t{metric['cpu_percent']:.1f}\t{metric['memory_percent']:.1f}\t{metric['disk_percent']:.1f}\t{metric['process_count']}")
            
            # 计算平均值
            if metrics:
                avg_cpu = sum(m['cpu_percent'] for m in metrics) / len(metrics)
                avg_memory = sum(m['memory_percent'] for m in metrics) / len(metrics)
                avg_disk = sum(m['disk_percent'] for m in metrics) / len(metrics)
                
                print(f"\n📊 平均值:")
                print(f"  CPU: {avg_cpu:.1f}%")
                print(f"  内存: {avg_memory:.1f}%")
                print(f"  磁盘: {avg_disk:.1f}%")
            
        except Exception as e:
            print(f"❌ 获取指标历史失败: {e}")
    
    def create_config(self, output_file: str = "monitor_config.json"):
        """创建监控配置文件"""
        try:
            print(f"📝 创建监控配置文件: {output_file}")
            
            config = {
                "monitor_config": {
                    "enabled": True,
                    "check_interval_seconds": 60,
                    "metrics_retention_hours": 24,
                    "alert_cooldown_minutes": 15,
                    "auto_restart_services": True,
                    "max_restart_attempts": 3,
                    "notification_enabled": True
                },
                "alert_thresholds": [
                    {
                        "metric_name": "cpu_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "memory_percent",
                        "warning_threshold": 80.0,
                        "critical_threshold": 90.0,
                        "duration_minutes": 5,
                        "enabled": True
                    },
                    {
                        "metric_name": "disk_percent",
                        "warning_threshold": 85.0,
                        "critical_threshold": 95.0,
                        "duration_minutes": 10,
                        "enabled": True
                    }
                ],
                "services": {
                    "moniit_api": {
                        "command": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000",
                        "working_dir": ".",
                        "auto_restart": True,
                        "max_restart_attempts": 3,
                        "restart_delay_seconds": 30,
                        "health_check_url": "http://localhost:8000/health",
                        "health_check_interval": 60
                    }
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 配置文件创建成功: {output_file}")
            print("💡 你可以编辑此文件来自定义监控配置")
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
    
    def show_logs(self, category: str = None, level: str = None, lines: int = 50):
        """显示日志"""
        try:
            print(f"📋 显示日志 (最近 {lines} 条)")
            
            log_manager = LogManager()
            
            # 转换参数
            log_category = None
            if category:
                try:
                    log_category = LogCategory(category.lower())
                except ValueError:
                    print(f"⚠️  无效的日志分类: {category}")
                    print(f"可用分类: {', '.join([c.value for c in LogCategory])}")
                    return
            
            log_level = None
            if level:
                try:
                    log_level = LogLevel(level.upper())
                except ValueError:
                    print(f"⚠️  无效的日志级别: {level}")
                    print(f"可用级别: {', '.join([l.value for l in LogLevel])}")
                    return
            
            # 搜索日志
            logs = log_manager.search_logs(
                category=log_category,
                level=log_level,
                limit=lines
            )
            
            if not logs:
                print("📭 没有找到匹配的日志")
                return
            
            print(f"\n📋 日志记录 (共 {len(logs)} 条)")
            print("时间\t\t级别\t分类\t\t消息")
            print("-" * 80)
            
            for log in logs:
                timestamp = datetime.fromisoformat(log['timestamp']).strftime('%H:%M:%S')
                level_str = log['level'][:4]
                category_str = log.get('category', 'N/A')[:10]
                message = log['message'][:50]
                print(f"{timestamp}\t{level_str}\t{category_str}\t{message}")
            
        except Exception as e:
            print(f"❌ 显示日志失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Moniit 系统监控工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动监控命令
    start_parser = subparsers.add_parser('start', help='启动系统监控')
    start_parser.add_argument('--config', '-c', help='配置文件路径')
    
    # 健康检查命令
    health_parser = subparsers.add_parser('health', help='执行健康检查')
    health_parser.add_argument('--components', help='要检查的组件 (逗号分隔)')
    
    # 状态查看命令
    status_parser = subparsers.add_parser('status', help='显示系统状态')
    status_parser.add_argument('--config', '-c', help='配置文件路径')
    
    # 指标查看命令
    metrics_parser = subparsers.add_parser('metrics', help='显示指标历史')
    metrics_parser.add_argument('--hours', type=int, default=1, help='查看小时数')
    metrics_parser.add_argument('--config', '-c', help='配置文件路径')
    
    # 配置创建命令
    config_parser = subparsers.add_parser('config', help='创建配置文件')
    config_parser.add_argument('--output', '-o', default='monitor_config.json', help='输出文件路径')
    
    # 日志查看命令
    logs_parser = subparsers.add_parser('logs', help='显示日志')
    logs_parser.add_argument('--category', help='日志分类')
    logs_parser.add_argument('--level', help='日志级别')
    logs_parser.add_argument('--lines', type=int, default=50, help='显示行数')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    script = MonitorScript()
    
    try:
        if args.command == 'start':
            await script.start_monitoring(args.config)
        elif args.command == 'health':
            await script.check_health(args.components)
        elif args.command == 'status':
            await script.show_status(args.config)
        elif args.command == 'metrics':
            await script.show_metrics(args.hours, args.config)
        elif args.command == 'config':
            script.create_config(args.output)
        elif args.command == 'logs':
            script.show_logs(args.category, args.level, args.lines)
        else:
            print(f"❌ 未知命令: {args.command}")
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n⏹️  操作已取消")
    except Exception as e:
        print(f"❌ 执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
