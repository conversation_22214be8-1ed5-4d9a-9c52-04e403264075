"""
用户认证模块

提供JWT令牌认证、用户登录和会话管理、密码安全存储功能
"""

from .auth_manager import AuthManager, AuthConfig
from .jwt_handler import J<PERSON><PERSON>and<PERSON>, TokenType
from .password_manager import PasswordManager
from .session_manager import SessionManager
from .permission_manager import PermissionManager, PermissionDeniedError
from .audit_logger import AuditLogger, AuditAction, AuditResult, AuditLevel, AuditEntry
from .models import User, UserRole, Permission, Session

__all__ = [
    "AuthManager",
    "AuthConfig",
    "JWTHandler",
    "TokenType",
    "PasswordManager",
    "SessionManager",
    "PermissionManager",
    "PermissionDeniedError",
    "AuditLogger",
    "AuditAction",
    "AuditResult",
    "AuditLevel",
    "AuditEntry",
    "User",
    "UserRole",
    "Permission",
    "Session"
]
