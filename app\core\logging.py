"""
日志配置模块
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any

from app.core.config import LoggingSettings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        # 创建结构化日志记录
        log_entry = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        
        import json
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging(config: LoggingSettings):
    """设置日志配置"""
    
    # 创建日志目录
    log_file_path = Path(config.file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    if config.console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, config.level.upper()))
        
        # 使用彩色格式化器
        console_formatter = ColoredFormatter(config.format)
        console_handler.setFormatter(console_formatter)
        
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if config.file:
        # 解析文件大小
        max_bytes = parse_size(config.max_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=config.file,
            maxBytes=max_bytes,
            backupCount=config.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, config.level.upper()))
        
        # 使用结构化格式化器
        file_formatter = StructuredFormatter()
        file_handler.setFormatter(file_formatter)
        
        root_logger.addHandler(file_handler)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.WARNING)
    logging.getLogger("celery").setLevel(logging.INFO)
    
    # 记录日志配置完成
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成 - 级别: {config.level}")


def parse_size(size_str: str) -> int:
    """解析文件大小字符串"""
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


class LoggerAdapter(logging.LoggerAdapter):
    """日志适配器，用于添加上下文信息"""
    
    def process(self, msg, kwargs):
        # 添加额外的上下文信息
        if 'extra' not in kwargs:
            kwargs['extra'] = {}
        
        kwargs['extra'].update(self.extra)
        return msg, kwargs


def get_request_logger(request_id: str, user_id: str = None) -> LoggerAdapter:
    """获取请求相关的日志器"""
    logger = logging.getLogger("request")
    extra = {"request_id": request_id}
    
    if user_id:
        extra["user_id"] = user_id
    
    return LoggerAdapter(logger, extra)


def get_task_logger(task_id: str, task_name: str = None) -> LoggerAdapter:
    """获取任务相关的日志器"""
    logger = logging.getLogger("task")
    extra = {"task_id": task_id}
    
    if task_name:
        extra["task_name"] = task_name
    
    return LoggerAdapter(logger, extra)


# 日志装饰器
def log_execution_time(logger: logging.Logger = None):
    """记录函数执行时间的装饰器"""
    import time
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            log = logger or logging.getLogger(func.__module__)
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                log.info(f"{func.__name__} 执行完成 - 耗时: {execution_time:.4f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                log.error(f"{func.__name__} 执行失败 - 耗时: {execution_time:.4f}s - 错误: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            log = logger or logging.getLogger(func.__module__)
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                log.info(f"{func.__name__} 执行完成 - 耗时: {execution_time:.4f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                log.error(f"{func.__name__} 执行失败 - 耗时: {execution_time:.4f}s - 错误: {e}")
                raise
        
        # 检查是否是异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def log_api_call(logger: logging.Logger = None):
    """记录API调用的装饰器"""
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            log = logger or logging.getLogger(func.__module__)
            
            # 记录API调用开始
            log.info(f"API调用开始: {func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                log.info(f"API调用成功: {func.__name__}")
                return result
            except Exception as e:
                log.error(f"API调用失败: {func.__name__} - 错误: {e}")
                raise
        
        return wrapper
    return decorator
