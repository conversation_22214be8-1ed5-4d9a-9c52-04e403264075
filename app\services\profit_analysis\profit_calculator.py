"""
利润计算器

提供实时利润率计算、利润趋势分析、定价建议等功能
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product
from .cost_manager import CostManager, CostType, CostRecord

logger = get_logger(__name__)


class ProfitMarginLevel(Enum):
    """利润率水平"""
    VERY_LOW = "very_low"       # 极低 (<5%)
    LOW = "low"                 # 低 (5-15%)
    MEDIUM = "medium"           # 中等 (15-30%)
    HIGH = "high"               # 高 (30-50%)
    VERY_HIGH = "very_high"     # 极高 (>50%)


class PricingStrategy(Enum):
    """定价策略"""
    COST_PLUS = "cost_plus"                 # 成本加成
    COMPETITIVE = "competitive"             # 竞争定价
    VALUE_BASED = "value_based"             # 价值定价
    PENETRATION = "penetration"             # 渗透定价
    SKIMMING = "skimming"                   # 撇脂定价


@dataclass
class ProfitCalculation:
    """利润计算结果"""
    product_id: str
    supplier_id: str
    selling_price: float
    total_cost: float
    gross_profit: float
    profit_margin: float
    profit_margin_level: ProfitMarginLevel
    cost_breakdown: Dict[CostType, float]
    calculation_date: datetime = field(default_factory=datetime.now)


@dataclass
class ProfitTrend:
    """利润趋势"""
    product_id: str
    supplier_id: str
    profit_history: List[ProfitCalculation]
    trend_direction: str  # increasing, decreasing, stable, volatile
    average_margin: float
    margin_volatility: float
    best_margin: float
    worst_margin: float
    trend_analysis: str


@dataclass
class PricingRecommendation:
    """定价建议"""
    product_id: str
    supplier_id: str
    current_price: float
    recommended_price: float
    price_change: float
    price_change_percent: float
    strategy: PricingStrategy
    expected_margin: float
    reasoning: str
    risk_assessment: str
    implementation_notes: List[str]


class ProfitCalculator:
    """利润计算器"""
    
    def __init__(self, cost_manager: CostManager):
        self.cost_manager = cost_manager
        
        # 利润计算历史
        self.profit_history: Dict[str, List[ProfitCalculation]] = {}
        
        # 定价策略配置
        self.pricing_strategies = {
            PricingStrategy.COST_PLUS: {
                "markup_range": (0.2, 0.5),  # 20%-50%加成
                "target_margin": 0.3
            },
            PricingStrategy.COMPETITIVE: {
                "markup_range": (0.1, 0.3),  # 10%-30%加成
                "target_margin": 0.2
            },
            PricingStrategy.VALUE_BASED: {
                "markup_range": (0.3, 0.8),  # 30%-80%加成
                "target_margin": 0.5
            }
        }
    
    async def calculate_profit(self, product: Product, supplier_id: str,
                             selling_price: Optional[float] = None) -> Optional[ProfitCalculation]:
        """
        计算商品利润
        
        Args:
            product: 商品信息
            supplier_id: 供应商ID
            selling_price: 销售价格（如果不提供则使用商品当前价格）
        
        Returns:
            ProfitCalculation: 利润计算结果
        """
        try:
            # 获取销售价格
            if selling_price is None:
                if product.price and product.price.current_price:
                    selling_price = product.price.current_price
                else:
                    logger.warning(f"无法获取商品销售价格: {product.id}")
                    return None
            
            # 获取成本信息
            cost_breakdown = await self._get_cost_breakdown(product.id, supplier_id)
            if not cost_breakdown:
                logger.warning(f"无法获取成本信息: {product.id}, {supplier_id}")
                return None
            
            # 计算总成本
            total_cost = sum(cost_breakdown.values())
            
            # 计算利润
            gross_profit = selling_price - total_cost
            profit_margin = gross_profit / selling_price if selling_price > 0 else 0
            
            # 确定利润率水平
            margin_level = self._determine_margin_level(profit_margin)
            
            # 创建利润计算结果
            calculation = ProfitCalculation(
                product_id=product.id,
                supplier_id=supplier_id,
                selling_price=selling_price,
                total_cost=total_cost,
                gross_profit=gross_profit,
                profit_margin=profit_margin,
                profit_margin_level=margin_level,
                cost_breakdown=cost_breakdown
            )
            
            # 保存到历史记录
            key = f"{product.id}_{supplier_id}"
            if key not in self.profit_history:
                self.profit_history[key] = []
            self.profit_history[key].append(calculation)
            
            logger.info(f"利润计算完成: {product.id}, 利润率: {profit_margin:.2%}")
            return calculation
            
        except Exception as e:
            logger.error(f"利润计算失败: {e}")
            return None
    
    async def analyze_profit_trend(self, product_id: str, supplier_id: str,
                                 days: int = 90) -> Optional[ProfitTrend]:
        """
        分析利润趋势
        
        Args:
            product_id: 商品ID
            supplier_id: 供应商ID
            days: 分析天数
        
        Returns:
            ProfitTrend: 利润趋势分析
        """
        try:
            key = f"{product_id}_{supplier_id}"
            
            if key not in self.profit_history:
                return None
            
            # 过滤历史记录
            cutoff_date = datetime.now() - timedelta(days=days)
            history = [calc for calc in self.profit_history[key] 
                      if calc.calculation_date >= cutoff_date]
            
            if len(history) < 2:
                return None
            
            # 按时间排序
            history.sort(key=lambda x: x.calculation_date)
            
            # 计算趋势
            margins = [calc.profit_margin for calc in history]
            trend_direction = self._calculate_trend_direction(margins)
            
            # 计算统计信息
            average_margin = statistics.mean(margins)
            margin_volatility = self._calculate_volatility(margins)
            best_margin = max(margins)
            worst_margin = min(margins)
            
            # 生成趋势分析
            trend_analysis = self._generate_trend_analysis(
                trend_direction, average_margin, margin_volatility, history
            )
            
            return ProfitTrend(
                product_id=product_id,
                supplier_id=supplier_id,
                profit_history=history,
                trend_direction=trend_direction,
                average_margin=average_margin,
                margin_volatility=margin_volatility,
                best_margin=best_margin,
                worst_margin=worst_margin,
                trend_analysis=trend_analysis
            )
            
        except Exception as e:
            logger.error(f"利润趋势分析失败: {e}")
            return None
    
    async def generate_pricing_recommendation(self, product: Product, supplier_id: str,
                                            target_margin: Optional[float] = None,
                                            strategy: Optional[PricingStrategy] = None) -> Optional[PricingRecommendation]:
        """
        生成定价建议
        
        Args:
            product: 商品信息
            supplier_id: 供应商ID
            target_margin: 目标利润率
            strategy: 定价策略
        
        Returns:
            PricingRecommendation: 定价建议
        """
        try:
            # 获取当前价格
            current_price = product.price.current_price if product.price else 0
            if current_price <= 0:
                logger.warning(f"商品价格无效: {product.id}")
                return None
            
            # 获取成本信息
            cost_breakdown = await self._get_cost_breakdown(product.id, supplier_id)
            if not cost_breakdown:
                return None
            
            total_cost = sum(cost_breakdown.values())
            
            # 确定定价策略
            if strategy is None:
                strategy = self._recommend_pricing_strategy(product, total_cost, current_price)
            
            # 确定目标利润率
            if target_margin is None:
                target_margin = self.pricing_strategies[strategy]["target_margin"]
            
            # 计算推荐价格
            recommended_price = total_cost / (1 - target_margin)
            
            # 计算价格变化
            price_change = recommended_price - current_price
            price_change_percent = price_change / current_price if current_price > 0 else 0
            
            # 生成建议说明
            reasoning = self._generate_pricing_reasoning(
                strategy, target_margin, total_cost, current_price, recommended_price
            )
            
            # 风险评估
            risk_assessment = self._assess_pricing_risk(
                price_change_percent, strategy, product
            )
            
            # 实施建议
            implementation_notes = self._generate_implementation_notes(
                price_change_percent, strategy
            )
            
            return PricingRecommendation(
                product_id=product.id,
                supplier_id=supplier_id,
                current_price=current_price,
                recommended_price=recommended_price,
                price_change=price_change,
                price_change_percent=price_change_percent,
                strategy=strategy,
                expected_margin=target_margin,
                reasoning=reasoning,
                risk_assessment=risk_assessment,
                implementation_notes=implementation_notes
            )
            
        except Exception as e:
            logger.error(f"定价建议生成失败: {e}")
            return None
    
    async def compare_supplier_profitability(self, product: Product, 
                                           supplier_ids: List[str]) -> Dict[str, Any]:
        """
        对比供应商盈利能力
        
        Args:
            product: 商品信息
            supplier_ids: 供应商ID列表
        
        Returns:
            Dict: 盈利能力对比结果
        """
        try:
            comparison_result = {
                "product_id": product.id,
                "suppliers": {},
                "best_supplier": None,
                "worst_supplier": None,
                "profit_range": {"min": 0, "max": 0},
                "average_margin": 0,
                "recommendations": []
            }
            
            supplier_profits = {}
            
            # 计算每个供应商的利润
            for supplier_id in supplier_ids:
                profit_calc = await self.calculate_profit(product, supplier_id)
                if profit_calc:
                    supplier_profits[supplier_id] = {
                        "profit_margin": profit_calc.profit_margin,
                        "gross_profit": profit_calc.gross_profit,
                        "total_cost": profit_calc.total_cost,
                        "margin_level": profit_calc.profit_margin_level.value,
                        "cost_breakdown": profit_calc.cost_breakdown
                    }
            
            if not supplier_profits:
                return comparison_result
            
            # 分析对比结果
            margins = [data["profit_margin"] for data in supplier_profits.values()]
            comparison_result["suppliers"] = supplier_profits
            comparison_result["profit_range"] = {"min": min(margins), "max": max(margins)}
            comparison_result["average_margin"] = statistics.mean(margins)
            
            # 找出最优和最差供应商
            best_supplier = max(supplier_profits.keys(), 
                              key=lambda x: supplier_profits[x]["profit_margin"])
            worst_supplier = min(supplier_profits.keys(), 
                               key=lambda x: supplier_profits[x]["profit_margin"])
            
            comparison_result["best_supplier"] = best_supplier
            comparison_result["worst_supplier"] = worst_supplier
            
            # 生成建议
            recommendations = self._generate_profitability_recommendations(supplier_profits)
            comparison_result["recommendations"] = recommendations
            
            return comparison_result
            
        except Exception as e:
            logger.error(f"供应商盈利能力对比失败: {e}")
            return {"error": str(e)}
    
    async def _get_cost_breakdown(self, product_id: str, supplier_id: str) -> Optional[Dict[CostType, float]]:
        """获取成本分解"""
        cost_breakdown = {}
        
        for cost_type in CostType:
            history = await self.cost_manager.get_cost_history(
                supplier_id, product_id, cost_type, days=30
            )
            
            if history and history.cost_records:
                # 使用最新的成本记录
                latest_record = max(history.cost_records, key=lambda x: x.created_at)
                cost_breakdown[cost_type] = latest_record.cost_value
        
        return cost_breakdown if cost_breakdown else None
    
    def _determine_margin_level(self, margin: float) -> ProfitMarginLevel:
        """确定利润率水平"""
        if margin < 0.05:
            return ProfitMarginLevel.VERY_LOW
        elif margin < 0.15:
            return ProfitMarginLevel.LOW
        elif margin < 0.30:
            return ProfitMarginLevel.MEDIUM
        elif margin < 0.50:
            return ProfitMarginLevel.HIGH
        else:
            return ProfitMarginLevel.VERY_HIGH
    
    def _calculate_trend_direction(self, margins: List[float]) -> str:
        """计算趋势方向"""
        if len(margins) < 2:
            return "stable"
        
        # 使用线性回归计算趋势
        n = len(margins)
        x = list(range(n))
        
        x_mean = sum(x) / n
        y_mean = sum(margins) / n
        
        numerator = sum((x[i] - x_mean) * (margins[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return "stable"
        
        slope = numerator / denominator
        
        # 计算波动性
        volatility = self._calculate_volatility(margins)
        
        if volatility > 0.3:  # 30%以上波动
            return "volatile"
        elif slope > 0.01:
            return "increasing"
        elif slope < -0.01:
            return "decreasing"
        else:
            return "stable"
    
    def _calculate_volatility(self, values: List[float]) -> float:
        """计算波动性"""
        if len(values) < 2:
            return 0.0
        
        try:
            mean_value = statistics.mean(values)
            if mean_value == 0:
                return 0.0
            
            variance = statistics.variance(values)
            std_dev = variance ** 0.5
            
            return std_dev / mean_value
        except Exception:
            return 0.0

    def _generate_trend_analysis(self, trend_direction: str, average_margin: float,
                               volatility: float, history: List[ProfitCalculation]) -> str:
        """生成趋势分析"""
        analysis_parts = []

        # 趋势描述
        if trend_direction == "increasing":
            analysis_parts.append("利润率呈上升趋势")
        elif trend_direction == "decreasing":
            analysis_parts.append("利润率呈下降趋势")
        elif trend_direction == "volatile":
            analysis_parts.append("利润率波动较大")
        else:
            analysis_parts.append("利润率相对稳定")

        # 平均利润率描述
        analysis_parts.append(f"平均利润率为 {average_margin:.1%}")

        # 波动性描述
        if volatility > 0.3:
            analysis_parts.append("波动性较高，需要关注成本和价格稳定性")
        elif volatility < 0.1:
            analysis_parts.append("波动性较低，利润表现稳定")

        # 最近表现
        if len(history) >= 2:
            recent_margin = history[-1].profit_margin
            previous_margin = history[-2].profit_margin

            if recent_margin > previous_margin * 1.05:
                analysis_parts.append("最近利润率有所改善")
            elif recent_margin < previous_margin * 0.95:
                analysis_parts.append("最近利润率有所下降")

        return "；".join(analysis_parts) + "。"

    def _recommend_pricing_strategy(self, product: Product, total_cost: float,
                                  current_price: float) -> PricingStrategy:
        """推荐定价策略"""
        current_margin = (current_price - total_cost) / current_price if current_price > 0 else 0

        # 基于当前利润率推荐策略
        if current_margin < 0.1:  # 低利润率
            return PricingStrategy.COST_PLUS
        elif current_margin > 0.4:  # 高利润率
            return PricingStrategy.VALUE_BASED
        else:
            return PricingStrategy.COMPETITIVE

    def _generate_pricing_reasoning(self, strategy: PricingStrategy, target_margin: float,
                                  total_cost: float, current_price: float,
                                  recommended_price: float) -> str:
        """生成定价建议说明"""
        reasoning_parts = []

        # 策略说明
        strategy_descriptions = {
            PricingStrategy.COST_PLUS: "基于成本加成的定价策略",
            PricingStrategy.COMPETITIVE: "基于市场竞争的定价策略",
            PricingStrategy.VALUE_BASED: "基于价值的定价策略"
        }

        reasoning_parts.append(strategy_descriptions.get(strategy.value, "定价策略"))

        # 目标利润率说明
        reasoning_parts.append(f"目标利润率设定为 {target_margin:.1%}")

        # 成本基础
        reasoning_parts.append(f"基于总成本 {total_cost:.2f} 元")

        # 价格调整说明
        price_change_percent = (recommended_price - current_price) / current_price if current_price > 0 else 0
        if abs(price_change_percent) > 0.05:  # 5%以上变化
            if price_change_percent > 0:
                reasoning_parts.append(f"建议提价 {price_change_percent:.1%} 以提高利润率")
            else:
                reasoning_parts.append(f"建议降价 {abs(price_change_percent):.1%} 以增强竞争力")
        else:
            reasoning_parts.append("当前价格基本合理")

        return "；".join(reasoning_parts) + "。"

    def _assess_pricing_risk(self, price_change_percent: float, strategy: PricingStrategy,
                           product: Product) -> str:
        """评估定价风险"""
        risk_factors = []

        # 价格变化风险
        if abs(price_change_percent) > 0.2:  # 20%以上变化
            risk_factors.append("价格变化幅度较大，可能影响销量")
        elif abs(price_change_percent) > 0.1:  # 10%以上变化
            risk_factors.append("价格变化适中，需要监控市场反应")

        # 策略风险
        if strategy == PricingStrategy.VALUE_BASED:
            risk_factors.append("价值定价需要强有力的品牌支撑")
        elif strategy == PricingStrategy.PENETRATION:
            risk_factors.append("渗透定价可能压缩利润空间")

        # 市场风险
        if product.metrics and product.metrics.sales_count:
            if product.metrics.sales_count < 1000:
                risk_factors.append("销量较低，价格敏感性可能较高")

        if not risk_factors:
            return "定价风险较低"
        else:
            return "；".join(risk_factors) + "。"

    def _generate_implementation_notes(self, price_change_percent: float,
                                     strategy: PricingStrategy) -> List[str]:
        """生成实施建议"""
        notes = []

        # 价格变化实施建议
        if abs(price_change_percent) > 0.15:  # 15%以上变化
            notes.append("建议分阶段调整价格，避免一次性大幅变动")
            notes.append("密切监控调价后的销量变化")
        elif abs(price_change_percent) > 0.05:  # 5%以上变化
            notes.append("可以一次性调整到位")
            notes.append("观察市场反应，必要时进行微调")

        # 策略实施建议
        if strategy == PricingStrategy.COST_PLUS:
            notes.append("确保成本计算的准确性")
            notes.append("定期更新成本数据")
        elif strategy == PricingStrategy.COMPETITIVE:
            notes.append("持续监控竞品价格变化")
            notes.append("保持价格竞争优势")
        elif strategy == PricingStrategy.VALUE_BASED:
            notes.append("强化产品价值传递")
            notes.append("提升客户对产品价值的认知")

        # 通用建议
        notes.append("建立价格调整的标准流程")
        notes.append("定期评估定价策略的有效性")

        return notes

    def _generate_profitability_recommendations(self, supplier_profits: Dict[str, Dict[str, Any]]) -> List[str]:
        """生成盈利能力建议"""
        recommendations = []

        if not supplier_profits:
            return recommendations

        # 找出最优供应商
        best_supplier = max(supplier_profits.keys(),
                          key=lambda x: supplier_profits[x]["profit_margin"])
        best_margin = supplier_profits[best_supplier]["profit_margin"]

        recommendations.append(f"推荐选择供应商 {best_supplier}，利润率最高 ({best_margin:.1%})")

        # 分析利润率差异
        margins = [data["profit_margin"] for data in supplier_profits.values()]
        margin_range = max(margins) - min(margins)
        avg_margin = statistics.mean(margins)

        if margin_range > 0.2:  # 利润率差异超过20%
            recommendations.append("供应商间利润率差异较大，优先选择高利润率供应商")

        # 成本分析建议
        for supplier_id, data in supplier_profits.items():
            if data["profit_margin"] < 0.1:  # 利润率低于10%
                recommendations.append(f"供应商 {supplier_id} 利润率过低，建议重新谈判成本或调整价格")

        # 整体建议
        if avg_margin < 0.15:
            recommendations.append("整体利润率偏低，建议优化成本结构或提高售价")
        elif avg_margin > 0.4:
            recommendations.append("利润率较高，可考虑适当降价以增强市场竞争力")

        return recommendations

    def get_profit_statistics(self) -> Dict[str, Any]:
        """获取利润统计信息"""
        total_calculations = sum(len(history) for history in self.profit_history.values())

        if total_calculations == 0:
            return {
                "total_calculations": 0,
                "total_products": 0,
                "average_margin": 0,
                "margin_distribution": {level.value: 0 for level in ProfitMarginLevel},
                "pricing_strategies": [strategy.value for strategy in PricingStrategy],
                "margin_levels": [level.value for level in ProfitMarginLevel]
            }

        # 收集所有利润率
        all_margins = []
        for history in self.profit_history.values():
            all_margins.extend([calc.profit_margin for calc in history])

        # 利润率分布
        margin_distribution = {level.value: 0 for level in ProfitMarginLevel}
        for margin in all_margins:
            level = self._determine_margin_level(margin)
            margin_distribution[level.value] += 1

        return {
            "total_calculations": total_calculations,
            "total_products": len(self.profit_history),
            "average_margin": statistics.mean(all_margins) if all_margins else 0,
            "margin_distribution": margin_distribution,
            "pricing_strategies": [strategy.value for strategy in PricingStrategy],
            "margin_levels": [level.value for level in ProfitMarginLevel]
        }
