#!/usr/bin/env python3
"""
Moniit 业务逻辑测试执行脚本

运行所有业务逻辑相关的测试，并生成测试报告
"""

import subprocess
import sys
import os
from datetime import datetime
import json


def run_command(command, description):
    """运行命令并返回结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"执行命令时出错: {e}")
        return False, "", str(e)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Moniit 业务逻辑测试套件")
    parser.add_argument("--mode", choices=["mock", "db", "all"], default="mock",
                       help="测试模式: mock(模拟数据), db(真实数据库), all(全部)")
    parser.add_argument("--generate-data", action="store_true",
                       help="是否生成测试数据")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")

    args = parser.parse_args()

    print("🧪 Moniit 业务逻辑测试套件")
    print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试模式: {args.mode}")
    print("=" * 60)

    # 如果需要生成测试数据
    if args.generate_data:
        print("🔧 生成测试数据...")
        # 首先尝试简化的数据生成脚本
        success, stdout, stderr = run_command(
            "python scripts/simple_test_data.py",
            "生成TimescaleDB测试数据"
        )
        if not success:
            print("⚠️ 简化脚本失败，尝试完整脚本...")
            success, stdout, stderr = run_command(
                "python scripts/generate_test_data.py",
                "生成TimescaleDB测试数据（完整版）"
            )
            if not success:
                print("❌ 测试数据生成失败，跳过数据库测试")
                print("💡 请确保TimescaleDB正在运行:")
                print("   docker-compose up db")
                if args.mode == "db":
                    return 1

    # 定义不同模式的测试套件
    mock_tests = [
        {
            "name": "商品监控业务逻辑测试",
            "command": "python -m pytest tests/test_product_monitoring_business.py -v",
            "file": "tests/test_product_monitoring_business.py",
            "type": "mock"
        },
        {
            "name": "价格趋势算法测试",
            "command": "python -m pytest tests/test_price_trend_algorithms.py -v",
            "file": "tests/test_price_trend_algorithms.py",
            "type": "mock"
        },
        {
            "name": "简化利润计算测试",
            "command": "python -m pytest tests/test_simple_profit_calculation.py -v",
            "file": "tests/test_simple_profit_calculation.py",
            "type": "mock"
        },
        {
            "name": "简化供货商管理测试",
            "command": "python -m pytest tests/test_simple_supplier_management.py -v",
            "file": "tests/test_simple_supplier_management.py",
            "type": "mock"
        },
        {
            "name": "简化爬虫集成测试",
            "command": "python -m pytest tests/test_simple_crawler_integration.py -v",
            "file": "tests/test_simple_crawler_integration.py",
            "type": "mock"
        }
    ]

    db_tests = [
        {
            "name": "简化数据库集成测试",
            "command": "python -m pytest tests/test_simple_db_integration.py -v",
            "file": "tests/test_simple_db_integration.py",
            "type": "db"
        },
        {
            "name": "修复后数据库集成测试",
            "command": "python -m pytest tests/test_fixed_database_integration.py -v",
            "file": "tests/test_fixed_database_integration.py",
            "type": "db"
        }
    ]

    # 移除有问题的旧测试文件，使用简化版本替代
    other_tests = []

    # 根据模式选择测试套件
    if args.mode == "mock":
        test_suites = mock_tests
    elif args.mode == "db":
        test_suites = db_tests
    else:  # all
        test_suites = mock_tests + db_tests + other_tests
    
    # 执行测试结果统计
    results = []
    total_passed = 0
    total_failed = 0
    total_tests = 0
    
    for suite in test_suites:
        print(f"\n\n🧪 开始执行: {suite['name']}")
        
        # 检查测试文件是否存在
        if not os.path.exists(suite['file']):
            print(f"⚠️  测试文件不存在: {suite['file']}")
            results.append({
                "name": suite['name'],
                "status": "SKIPPED",
                "reason": "文件不存在",
                "passed": 0,
                "failed": 0,
                "total": 0
            })
            continue
        
        success, stdout, stderr = run_command(suite['command'], suite['name'])
        
        # 解析测试结果
        passed = stdout.count(" PASSED")
        failed = stdout.count(" FAILED")
        total = passed + failed
        
        total_passed += passed
        total_failed += failed
        total_tests += total
        
        status = "PASSED" if success and failed == 0 else "FAILED" if failed > 0 else "ERROR"
        
        results.append({
            "name": suite['name'],
            "status": status,
            "passed": passed,
            "failed": failed,
            "total": total,
            "stdout": stdout,
            "stderr": stderr
        })
        
        # 显示结果
        if success:
            print(f"✅ {suite['name']} - 通过: {passed}, 失败: {failed}")
        else:
            print(f"❌ {suite['name']} - 通过: {passed}, 失败: {failed}")
    
    # 生成总结报告
    print(f"\n\n{'='*80}")
    print("📊 测试执行总结")
    print('='*80)
    
    for result in results:
        status_icon = "✅" if result['status'] == "PASSED" else "❌" if result['status'] == "FAILED" else "⚠️"
        print(f"{status_icon} {result['name']:<40} {result['status']:<10} ({result['passed']}/{result['total']})")
    
    print(f"\n📈 总体统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数量: {total_passed}")
    print(f"   失败数量: {total_failed}")
    print(f"   成功率: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "   成功率: N/A")
    
    # 保存详细报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "success_rate": (total_passed/total_tests*100) if total_tests > 0 else 0
        },
        "results": results
    }
    
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 根据模式运行特定的核心测试
    if args.mode == "mock" or args.mode == "all":
        print(f"\n\n🎯 运行核心模拟数据测试")
        core_command = "python -m pytest tests/test_product_monitoring_business.py tests/test_price_trend_algorithms.py -v --tb=short"
        success, stdout, stderr = run_command(core_command, "核心模拟数据测试")

        if success:
            print("\n✅ 核心模拟数据测试全部通过！")
        else:
            print("\n❌ 核心模拟数据测试存在问题")
            if args.mode == "mock":
                return 1

    # 原始数据库集成测试已移除，使用修复后的版本
    if args.mode == "db" or args.mode == "all":
        print(f"\n\n🎯 运行数据库集成测试")
        db_command = "python -m pytest tests/test_fixed_database_integration.py -v --tb=short"
        success, stdout, stderr = run_command(db_command, "修复后数据库集成测试")

        if success:
            print("\n✅ 数据库集成测试全部通过！")
        else:
            print("\n❌ 数据库集成测试存在问题")
            print("💡 提示: 请确保TimescaleDB正在运行并且已生成测试数据")
            print("   docker-compose up db")
            print("   python scripts/simple_test_data.py")
            if args.mode == "db":
                return 1

    # 最终结果
    if total_failed == 0:
        print(f"\n🎉 所有测试通过！成功率: 100%")
        return 0
    else:
        print(f"\n⚠️ 部分测试失败。成功率: {(total_passed/total_tests*100):.1f}%")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
