/**
 * 供货商状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Supplier, SupplierForm, PaginatedResponse, SearchParams } from '../../types';
import { supplierApi } from '../../services/supplierApi';

// 状态类型
interface SupplierState {
  suppliers: Supplier[];
  currentSupplier: Supplier | null;
  total: number;
  page: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
  searchParams: SearchParams;
  selectedSuppliers: string[];
}

// 初始状态
const initialState: SupplierState = {
  suppliers: [],
  currentSupplier: null,
  total: 0,
  page: 1,
  pageSize: 20,
  isLoading: false,
  error: null,
  searchParams: {},
  selectedSuppliers: [],
};

// 异步actions
export const fetchSuppliersAsync = createAsyncThunk(
  'supplier/fetchSuppliers',
  async (params: { page?: number; page_size?: number; search?: SearchParams }, { rejectWithValue }) => {
    try {
      const response = await supplierApi.getSuppliers(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取供货商列表失败');
    }
  }
);

export const fetchSupplierByIdAsync = createAsyncThunk(
  'supplier/fetchSupplierById',
  async (supplierId: string, { rejectWithValue }) => {
    try {
      const response = await supplierApi.getSupplierById(supplierId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取供货商详情失败');
    }
  }
);

export const createSupplierAsync = createAsyncThunk(
  'supplier/createSupplier',
  async (supplierData: SupplierForm, { rejectWithValue }) => {
    try {
      const response = await supplierApi.createSupplier(supplierData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建供货商失败');
    }
  }
);

export const updateSupplierAsync = createAsyncThunk(
  'supplier/updateSupplier',
  async ({ supplierId, supplierData }: { supplierId: string; supplierData: Partial<SupplierForm> }, { rejectWithValue }) => {
    try {
      const response = await supplierApi.updateSupplier(supplierId, supplierData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新供货商失败');
    }
  }
);

export const deleteSupplierAsync = createAsyncThunk(
  'supplier/deleteSupplier',
  async (supplierId: string, { rejectWithValue }) => {
    try {
      await supplierApi.deleteSupplier(supplierId);
      return supplierId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除供货商失败');
    }
  }
);

// Slice
const supplierSlice = createSlice({
  name: 'supplier',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentSupplier: (state, action: PayloadAction<Supplier | null>) => {
      state.currentSupplier = action.payload;
    },
    setSearchParams: (state, action: PayloadAction<SearchParams>) => {
      state.searchParams = action.payload;
      state.page = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.page = 1;
    },
    setSelectedSuppliers: (state, action: PayloadAction<string[]>) => {
      state.selectedSuppliers = action.payload;
    },
    toggleSupplierSelection: (state, action: PayloadAction<string>) => {
      const supplierId = action.payload;
      const index = state.selectedSuppliers.indexOf(supplierId);
      if (index > -1) {
        state.selectedSuppliers.splice(index, 1);
      } else {
        state.selectedSuppliers.push(supplierId);
      }
    },
    clearSelection: (state) => {
      state.selectedSuppliers = [];
    },
  },
  extraReducers: (builder) => {
    // 获取供货商列表
    builder
      .addCase(fetchSuppliersAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSuppliersAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload as PaginatedResponse<Supplier>;
        state.suppliers = data.items;
        state.total = data.total;
        state.page = data.page;
        state.pageSize = data.page_size;
        state.error = null;
      })
      .addCase(fetchSuppliersAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取供货商详情
    builder
      .addCase(fetchSupplierByIdAsync.fulfilled, (state, action) => {
        state.currentSupplier = action.payload;
      });

    // 创建供货商
    builder
      .addCase(createSupplierAsync.fulfilled, (state, action) => {
        state.suppliers.unshift(action.payload);
        state.total += 1;
      });

    // 更新供货商
    builder
      .addCase(updateSupplierAsync.fulfilled, (state, action) => {
        const updatedSupplier = action.payload;
        const index = state.suppliers.findIndex(s => s.supplier_id === updatedSupplier.supplier_id);
        if (index > -1) {
          state.suppliers[index] = updatedSupplier;
        }
        if (state.currentSupplier?.supplier_id === updatedSupplier.supplier_id) {
          state.currentSupplier = updatedSupplier;
        }
      });

    // 删除供货商
    builder
      .addCase(deleteSupplierAsync.fulfilled, (state, action) => {
        const supplierId = action.payload;
        state.suppliers = state.suppliers.filter(s => s.supplier_id !== supplierId);
        state.total -= 1;
        if (state.currentSupplier?.supplier_id === supplierId) {
          state.currentSupplier = null;
        }
        state.selectedSuppliers = state.selectedSuppliers.filter(id => id !== supplierId);
      });
  },
});

// 导出actions
export const {
  clearError,
  setCurrentSupplier,
  setSearchParams,
  setPage,
  setPageSize,
  setSelectedSuppliers,
  toggleSupplierSelection,
  clearSelection,
} = supplierSlice.actions;

// 选择器
export const selectSuppliers = (state: { supplier: SupplierState }) => state.supplier.suppliers;
export const selectCurrentSupplier = (state: { supplier: SupplierState }) => state.supplier.currentSupplier;
export const selectSuppliersLoading = (state: { supplier: SupplierState }) => state.supplier.isLoading;
export const selectSuppliersError = (state: { supplier: SupplierState }) => state.supplier.error;
export const selectSuppliersPagination = (state: { supplier: SupplierState }) => ({
  total: state.supplier.total,
  page: state.supplier.page,
  pageSize: state.supplier.pageSize,
});

export default supplierSlice.reducer;
