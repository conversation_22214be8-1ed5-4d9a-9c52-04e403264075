#!/bin/bash

# Moniit 系统清理脚本
# 清理日志文件、临时文件、旧数据等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
LOG_RETENTION_DAYS=30
BACKUP_RETENTION_DAYS=90
TEMP_RETENTION_DAYS=7
DB_VACUUM_THRESHOLD=100  # MB

# 显示帮助信息
show_help() {
    echo "Moniit 系统清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -l, --logs           清理日志文件"
    echo "  -b, --backups        清理旧备份文件"
    echo "  -t, --temp           清理临时文件"
    echo "  -d, --database       清理数据库"
    echo "  -c, --containers     清理Docker容器和镜像"
    echo "  -a, --all            执行所有清理操作"
    echo "  -f, --force          强制清理（不询问确认）"
    echo "  -n, --dry-run        预览模式（不实际删除）"
    echo "  --log-days DAYS      日志保留天数 [默认: 30]"
    echo "  --backup-days DAYS   备份保留天数 [默认: 90]"
    echo "  --temp-days DAYS     临时文件保留天数 [默认: 7]"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -a                执行所有清理操作"
    echo "  $0 -l --log-days 7   清理7天前的日志"
    echo "  $0 -n -a             预览所有清理操作"
}

# 确认操作
confirm_action() {
    local action=$1
    
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    echo ""
    log_warning "即将执行: $action"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 1
    fi
    
    return 0
}

# 获取文件大小（人类可读格式）
get_size() {
    local path=$1
    
    if [ -e "$path" ]; then
        du -sh "$path" 2>/dev/null | cut -f1 || echo "0B"
    else
        echo "0B"
    fi
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件 (保留 $LOG_RETENTION_DAYS 天)..."
    
    local log_dirs=(
        "./logs"
        "./data/logs"
    )
    
    local total_cleaned=0
    local total_size_before=0
    local total_size_after=0
    
    for log_dir in "${log_dirs[@]}"; do
        if [ ! -d "$log_dir" ]; then
            continue
        fi
        
        log_info "处理目录: $log_dir"
        
        # 计算清理前大小
        local size_before=$(du -sb "$log_dir" 2>/dev/null | cut -f1 || echo "0")
        total_size_before=$((total_size_before + size_before))
        
        # 查找旧日志文件
        local old_files=$(find "$log_dir" -name "*.log*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null || true)
        
        if [ -n "$old_files" ]; then
            local file_count=$(echo "$old_files" | wc -l)
            log_info "找到 $file_count 个旧日志文件"
            
            if [ "$DRY_RUN" = true ]; then
                echo "$old_files" | while read -r file; do
                    local file_size=$(get_size "$file")
                    log_info "[预览] 将删除: $file ($file_size)"
                done
            else
                echo "$old_files" | while read -r file; do
                    local file_size=$(get_size "$file")
                    rm -f "$file"
                    log_info "已删除: $file ($file_size)"
                done
                total_cleaned=$((total_cleaned + file_count))
            fi
        else
            log_info "未找到需要清理的旧日志文件"
        fi
        
        # 计算清理后大小
        if [ "$DRY_RUN" = false ]; then
            local size_after=$(du -sb "$log_dir" 2>/dev/null | cut -f1 || echo "0")
            total_size_after=$((total_size_after + size_after))
        fi
    done
    
    # 清理Docker容器日志
    log_info "清理Docker容器日志..."
    
    local containers=$(docker ps -aq 2>/dev/null || true)
    if [ -n "$containers" ]; then
        for container in $containers; do
            local log_file=$(docker inspect --format='{{.LogPath}}' "$container" 2>/dev/null || true)
            if [ -n "$log_file" ] && [ -f "$log_file" ]; then
                local log_size=$(get_size "$log_file")
                if [ "$DRY_RUN" = true ]; then
                    log_info "[预览] 将清理容器日志: $container ($log_size)"
                else
                    # 清空日志文件而不删除
                    truncate -s 0 "$log_file" 2>/dev/null || true
                    log_info "已清理容器日志: $container ($log_size)"
                fi
            fi
        done
    fi
    
    if [ "$DRY_RUN" = false ]; then
        local saved_size=$((total_size_before - total_size_after))
        log_success "日志清理完成，共清理 $total_cleaned 个文件，释放空间: $(numfmt --to=iec $saved_size)"
    else
        log_info "日志清理预览完成"
    fi
}

# 清理备份文件
cleanup_backups() {
    log_info "清理旧备份文件 (保留 $BACKUP_RETENTION_DAYS 天)..."
    
    local backup_dirs=(
        "./backups"
        "./data/backups"
    )
    
    local total_cleaned=0
    local total_size_saved=0
    
    for backup_dir in "${backup_dirs[@]}"; do
        if [ ! -d "$backup_dir" ]; then
            continue
        fi
        
        log_info "处理备份目录: $backup_dir"
        
        # 查找旧备份文件
        local old_backups=$(find "$backup_dir" -name "moniit_backup_*" -type f -mtime +$BACKUP_RETENTION_DAYS 2>/dev/null || true)
        local old_backup_dirs=$(find "$backup_dir" -name "moniit_backup_*" -type d -mtime +$BACKUP_RETENTION_DAYS 2>/dev/null || true)
        
        # 处理备份文件
        if [ -n "$old_backups" ]; then
            echo "$old_backups" | while read -r backup; do
                local backup_size=$(get_size "$backup")
                if [ "$DRY_RUN" = true ]; then
                    log_info "[预览] 将删除备份文件: $backup ($backup_size)"
                else
                    rm -f "$backup"
                    log_info "已删除备份文件: $backup ($backup_size)"
                    ((total_cleaned++))
                fi
            done
        fi
        
        # 处理备份目录
        if [ -n "$old_backup_dirs" ]; then
            echo "$old_backup_dirs" | while read -r backup_dir; do
                local backup_size=$(get_size "$backup_dir")
                if [ "$DRY_RUN" = true ]; then
                    log_info "[预览] 将删除备份目录: $backup_dir ($backup_size)"
                else
                    rm -rf "$backup_dir"
                    log_info "已删除备份目录: $backup_dir ($backup_size)"
                    ((total_cleaned++))
                fi
            done
        fi
    done
    
    if [ "$DRY_RUN" = false ]; then
        log_success "备份清理完成，共清理 $total_cleaned 个项目"
    else
        log_info "备份清理预览完成"
    fi
}

# 清理临时文件
cleanup_temp() {
    log_info "清理临时文件 (保留 $TEMP_RETENTION_DAYS 天)..."
    
    local temp_dirs=(
        "./tmp"
        "./temp"
        "./uploads/temp"
        "/tmp/moniit*"
    )
    
    local total_cleaned=0
    
    for temp_pattern in "${temp_dirs[@]}"; do
        # 使用glob模式查找文件
        for temp_path in $temp_pattern; do
            if [ ! -e "$temp_path" ]; then
                continue
            fi
            
            if [ -d "$temp_path" ]; then
                log_info "处理临时目录: $temp_path"
                
                # 查找旧临时文件
                local old_files=$(find "$temp_path" -type f -mtime +$TEMP_RETENTION_DAYS 2>/dev/null || true)
                
                if [ -n "$old_files" ]; then
                    echo "$old_files" | while read -r file; do
                        local file_size=$(get_size "$file")
                        if [ "$DRY_RUN" = true ]; then
                            log_info "[预览] 将删除临时文件: $file ($file_size)"
                        else
                            rm -f "$file"
                            log_info "已删除临时文件: $file ($file_size)"
                            ((total_cleaned++))
                        fi
                    done
                fi
            elif [ -f "$temp_path" ]; then
                # 单个文件
                local file_age=$(stat -c %Y "$temp_path" 2>/dev/null || echo "0")
                local current_time=$(date +%s)
                local age_days=$(( (current_time - file_age) / 86400 ))
                
                if [ $age_days -gt $TEMP_RETENTION_DAYS ]; then
                    local file_size=$(get_size "$temp_path")
                    if [ "$DRY_RUN" = true ]; then
                        log_info "[预览] 将删除临时文件: $temp_path ($file_size)"
                    else
                        rm -f "$temp_path"
                        log_info "已删除临时文件: $temp_path ($file_size)"
                        ((total_cleaned++))
                    fi
                fi
            fi
        done
    done
    
    if [ "$DRY_RUN" = false ]; then
        log_success "临时文件清理完成，共清理 $total_cleaned 个文件"
    else
        log_info "临时文件清理预览完成"
    fi
}

# 清理数据库
cleanup_database() {
    log_info "清理数据库..."
    
    # 检查TimescaleDB数据库连接
    if ! docker exec moniit-timescaledb-dev pg_isready -U moniit -d moniit &>/dev/null; then
        log_error "TimescaleDB数据库连接失败，跳过数据库清理"
        return 1
    fi
    
    # 获取TimescaleDB数据库大小
    local db_size_mb=$(docker exec moniit-timescaledb-dev psql -U moniit -d moniit -t -c "SELECT pg_size_pretty(pg_database_size('moniit'));" 2>/dev/null | xargs || echo "0")
    log_info "当前TimescaleDB数据库大小: $db_size_mb"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行TimescaleDB数据库清理操作"
        log_info "[预览] - TimescaleDB自动保留策略已配置（价格记录90天，系统日志30天）"
        log_info "[预览] - 手动清理过期数据块"
        log_info "[预览] - 执行VACUUM和压缩操作"
        return 0
    fi

    # TimescaleDB特定清理操作
    log_info "执行TimescaleDB清理操作..."
    docker exec moniit-timescaledb-dev psql -U moniit -d moniit -c "
        -- 手动触发保留策略（如果需要）
        SELECT drop_chunks('price_records', INTERVAL '90 days');
        SELECT drop_chunks('system_logs', INTERVAL '30 days');

        -- 压缩旧数据块
        SELECT compress_chunk(chunk) FROM show_chunks('price_records') AS chunk
        WHERE NOT is_compressed(chunk) AND chunk < now() - INTERVAL '7 days';

        -- 刷新连续聚合视图
        CALL refresh_continuous_aggregate('price_records_hourly', NULL, NULL);
        CALL refresh_continuous_aggregate('price_records_daily', NULL, NULL);
        CALL refresh_continuous_aggregate('system_logs_hourly', NULL, NULL);

        -- 执行VACUUM操作
        VACUUM ANALYZE;
    " &>/dev/null

    log_info "TimescaleDB自动保留策略和压缩已执行"

    # 获取清理后的数据库大小
    local new_db_size_mb=$(docker exec moniit-timescaledb-dev psql -U moniit -d moniit -t -c "SELECT pg_size_pretty(pg_database_size('moniit'));" 2>/dev/null | xargs || echo "0")
    log_success "TimescaleDB清理完成，当前大小: $new_db_size_mb"
}

# 清理Docker资源
cleanup_containers() {
    log_info "清理Docker资源..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[预览] 将执行Docker清理操作"
        log_info "[预览] - 清理停止的容器"
        log_info "[预览] - 清理未使用的镜像"
        log_info "[预览] - 清理未使用的网络"
        log_info "[预览] - 清理未使用的数据卷"
        return 0
    fi
    
    # 清理停止的容器
    log_info "清理停止的容器..."
    local stopped_containers=$(docker ps -aq --filter "status=exited" 2>/dev/null || true)
    if [ -n "$stopped_containers" ]; then
        docker rm $stopped_containers &>/dev/null || true
        local count=$(echo "$stopped_containers" | wc -l)
        log_info "已清理 $count 个停止的容器"
    else
        log_info "未找到停止的容器"
    fi
    
    # 清理未使用的镜像
    log_info "清理未使用的镜像..."
    docker image prune -f &>/dev/null || true
    log_info "已清理未使用的镜像"
    
    # 清理未使用的网络
    log_info "清理未使用的网络..."
    docker network prune -f &>/dev/null || true
    log_info "已清理未使用的网络"
    
    # 清理未使用的数据卷
    log_info "清理未使用的数据卷..."
    docker volume prune -f &>/dev/null || true
    log_info "已清理未使用的数据卷"
    
    # 显示清理后的Docker资源使用情况
    log_info "Docker资源使用情况:"
    docker system df 2>/dev/null || true
    
    log_success "Docker资源清理完成"
}

# 主函数
main() {
    # 默认参数
    local clean_logs=false
    local clean_backups=false
    local clean_temp=false
    local clean_database=false
    local clean_containers=false
    local clean_all=false
    FORCE=false
    DRY_RUN=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -l|--logs)
                clean_logs=true
                shift
                ;;
            -b|--backups)
                clean_backups=true
                shift
                ;;
            -t|--temp)
                clean_temp=true
                shift
                ;;
            -d|--database)
                clean_database=true
                shift
                ;;
            -c|--containers)
                clean_containers=true
                shift
                ;;
            -a|--all)
                clean_all=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            --log-days)
                LOG_RETENTION_DAYS="$2"
                shift 2
                ;;
            --backup-days)
                BACKUP_RETENTION_DAYS="$2"
                shift 2
                ;;
            --temp-days)
                TEMP_RETENTION_DAYS="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何清理选项，显示帮助
    if [ "$clean_all" = false ] && [ "$clean_logs" = false ] && [ "$clean_backups" = false ] && [ "$clean_temp" = false ] && [ "$clean_database" = false ] && [ "$clean_containers" = false ]; then
        log_error "请指定要执行的清理操作"
        show_help
        exit 1
    fi
    
    log_info "开始 Moniit 系统清理"
    if [ "$DRY_RUN" = true ]; then
        log_warning "预览模式：不会实际删除文件"
    fi
    
    # 执行清理操作
    if [ "$clean_all" = true ] || [ "$clean_logs" = true ]; then
        if confirm_action "清理日志文件"; then
            cleanup_logs
        fi
    fi
    
    if [ "$clean_all" = true ] || [ "$clean_backups" = true ]; then
        if confirm_action "清理备份文件"; then
            cleanup_backups
        fi
    fi
    
    if [ "$clean_all" = true ] || [ "$clean_temp" = true ]; then
        if confirm_action "清理临时文件"; then
            cleanup_temp
        fi
    fi
    
    if [ "$clean_all" = true ] || [ "$clean_database" = true ]; then
        if confirm_action "清理数据库"; then
            cleanup_database
        fi
    fi
    
    if [ "$clean_all" = true ] || [ "$clean_containers" = true ]; then
        if confirm_action "清理Docker资源"; then
            cleanup_containers
        fi
    fi
    
    log_success "Moniit 系统清理完成!"
}

# 执行主函数
main "$@"
