# Moniit 生产环境 Docker Compose 配置
# 专门用于生产环境部署

version: '3.8'

services:
  # 后端API服务 - 生产模式
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-backend-prod
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MONIIT_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost}
      - DEBUG=false
      - LOG_LEVEL=INFO
    volumes:
      - moniit-prod-data:/app/data
      - moniit-prod-logs:/app/logs
      - moniit-prod-backups:/app/backups
    depends_on:
      - redis
      - db
    networks:
      - moniit-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # 前端Web服务 - 生产模式
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: moniit-frontend-prod
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - moniit-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Celery工作进程
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-worker-prod
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    environment:
      - MONIIT_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
    volumes:
      - moniit-prod-data:/app/data
      - moniit-prod-logs:/app/logs
    depends_on:
      - redis
      - db
    networks:
      - moniit-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Celery Beat调度器
  beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: moniit-beat-prod
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - MONIIT_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
    volumes:
      - moniit-prod-data:/app/data
      - moniit-prod-logs:/app/logs
    depends_on:
      - redis
      - db
    networks:
      - moniit-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: moniit-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - moniit-prod-redis-data:/data
    networks:
      - moniit-prod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # TimescaleDB数据库 - 生产环境
  db:
    image: timescale/timescaledb:latest-pg15
    container_name: moniit-timescaledb-prod
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - moniit-prod-postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/init-timescale.sql:/docker-entrypoint-initdb.d/02-timescale.sql:ro
    networks:
      - moniit-prod-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: moniit-nginx-prod
    restart: unless-stopped
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx-prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - moniit-prod-logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - moniit-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

# 网络配置
networks:
  moniit-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  moniit-prod-data:
    driver: local
  moniit-prod-logs:
    driver: local
  moniit-prod-backups:
    driver: local
  moniit-prod-redis-data:
    driver: local
  moniit-prod-postgres-data:
    driver: local
