#!/usr/bin/env python3
"""
简化的测试数据生成脚本
直接使用环境变量连接数据库，避免配置文件依赖
"""

import asyncio
import asyncpg
import math
import random
import os
from datetime import datetime, timedelta
from decimal import Decimal


async def main():
    """主函数"""
    print("🚀 开始生成TimescaleDB测试数据")
    print("=" * 60)
    
    # 使用环境变量或默认值
    db_url = os.getenv("DATABASE_URL", "postgresql://moniit:moniit123@localhost:5432/moniit")
    print(f"📡 数据库连接: {db_url}")
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(db_url)
        print("✅ 数据库连接成功")
        
        # 创建测试表
        await create_test_tables(conn)
        
        # 生成测试数据
        products = await generate_test_products(conn)
        await generate_test_price_data(conn, products)
        
        # 显示统计信息
        await show_statistics(conn)
        
        await conn.close()
        print("\n🎉 测试数据生成完成！")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n💡 请确保TimescaleDB正在运行:")
        print("   docker-compose up db")
        return 1
    
    return 0


async def create_test_tables(conn):
    """创建测试表"""
    print("🔧 创建测试表...")
    
    # 创建商品表
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS test_products (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(500) NOT NULL,
            url TEXT NOT NULL,
            platform VARCHAR(50) NOT NULL,
            category VARCHAR(100),
            created_at TIMESTAMP DEFAULT NOW()
        )
    """)
    
    # 创建价格历史表
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS test_price_history (
            id SERIAL,
            product_id VARCHAR(50) NOT NULL,
            price DECIMAL(12,2) NOT NULL,
            currency VARCHAR(10) DEFAULT 'CNY',
            recorded_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT NOW()
        )
    """)
    
    # 尝试创建TimescaleDB超表
    try:
        await conn.execute("""
            SELECT create_hypertable('test_price_history', 'recorded_at', 
                                   if_not_exists => TRUE)
        """)
        print("✅ TimescaleDB超表创建成功")
    except Exception as e:
        print(f"⚠️ 超表创建失败（可能已存在或不是TimescaleDB）: {e}")
    
    # 创建索引
    await conn.execute("""
        CREATE INDEX IF NOT EXISTS idx_test_price_history_product_time 
        ON test_price_history(product_id, recorded_at DESC)
    """)
    
    print("✅ 表结构创建完成")


async def generate_test_products(conn):
    """生成测试商品"""
    print("📱 生成测试商品...")
    
    products = [
        {
            "id": "test_iphone_15_pro",
            "name": "iPhone 15 Pro 钛金属 128GB",
            "url": "https://www.apple.com/cn/iphone-15-pro/",
            "platform": "apple",
            "category": "智能手机"
        },
        {
            "id": "test_macbook_pro_14",
            "name": "MacBook Pro 14英寸 M3芯片",
            "url": "https://www.apple.com/cn/macbook-pro/",
            "platform": "apple",
            "category": "笔记本电脑"
        },
        {
            "id": "test_xiaomi_14_ultra",
            "name": "小米14 Ultra 16GB+512GB",
            "url": "https://www.mi.com/xiaomi-14-ultra",
            "platform": "xiaomi",
            "category": "智能手机"
        }
    ]
    
    # 清理旧数据
    await conn.execute("DELETE FROM test_price_history")
    await conn.execute("DELETE FROM test_products")
    
    # 插入商品
    for product in products:
        await conn.execute("""
            INSERT INTO test_products (id, name, url, platform, category)
            VALUES ($1, $2, $3, $4, $5)
        """, product["id"], product["name"], product["url"], 
            product["platform"], product["category"])
    
    print(f"✅ 生成了 {len(products)} 个测试商品")
    return products


async def generate_test_price_data(conn, products, days=30):
    """生成价格历史数据"""
    print(f"💰 生成 {days} 天的价格数据...")
    
    base_time = datetime.now() - timedelta(days=days)
    total_records = 0
    
    # 商品基础价格配置
    price_configs = {
        "test_iphone_15_pro": {"base": 8999, "volatility": 0.05},
        "test_macbook_pro_14": {"base": 14999, "volatility": 0.03},
        "test_xiaomi_14_ultra": {"base": 6499, "volatility": 0.08}
    }
    
    for product in products:
        product_id = product["id"]
        config = price_configs.get(product_id, {"base": 5000, "volatility": 0.05})
        base_price = config["base"]
        volatility = config["volatility"]
        
        print(f"  生成 {product['name']} 的价格数据...")
        
        # 每6小时一个数据点
        for hour in range(0, days * 24, 6):
            timestamp = base_time + timedelta(hours=hour)
            
            # 价格波动计算
            # 1. 长期趋势
            trend = hour * 0.001
            
            # 2. 周期性波动
            weekly_cycle = math.sin(2 * math.pi * hour / (24 * 7)) * base_price * 0.02
            
            # 3. 随机波动
            random.seed(hour + hash(product_id))
            random_factor = random.gauss(0, base_price * volatility)
            
            # 4. 特殊事件（促销）
            event_factor = -base_price * 0.1 if hour % (24 * 14) < 24 else 0
            
            # 计算最终价格
            price_change = trend + weekly_cycle + random_factor + event_factor
            final_price = base_price + price_change
            final_price = max(final_price, base_price * 0.7)  # 最低7折
            
            # 插入价格记录
            await conn.execute("""
                INSERT INTO test_price_history (product_id, price, currency, recorded_at)
                VALUES ($1, $2, $3, $4)
            """, product_id, round(final_price, 2), "CNY", timestamp)
            
            total_records += 1
    
    print(f"✅ 生成了 {total_records} 条价格记录")


async def show_statistics(conn):
    """显示数据统计"""
    print("\n📊 数据统计:")
    
    # 商品数量
    product_count = await conn.fetchval("SELECT COUNT(*) FROM test_products")
    print(f"  商品数量: {product_count}")
    
    # 价格记录数量
    price_count = await conn.fetchval("SELECT COUNT(*) FROM test_price_history")
    print(f"  价格记录数量: {price_count}")
    
    # 时间范围
    time_range = await conn.fetchrow("""
        SELECT MIN(recorded_at) as earliest, MAX(recorded_at) as latest 
        FROM test_price_history
    """)
    if time_range:
        print(f"  数据时间范围: {time_range['earliest']} 到 {time_range['latest']}")
    
    # 每个商品的记录数
    product_stats = await conn.fetch("""
        SELECT p.name, COUNT(ph.id) as record_count,
               MIN(ph.price) as min_price, MAX(ph.price) as max_price
        FROM test_products p
        LEFT JOIN test_price_history ph ON p.id = ph.product_id
        GROUP BY p.id, p.name
        ORDER BY record_count DESC
    """)
    
    print("\n  各商品统计:")
    for stat in product_stats:
        print(f"    {stat['name']}: {stat['record_count']} 条记录, "
              f"价格范围: ¥{stat['min_price']:.2f} - ¥{stat['max_price']:.2f}")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
