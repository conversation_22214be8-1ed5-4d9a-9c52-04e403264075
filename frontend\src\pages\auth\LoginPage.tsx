/**
 * 登录页面
 */

import React, { useState } from 'react';
import { Form, Input, Button, Card, Alert, Checkbox } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { loginAsync, selectAuthLoading, selectAuthError, clearError } from '../../store/slices/authSlice';

interface LoginForm {
  username: string;
  password: string;
  remember: boolean;
}

const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  
  const [form] = Form.useForm();

  // 处理表单提交
  const handleSubmit = async (values: LoginForm) => {
    try {
      await dispatch(loginAsync({
        username: values.username,
        password: values.password,
      })).unwrap();
      
      // 记住登录状态
      if (values.remember) {
        localStorage.setItem('remember_login', 'true');
      } else {
        localStorage.removeItem('remember_login');
      }
    } catch (error) {
      // 错误已在Redux中处理
    }
  };

  // 清除错误信息
  const handleErrorClose = () => {
    dispatch(clearError());
  };

  return (
    <div 
      className="d-flex align-items-center justify-content-center"
      style={{ 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px'
      }}
    >
      <Card
        style={{ 
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        }}
        bodyStyle={{ padding: '40px 32px' }}
      >
        {/* Logo和标题 */}
        <div className="text-center mb-4">
          <div style={{ 
            fontSize: 32, 
            fontWeight: 'bold', 
            color: '#1890ff',
            marginBottom: 8
          }}>
            Moniit
          </div>
          <div style={{ color: '#666', fontSize: 14 }}>
            商品监控系统
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert
            message={error}
            type="error"
            closable
            onClose={handleErrorClose}
            style={{ marginBottom: 24 }}
          />
        )}

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
          initialValues={{
            remember: localStorage.getItem('remember_login') === 'true',
          }}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <div className="d-flex justify-content-between align-items-center">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住登录</Checkbox>
              </Form.Item>
              <Button type="link" style={{ padding: 0 }}>
                忘记密码？
              </Button>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: 44 }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        {/* 演示账户信息 */}
        <div style={{
          marginTop: 24,
          padding: 16,
          background: '#f8f9fa',
          borderRadius: 6,
          fontSize: 12,
          color: '#666'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: 12, color: '#333' }}>
            🎯 演示账户（点击快速填充）：
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            <div
              style={{
                padding: '8px 12px',
                background: '#e6f7ff',
                borderRadius: 4,
                cursor: 'pointer',
                border: '1px solid #91d5ff',
                transition: 'all 0.2s'
              }}
              onClick={() => {
                form.setFieldsValue({ username: 'admin', password: 'Admin123!' });
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#bae7ff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = '#e6f7ff';
              }}
            >
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>👑 管理员账户</div>
              <div>用户名：admin</div>
              <div>密码：Admin123!</div>
              <div style={{ fontSize: 11, color: '#666', marginTop: 4 }}>
                拥有所有权限，可管理用户、商品、监控任务等
              </div>
            </div>

            <div
              style={{
                padding: '8px 12px',
                background: '#f6ffed',
                borderRadius: 4,
                cursor: 'pointer',
                border: '1px solid #b7eb8f',
                transition: 'all 0.2s'
              }}
              onClick={() => {
                form.setFieldsValue({ username: 'operator', password: 'Operator123!' });
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#d9f7be';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = '#f6ffed';
              }}
            >
              <div style={{ fontWeight: 'bold', color: '#52c41a' }}>👤 操作员账户</div>
              <div>用户名：operator</div>
              <div>密码：Operator123!</div>
              <div style={{ fontSize: 11, color: '#666', marginTop: 4 }}>
                可管理商品和监控任务，查看分析报告
              </div>
            </div>
          </div>

          <div style={{
            marginTop: 12,
            padding: 8,
            background: '#fff2e8',
            borderRadius: 4,
            border: '1px solid #ffd591',
            fontSize: 11,
            color: '#d46b08'
          }}>
            💡 提示：这是演示环境，密码已固定便于测试。生产环境请使用强密码！
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
