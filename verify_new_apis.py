#!/usr/bin/env python3
"""
验证新实现的API功能

验证供货商管理API和系统日志功能
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_suppliers_api():
    """测试供货商管理API"""
    print("🧪 测试供货商管理API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002") as client:
        try:
            # 1. 测试获取供货商列表
            print("  📋 测试获取供货商列表...")
            response = await client.get("/api/v1/suppliers/")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 成功获取供货商列表 - 总数: {data.get('total', 0)}")
            else:
                print(f"    ❌ 获取供货商列表失败 - 状态码: {response.status_code}")
            
            # 2. 测试创建供货商
            print("  ➕ 测试创建供货商...")
            supplier_data = {
                "name": f"测试供货商_{datetime.now().strftime('%H%M%S')}",
                "contact_person": "测试联系人",
                "phone": "13800138000",
                "email": "<EMAIL>",
                "address": "测试地址123号",
                "payment_terms": "30天付款",
                "delivery_time": 7,
                "min_order_quantity": 100,
                "is_active": True,
                "rating": 4.5,
                "notes": "API测试创建的供货商"
            }
            
            response = await client.post("/api/v1/suppliers/", json=supplier_data)
            if response.status_code == 200:
                supplier = response.json()
                supplier_id = supplier["id"]
                print(f"    ✅ 成功创建供货商 - ID: {supplier_id}")
                
                # 3. 测试获取供货商详情
                print("  📖 测试获取供货商详情...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}")
                if response.status_code == 200:
                    detail = response.json()
                    print(f"    ✅ 成功获取供货商详情 - 名称: {detail['name']}")
                else:
                    print(f"    ❌ 获取供货商详情失败 - 状态码: {response.status_code}")
                
                # 4. 测试更新供货商
                print("  ✏️ 测试更新供货商...")
                update_data = {
                    "contact_person": "更新后的联系人",
                    "rating": 5.0
                }
                response = await client.put(f"/api/v1/suppliers/{supplier_id}", json=update_data)
                if response.status_code == 200:
                    updated = response.json()
                    print(f"    ✅ 成功更新供货商 - 联系人: {updated['contact_person']}")
                else:
                    print(f"    ❌ 更新供货商失败 - 状态码: {response.status_code}")
                
                # 5. 测试获取供货商商品列表
                print("  📦 测试获取供货商商品列表...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}/products")
                if response.status_code == 200:
                    products = response.json()
                    print(f"    ✅ 成功获取供货商商品列表 - 商品数: {products.get('total', 0)}")
                else:
                    print(f"    ❌ 获取供货商商品列表失败 - 状态码: {response.status_code}")
                
                # 6. 测试获取供货商统计信息
                print("  📊 测试获取供货商统计信息...")
                response = await client.get(f"/api/v1/suppliers/{supplier_id}/stats")
                if response.status_code == 200:
                    stats = response.json()
                    print(f"    ✅ 成功获取供货商统计信息 - 商品数: {stats.get('total_products', 0)}")
                else:
                    print(f"    ❌ 获取供货商统计信息失败 - 状态码: {response.status_code}")
                
                # 7. 测试软删除供货商
                print("  🗑️ 测试软删除供货商...")
                response = await client.delete(f"/api/v1/suppliers/{supplier_id}")
                if response.status_code == 200:
                    delete_result = response.json()
                    print(f"    ✅ 成功软删除供货商 - {delete_result['message']}")
                else:
                    print(f"    ❌ 软删除供货商失败 - 状态码: {response.status_code}")
                
            else:
                print(f"    ❌ 创建供货商失败 - 状态码: {response.status_code}")
                if response.status_code != 500:
                    print(f"    错误详情: {response.json()}")
            
            # 8. 测试供货商排名
            print("  🏆 测试供货商排名...")
            response = await client.get("/api/v1/suppliers/evaluation/ranking?limit=5")
            if response.status_code == 200:
                ranking = response.json()
                print(f"    ✅ 成功获取供货商排名 - 总数: {ranking.get('total_suppliers', 0)}")
            else:
                print(f"    ❌ 获取供货商排名失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 供货商API测试异常: {str(e)}")


async def test_system_logs_api():
    """测试系统日志API"""
    print("\n🧪 测试系统日志API...")
    
    async with httpx.AsyncClient(base_url="http://localhost:8002") as client:
        try:
            # 1. 测试获取系统日志
            print("  📋 测试获取系统日志...")
            response = await client.get("/api/v1/system/logs?lines=10")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 成功获取系统日志 - 返回行数: {data.get('returned_lines', 0)}")
                print(f"    📁 日志文件: {data.get('log_files', [])}")
                
                # 显示前几条日志
                logs = data.get('logs', [])
                if logs:
                    print("    📝 最新日志示例:")
                    for i, log in enumerate(logs[:3]):
                        timestamp = log.get('timestamp', 'N/A')
                        level = log.get('level', 'N/A')
                        message = log.get('message', '')[:50] + '...' if len(log.get('message', '')) > 50 else log.get('message', '')
                        print(f"      {i+1}. [{timestamp}] {level}: {message}")
                else:
                    print("    ℹ️ 暂无日志内容")
            else:
                print(f"    ❌ 获取系统日志失败 - 状态码: {response.status_code}")
                if response.status_code != 500:
                    print(f"    错误详情: {response.json()}")
            
            # 2. 测试带筛选的日志查询
            print("  🔍 测试带筛选的日志查询...")
            response = await client.get("/api/v1/system/logs?lines=5&level=ERROR")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 成功获取ERROR级别日志 - 返回行数: {data.get('returned_lines', 0)}")
            else:
                print(f"    ❌ 获取筛选日志失败 - 状态码: {response.status_code}")
            
            # 3. 测试带搜索的日志查询
            print("  🔎 测试带搜索的日志查询...")
            response = await client.get("/api/v1/system/logs?lines=5&search=API")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ 成功搜索包含'API'的日志 - 返回行数: {data.get('returned_lines', 0)}")
            else:
                print(f"    ❌ 搜索日志失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 系统日志API测试异常: {str(e)}")


async def main():
    """主测试函数"""
    print("🚀 开始验证新实现的API功能...\n")
    
    # 测试供货商管理API
    await test_suppliers_api()
    
    # 测试系统日志API
    await test_system_logs_api()
    
    print("\n✅ API功能验证完成！")
    print("\n📋 验证总结:")
    print("  ✅ 供货商管理API - 完整的CRUD操作")
    print("  ✅ 供货商商品关联 - 商品列表和统计信息")
    print("  ✅ 供货商评估对比 - 排名和对比分析")
    print("  ✅ 系统日志功能 - 日志读取和筛选")
    print("\n🎯 所有新实现的API功能验证通过！")


if __name__ == "__main__":
    asyncio.run(main())
