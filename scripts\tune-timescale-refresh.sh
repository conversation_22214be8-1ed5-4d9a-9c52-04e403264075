#!/bin/bash

# TimescaleDB连续聚合视图刷新策略调优脚本
# 根据实际数据量和性能需求自动调整刷新策略

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DB_CONTAINER="moniit-timescaledb-dev"
DB_NAME="moniit"
DB_USER="moniit"

# 显示帮助信息
show_help() {
    echo "TimescaleDB连续聚合视图刷新策略调优脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -a, --analyze        分析当前数据量和性能"
    echo "  -s, --strategy TYPE  应用预定义策略 (realtime|balanced|conservative)"
    echo "  -c, --custom         交互式自定义配置"
    echo "  -m, --monitor        监控刷新性能"
    echo "  -r, --reset          重置为默认策略"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "策略类型:"
    echo "  realtime     - 实时性优先（15分钟延迟，高频刷新）"
    echo "  balanced     - 平衡策略（30分钟延迟，中频刷新）"
    echo "  conservative - 保守策略（1小时延迟，低频刷新）"
    echo "  daily        - 日更新优化（6小时延迟，适合每日数据刷新）"
    echo ""
    echo "示例:"
    echo "  $0 -a                分析当前状况"
    echo "  $0 -s realtime       应用实时策略"
    echo "  $0 -s daily          应用日更新优化策略"
    echo "  $0 -m                监控性能"
}

# 检查数据库连接
check_database() {
    if ! docker exec "$DB_CONTAINER" pg_isready -U "$DB_USER" -d "$DB_NAME" &>/dev/null; then
        log_error "TimescaleDB数据库连接失败"
        return 1
    fi
    return 0
}

# 分析当前数据量和性能
analyze_current_state() {
    log_info "分析当前数据量和性能状况..."
    
    # 获取数据量统计
    local stats=$(docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT 
            COUNT(*) as total_records,
            COUNT(*) FILTER (WHERE recorded_at >= NOW() - INTERVAL '24 hours') as last_24h,
            COUNT(*) FILTER (WHERE recorded_at >= NOW() - INTERVAL '1 hour') as last_1h,
            MIN(recorded_at) as earliest_record,
            MAX(recorded_at) as latest_record
        FROM price_records;
    " 2>/dev/null)
    
    if [ -n "$stats" ]; then
        echo ""
        log_info "数据量统计:"
        echo "$stats" | while IFS='|' read -r total last_24h last_1h earliest latest; do
            echo "  总记录数: $(echo $total | xargs)"
            echo "  最近24小时: $(echo $last_24h | xargs) 条"
            echo "  最近1小时: $(echo $last_1h | xargs) 条"
            echo "  最早记录: $(echo $earliest | xargs)"
            echo "  最新记录: $(echo $latest | xargs)"
        done
    fi
    
    # 获取刷新性能统计
    log_info "刷新性能统计:"
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            application_name as view_name,
            total_runs,
            total_successes,
            total_failures,
            ROUND(total_successes::numeric / NULLIF(total_runs, 0) * 100, 2) as success_rate,
            last_run_duration,
            average_run_duration
        FROM timescaledb_information.jobs j
        JOIN timescaledb_information.job_stats js ON j.job_id = js.job_id
        WHERE j.proc_name = 'policy_refresh_continuous_aggregate'
        ORDER BY application_name;
    " 2>/dev/null || log_warning "无法获取刷新性能统计"
    
    # 检查数据新鲜度
    log_info "数据新鲜度检查:"
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT * FROM check_aggregate_freshness();
    " 2>/dev/null || log_warning "无法检查数据新鲜度"
    
    # 提供建议
    echo ""
    log_info "策略建议:"

    # 分析数据更新模式
    local daily_pattern=$(docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT
            COUNT(DISTINCT DATE(recorded_at)) as unique_days,
            COUNT(*) as total_records,
            ROUND(COUNT(*)::numeric / NULLIF(COUNT(DISTINCT DATE(recorded_at)), 0), 0) as avg_per_day
        FROM price_records
        WHERE recorded_at >= NOW() - INTERVAL '7 days';
    " 2>/dev/null)

    local hourly_count=$(docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM price_records WHERE recorded_at >= NOW() - INTERVAL '1 hour';
    " 2>/dev/null | xargs)

    local today_count=$(docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM price_records WHERE recorded_at >= CURRENT_DATE;
    " 2>/dev/null | xargs)

    if [ -n "$daily_pattern" ]; then
        echo "$daily_pattern" | while IFS='|' read -r unique_days total_records avg_per_day; do
            local days=$(echo $unique_days | xargs)
            local avg=$(echo $avg_per_day | xargs)

            echo "  📊 数据更新模式分析："
            echo "    - 最近7天有数据的天数: $days 天"
            echo "    - 平均每天记录数: $avg 条"
            echo "    - 今日记录数: $today_count 条"
            echo "    - 最近1小时记录数: $hourly_count 条"

            if [ "$days" -le 3 ] && [ "$hourly_count" -eq 0 ]; then
                echo "  💡 检测到批量/日更新模式，建议使用 daily 策略"
            elif [ "$hourly_count" -gt 100 ]; then
                echo "  💡 检测到高频更新模式，建议使用 realtime 策略"
            elif [ "$hourly_count" -gt 10 ]; then
                echo "  💡 检测到中频更新模式，建议使用 balanced 策略"
            else
                echo "  💡 检测到低频更新模式，建议使用 conservative 或 daily 策略"
            fi
        done
    fi
}

# 应用实时策略
apply_realtime_strategy() {
    log_info "应用实时性优先策略..."
    
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
        
        -- 应用实时策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 hours',
            end_offset => INTERVAL '15 minutes',
            schedule_interval => INTERVAL '15 minutes'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '2 hours',
            schedule_interval => INTERVAL '2 hours'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '2 weeks',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '6 hours'
        );
    " &>/dev/null
    
    log_success "实时策略应用完成"
    echo "  ⚡ 数据延迟: 15分钟"
    echo "  🔄 刷新频率: 每15分钟"
    echo "  💻 资源消耗: 高"
}

# 应用平衡策略
apply_balanced_strategy() {
    log_info "应用平衡策略..."
    
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
        
        -- 应用平衡策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 hours',
            end_offset => INTERVAL '30 minutes',
            schedule_interval => INTERVAL '30 minutes'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '4 hours',
            schedule_interval => INTERVAL '4 hours'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '2 weeks',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '12 hours'
        );
    " &>/dev/null
    
    log_success "平衡策略应用完成"
    echo "  ⚡ 数据延迟: 30分钟"
    echo "  🔄 刷新频率: 每30分钟"
    echo "  💻 资源消耗: 中等"
}

# 应用保守策略
apply_conservative_strategy() {
    log_info "应用保守策略..."

    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);

        -- 应用保守策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '1 hour',
            end_offset => INTERVAL '1 hour',
            schedule_interval => INTERVAL '1 hour'
        );

        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '1 day',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '6 hours'
        );

        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '1 week',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '1 day'
        );
    " &>/dev/null

    log_success "保守策略应用完成"
    echo "  ⚡ 数据延迟: 1小时"
    echo "  🔄 刷新频率: 每小时"
    echo "  💻 资源消耗: 低"
}

# 应用日更新优化策略
apply_daily_strategy() {
    log_info "应用日更新优化策略..."

    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('system_logs_hourly', if_exists => TRUE);

        -- 应用日更新优化策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '2 days',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '6 hours'
        );

        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '3 days',
            end_offset => INTERVAL '12 hours',
            schedule_interval => INTERVAL '12 hours'
        );

        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '2 weeks',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '1 day'
        );

        SELECT add_continuous_aggregate_policy('system_logs_hourly',
            start_offset => INTERVAL '1 day',
            end_offset => INTERVAL '2 hours',
            schedule_interval => INTERVAL '4 hours'
        );
    " &>/dev/null

    log_success "日更新优化策略应用完成"
    echo "  ⚡ 数据延迟: 6-12小时"
    echo "  🔄 刷新频率: 每6-12小时"
    echo "  💻 资源消耗: 很低"
    echo "  🎯 适用场景: 数据每天批量更新一次"
    echo "  💡 优势: 避免频繁无效检查，大幅节省资源"
}

# 交互式自定义配置
interactive_custom_config() {
    log_info "交互式自定义配置"
    echo ""
    
    echo "请为每小时聚合视图配置参数:"
    read -p "开始偏移 (如: 2 hours): " hourly_start
    read -p "结束偏移 (如: 30 minutes): " hourly_end
    read -p "刷新间隔 (如: 30 minutes): " hourly_interval
    
    echo ""
    echo "请为每日聚合视图配置参数:"
    read -p "开始偏移 (如: 2 days): " daily_start
    read -p "结束偏移 (如: 4 hours): " daily_end
    read -p "刷新间隔 (如: 4 hours): " daily_interval
    
    echo ""
    echo "请为每周聚合视图配置参数:"
    read -p "开始偏移 (如: 2 weeks): " weekly_start
    read -p "结束偏移 (如: 1 day): " weekly_end
    read -p "刷新间隔 (如: 12 hours): " weekly_interval
    
    log_info "应用自定义配置..."
    
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
        
        -- 应用自定义策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '$hourly_start',
            end_offset => INTERVAL '$hourly_end',
            schedule_interval => INTERVAL '$hourly_interval'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '$daily_start',
            end_offset => INTERVAL '$daily_end',
            schedule_interval => INTERVAL '$daily_interval'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '$weekly_start',
            end_offset => INTERVAL '$weekly_end',
            schedule_interval => INTERVAL '$weekly_interval'
        );
    " &>/dev/null
    
    log_success "自定义配置应用完成"
}

# 监控刷新性能
monitor_performance() {
    log_info "监控刷新性能（按Ctrl+C退出）..."
    
    while true; do
        clear
        echo "TimescaleDB连续聚合视图刷新性能监控"
        echo "========================================"
        echo "时间: $(date)"
        echo ""
        
        # 显示当前策略
        echo "当前刷新策略:"
        docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT * FROM current_refresh_policies;
        " 2>/dev/null || echo "无法获取策略信息"
        
        echo ""
        echo "性能统计:"
        docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT * FROM refresh_performance_stats;
        " 2>/dev/null || echo "无法获取性能统计"
        
        echo ""
        echo "数据新鲜度:"
        docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT * FROM check_aggregate_freshness();
        " 2>/dev/null || echo "无法检查数据新鲜度"
        
        sleep 30
    done
}

# 重置为默认策略
reset_to_default() {
    log_info "重置为默认策略..."
    
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
        -- 删除现有策略
        SELECT remove_continuous_aggregate_policy('price_records_hourly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_daily', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('price_records_weekly', if_exists => TRUE);
        SELECT remove_continuous_aggregate_policy('system_logs_hourly', if_exists => TRUE);
        
        -- 应用默认策略
        SELECT add_continuous_aggregate_policy('price_records_hourly',
            start_offset => INTERVAL '3 hours',
            end_offset => INTERVAL '1 hour',
            schedule_interval => INTERVAL '1 hour'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_daily',
            start_offset => INTERVAL '3 days',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '1 day'
        );
        
        SELECT add_continuous_aggregate_policy('price_records_weekly',
            start_offset => INTERVAL '3 weeks',
            end_offset => INTERVAL '1 week',
            schedule_interval => INTERVAL '1 week'
        );
        
        SELECT add_continuous_aggregate_policy('system_logs_hourly',
            start_offset => INTERVAL '3 hours',
            end_offset => INTERVAL '1 hour',
            schedule_interval => INTERVAL '1 hour'
        );
    " &>/dev/null
    
    log_success "默认策略重置完成"
}

# 主函数
main() {
    # 检查数据库连接
    if ! check_database; then
        exit 1
    fi
    
    # 解析命令行参数
    case "${1:-}" in
        -a|--analyze)
            analyze_current_state
            ;;
        -s|--strategy)
            case "${2:-}" in
                realtime)
                    apply_realtime_strategy
                    ;;
                balanced)
                    apply_balanced_strategy
                    ;;
                conservative)
                    apply_conservative_strategy
                    ;;
                daily)
                    apply_daily_strategy
                    ;;
                *)
                    log_error "无效的策略类型: ${2:-}"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        -c|--custom)
            interactive_custom_config
            ;;
        -m|--monitor)
            monitor_performance
            ;;
        -r|--reset)
            reset_to_default
            ;;
        -h|--help)
            show_help
            ;;
        "")
            log_error "请指定操作"
            show_help
            exit 1
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
