"""
商品管理API测试

测试商品管理API的所有业务逻辑功能
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import uuid4
import io
import pandas as pd

from app.main import app
from app.core.database import get_db_session
from app.models.database import Product as DBProduct, ProductHistory as DBProductHistory


@pytest.fixture
async def client():
    """创建测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def db_session():
    """创建测试数据库会话"""
    async with get_db_session() as session:
        yield session


@pytest.fixture
async def sample_product(db_session: AsyncSession):
    """创建测试商品"""
    product = DBProduct(
        url="https://example.com/test-product",
        platform="test_platform",
        title="测试商品",
        title_translated="Test Product",
        category="electronics",
        status="active",
        monitoring_frequency=24,
        is_active=True,
        notes="测试商品备注"
    )
    db_session.add(product)
    await db_session.commit()
    await db_session.refresh(product)
    return product


class TestProductsAPI:
    """商品管理API测试类"""
    
    async def test_get_products_empty(self, client: AsyncClient):
        """测试获取空商品列表"""
        response = await client.get("/api/v1/products/")
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert data["total"] >= 0
    
    async def test_get_products_with_filters(self, client: AsyncClient, sample_product):
        """测试带筛选条件的商品查询"""
        # 测试平台筛选
        response = await client.get("/api/v1/products/?platform=test_platform")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 测试分类筛选
        response = await client.get("/api/v1/products/?category=electronics")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 测试状态筛选
        response = await client.get("/api/v1/products/?status=active")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 测试搜索功能
        response = await client.get("/api/v1/products/?search=测试")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
    
    async def test_create_product_success(self, client: AsyncClient):
        """测试成功创建商品"""
        product_data = {
            "url": "https://example.com/new-product",
            "platform": "new_platform",
            "title": "新商品",
            "title_translated": "New Product",
            "category": "books",
            "status": "active",
            "monitoring_frequency": 12,
            "is_active": True,
            "notes": "新商品备注"
        }
        
        response = await client.post("/api/v1/products/", json=product_data)
        assert response.status_code == 200
        data = response.json()
        assert data["url"] == product_data["url"]
        assert data["platform"] == product_data["platform"]
        assert data["title"] == product_data["title"]
        assert "id" in data
    
    async def test_create_product_duplicate_url(self, client: AsyncClient, sample_product):
        """测试创建重复URL的商品"""
        product_data = {
            "url": sample_product.url,
            "platform": "duplicate_platform",
            "title": "重复URL商品",
            "status": "active",
            "monitoring_frequency": 24,
            "is_active": True
        }
        
        response = await client.post("/api/v1/products/", json=product_data)
        assert response.status_code == 400
        assert "该商品URL已存在" in response.json()["detail"]
    
    async def test_get_product_detail_success(self, client: AsyncClient, sample_product):
        """测试成功获取商品详情"""
        response = await client.get(f"/api/v1/products/{sample_product.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(sample_product.id)
        assert data["url"] == sample_product.url
        assert data["platform"] == sample_product.platform
        assert "recent_history" in data
    
    async def test_get_product_detail_not_found(self, client: AsyncClient):
        """测试获取不存在商品的详情"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/products/{fake_id}")
        assert response.status_code == 404
        assert "商品不存在" in response.json()["detail"]
    
    async def test_get_product_detail_invalid_id(self, client: AsyncClient):
        """测试无效ID格式"""
        response = await client.get("/api/v1/products/invalid-id")
        assert response.status_code == 400
        assert "无效的商品ID格式" in response.json()["detail"]
    
    async def test_update_product_success(self, client: AsyncClient, sample_product):
        """测试成功更新商品"""
        update_data = {
            "title": "更新后的商品标题",
            "category": "updated_category",
            "notes": "更新后的备注"
        }
        
        response = await client.put(f"/api/v1/products/{sample_product.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["category"] == update_data["category"]
        assert data["notes"] == update_data["notes"]
    
    async def test_update_product_not_found(self, client: AsyncClient):
        """测试更新不存在的商品"""
        fake_id = str(uuid4())
        update_data = {"title": "不存在的商品"}
        
        response = await client.put(f"/api/v1/products/{fake_id}", json=update_data)
        assert response.status_code == 404
        assert "商品不存在" in response.json()["detail"]
    
    async def test_delete_product_soft_delete(self, client: AsyncClient, sample_product):
        """测试软删除商品"""
        response = await client.delete(f"/api/v1/products/{sample_product.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["deleted_permanently"] is False
        assert "可恢复" in data["message"]
    
    async def test_delete_product_force_delete(self, client: AsyncClient, sample_product):
        """测试强制删除商品"""
        response = await client.delete(f"/api/v1/products/{sample_product.id}?force=true")
        assert response.status_code == 200
        data = response.json()
        assert data["deleted_permanently"] is True
        assert "永久删除" in data["message"]
    
    async def test_delete_product_not_found(self, client: AsyncClient):
        """测试删除不存在的商品"""
        fake_id = str(uuid4())
        response = await client.delete(f"/api/v1/products/{fake_id}")
        assert response.status_code == 404
        assert "商品不存在" in response.json()["detail"]


class TestProductImport:
    """商品批量导入测试类"""
    
    def create_test_csv(self, data):
        """创建测试CSV文件"""
        df = pd.DataFrame(data)
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_content = csv_buffer.getvalue()
        return ("test.csv", csv_content.encode(), "text/csv")
    
    async def test_import_products_success(self, client: AsyncClient):
        """测试成功批量导入商品"""
        test_data = [
            {
                "url": "https://example.com/import1",
                "platform": "import_platform",
                "title": "导入商品1",
                "category": "import_category",
                "status": "active",
                "monitoring_frequency": 24,
                "is_active": True
            },
            {
                "url": "https://example.com/import2",
                "platform": "import_platform",
                "title": "导入商品2",
                "category": "import_category",
                "status": "active",
                "monitoring_frequency": 12,
                "is_active": True
            }
        ]
        
        file_data = self.create_test_csv(test_data)
        
        response = await client.post(
            "/api/v1/products/import",
            files={"file": file_data}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success_count"] == 2
        assert data["error_count"] == 0
    
    async def test_import_products_invalid_file_type(self, client: AsyncClient):
        """测试导入无效文件类型"""
        response = await client.post(
            "/api/v1/products/import",
            files={"file": ("test.txt", b"invalid content", "text/plain")}
        )
        assert response.status_code == 400
        assert "仅支持Excel" in response.json()["detail"]
    
    async def test_import_products_missing_columns(self, client: AsyncClient):
        """测试导入缺少必需列的文件"""
        test_data = [{"title": "缺少URL和平台的商品"}]
        file_data = self.create_test_csv(test_data)
        
        response = await client.post(
            "/api/v1/products/import",
            files={"file": file_data}
        )
        assert response.status_code == 400
        assert "缺少必需的列" in response.json()["detail"]


class TestProductHistory:
    """商品历史数据测试类"""
    
    async def test_get_product_history_success(self, client: AsyncClient, sample_product, db_session: AsyncSession):
        """测试成功获取商品历史数据"""
        # 创建测试历史记录
        history_record = DBProductHistory(
            product_id=sample_product.id,
            price=99.99,
            currency="USD",
            stock_quantity=100,
            rating=4.5,
            review_count=50
        )
        db_session.add(history_record)
        await db_session.commit()
        
        response = await client.get(f"/api/v1/products/{sample_product.id}/history")
        assert response.status_code == 200
        data = response.json()
        assert data["product_id"] == str(sample_product.id)
        assert len(data["history"]) >= 1
        assert "stats" in data
    
    async def test_get_product_history_not_found(self, client: AsyncClient):
        """测试获取不存在商品的历史数据"""
        fake_id = str(uuid4())
        response = await client.get(f"/api/v1/products/{fake_id}/history")
        assert response.status_code == 404
        assert "商品不存在" in response.json()["detail"]
    
    async def test_get_product_history_with_params(self, client: AsyncClient, sample_product):
        """测试带参数的历史数据查询"""
        response = await client.get(f"/api/v1/products/{sample_product.id}/history?days=7&limit=50")
        assert response.status_code == 200
        data = response.json()
        assert data["days"] == 7
        assert len(data["history"]) <= 50


if __name__ == "__main__":
    pytest.main([__file__])
