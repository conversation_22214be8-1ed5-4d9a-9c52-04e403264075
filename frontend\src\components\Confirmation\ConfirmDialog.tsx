/**
 * 确认对话框组件
 */

import React from 'react';
import { Modal, Button, Space } from 'antd';
import { 
  ExclamationCircleOutlined, 
  DeleteOutlined, 
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

export interface ConfirmDialogProps {
  visible: boolean;
  title: string;
  content: string;
  type?: 'info' | 'success' | 'warning' | 'error' | 'delete';
  okText?: string;
  cancelText?: string;
  loading?: boolean;
  onOk: () => void | Promise<void>;
  onCancel: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  visible,
  title,
  content,
  type = 'warning',
  okText = '确定',
  cancelText = '取消',
  loading = false,
  onOk,
  onCancel,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'delete':
        return <DeleteOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getOkButtonProps = () => {
    switch (type) {
      case 'delete':
      case 'error':
        return { danger: true };
      case 'success':
      case 'info':
        return { type: 'primary' as const };
      default:
        return { type: 'primary' as const };
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {getIcon()}
          {title}
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel} disabled={loading}>
            {cancelText}
          </Button>
          <Button
            {...getOkButtonProps()}
            loading={loading}
            onClick={onOk}
          >
            {okText}
          </Button>
        </Space>
      }
      closable={!loading}
      maskClosable={!loading}
    >
      <div style={{ padding: '16px 0' }}>
        {content}
      </div>
    </Modal>
  );
};

// 便捷方法
export const showConfirm = (props: Omit<ConfirmDialogProps, 'visible'>) => {
  return new Promise<boolean>((resolve) => {
    const modal = Modal.confirm({
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {props.type === 'delete' && <DeleteOutlined style={{ color: '#ff4d4f' }} />}
          {props.type === 'warning' && <WarningOutlined style={{ color: '#faad14' }} />}
          {props.type === 'error' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
          {props.type === 'info' && <InfoCircleOutlined style={{ color: '#1890ff' }} />}
          {props.type === 'success' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
          {!props.type && <ExclamationCircleOutlined style={{ color: '#faad14' }} />}
          {props.title}
        </div>
      ),
      content: props.content,
      okText: props.okText || '确定',
      cancelText: props.cancelText || '取消',
      okButtonProps: props.type === 'delete' || props.type === 'error' ? { danger: true } : {},
      onOk: async () => {
        try {
          await props.onOk();
          resolve(true);
        } catch (error) {
          resolve(false);
        }
      },
      onCancel: () => {
        props.onCancel();
        resolve(false);
      },
    });
  });
};

// 删除确认
export const showDeleteConfirm = (
  title: string,
  content: string,
  onOk: () => void | Promise<void>
) => {
  return showConfirm({
    title,
    content,
    type: 'delete',
    okText: '删除',
    cancelText: '取消',
    onOk,
    onCancel: () => {},
  });
};

// 警告确认
export const showWarningConfirm = (
  title: string,
  content: string,
  onOk: () => void | Promise<void>
) => {
  return showConfirm({
    title,
    content,
    type: 'warning',
    okText: '继续',
    cancelText: '取消',
    onOk,
    onCancel: () => {},
  });
};

export default ConfirmDialog;
