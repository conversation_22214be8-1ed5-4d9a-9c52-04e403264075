"""
商品生命周期管理器

负责商品状态管理、变更追踪和生命周期控制
"""

import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from app.core.logging import get_logger
from app.models.product import Product, ProductStatus, ProductChangeRecord, ProductType

logger = get_logger(__name__)


class LifecycleEvent(Enum):
    """生命周期事件"""
    CREATED = "created"                 # 商品创建
    ACTIVATED = "activated"             # 激活监控
    PAUSED = "paused"                  # 暂停监控
    RESUMED = "resumed"                # 恢复监控
    ARCHIVED = "archived"              # 归档
    RESTORED = "restored"              # 恢复
    DELETED = "deleted"                # 删除
    UPDATED = "updated"                # 信息更新
    QUALITY_CHANGED = "quality_changed" # 质量变更
    CATEGORY_CHANGED = "category_changed" # 分类变更


class MonitoringPriority(Enum):
    """监控优先级"""
    CRITICAL = "critical"              # 关键商品，高频监控
    HIGH = "high"                     # 重要商品，中高频监控
    NORMAL = "normal"                 # 普通商品，正常频率
    LOW = "low"                       # 低优先级，低频监控
    MINIMAL = "minimal"               # 最低优先级，最低频率


@dataclass
class LifecycleRule:
    """生命周期规则"""
    id: str
    name: str
    trigger_event: LifecycleEvent
    conditions: Dict[str, Any]
    actions: List[str]
    priority: int = 1
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class MonitoringConfig:
    """监控配置"""
    product_id: str
    priority: MonitoringPriority
    frequency_minutes: int
    enabled: bool = True
    last_monitored: Optional[datetime] = None
    next_monitor_time: Optional[datetime] = None
    failure_count: int = 0
    max_failures: int = 5
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class LifecycleMetrics:
    """生命周期指标"""
    product_id: str
    total_events: int = 0
    status_changes: int = 0
    quality_changes: int = 0
    monitoring_failures: int = 0
    last_activity: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)


class ProductLifecycleManager:
    """商品生命周期管理器"""
    
    def __init__(self):
        self.lifecycle_rules: List[LifecycleRule] = []
        self.monitoring_configs: Dict[str, MonitoringConfig] = {}
        self.lifecycle_metrics: Dict[str, LifecycleMetrics] = {}
        self._initialize_default_rules()
        
    def _initialize_default_rules(self):
        """初始化默认生命周期规则"""
        default_rules = [
            # 新商品自动激活规则
            LifecycleRule(
                id="rule_auto_activate_new",
                name="新商品自动激活",
                trigger_event=LifecycleEvent.CREATED,
                conditions={"product_type": ["competitor", "supplier"]},
                actions=["activate_monitoring", "set_priority"],
                priority=10
            ),
            
            # 质量差的商品降低优先级
            LifecycleRule(
                id="rule_downgrade_poor_quality",
                name="低质量商品降级",
                trigger_event=LifecycleEvent.QUALITY_CHANGED,
                conditions={"quality_score_max": 0.3},
                actions=["lower_priority", "reduce_frequency"],
                priority=8
            ),
            
            # 竞品商品提高优先级
            LifecycleRule(
                id="rule_upgrade_competitor",
                name="竞品商品升级",
                trigger_event=LifecycleEvent.CATEGORY_CHANGED,
                conditions={"product_type": "competitor"},
                actions=["raise_priority", "increase_frequency"],
                priority=9
            ),
            
            # 长期失败的商品暂停监控
            LifecycleRule(
                id="rule_pause_failed",
                name="失败商品暂停",
                trigger_event=LifecycleEvent.UPDATED,
                conditions={"failure_count_min": 5},
                actions=["pause_monitoring", "notify_admin"],
                priority=7
            ),
        ]
        
        self.lifecycle_rules = default_rules
    
    async def track_lifecycle_event(self, product: Product, event: LifecycleEvent, 
                                  metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        跟踪生命周期事件
        
        Args:
            product: 商品对象
            event: 生命周期事件
            metadata: 事件元数据
        
        Returns:
            bool: 是否处理成功
        """
        try:
            logger.info(f"跟踪生命周期事件: {product.id} - {event.value}")
            
            # 记录事件到变更历史
            await self._record_lifecycle_event(product, event, metadata)
            
            # 更新生命周期指标
            await self._update_lifecycle_metrics(product.id, event)
            
            # 应用生命周期规则
            await self._apply_lifecycle_rules(product, event, metadata)
            
            # 更新监控配置
            await self._update_monitoring_config(product, event)
            
            logger.info(f"生命周期事件处理完成: {product.id} - {event.value}")
            return True
            
        except Exception as e:
            logger.error(f"跟踪生命周期事件失败: {e}")
            return False
    
    async def _record_lifecycle_event(self, product: Product, event: LifecycleEvent,
                                    metadata: Optional[Dict[str, Any]] = None):
        """记录生命周期事件到变更历史"""
        # 直接使用Product的add_change_record方法，它会自动生成ID
        product.add_change_record(
            change_type="lifecycle_event",
            old_value=product.status.value if hasattr(product, 'status') else None,
            new_value=event.value,
            reason=f"生命周期事件: {event.value}",
            metadata=metadata or {}
        )
    
    async def _update_lifecycle_metrics(self, product_id: str, event: LifecycleEvent):
        """更新生命周期指标"""
        if product_id not in self.lifecycle_metrics:
            self.lifecycle_metrics[product_id] = LifecycleMetrics(product_id=product_id)
        
        metrics = self.lifecycle_metrics[product_id]
        metrics.total_events += 1
        metrics.last_activity = datetime.now()
        
        # 根据事件类型更新特定指标
        if event in [LifecycleEvent.ACTIVATED, LifecycleEvent.PAUSED, 
                    LifecycleEvent.ARCHIVED, LifecycleEvent.RESTORED]:
            metrics.status_changes += 1
        elif event == LifecycleEvent.QUALITY_CHANGED:
            metrics.quality_changes += 1
    
    async def _apply_lifecycle_rules(self, product: Product, event: LifecycleEvent,
                                   metadata: Optional[Dict[str, Any]] = None):
        """应用生命周期规则"""
        for rule in self.lifecycle_rules:
            if not rule.enabled or rule.trigger_event != event:
                continue
            
            if await self._rule_matches_product(rule, product, metadata):
                await self._execute_rule_actions(rule, product)
    
    async def _rule_matches_product(self, rule: LifecycleRule, product: Product,
                                  metadata: Optional[Dict[str, Any]] = None) -> bool:
        """检查规则是否匹配商品"""
        try:
            conditions = rule.conditions
            
            # 检查商品类型
            if "product_type" in conditions:
                allowed_types = conditions["product_type"]
                if isinstance(allowed_types, str):
                    allowed_types = [allowed_types]
                if product.product_type.value not in allowed_types:
                    return False
            
            # 检查质量分数
            if "quality_score_max" in conditions:
                if product.data_quality_score > conditions["quality_score_max"]:
                    return False
            
            if "quality_score_min" in conditions:
                if product.data_quality_score < conditions["quality_score_min"]:
                    return False
            
            # 检查失败次数
            if "failure_count_min" in conditions:
                config = self.monitoring_configs.get(product.id)
                if not config or config.failure_count < conditions["failure_count_min"]:
                    return False
            
            # 检查元数据条件
            if metadata and "metadata" in conditions:
                metadata_conditions = conditions["metadata"]
                for key, expected_value in metadata_conditions.items():
                    if key not in metadata or metadata[key] != expected_value:
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查规则匹配失败: {e}")
            return False
    
    async def _execute_rule_actions(self, rule: LifecycleRule, product: Product):
        """执行规则动作"""
        for action in rule.actions:
            try:
                if action == "activate_monitoring":
                    await self._activate_monitoring(product)
                elif action == "pause_monitoring":
                    await self._pause_monitoring(product)
                elif action == "set_priority":
                    await self._set_monitoring_priority(product)
                elif action == "raise_priority":
                    await self._raise_monitoring_priority(product)
                elif action == "lower_priority":
                    await self._lower_monitoring_priority(product)
                elif action == "increase_frequency":
                    await self._increase_monitoring_frequency(product)
                elif action == "reduce_frequency":
                    await self._reduce_monitoring_frequency(product)
                elif action == "notify_admin":
                    await self._notify_admin(product, rule)
                
                logger.info(f"执行规则动作: {action} - {product.id}")
                
            except Exception as e:
                logger.error(f"执行规则动作失败: {action} - {e}")
    
    async def _activate_monitoring(self, product: Product):
        """激活监控"""
        if product.id not in self.monitoring_configs:
            priority = self._determine_initial_priority(product)
            frequency = self._get_frequency_by_priority(priority)
            
            self.monitoring_configs[product.id] = MonitoringConfig(
                product_id=product.id,
                priority=priority,
                frequency_minutes=frequency,
                enabled=True,
                next_monitor_time=datetime.now() + timedelta(minutes=frequency)
            )
        else:
            config = self.monitoring_configs[product.id]
            config.enabled = True
            config.updated_at = datetime.now()
        
        # 更新商品状态
        if product.status in [ProductStatus.NEW, ProductStatus.PAUSED]:
            product.status = ProductStatus.ACTIVE
    
    async def _pause_monitoring(self, product: Product):
        """暂停监控"""
        if product.id in self.monitoring_configs:
            config = self.monitoring_configs[product.id]
            config.enabled = False
            config.updated_at = datetime.now()
        
        # 更新商品状态
        product.status = ProductStatus.PAUSED
    
    def _determine_initial_priority(self, product: Product) -> MonitoringPriority:
        """确定初始监控优先级"""
        # 竞品商品高优先级
        if product.product_type == ProductType.COMPETITOR:
            return MonitoringPriority.HIGH
        
        # 供货商商品正常优先级
        elif product.product_type == ProductType.SUPPLIER:
            return MonitoringPriority.NORMAL
        
        # 高质量商品提高优先级
        elif product.data_quality_score > 0.8:
            return MonitoringPriority.HIGH
        
        # 低质量商品降低优先级
        elif product.data_quality_score < 0.5:
            return MonitoringPriority.LOW
        
        else:
            return MonitoringPriority.NORMAL
    
    def _get_frequency_by_priority(self, priority: MonitoringPriority) -> int:
        """根据优先级获取监控频率（分钟）"""
        frequency_map = {
            MonitoringPriority.CRITICAL: 15,    # 15分钟
            MonitoringPriority.HIGH: 60,        # 1小时
            MonitoringPriority.NORMAL: 240,     # 4小时
            MonitoringPriority.LOW: 720,        # 12小时
            MonitoringPriority.MINIMAL: 1440,   # 24小时
        }
        return frequency_map.get(priority, 240)
    
    async def _set_monitoring_priority(self, product: Product):
        """设置监控优先级"""
        priority = self._determine_initial_priority(product)
        await self._update_monitoring_priority(product, priority)
    
    async def _raise_monitoring_priority(self, product: Product):
        """提高监控优先级"""
        if product.id not in self.monitoring_configs:
            await self._activate_monitoring(product)
            return
        
        config = self.monitoring_configs[product.id]
        current_priority = config.priority
        
        # 提升优先级
        priority_levels = list(MonitoringPriority)
        current_index = priority_levels.index(current_priority)
        
        if current_index > 0:
            new_priority = priority_levels[current_index - 1]
            await self._update_monitoring_priority(product, new_priority)
    
    async def _lower_monitoring_priority(self, product: Product):
        """降低监控优先级"""
        if product.id not in self.monitoring_configs:
            await self._activate_monitoring(product)
            return
        
        config = self.monitoring_configs[product.id]
        current_priority = config.priority
        
        # 降低优先级
        priority_levels = list(MonitoringPriority)
        current_index = priority_levels.index(current_priority)
        
        if current_index < len(priority_levels) - 1:
            new_priority = priority_levels[current_index + 1]
            await self._update_monitoring_priority(product, new_priority)
    
    async def _update_monitoring_priority(self, product: Product, priority: MonitoringPriority):
        """更新监控优先级"""
        if product.id not in self.monitoring_configs:
            await self._activate_monitoring(product)
            return
        
        config = self.monitoring_configs[product.id]
        config.priority = priority
        config.frequency_minutes = self._get_frequency_by_priority(priority)
        config.updated_at = datetime.now()
        
        # 重新计算下次监控时间
        if config.enabled:
            config.next_monitor_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
    
    async def _increase_monitoring_frequency(self, product: Product):
        """增加监控频率"""
        if product.id not in self.monitoring_configs:
            await self._activate_monitoring(product)
            return
        
        config = self.monitoring_configs[product.id]
        # 频率提高50%（时间间隔减少）
        config.frequency_minutes = max(15, int(config.frequency_minutes * 0.5))
        config.updated_at = datetime.now()
        
        if config.enabled:
            config.next_monitor_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
    
    async def _reduce_monitoring_frequency(self, product: Product):
        """减少监控频率"""
        if product.id not in self.monitoring_configs:
            await self._activate_monitoring(product)
            return
        
        config = self.monitoring_configs[product.id]
        # 频率降低50%（时间间隔增加）
        config.frequency_minutes = min(1440, int(config.frequency_minutes * 2))
        config.updated_at = datetime.now()
        
        if config.enabled:
            config.next_monitor_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
    
    async def _notify_admin(self, product: Product, rule: LifecycleRule):
        """通知管理员"""
        # 这里可以实现邮件、短信或其他通知方式
        logger.warning(f"管理员通知: 商品 {product.id} 触发规则 {rule.name}")
    
    async def _update_monitoring_config(self, product: Product, event: LifecycleEvent):
        """更新监控配置"""
        if product.id not in self.monitoring_configs:
            return
        
        config = self.monitoring_configs[product.id]
        
        # 根据事件类型更新配置
        if event == LifecycleEvent.UPDATED:
            config.last_monitored = datetime.now()
            # 重置失败计数（如果更新成功）
            config.failure_count = 0
        
        config.updated_at = datetime.now()
    
    async def get_products_to_monitor(self) -> List[Tuple[str, MonitoringConfig]]:
        """获取需要监控的商品列表"""
        now = datetime.now()
        products_to_monitor = []
        
        for product_id, config in self.monitoring_configs.items():
            if (config.enabled and 
                config.next_monitor_time and 
                config.next_monitor_time <= now):
                products_to_monitor.append((product_id, config))
        
        # 按优先级排序
        priority_order = {
            MonitoringPriority.CRITICAL: 0,
            MonitoringPriority.HIGH: 1,
            MonitoringPriority.NORMAL: 2,
            MonitoringPriority.LOW: 3,
            MonitoringPriority.MINIMAL: 4,
        }
        
        products_to_monitor.sort(key=lambda x: priority_order.get(x[1].priority, 5))
        
        return products_to_monitor
    
    async def record_monitoring_failure(self, product_id: str):
        """记录监控失败"""
        if product_id not in self.monitoring_configs:
            return

        config = self.monitoring_configs[product_id]
        config.failure_count += 1
        config.updated_at = datetime.now()

        # 更新指标
        if product_id not in self.lifecycle_metrics:
            self.lifecycle_metrics[product_id] = LifecycleMetrics(product_id=product_id)

        self.lifecycle_metrics[product_id].monitoring_failures += 1

        # 如果失败次数过多，触发事件
        if config.failure_count >= config.max_failures:
            # 这里可以创建一个模拟的Product对象来触发事件
            # 实际应用中应该从数据库获取完整的Product对象
            logger.warning(f"商品 {product_id} 监控失败次数过多: {config.failure_count}")
    
    async def record_monitoring_success(self, product_id: str):
        """记录监控成功"""
        if product_id not in self.monitoring_configs:
            return
        
        config = self.monitoring_configs[product_id]
        config.last_monitored = datetime.now()
        config.failure_count = 0  # 重置失败计数
        config.next_monitor_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
        config.updated_at = datetime.now()
    
    def get_lifecycle_statistics(self) -> Dict[str, Any]:
        """获取生命周期统计信息"""
        total_products = len(self.monitoring_configs)
        active_monitoring = sum(1 for config in self.monitoring_configs.values() if config.enabled)
        
        # 按优先级统计
        priority_stats = {}
        for priority in MonitoringPriority:
            count = sum(1 for config in self.monitoring_configs.values() 
                       if config.priority == priority)
            priority_stats[priority.value] = count
        
        # 按状态统计
        total_events = sum(metrics.total_events for metrics in self.lifecycle_metrics.values())
        total_status_changes = sum(metrics.status_changes for metrics in self.lifecycle_metrics.values())
        total_failures = sum(metrics.monitoring_failures for metrics in self.lifecycle_metrics.values())
        
        return {
            "total_products": total_products,
            "active_monitoring": active_monitoring,
            "paused_monitoring": total_products - active_monitoring,
            "priority_distribution": priority_stats,
            "total_lifecycle_events": total_events,
            "total_status_changes": total_status_changes,
            "total_monitoring_failures": total_failures,
            "rules_count": len(self.lifecycle_rules),
            "enabled_rules": sum(1 for rule in self.lifecycle_rules if rule.enabled)
        }
    
    def add_lifecycle_rule(self, rule: LifecycleRule):
        """添加生命周期规则"""
        self.lifecycle_rules.append(rule)
        # 按优先级排序
        self.lifecycle_rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"添加生命周期规则: {rule.name}")
    
    def remove_lifecycle_rule(self, rule_id: str) -> bool:
        """移除生命周期规则"""
        original_count = len(self.lifecycle_rules)
        self.lifecycle_rules = [r for r in self.lifecycle_rules if r.id != rule_id]
        
        if len(self.lifecycle_rules) < original_count:
            logger.info(f"移除生命周期规则: {rule_id}")
            return True
        
        return False
    
    def get_lifecycle_rules(self) -> List[Dict[str, Any]]:
        """获取生命周期规则列表"""
        return [
            {
                "id": rule.id,
                "name": rule.name,
                "trigger_event": rule.trigger_event.value,
                "conditions": rule.conditions,
                "actions": rule.actions,
                "priority": rule.priority,
                "enabled": rule.enabled,
                "created_at": rule.created_at.isoformat()
            }
            for rule in self.lifecycle_rules
        ]
