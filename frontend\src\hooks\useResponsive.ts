/**
 * 响应式布局Hook
 */

import { useState, useEffect } from 'react';

export interface BreakpointMap {
  xs: boolean;  // < 576px
  sm: boolean;  // >= 576px
  md: boolean;  // >= 768px
  lg: boolean;  // >= 992px
  xl: boolean;  // >= 1200px
  xxl: boolean; // >= 1600px
}

export interface ResponsiveInfo extends BreakpointMap {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
};

export const useResponsive = (): ResponsiveInfo => {
  const [responsive, setResponsive] = useState<ResponsiveInfo>(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      width,
      height,
      xs: width < breakpoints.sm,
      sm: width >= breakpoints.sm,
      md: width >= breakpoints.md,
      lg: width >= breakpoints.lg,
      xl: width >= breakpoints.xl,
      xxl: width >= breakpoints.xxl,
      isMobile: width < breakpoints.md,
      isTablet: width >= breakpoints.md && width < breakpoints.lg,
      isDesktop: width >= breakpoints.lg,
    };
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setResponsive({
        width,
        height,
        xs: width < breakpoints.sm,
        sm: width >= breakpoints.sm,
        md: width >= breakpoints.md,
        lg: width >= breakpoints.lg,
        xl: width >= breakpoints.xl,
        xxl: width >= breakpoints.xxl,
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return responsive;
};

// 响应式列配置Hook
export const useResponsiveColumns = () => {
  const responsive = useResponsive();

  const getColumns = (config: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  }) => {
    if (responsive.xxl && config.xxl) return config.xxl;
    if (responsive.xl && config.xl) return config.xl;
    if (responsive.lg && config.lg) return config.lg;
    if (responsive.md && config.md) return config.md;
    if (responsive.sm && config.sm) return config.sm;
    if (responsive.xs && config.xs) return config.xs;
    return config.lg || config.md || config.sm || config.xs || 1;
  };

  return { responsive, getColumns };
};

// 响应式表格配置Hook
export const useResponsiveTable = () => {
  const responsive = useResponsive();

  const getTableProps = () => {
    if (responsive.isMobile) {
      return {
        size: 'small' as const,
        scroll: { x: 'max-content' },
        pagination: {
          simple: true,
          showSizeChanger: false,
          showQuickJumper: false,
        },
      };
    }

    if (responsive.isTablet) {
      return {
        size: 'middle' as const,
        pagination: {
          showSizeChanger: true,
          showQuickJumper: false,
          showTotal: (total: number, range: [number, number]) => 
            `${range[0]}-${range[1]} / ${total}`,
        },
      };
    }

    return {
      size: 'middle' as const,
      pagination: {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number, range: [number, number]) => 
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      },
    };
  };

  return { responsive, getTableProps };
};

// 响应式表单配置Hook
export const useResponsiveForm = () => {
  const responsive = useResponsive();

  const getFormProps = () => {
    if (responsive.isMobile) {
      return {
        layout: 'vertical' as const,
        labelCol: undefined,
        wrapperCol: undefined,
      };
    }

    return {
      layout: 'horizontal' as const,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    };
  };

  const getModalProps = () => {
    if (responsive.isMobile) {
      return {
        width: '95%',
        style: { top: 20 },
      };
    }

    if (responsive.isTablet) {
      return {
        width: '80%',
        style: { top: 50 },
      };
    }

    return {
      width: 600,
    };
  };

  return { responsive, getFormProps, getModalProps };
};
