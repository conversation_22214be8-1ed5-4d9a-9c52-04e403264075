"""
主应用测试
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock

from tests.test_app import create_test_app, mock_db_session_generator


@pytest.fixture
def mock_database():
    """模拟数据库"""
    with patch('app.core.database.init_database') as mock_init_db:
        mock_init_db.return_value = AsyncMock()
        yield mock_init_db


@pytest.fixture
def mock_cache():
    """模拟缓存"""
    with patch('app.core.cache.init_cache_manager') as mock_init_cache:
        mock_init_cache.return_value = AsyncMock()
        yield mock_init_cache


@pytest.fixture
def client():
    """测试客户端"""
    app = create_test_app()
    # 重写数据库依赖
    from app.core.database import get_db_session
    app.dependency_overrides[get_db_session] = mock_db_session_generator
    return TestClient(app)


def test_root_endpoint(client):
    """测试根路径端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data



def test_health_endpoint(client):
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data


def test_docs_endpoint(client):
    """测试API文档端点"""
    response = client.get("/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


def test_openapi_endpoint(client):
    """测试OpenAPI规范端点"""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data
    assert "paths" in data


def test_products_endpoint(client):
    """测试商品API端点"""
    response = client.get("/api/v1/products")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data


def test_analytics_endpoint(client):
    """测试分析API端点"""
    response = client.get("/api/v1/analytics/price/trends/test-product-id?days=30")
    assert response.status_code == 200
    data = response.json()
    assert "product_id" in data
    assert "time_range" in data


def test_system_info_endpoint(client):
    """测试系统信息端点"""
    response = client.get("/api/v1/system/info")
    assert response.status_code == 200
    data = response.json()
    assert "name" in data
    assert "version" in data


class TestApplicationStartup:
    """应用启动测试"""
    
    @patch('app.core.database.init_database')
    @patch('app.core.cache.init_cache_manager')
    def test_app_creation(self, mock_cache, mock_db):
        """测试应用创建"""
        from app.main import create_app
        
        app = create_app()
        assert app is not None
        assert app.title == "电商商品监控系统"
        assert app.version == "1.0.0"
    
    def test_middleware_configuration(self):
        """测试中间件配置"""
        app = create_test_app()
        
        # 检查中间件是否正确配置
        middleware_types = [type(middleware) for middleware in app.user_middleware]
        middleware_names = [middleware.__class__.__name__ for middleware in app.user_middleware]
        
        # 应该包含我们配置的中间件
        expected_middleware = [
            "CORSMiddleware",
            "TrustedHostMiddleware", 
            "PerformanceMiddleware",
            "RateLimitMiddleware",
            "CacheMiddleware",
            "CacheCleanupMiddleware"
        ]
        
        # 检查是否包含中间件（测试应用有简化的中间件配置）
        assert len(middleware_names) > 0


class TestErrorHandling:
    """错误处理测试"""
    
    def test_404_error(self, client):
        """测试404错误处理"""
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
    
    def test_validation_error(self, client):
        """测试验证错误处理"""
        # 发送无效的查询参数
        response = client.get("/api/v1/products?limit=invalid")
        assert response.status_code == 422  # Validation error
    
    def test_internal_server_error(self, client):
        """测试内部服务器错误处理"""
        # 测试不存在的端点，应该返回404而不是500
        response = client.get("/api/v1/nonexistent-endpoint")
        assert response.status_code == 404



@pytest.mark.asyncio
class TestAsyncEndpoints:
    """异步端点测试"""
    
    async def test_async_health_check(self):
        """测试异步健康检查"""
        from app.main import app
        
        # 这里可以添加更复杂的异步测试
        assert app is not None
