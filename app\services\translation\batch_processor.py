"""
批量翻译处理器

实现智能批量翻译处理，支持大批量商品标题和描述的翻译
"""

import asyncio
import uuid
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import statistics

from app.core.logging import get_logger
from app.services.translation.translation_engine import (
    TranslationEngine, TranslationRequest, TranslationResult, 
    LanguageCode, TextType, TranslationStatus
)

logger = get_logger(__name__)


class BatchStatus(Enum):
    """批量翻译状态"""
    PENDING = "pending"         # 等待中
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消
    PAUSED = "paused"          # 已暂停


class BatchPriority(Enum):
    """批量翻译优先级"""
    LOW = 1         # 低优先级
    NORMAL = 2      # 普通优先级
    HIGH = 3        # 高优先级
    URGENT = 4      # 紧急优先级
    CRITICAL = 5    # 关键优先级


@dataclass
class BatchItem:
    """批量翻译项目"""
    item_id: str
    text: str
    source_lang: LanguageCode
    target_lang: LanguageCode
    text_type: TextType
    priority: int = 2
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 状态信息
    status: TranslationStatus = TranslationStatus.PENDING
    translated_text: str = ""
    quality_score: Optional[float] = None
    error_message: str = ""
    retry_count: int = 0
    processing_time: float = 0.0
    cost: float = 0.0
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


@dataclass
class BatchJob:
    """批量翻译作业"""
    job_id: str
    name: str
    description: str = ""
    priority: BatchPriority = BatchPriority.NORMAL
    
    # 批量项目
    items: List[BatchItem] = field(default_factory=list)
    
    # 配置
    max_concurrent: int = 5
    retry_attempts: int = 3
    timeout_seconds: int = 30
    
    # 状态信息
    status: BatchStatus = BatchStatus.PENDING
    progress: float = 0.0
    total_items: int = 0
    completed_items: int = 0
    failed_items: int = 0
    
    # 统计信息
    total_cost: float = 0.0
    total_processing_time: float = 0.0
    average_quality_score: float = 0.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    
    # 回调函数
    progress_callback: Optional[Callable[[str, float], None]] = None
    completion_callback: Optional[Callable[[str, 'BatchJob'], None]] = None


class BatchProcessor:
    """批量翻译处理器"""
    
    def __init__(self, translation_engine: TranslationEngine):
        self.translation_engine = translation_engine
        
        # 批量作业存储
        self.batch_jobs: Dict[str, BatchJob] = {}
        
        # 处理配置
        self.processor_config = {
            "max_concurrent_jobs": 3,           # 最大并发作业数
            "max_concurrent_per_job": 10,       # 每个作业最大并发数
            "default_batch_size": 100,          # 默认批量大小
            "progress_update_interval": 5,      # 进度更新间隔（秒）
            "auto_retry_enabled": True,         # 启用自动重试
            "quality_threshold": 6.0,           # 质量阈值
            "cost_limit_per_job": 50.0,        # 每个作业成本限制
        }
        
        # 处理统计
        self.processor_stats = {
            "total_jobs": 0,
            "completed_jobs": 0,
            "failed_jobs": 0,
            "total_items_processed": 0,
            "total_processing_time": 0.0,
            "total_cost": 0.0,
            "average_items_per_job": 0.0,
            "average_processing_time_per_item": 0.0
        }
        
        # 活跃作业跟踪
        self.active_jobs: Dict[str, asyncio.Task] = {}
        
        # 进度更新任务
        self.progress_update_task: Optional[asyncio.Task] = None
        self._start_progress_updater()
    
    async def create_batch_job(self, name: str, items: List[BatchItem], 
                              priority: BatchPriority = BatchPriority.NORMAL,
                              **kwargs) -> str:
        """
        创建批量翻译作业
        
        Args:
            name: 作业名称
            items: 翻译项目列表
            priority: 作业优先级
            **kwargs: 其他配置参数
        
        Returns:
            str: 作业ID
        """
        try:
            job_id = str(uuid.uuid4())
            
            # 创建批量作业
            batch_job = BatchJob(
                job_id=job_id,
                name=name,
                priority=priority,
                items=items,
                total_items=len(items),
                **kwargs
            )
            
            # 存储作业
            self.batch_jobs[job_id] = batch_job
            self.processor_stats["total_jobs"] += 1
            
            logger.info(f"创建批量翻译作业: {job_id}, 名称: {name}, 项目数: {len(items)}")
            return job_id
            
        except Exception as e:
            logger.error(f"创建批量翻译作业失败: {e}")
            raise
    
    async def start_batch_job(self, job_id: str) -> bool:
        """
        启动批量翻译作业
        
        Args:
            job_id: 作业ID
        
        Returns:
            bool: 是否启动成功
        """
        try:
            if job_id not in self.batch_jobs:
                logger.error(f"批量作业不存在: {job_id}")
                return False
            
            batch_job = self.batch_jobs[job_id]
            
            if batch_job.status != BatchStatus.PENDING:
                logger.error(f"批量作业状态不正确: {job_id}, 状态: {batch_job.status}")
                return False
            
            # 检查并发限制
            if len(self.active_jobs) >= self.processor_config["max_concurrent_jobs"]:
                logger.warning(f"达到最大并发作业数限制: {self.processor_config['max_concurrent_jobs']}")
                return False
            
            # 更新作业状态
            batch_job.status = BatchStatus.PROCESSING
            batch_job.started_at = datetime.now()
            
            # 启动处理任务
            task = asyncio.create_task(self._process_batch_job(batch_job))
            self.active_jobs[job_id] = task
            
            logger.info(f"启动批量翻译作业: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动批量翻译作业失败: {job_id}, {e}")
            return False
    
    async def _process_batch_job(self, batch_job: BatchJob):
        """处理批量翻译作业"""
        try:
            logger.info(f"开始处理批量作业: {batch_job.job_id}, 项目数: {batch_job.total_items}")
            
            # 按优先级排序项目
            sorted_items = sorted(batch_job.items, key=lambda x: x.priority, reverse=True)
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(batch_job.max_concurrent)
            
            # 创建翻译任务
            tasks = []
            for item in sorted_items:
                task = asyncio.create_task(
                    self._process_batch_item(batch_job, item, semaphore)
                )
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 更新作业状态
            await self._finalize_batch_job(batch_job)
            
        except Exception as e:
            logger.error(f"处理批量作业失败: {batch_job.job_id}, {e}")
            batch_job.status = BatchStatus.FAILED
        finally:
            # 清理活跃作业
            if batch_job.job_id in self.active_jobs:
                del self.active_jobs[batch_job.job_id]
    
    async def _process_batch_item(self, batch_job: BatchJob, item: BatchItem, 
                                 semaphore: asyncio.Semaphore):
        """处理单个批量项目"""
        async with semaphore:
            try:
                # 更新项目状态
                item.status = TranslationStatus.PROCESSING
                item.started_at = datetime.now()
                
                # 创建翻译请求
                request = TranslationRequest(
                    request_id=f"{batch_job.job_id}_{item.item_id}",
                    text=item.text,
                    source_lang=item.source_lang,
                    target_lang=item.target_lang,
                    text_type=item.text_type,
                    priority=item.priority
                )
                
                # 执行翻译
                start_time = time.time()
                result = await self.translation_engine.translate(request)
                processing_time = time.time() - start_time
                
                # 更新项目结果
                item.translated_text = result.translated_text
                item.quality_score = result.quality_score.overall_score if result.quality_score else 0.0
                item.processing_time = processing_time
                item.cost = result.cost
                item.completed_at = datetime.now()
                
                # 检查翻译质量
                if (result.status == TranslationStatus.COMPLETED and 
                    item.quality_score >= self.processor_config["quality_threshold"]):
                    item.status = TranslationStatus.COMPLETED
                    batch_job.completed_items += 1
                else:
                    # 质量不达标，尝试重试
                    if (item.retry_count < batch_job.retry_attempts and 
                        self.processor_config["auto_retry_enabled"]):
                        item.retry_count += 1
                        item.status = TranslationStatus.PENDING
                        logger.warning(f"翻译质量不达标，重试: {item.item_id}, 质量评分: {item.quality_score}")
                        # 递归重试
                        await self._process_batch_item(batch_job, item, semaphore)
                    else:
                        item.status = TranslationStatus.FAILED
                        item.error_message = f"翻译质量不达标: {item.quality_score}"
                        batch_job.failed_items += 1
                
                # 更新批量作业统计
                batch_job.total_cost += item.cost
                batch_job.total_processing_time += processing_time
                
            except Exception as e:
                logger.error(f"处理批量项目失败: {item.item_id}, {e}")
                item.status = TranslationStatus.FAILED
                item.error_message = str(e)
                item.completed_at = datetime.now()
                batch_job.failed_items += 1
    
    async def _finalize_batch_job(self, batch_job: BatchJob):
        """完成批量作业"""
        try:
            # 计算统计信息
            completed_items = [item for item in batch_job.items 
                             if item.status == TranslationStatus.COMPLETED]
            
            if completed_items:
                quality_scores = [item.quality_score for item in completed_items 
                                if item.quality_score is not None]
                batch_job.average_quality_score = statistics.mean(quality_scores) if quality_scores else 0.0
            
            # 更新作业状态
            batch_job.completed_at = datetime.now()
            batch_job.progress = 100.0
            
            if batch_job.failed_items == 0:
                batch_job.status = BatchStatus.COMPLETED
                self.processor_stats["completed_jobs"] += 1
            else:
                batch_job.status = BatchStatus.FAILED if batch_job.completed_items == 0 else BatchStatus.COMPLETED
                if batch_job.status == BatchStatus.FAILED:
                    self.processor_stats["failed_jobs"] += 1
                else:
                    self.processor_stats["completed_jobs"] += 1
            
            # 更新全局统计
            self.processor_stats["total_items_processed"] += batch_job.total_items
            self.processor_stats["total_processing_time"] += batch_job.total_processing_time
            self.processor_stats["total_cost"] += batch_job.total_cost
            
            # 计算平均值
            if self.processor_stats["completed_jobs"] > 0:
                self.processor_stats["average_items_per_job"] = (
                    self.processor_stats["total_items_processed"] / 
                    self.processor_stats["completed_jobs"]
                )
            
            if self.processor_stats["total_items_processed"] > 0:
                self.processor_stats["average_processing_time_per_item"] = (
                    self.processor_stats["total_processing_time"] / 
                    self.processor_stats["total_items_processed"]
                )
            
            # 调用完成回调
            if batch_job.completion_callback:
                try:
                    batch_job.completion_callback(batch_job.job_id, batch_job)
                except Exception as e:
                    logger.error(f"批量作业完成回调失败: {batch_job.job_id}, {e}")
            
            logger.info(f"批量作业完成: {batch_job.job_id}, 状态: {batch_job.status.value}, "
                       f"完成: {batch_job.completed_items}/{batch_job.total_items}, "
                       f"成本: ${batch_job.total_cost:.4f}")
            
        except Exception as e:
            logger.error(f"完成批量作业失败: {batch_job.job_id}, {e}")
            batch_job.status = BatchStatus.FAILED
    
    async def pause_batch_job(self, job_id: str) -> bool:
        """暂停批量翻译作业"""
        try:
            if job_id not in self.batch_jobs:
                return False
            
            batch_job = self.batch_jobs[job_id]
            if batch_job.status != BatchStatus.PROCESSING:
                return False
            
            # 取消处理任务
            if job_id in self.active_jobs:
                self.active_jobs[job_id].cancel()
                del self.active_jobs[job_id]
            
            batch_job.status = BatchStatus.PAUSED
            logger.info(f"暂停批量翻译作业: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"暂停批量翻译作业失败: {job_id}, {e}")
            return False
    
    async def resume_batch_job(self, job_id: str) -> bool:
        """恢复批量翻译作业"""
        try:
            if job_id not in self.batch_jobs:
                return False
            
            batch_job = self.batch_jobs[job_id]
            if batch_job.status != BatchStatus.PAUSED:
                return False
            
            # 重置未完成项目状态
            for item in batch_job.items:
                if item.status == TranslationStatus.PROCESSING:
                    item.status = TranslationStatus.PENDING
            
            batch_job.status = BatchStatus.PENDING
            return await self.start_batch_job(job_id)
            
        except Exception as e:
            logger.error(f"恢复批量翻译作业失败: {job_id}, {e}")
            return False
    
    async def cancel_batch_job(self, job_id: str) -> bool:
        """取消批量翻译作业"""
        try:
            if job_id not in self.batch_jobs:
                return False
            
            batch_job = self.batch_jobs[job_id]
            
            # 取消处理任务
            if job_id in self.active_jobs:
                self.active_jobs[job_id].cancel()
                del self.active_jobs[job_id]
            
            batch_job.status = BatchStatus.CANCELLED
            batch_job.completed_at = datetime.now()
            
            logger.info(f"取消批量翻译作业: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消批量翻译作业失败: {job_id}, {e}")
            return False
    
    def get_batch_job(self, job_id: str) -> Optional[BatchJob]:
        """获取批量翻译作业"""
        return self.batch_jobs.get(job_id)
    
    def get_batch_jobs(self, status: Optional[BatchStatus] = None) -> List[BatchJob]:
        """获取批量翻译作业列表"""
        jobs = list(self.batch_jobs.values())
        
        if status:
            jobs = [job for job in jobs if job.status == status]
        
        # 按创建时间排序
        jobs.sort(key=lambda x: x.created_at, reverse=True)
        return jobs
    
    def _start_progress_updater(self):
        """启动进度更新器"""
        async def update_progress():
            while True:
                try:
                    await asyncio.sleep(self.processor_config["progress_update_interval"])
                    await self._update_all_progress()
                except Exception as e:
                    logger.error(f"进度更新失败: {e}")
        
        try:
            self.progress_update_task = asyncio.create_task(update_progress())
        except RuntimeError:
            # 没有运行的事件循环，跳过启动进度更新器
            logger.warning("没有运行的事件循环，跳过启动进度更新器")
    
    async def _update_all_progress(self):
        """更新所有作业进度"""
        for job_id, batch_job in self.batch_jobs.items():
            if batch_job.status == BatchStatus.PROCESSING:
                await self._update_job_progress(batch_job)
    
    async def _update_job_progress(self, batch_job: BatchJob):
        """更新作业进度"""
        try:
            if batch_job.total_items == 0:
                batch_job.progress = 0.0
                return
            
            # 计算进度
            completed_count = len([item for item in batch_job.items 
                                 if item.status in [TranslationStatus.COMPLETED, TranslationStatus.FAILED]])
            batch_job.progress = (completed_count / batch_job.total_items) * 100
            
            # 估算完成时间
            if completed_count > 0 and batch_job.started_at:
                elapsed_time = (datetime.now() - batch_job.started_at).total_seconds()
                estimated_total_time = elapsed_time * (batch_job.total_items / completed_count)
                batch_job.estimated_completion = batch_job.started_at + timedelta(seconds=estimated_total_time)
            
            # 调用进度回调
            if batch_job.progress_callback:
                try:
                    batch_job.progress_callback(batch_job.job_id, batch_job.progress)
                except Exception as e:
                    logger.error(f"进度回调失败: {batch_job.job_id}, {e}")
            
        except Exception as e:
            logger.error(f"更新作业进度失败: {batch_job.job_id}, {e}")
    
    def get_processor_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        try:
            active_jobs_count = len(self.active_jobs)
            pending_jobs_count = len([job for job in self.batch_jobs.values() 
                                    if job.status == BatchStatus.PENDING])
            
            return {
                "total_jobs": self.processor_stats["total_jobs"],
                "completed_jobs": self.processor_stats["completed_jobs"],
                "failed_jobs": self.processor_stats["failed_jobs"],
                "active_jobs": active_jobs_count,
                "pending_jobs": pending_jobs_count,
                "success_rate": (self.processor_stats["completed_jobs"] / 
                               max(self.processor_stats["total_jobs"], 1) * 100),
                "total_items_processed": self.processor_stats["total_items_processed"],
                "total_processing_time": self.processor_stats["total_processing_time"],
                "total_cost": self.processor_stats["total_cost"],
                "average_items_per_job": self.processor_stats["average_items_per_job"],
                "average_processing_time_per_item": self.processor_stats["average_processing_time_per_item"],
                "average_cost_per_item": (self.processor_stats["total_cost"] / 
                                        max(self.processor_stats["total_items_processed"], 1)),
                "config": self.processor_config
            }
            
        except Exception as e:
            logger.error(f"获取处理器统计失败: {e}")
            return {
                "total_jobs": 0,
                "completed_jobs": 0,
                "failed_jobs": 0,
                "active_jobs": 0,
                "pending_jobs": 0,
                "success_rate": 0.0
            }
    
    def stop_progress_updater(self):
        """停止进度更新器"""
        if self.progress_update_task:
            self.progress_update_task.cancel()
            self.progress_update_task = None
